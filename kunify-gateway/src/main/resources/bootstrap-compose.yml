server:
  port: 6666
router:
  name:
spring:
  application:
    name: kunify-gateway
  redis:
    database: 0
    port: 6379
    host: ************
    password: kunify@411
  cloud:
    gateway:
      discovery:
        locator:
          enabled: true #开启从注册中心动态创建路由的功能
          lower-case-service-id: true #使用小写服务名，默认是大写
    nacos:
      discovery:
        server-addr: ************:8848
        namespace: 5337f3ec-b831-4de9-8120-bce141e18fc7
      config:
        file-extension: yaml
        namespace: 5337f3ec-b831-4de9-8120-bce141e18fc7
        server-addr: ************:8848
  security:
    oauth2:
      resourceserver:
        jwt:
          jwk-set-uri: 'http://localhost:6666/kunify-auth/rsa/publicKey' #配置RSA的公钥访问地址
  servlet:
    multipart:
      max-request-size: 2000MB
      max-file-size: 2000MB
#ribbon config,Interval to refresh the server list from the source
ribbon:
  ServerListRefreshInterval: 1000

mongodb:
  host: ************
  port: 27017
  database: kunify-hn
  username: root
  password: mongo2022
  authenticationDatabase: admin

secure:
  ignore:
    urls:
      - '/kunify-system/tenant/list'
      - '/kunify-auth/oauth/token'
      - '/kunify-auth/oauth/token'
      - '/kunify-auth/oauth/captcha'
      - '/kunify-auth/rsa/publicKey'
      - '/kunify-user/user/login'
      - '/kunify-user/captcha/captchaImage'
      - '/kunify-user/user/register'
      - '/resource/oss/endpoint/download-file'
      - '/kunify-kg/qa/sync'
      - '/kunify-repository/test/download-no-water-match'
      - '/kunify-kg/param/test-download'