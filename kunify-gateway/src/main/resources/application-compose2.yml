spring:
  cloud:
    nacos:
      discovery:
        server-addr: ***********:8848
  redis:
    database: 0
    port: 6379
    host: ***********
    password: kunify@411
mongodb:
  host: ***********
  port: 27017
  database: kunify-hn
  username: root
  password: mongo2022
  authenticationDatabase: admin

img-constant:
  internet-domain: http://***********:9000
  domain: http://**************:9000
  bucket: /kunify-hn-

qa:
  dataset:
    all-id: 1783409423943733250
    bzry-id: 1783409342967508994
    hyjy-id: 1787673080802955266
    gzzd-id: 1787424937336684545
    ldps-id: 1783409270379823106

secure:
  ignore:
    urls:
      - '/kunify-system/tenant/list'
      - '/kunify-auth/oauth/token'
      - '/kunify-auth/oauth/token'
      - '/kunify-auth/oauth/captcha'
      - '/kunify-auth/rsa/publicKey'
      - '/kunify-user/user/login'
      - '/kunify-user/user/register'
      - '/resource/oss/endpoint/download-file'
      - '/kunify-kg/qa/sync'
      - '/kunify-repository/test/download-no-water-match'
      - '/kunify-kg/param/test-download'