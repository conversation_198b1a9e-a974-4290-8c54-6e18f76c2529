server:
  port: 1111
router:
  name:

spring:
  application:
    name: kunify-gateway
  cloud:
    gateway:
      discovery:
        locator:
          enabled: true #开启从注册中心动态创建路由的功能
          lower-case-service-id: true #使用小写服务名，默认是大写
    nacos:
      discovery:
        heart-beat-timeout: 60000
        server-addr: http://*************:8848
        username: nacos
        password: 'Nacosabc'
        namespace: zsl
        # 添加连接稳定性配置
        heart-beat-interval: 30000
        ip-delete-timeout: 60000
        instance-enabled: true
      config:
        file-extension: yml
        name: ${spring.application.name}.${spring.cloud.nacos.config.file-extension}
        namespace: zsl
        config-long-poll-timeout: 60000 # 默认30000;长轮询时间 1分钟
        config-retry-time: 5000 # 默认2000;重试5秒
        max-retry: 6000 # 默认3;最大重试次数
        server-addr: http://*************:8848
        username: nacos
        password: 'Nacosabc'
        # 添加配置刷新和连接稳定性配置
        refresh-enabled: true
        enabled: true

#spring:
#  cloud:
#    gateway:
#      discovery:
#        locator:
#          enabled: true #开启从注册中心动态创建路由的功能
#          lower-case-service-id: true #使用小写服务名，默认是大写
#    nacos:
#      discovery:
#        server-addr: kunify-nacos:8848
#        namespace: 5337f3ec-b831-4de9-8120-bce141e18fc7
#      config:
#        file-extension: yaml
#        namespace: 5337f3ec-b831-4de9-8120-bce141e18fc7
#        server-addr: kunify-nacos:8848


#spring:
#  cloud:
#    gateway:
#      discovery:
#        locator:
#          enabled: true #开启从注册中心动态创建路由的功能
#          lower-case-service-id: true #使用小写服务名，默认是大写
#    nacos:
#      discovery:
#        server-addr: 192.168.0.2:8848
#        namespace: 5337f3ec-b831-4de9-8120-bce141e18fc7
#      config:
#        file-extension: yaml
#        namespace: 5337f3ec-b831-4de9-8120-bce141e18fc7
#        server-addr: 192.168.0.2:8848