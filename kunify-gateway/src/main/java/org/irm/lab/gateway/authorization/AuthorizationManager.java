/*

 * @Author: <PERSON><PERSON> lich<PERSON>

 * @Date: 2020-11-13 17:49:20

 * @LastEditors: liu lichao

 * @LastEditTime: 2021-01-13 11:08:17

 * @Description: 鉴权管理器，用于判断是否有资源的访问权限

 */

package org.irm.lab.gateway.authorization;



import cn.hutool.core.util.StrUtil;

import cn.hutool.json.JSONObject;

import cn.hutool.json.JSONUtil;

import com.nimbusds.jose.JWSObject;

import org.irm.lab.common.constant.AuthConstant;

import org.irm.lab.gateway.config.IgnoreUrlsConfig;

import org.irm.lab.user.dto.UserDTO;

import org.irm.lab.user.entity.User;

import org.irm.lab.user.feign.DataPermissionFeign;

import org.irm.lab.user.feign.RoleFeign;

import org.irm.lab.user.feign.UserFeign;

import org.springframework.beans.factory.annotation.Autowired;

import org.springframework.data.redis.core.RedisTemplate;

import org.springframework.http.HttpMethod;

import org.springframework.http.server.reactive.ServerHttpRequest;

import org.springframework.security.authorization.AuthorizationDecision;

import org.springframework.security.authorization.ReactiveAuthorizationManager;

import org.springframework.security.core.Authentication;

import org.springframework.security.web.server.authorization.AuthorizationContext;

import org.springframework.stereotype.Component;

import org.springframework.util.AntPathMatcher;

import org.springframework.util.PathMatcher;

import reactor.core.publisher.Mono;



import javax.annotation.Resource;

import java.net.URI;

import java.text.ParseException;

import java.util.ArrayList;

import java.util.List;

import java.util.stream.Collectors;
import reactor.core.scheduler.Schedulers;


@Component

public class AuthorizationManager implements ReactiveAuthorizationManager<AuthorizationContext> {



    @Autowired

    private RedisTemplate<String, Object> redisTemplate;

    @Autowired

    private IgnoreUrlsConfig ignoreUrlsConfig;

    @Resource

    private RoleFeign roleFeign;

    @Resource

    private DataPermissionFeign dataPermissionFeign;

    @Resource

    private UserFeign userFeign;



    @Override

    public Mono<AuthorizationDecision> check(Mono<Authentication> authentication, AuthorizationContext authorizationContext) {

        ServerHttpRequest request = authorizationContext.getExchange().getRequest();

        URI uri = request.getURI();

//        System.out.println("网关：" +  uri.getPath());

        //截取路径格式为：/controller/service

//        String[] split = uri.getPath().split("/");

//        String requestPath = "/" + split[split.length - 2] + "/" + split[split.length - 1];

        PathMatcher pathMatcher = new AntPathMatcher();

        //获取所有数据权限的路径

//        List<String> allPathList = dataPermissionFeign.getAllPath().getData();

//        //如果当前路径不在权限路径之内则直接放行

//        if (!allPathList.contains(requestPath)){

//            return Mono.just(new AuthorizationDecision(true));

//        }

        // 白名单路径直接放行

        List<String> ignoreUrls = ignoreUrlsConfig.getUrls();

        for (String ignoreUrl : ignoreUrls) {

            if (pathMatcher.match(ignoreUrl, uri.getPath())) {

                return Mono.just(new AuthorizationDecision(true));

            }

        }

//        //非管理端路径直接放行

//        if (pathMatcher.match(AuthConstant.APP_URL_PATTERN, uri.getPath())) {

//            return Mono.just(new AuthorizationDecision(true));

//        }

        // 对应跨域的预检请求直接放行

        if (request.getMethod() == HttpMethod.OPTIONS) {

            return Mono.just(new AuthorizationDecision(true));

        }

        // 不同用户体系登录不允许互相访问

        UserDTO userDto;

        try {

            String token = request.getHeaders().getFirst(AuthConstant.JWT_TOKEN_HEADER);

            if (StrUtil.isEmpty(token)) {

                return Mono.just(new AuthorizationDecision(false));

            }

            String realToken = token.replace(AuthConstant.JWT_TOKEN_PREFIX, "");

            JWSObject jwsObject = JWSObject.parse(realToken);

            String userStr = jwsObject.getPayload().toString();

            JSONObject jsonObject = JSONUtil.parseObj(userStr);

            userDto = JSONUtil.toBean(userStr, UserDTO.class);

            userDto.setUsername(jsonObject.get("user_name").toString());

            userDto.setId(jsonObject.get("user_id").toString());

            if (AuthConstant.ADMIN_CLIENT_ID.equals(userDto.getClientId()) ||

                    AuthConstant.PORTAL_CLIENT_ID.equals(userDto.getClientId())) {

                boolean belongToAdmin = false;

                for (String pattern : AuthConstant.ADMIN_URL_PATTERNS) {

                    if (pathMatcher.match(pattern, uri.getPath())) belongToAdmin = true;

                }

                if (!belongToAdmin) return Mono.just(new AuthorizationDecision(false));

            }

        } catch (ParseException e) {

            e.printStackTrace();

            return Mono.just(new AuthorizationDecision(false));

        }

        //判断当前用户有没有本接口权限

        if (!"suwell".equals(userDto.getUsername())) {
            List<String> finalPathList = new ArrayList<>();
            //查询当前用户的角色列表
            final UserDTO finalUserDto = userDto;
            return Mono.fromCallable(() -> {
                        // 在单独的线程池中执行阻塞操作
                        User currentUser = userFeign.info(finalUserDto.getId()).getData();
                        List<String> roleIds = currentUser.getRoleIds();

                        roleIds.forEach(roleId -> {
                            finalPathList.addAll(roleFeign.getPermissionByRoleId(roleId).getData());
                        });

                        System.out.println("--------------------token已获取，经过验证可进入系统---------------------");
                        return new AuthorizationDecision(true);
                    })
                    .subscribeOn(Schedulers.boundedElastic());
//            }
        }

//            if (!pathList.contains(requestPath)) {

            System.out.println("--------------------token已获取，经过验证可进入系统---------------------" );

            return Mono.just(new AuthorizationDecision(true));

//            }

        }




//        Map<Object, Object> resourceRolesMap = redisTemplate.opsForHash().entries(AuthConstant.RESOURCE_ROLES_MAP_KEY);

//        Iterator<Object> iterator = resourceRolesMap.keySet().iterator();

//        List<String> authorities = new ArrayList<>();

//        boolean pathMatched = false; // redis中是否存在与当前路径相匹配的 key

//        while (iterator.hasNext()) {

//            String pattern = (String) iterator.next();

//            if (pathMatcher.match(pattern, uri.getPath())) {

//                pathMatched = true;

//                authorities.addAll(Convert.toList(String.class, resourceRolesMap.get(pattern)));

//            }

//        }

//        // 如果redis中不存在与当前路径匹配的 key，说明该路径未进行权限控制，直接放行

//        if (!pathMatched) return Mono.just(new AuthorizationDecision(false));

//

//        authorities = authorities.stream().map(i -> i = AuthConstant.AUTHORITY_PREFIX + i).collect(Collectors.toList());

//        // authorities.add(AuthConstant.AUTHORITY_PREFIX + "ADMIN");

//        // authorities.add(AuthConstant.AUTHORITY_PREFIX + "USER");

//

//        // 超级管理员默认加上所有

//        //认证通过且角色匹配的用户可访问当前路径

//        return authentication

//                .filter(Authentication::isAuthenticated)

//                .flatMapIterable(Authentication::getAuthorities)

//                .map(GrantedAuthority::getAuthority)

//                .any(authorities::contains)

//                .map(AuthorizationDecision::new)

//                .defaultIfEmpty(new AuthorizationDecision(false));

    }



