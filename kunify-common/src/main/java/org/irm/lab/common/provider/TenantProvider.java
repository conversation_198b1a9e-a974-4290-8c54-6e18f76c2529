package org.irm.lab.common.provider;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.json.JSONObject;
import org.irm.lab.common.config.EnvironmentConfig;
import org.irm.lab.common.constant.AppConstant;
import org.irm.lab.common.constant.AuthConstant;
import org.irm.lab.common.utils.ThreadLocalUtil;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;

@Component
@Order(0)
public class TenantProvider {
    @Resource
    private EnvironmentConfig environmentConfig;

    public String getDatabaseName() {
        //从Header中获取用户信息
        ServletRequestAttributes attributes = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
        if (attributes != null) {
            HttpServletRequest request = attributes.getRequest();
            if (ObjectUtil.isNotEmpty(request.getHeader("user"))) {
                String userStr = request.getHeader("user");
                return AppConstant.TENANT_DB_PREFIX + new JSONObject(userStr).getStr(AuthConstant.TENANT_ID);
            } else {
                String tenantId = request.getHeader("TENANT_ID");
                return AppConstant.TENANT_DB_PREFIX + tenantId;
            }
        } else {
            return AppConstant.TENANT_DB_PREFIX + new JSONObject(ThreadLocalUtil.get("user")).getStr(AuthConstant.TENANT_ID);
        }
    }

    public String getTenantId() {
        ServletRequestAttributes attributes = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
        if (attributes != null) {
            HttpServletRequest request = attributes.getRequest();
            if (ObjectUtil.isNotEmpty(request.getHeader("user"))) {
                String userStr = request.getHeader("user");
                return new JSONObject(userStr).getStr(AuthConstant.TENANT_ID);
            }
            if (ObjectUtil.isNotEmpty(request.getHeader("TENANT_ID"))) return request.getHeader("TENANT_ID");
        }
        if (ObjectUtil.isNotEmpty(ThreadLocalUtil.get("user"))) {
            return new JSONObject(ThreadLocalUtil.get("user")).getStr(AuthConstant.TENANT_ID);
        }

        // 现场默认租户028641
        // 公司默认租户028639
        return environmentConfig.getTenantId();
    }

}
