package org.irm.lab.common.constant;

//概念常量
public interface ModelConceptConst {
    String CONCEPT_XIANGMU_ANLI = "项目/案例";
    String CONCEPT_JIANGXIANG_JIYU = "奖项寄语";
    String CONCEPT_SHOUJIANG_JIGOU = "授奖机构";
    String CONCEPT_BIAOZHANG_SHIJIAN = "表彰时间";
    String CONCEPT_BIAOZHANG_NIANDU = "表彰年度";
    String CONCEPT_BIAOZHANG_RONGYU = "表彰荣誉";
    String CONCEPT_HUIYI_SHIJIAN = "会议时间";
    String CONCEPT_HUIYI_QISHU = "会议期数";
    String CONCEPT_SHUNXU = "顺序";
    String CONCEPT_JIYAO_NEIRONG = "纪要内容";
    String CONCEPT_MINGCHENG = "名称";
    String CONCEPT_DINGYI_NEIRONG = "定义内容";
    String CONCEPT_GAINIAN_SHUYU = "概念术语";
    String CONCEPT_JIGOU = "机构";
    String CONCEPT_ZHIZE_NEIRONG = "制度内容";
    String CONCEPT_NEIRONG = "内容";
    String CONCEPT_ZHIDU_SHUYU = "制度术语";
    String CONCEPT_TIAOKUAN_NEIRONG = "条款内容";
    String CONCEPT_TIAOKUAN_XUHAO = "条款序号";
    String CONCEPT_ZHIZE = "职责";
    String CONCEPT_FEIZHI_WENJIAN = "废止文件";
    String CONCEPT_ZHIDING_YIJU = "制定依据";
    String CONCEPT_SHIYONG_FANWEI = "适用范围";
    String CONCEPT_ZHIDING_MUDI = "制定目的";
    String CONCEPT_XUHAO = "序号";
    String CONCEPT_TIAOKUAN_XIZE = "条款细则";
    String CONCEPT_ZHIDU_TIAOKUAN = "制度条款";
    String CONCEPT_ZHUANYE_SHUYU = "专业术语";
    String CONCEPT_GUANJIANCI = "关键词";
    String CONCEPT_ZHIDU = "制度";
    String CONCEPT_SHIXIANG = "事项";
    String CONCEPT_XIANGMU = "项目";
    String CONCEPT_JIYAO = "纪要";
    String CONCEPT_HUIYI_YITI = "会议议题";
    String CONCEPT_HUIYI = "会议";
    String CONCEPT_ZHIWU = "职务";
    String CONCEPT_BIAOZHANG_JIANGXIANG = "表彰奖项";
    String CONCEPT_JIGOU_BUMEN = "机构/部门";
    String CONCEPT_RENYUAN = "人员";
    String CONCEPT_BIAOZHANG_CHENGHAO = "表彰称号";
    String CONCEPT_BIAOZHANG_LEIXING = "表彰类型";
    String CONCEPT_WENJIAN = "文件";
    String CONCEPT_GONGWEN = "公文";
    String CONCEPT_FUJIAN = "附件";


}
