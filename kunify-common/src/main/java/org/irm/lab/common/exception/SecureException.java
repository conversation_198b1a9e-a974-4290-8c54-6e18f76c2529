package org.irm.lab.common.exception;

import org.irm.lab.common.api.IErrorCode;
import org.irm.lab.common.api.ResultCode;

import lombok.Getter;

public class SecureException extends RuntimeException {
    private static final long serialVersionUID = 2359767895161832954L;

    @Getter
    private final IErrorCode errorCode;

    public SecureException(String message) {
        super(message);
        this.errorCode = ResultCode.UNAUTHORIZED;
    }

    public SecureException(IErrorCode errorCode) {
        super(errorCode.getMessage());
        this.errorCode = errorCode;
    }

    public SecureException(IErrorCode errorCode, Throwable cause) {
        super(cause);
        this.errorCode = errorCode;
    }

    @Override
    public Throwable fillInStackTrace() {
        return this;
    }
}