package org.irm.lab.common.repository.mongo;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONObject;
import com.mongodb.bulk.BulkWriteInsert;
import com.mongodb.bulk.BulkWriteResult;
import com.mongodb.bulk.BulkWriteUpsert;
import com.mongodb.client.MongoCollection;
import com.mongodb.client.MongoCursor;
import com.mongodb.client.MongoDatabase;
import com.mongodb.client.model.*;
import com.mongodb.client.result.DeleteResult;
import com.mongodb.client.result.InsertManyResult;
import com.mongodb.client.result.InsertOneResult;
import com.mongodb.client.result.UpdateResult;
import lombok.extern.slf4j.Slf4j;
import org.bson.BsonValue;
import org.bson.conversions.Bson;
import org.bson.types.ObjectId;
import org.irm.lab.common.annotation.MongoDBCollection;
import org.irm.lab.common.annotation.MongoDao;
import org.irm.lab.common.config.tenant.MongoTenantFactory;
import org.irm.lab.common.constant.AbstractBaseEntityFieldConstant;
import org.irm.lab.common.constant.AuthConstant;
import org.irm.lab.common.constant.MongoExceptionConstant;
import org.irm.lab.common.entity.AbstractBaseEntity;
import org.irm.lab.common.exception.BaseDaoException;
import org.irm.lab.common.pojo.BaseStatus;
import org.irm.lab.common.provider.TenantProvider;
import org.irm.lab.common.support.MyPage;
import org.irm.lab.common.utils.ThreadLocalUtil;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.lang.reflect.ParameterizedType;
import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static java.util.Collections.singletonList;


@MongoDao
@Slf4j
public class BaseDao<T extends AbstractBaseEntity> {

    @Resource
    protected TenantProvider tenantProvider;
    @Resource
    protected MongoTenantFactory mongoTenantFactory;


    /**
     * 获取泛型类 T 的 {@link Class} 对象
     *
     * @return {@link Class}
     */
    protected Class<T> getTClass() {
        return (Class<T>) ((ParameterizedType) getClass().getGenericSuperclass()).getActualTypeArguments()[0];
    }

    /**
     * 根据泛型类 T 对应的实体类上的 {@link MongoDBCollection} 确定对应的 MongoDB 集合
     *
     * @return {@link MongoCollection}
     */
    protected MongoCollection<T> getTCollection() {
        MongoDBCollection annotation = getTClass().getAnnotation(MongoDBCollection.class);
        if (!annotation.checkTenant()) {
            MongoDatabase mongoDatabase = mongoTenantFactory.selectDatabase(null);
            return mongoDatabase.getCollection(annotation.collectionName(), getTClass());
        } else {
            MongoDatabase mongoDatabase = mongoTenantFactory.selectDatabase(tenantProvider.getTenantId());
            return mongoDatabase.getCollection(annotation.collectionName(), getTClass());
        }
    }

    /**
     * 根据条件统计文档数据
     *
     * @param filter 条件
     * @return 数量
     */
    public Long countByCondition(Bson filter) {
        Bson isDel = Filters.eq(AbstractBaseEntityFieldConstant.IS_DELETED, BaseStatus.OK);
        filter = filter == null ? isDel : Filters.and(isDel, filter);
        return getTCollection().countDocuments(filter);
    }

    /**
     * 根据 id 获取泛型类 T 对应的单篇文档
     *
     * @param id 文档 id
     * @return 泛型类 T 对应的单篇文档
     */
    public T findById(String id) {
        if (id == null || "".equals(id)) return null;
        MongoCursor<T> iterator = getTCollection().find(Filters.and(Filters.eq(new ObjectId(id)),
                Filters.eq(AbstractBaseEntityFieldConstant.IS_DELETED, BaseStatus.OK))).iterator();
        if (iterator.hasNext()) {
            return iterator.next();
        }
        throw new BaseDaoException(MongoExceptionConstant.DOCUMENT_NOT_EXIST + "【" + id + "】");
    }

    public T findOne(String id) {
        if (id == null || "".equals(id)) return null;
        MongoCursor<T> iterator = getTCollection().find(Filters.and(Filters.eq(new ObjectId(id)),
                Filters.eq(AbstractBaseEntityFieldConstant.IS_DELETED, BaseStatus.OK))).iterator();
        if (iterator.hasNext()) {
            return iterator.next();
        }
        return null;

    }

    /**
     * 根据 id集合 获取泛型类 T 对应的文档
     *
     * @param ids 文档 id集合
     * @return 泛型类 T 对应的文档
     */
    public List<T> findById(Collection<String> ids) {
        ArrayList<T> result = new ArrayList<>();
        ids.forEach(id -> {
            if (ObjectId.isValid(id)) {
                result.add(findById(id));
            } else {
                log.error("不合法的id:【{}】", id);
            }
        });
        return result;
    }

    /**
     * 获取泛型类 T 对应集合中符合筛选条件的所有文档
     *
     * @param filter 筛选条件
     * @return 泛型类 T 对应集合中符合筛选条件的所有文档
     */
    public T findOne(Bson filter) {
        Bson isDel = Filters.eq(AbstractBaseEntityFieldConstant.IS_DELETED, BaseStatus.OK);
        filter = filter == null ? isDel : Filters.and(isDel, filter);
        MongoCursor<T> iterator = getTCollection().find(filter).iterator();
        if (iterator.hasNext()) return iterator.next();
        throw new BaseDaoException(MongoExceptionConstant.DOCUMENT_NOT_EXIST);
    }

    /**
     * 获取泛型类 T 对应集合中符合筛选条件的所有文档
     *
     * @param filter 筛选条件
     * @return 泛型类 T 对应集合中符合筛选条件的所有文档
     */
    public T findOneNotExist(Bson filter) {
        Bson isDel = Filters.eq(AbstractBaseEntityFieldConstant.IS_DELETED, BaseStatus.OK);
        filter = filter == null ? isDel : Filters.and(isDel, filter);
        MongoCursor<T> iterator = getTCollection().find(filter).iterator();
        if (iterator.hasNext()) return iterator.next();

        return null;
    }


    /**
     * 获取泛型类 T 对应集合中的所有文档
     *
     * @return 泛型类 T 对应集合中的所有文档
     */
    public List<T> findAll() {
        return getTCollection().find(Filters.eq(AbstractBaseEntityFieldConstant.IS_DELETED, BaseStatus.OK)).into(new ArrayList<>());
    }

    /**
     * 获取泛型类 T 对应集合中的所有文档
     *
     * @return 泛型类 T 对应集合中的所有文档
     */
    public List<T> findAllDelete() {
        return getTCollection().find(Filters.eq(AbstractBaseEntityFieldConstant.IS_DELETED, BaseStatus.DELETED)).into(new ArrayList<>());
    }


    /**
     * 获取排序后泛型类 T 对应集合中的所有文档
     *
     * @param sort 排序条件
     * @return 排序后泛型类 T 对应集合中的所有文档
     */
    public List<T> findAllSorted(Bson sort) {
        sort = Filters.and(Filters.eq(AbstractBaseEntityFieldConstant.IS_DELETED, BaseStatus.OK), sort);
        return getTCollection().find().sort(sort).into(new ArrayList<>());
    }

    /**
     * 获取泛型类 T 对应集合中符合筛选条件的所有文档
     *
     * @param filter 筛选条件
     * @return 泛型类 T 对应集合中符合筛选条件的所有文档
     */
    public List<T> findByCondition(Bson filter) {
        Bson isDel = Filters.eq(AbstractBaseEntityFieldConstant.IS_DELETED, BaseStatus.OK);
        filter = filter == null ? isDel : Filters.and(isDel, filter);
        ArrayList<T> into;
        try {
            into = getTCollection().find(filter).into(new ArrayList<>());
        } catch (NullPointerException e) {
            into = new ArrayList<>();
        }
        return into;
    }


    /**
     * 聚合查询
     *
     * @param bsonList 条件List
     * @return {@link List}
     */
    public List<T> findByAggregate(List<Bson> bsonList) {
        return getTCollection().aggregate(bsonList).into(new ArrayList<>());
    }

    /**
     * 聚合查询
     *
     * @param bsonList 条件List
     * @return {@link List}
     */
    public List<Object> findByAggregateReturnObj(List<Bson> bsonList) {
        return getTCollection().aggregate(bsonList).into(new ArrayList<>());
    }


    /**
     * 获取排序后泛型类 T 对应集合中符合筛选条件的所有文档
     *
     * @param filter 筛选条件
     * @param sort   Filters.eq("要排序的字段名称", [1：正序;-1：倒序])
     * @return 排序后泛型类 T 对应集合中符合筛选条件的所有文档
     */
    public List<T> findByConditionAndSorted(Bson filter, Bson sort) {
        Bson isDel = Filters.eq(AbstractBaseEntityFieldConstant.IS_DELETED, BaseStatus.OK);
        filter = filter == null ? isDel : Filters.and(isDel, filter);
        return getTCollection().find(filter).sort(sort).into(new ArrayList<>());
    }

    /**
     * 获取泛型类 T 对应集合中的分页对象
     *
     * @param page 当前页，0，1都表示第一页
     * @param size 每页数量
     * @return 泛型类 T 对应集合中的分页对象
     */
    public MyPage<T> findPage(int page, int size) {
        Bson isDel = Filters.eq(AbstractBaseEntityFieldConstant.IS_DELETED, BaseStatus.OK);
        List<T> content = getTCollection()
                .find(isDel)
                .skip((page == 0 ? page : page - 1) * size).limit(size).into(new ArrayList<>());
        long totalElements = getTCollection().countDocuments(isDel);
        return new MyPage<>(page, size, totalElements, content);
    }

    /**
     * 获取泛型类 T 对应集合中的分页对象
     *
     * @param page 当前页，0，1都表示第一页
     * @param size 每页数量
     * @return 泛型类 T 对应集合中的分页对象
     */
    public MyPage<T> findPageAndSorted(Bson sort, int page, int size) {
        Bson isDel = Filters.eq(AbstractBaseEntityFieldConstant.IS_DELETED, BaseStatus.OK);
        List<T> content = getTCollection()
                .find(isDel)
                .sort(sort)
                .skip((page == 0 ? page : page - 1) * size).limit(size).into(new ArrayList<>());
        long totalElements = getTCollection().countDocuments(isDel);
        return new MyPage<>(page, size, totalElements, content);
    }

    /**
     * 获取泛型类 T 对应集合中符合筛选条件的分页对象
     *
     * @param filter 筛选条件
     * @param page   当前页，0，1都表示第一页
     * @param size   每页数量
     * @return 泛型类 T 对应集合中符合筛选条件的分页对象
     */
    public MyPage<T> findPageByCondition(Bson filter, int page, int size) {
        Bson isDel = Filters.eq(AbstractBaseEntityFieldConstant.IS_DELETED, BaseStatus.OK);
        filter = filter == null ? isDel : Filters.and(isDel, filter);
        List<T> content = getTCollection().find(filter).sort(Filters.eq(AbstractBaseEntityFieldConstant.UNDERLINE_ID, -1)).skip((page == 0 ? page : page - 1) * size).limit(size).into(new ArrayList<>());
        long totalElements = getTCollection().countDocuments(filter);
        return new MyPage<>(page, size, totalElements, content);
    }


    /**
     * 获取排序后泛型类 T 对应集合中符合筛选条件的分页对象
     *
     * @param filter 筛选条件
     * @param sort   拼接Bson： Filters.eq("要排序的字段名称", [1：正序;-1：倒序])
     * @param page   当前页，0,1都表示第一页
     * @param size   每页数量
     * @return 排序后泛型类 T 对应集合中符合筛选条件的分页对象
     */
    public MyPage<T> findPageByConditionAndSorted(Bson filter, Bson sort, int page, int size) {
        Bson isDel = Filters.eq(AbstractBaseEntityFieldConstant.IS_DELETED, BaseStatus.OK);
        filter = filter == null ? isDel : Filters.and(isDel, filter);
        List<T> content = getTCollection().find(filter).sort(sort).skip((page == 0 ? page : page - 1) * size).limit(size)
                .into(new ArrayList<>());
        long totalElements = getTCollection().countDocuments(filter);
        return new MyPage<>(page, size, totalElements, content);
    }

    /**
     * 返回某个字段的值域
     */
    public List<String> distinctFieldValue(Bson filter, String fieldName, Class<String> classType) {
        Bson isDel = Filters.eq(AbstractBaseEntityFieldConstant.IS_DELETED, BaseStatus.OK);
        filter = filter == null ? isDel : Filters.and(isDel, filter);

        return getTCollection().distinct(fieldName, filter, classType).into(new ArrayList<>());
    }

    /**
     * 插入一条文档
     *
     * @param document 待插入文档
     * @return 插入成功返回文档 id；插入失败抛异常
     */
    public String insertOne(T document) {
        insertAuditor(document);
        InsertOneResult result = getTCollection().insertOne(document);
        return handleResult(result);
    }

    /**
     * 插入多条文档
     *
     * @param documents 待插入文档
     * @return 插入成功返回文档 id List；插入失败抛异常
     */
    public List<String> insertMany(List<T> documents) {
        insertAuditor(documents);
        InsertManyResult insertManyResult = getTCollection().insertMany(documents);
        return handleResult(insertManyResult);
    }

    /**
     * 根据 id 删除文档
     *
     * @param id 文档 id
     * @return 删除成功返回删除文档 数量；删除失败抛异常
     */
    public Long deleteById(String id) {
        DeleteResult result = getTCollection().deleteOne(Filters.eq(new ObjectId(id)));
        return handleResult(result);
    }

    /**
     * 根据 ids 删除文档
     *
     * @param ids 文档 ids
     * @return 删除成功返回删除文档 数量；删除失败抛异常
     */
    public Long deleteManyByIds(List<String> ids) {
        DeleteResult deleteResult = getTCollection().deleteMany(Filters.in(AbstractBaseEntityFieldConstant.UNDERLINE_ID, ids));
        return handleResult(deleteResult);
    }

    /**
     * 假删
     *
     * @param id 文档 id
     * @return 更新文档的id，删除失败抛异常
     */
    public List<String> deleteByIdFake(String id) {
        Bson filter = Filters.eq(new ObjectId(id));
        Bson updates = Updates.set("isDeleted", BaseStatus.DELETED);
        updates = updateAuditor(updates);
        UpdateResult updateResult = getTCollection().updateOne(filter, updates);
        return handleResult(updateResult);
    }

    public List<String> deleteFake(Bson filter) {
        Bson updates = Updates.set("isDeleted", BaseStatus.DELETED);
        updates = updateAuditor(updates);
        UpdateResult updateResult = getTCollection().updateOne(filter, updates);
        return handleResult(updateResult);
    }

    /**
     * 假删
     *
     * @param ids 文档 id
     * @return 更新文档的id集合，已经被删除的文档 抛异常
     */
    public List<String> deleteByIdFake(List<String> ids) {
        Bson filter = Filters.in(AbstractBaseEntityFieldConstant.UNDERLINE_ID, ids.stream().map(ObjectId::new).collect(Collectors.toList()));
        Bson updates = Updates.set("isDeleted", BaseStatus.DELETED);
        Bson bson = updateAuditor(updates);
        UpdateResult updateResult = getTCollection().updateMany(filter, bson);
        return handleResult(updateResult);
    }

    /**
     * 根据传入文档更新除 id 外的其他字段（若需要通过设置某个字段为 null 来更新文档，需要重写该方法）
     *
     * @param document 待更新文档
     * @return id 无效或不存在；更新失败报错；被更新文档的 id
     */
    public String updateOne(T document) {
        WriteModel<T> updateOneModel = getUpdateOneModel(document);
        BulkWriteResult bulkWriteResult = getTCollection().bulkWrite(singletonList(updateOneModel));
        List<String> result = handleResult(bulkWriteResult);
        return result.isEmpty() ? document.getId() : result.get(0);
    }

    public List<String> updateAll(List<T> document) {
        if (ObjectUtil.isEmpty(document)) return new ArrayList<>();
        return executeBulkUpsert(document.stream().map(this::getUpdateOneModel));
    }

    public List<String> insertAll(List<T> document) {
        if (ObjectUtil.isEmpty(document)) return new ArrayList<>();
        return executeBulkUpsert(document.stream().map(this::getInsertOneModel));
    }

    private List<String> executeBulkUpsert(Stream<WriteModel<T>> writeModelStream) {
        List<WriteModel<T>> writeModels = writeModelStream.collect(Collectors.toList());
        BulkWriteResult bulkWriteResult = null;
        try {
            bulkWriteResult = getTCollection().bulkWrite(writeModels, new BulkWriteOptions().ordered(false));
        } catch (Exception e) {
            log.error(e.getMessage());
        }
        List<String> result = handleResult(bulkWriteResult);
        return result.isEmpty() ? List.of() : result;
    }


    private WriteModel<T> getUpdateOneModel(T document) {
        Map<String, Object> map = BeanUtil.beanToMap(document, false, true);
        if (!map.containsKey(AbstractBaseEntityFieldConstant.ID) || !ObjectId.isValid(map.get(AbstractBaseEntityFieldConstant.ID).toString())) {
            throw new BaseDaoException(MongoExceptionConstant.DOCUMENT_UPDATE_ERROR);
        }
        String id = map.get(AbstractBaseEntityFieldConstant.ID).toString();
        map.remove(AbstractBaseEntityFieldConstant.ID);
        return new UpdateOneModel<>(Filters.eq(new ObjectId(id)), genUpsertBson(map));
    }

    private WriteModel<T> getInsertOneModel(T document) {
        insertAuditor(document);
        return new InsertOneModel<>(document);
    }

    private Bson genUpsertBson(Map<String, Object> map) {
        List<Bson> bsonList = new ArrayList<>();
        for (Map.Entry<String, Object> entry : map.entrySet()) {
            bsonList.add(Updates.combine(Updates.set(entry.getKey(), entry.getValue())));
        }
        Bson condition = Updates.combine(bsonList);
        return updateAuditor(condition);
    }

    public String updateOne(Bson filter, Bson update) {
        update = updateAuditor(update);
        UpdateResult updateResult = getTCollection().updateOne(filter, update);
        List<String> result = handleResult(updateResult);
        return result.isEmpty() ? "" : result.get(0);
    }

    /**
     * 批量插入多个嵌套字段，
     *
     * @param id     过滤id
     * @param values 遍历插入
     * @return 更新的行数
     */
    public List<String> pushCollectDistinct(List<String> id, Map<String, List<String>> values) {
        Bson filter = Filters.in(AbstractBaseEntityFieldConstant.UNDERLINE_ID, id.stream().map(ObjectId::new).collect(Collectors.toList()));
        return commonPush(filter, values);
    }

    /**
     * 在指定的对象中插入多个嵌套字段
     *
     * @param id     过滤id
     * @param values 遍历插入
     * @return 更新的行数
     */
    public List<String> pushCollectDistinct(String id, Map<String, List<String>> values) {
        Bson filter = Filters.eq(new ObjectId(id));
        return commonPush(filter, values);
    }

    public List<String> commonPush(Bson filter, Map<String, List<String>> values) {
        Bson updates = null;
        for (Map.Entry<String, List<String>> map : values.entrySet()) {
            updates = Updates.combine(Updates.addEachToSet(map.getKey(), map.getValue()));
        }
        updates = updateAuditor(updates);
        UpdateResult updateResult = getTCollection().updateOne(filter, updates);
        return handleResult(updateResult);
    }

    public List<String> commonPull(Bson filter, Map<String, List<String>> values) {
        Bson updates = null;
        for (Map.Entry<String, List<String>> map : values.entrySet()) {
            updates = Updates.combine(Updates.pullAll(map.getKey(), map.getValue()));
        }
        updates = updateAuditor(updates);
        UpdateResult updateResult = getTCollection().updateMany(filter == null ? Filters.empty() : filter, updates);
        return handleResult(updateResult);
    }

    /**
     * 自定义式的Pull
     *
     * @param id      条件id
     * @param bsonMap key:第一层的属性值，value：自定义
     *                原数据 { scores: [{"id":"1"},{"id":"2"}]}  传入{"scores":{"id":"1"}}  结果  { scores: [{"id":"2"}]}
     * @return 更新的id
     */
    public List<String> pullDistinct(String id, Map<String, Bson> bsonMap) {
        Bson filter = null;
        Bson updates = null;
        if (StrUtil.isNotBlank(id)) filter = Filters.eq(new ObjectId(id));
        filter = filter == null ? Filters.empty() : filter;
        for (Map.Entry<String, Bson> bsonEntry : bsonMap.entrySet()) {
            updates = Updates.combine(Updates.pull(bsonEntry.getKey(), bsonEntry.getValue()));
        }
        updates = updateAuditor(updates);
        UpdateResult updateResult = getTCollection().updateMany(filter, updates);
        return handleResult(updateResult);
    }

    /**
     * 自定义式的Pull
     *
     * @param id      条件id
     * @param bsonMap key:第一层的属性值，value：自定义
     * @return 更新的id
     */
    public List<String> pushDistinct(String id, Map<String, Bson> bsonMap) {
        Bson filter = null;
        Bson updates = null;
        if (StrUtil.isNotBlank(id)) filter = Filters.eq(new ObjectId(id));
        filter = filter == null ? Filters.empty() : filter;
        for (Map.Entry<String, Bson> bsonEntry : bsonMap.entrySet()) {
            updates = Updates.combine(Updates.push(bsonEntry.getKey(), bsonEntry.getValue()));
        }
        updates = updateAuditor(updates);
        UpdateResult updateResult = getTCollection().updateMany(filter, updates);
        return handleResult(updateResult);
    }

    /**
     * 批量删除嵌套字段
     *
     * @param id     id=null 全表删除  ；  id！= null  指定删除
     * @param values 遍历删除的集合
     * @return 更新的数量
     */
    public List<String> pullCollectDistinct(String id, Map<String, List<String>> values) {
        Bson filter = null;
        if (StrUtil.isNotBlank(id)) filter = Filters.eq(new ObjectId(id));
        filter = filter == null ? Filters.empty() : filter;
        return commonPull(filter, values);
    }

    public List<String> pullCollectDistinct(List<String> id, Map<String, List<String>> values) {
        Bson filter = Filters.in(AbstractBaseEntityFieldConstant.UNDERLINE_ID, id.stream().map(ObjectId::new).collect(Collectors.toList()));
        return commonPull(filter, values);

    }

    /**
     * 批量更新或新增多个文档
     *
     * @param documents 文档集合
     * @return 更新后的文档对象
     */
    public List<T> saveAllAndGet(List<T> documents) {
        return findById(saveAll(documents));
    }

    /**
     * 批量更新或新增多个文档
     *
     * @param documents 文档集合
     * @return 更新或新增的文档id集合
     */
    public List<String> saveAll(List<T> documents) {
        List<String> updatedIdList = new ArrayList<>();
        ArrayList<T> updateDoc = new ArrayList<>();
        ArrayList<T> insertDoc = new ArrayList<>();
        documents.forEach(s -> {
            if (StrUtil.isNotBlank(s.getId())) {
                updateDoc.add(s);
            } else {
                insertDoc.add(s);
            }
        });
        updatedIdList.addAll(updateAll(updateDoc));
        updatedIdList.addAll(insertAll(insertDoc));
        return updatedIdList;
    }

    /**
     * id为空插入，id不为空更新。更新时找不到文档不插入。
     *
     * @param document 文档
     * @return id
     */
    public String save(T document) {
        if (!Objects.isNull(document.getId())) {
            return updateOne(document);
        } else {
            return insertOne(document);
        }
    }

    /**
     * 插入操作的审计
     *
     * @param document 插入的文档
     */
    protected void insertAuditor(T document) {
        getCurrentUserId().ifPresent(document::setCreateUser);
        document.setCreateTime(new Date());
    }

    protected void insertAuditor(List<T> document) {
        String userId = getCurrentUserId().orElse(null);
        document.forEach(t -> {
            t.setCreateTime(new Date());
            t.setCreateUser(userId);
        });
    }

    /**
     * 更新操作的审计
     *
     * @return 需要审计的字段
     */
    protected Bson updateAuditor(Bson updateBson) {
        Bson combine = Updates.combine(updateBson, Updates.set(AbstractBaseEntityFieldConstant.UPDATE_TIME, new Date()));
        Optional<String> currentUserId = getCurrentUserId();
        if (currentUserId.isPresent()) {
            combine = Updates.combine(combine, Updates.set(AbstractBaseEntityFieldConstant.UPDATE_USER, currentUserId.get()));
        }
        return combine;
    }

    /**
     * 更新的结果处理逻辑
     *
     * @param updateResult 更新数据
     * @return 如果是插入操作，则返回插入数据的id，否则返回空集合
     */
    protected List<String> handleResult(UpdateResult updateResult) {
        long modifiedCount = updateResult.getModifiedCount();
        if (modifiedCount == 0) {
            if (updateResult.getMatchedCount() == 0) {
                log.info(MongoExceptionConstant.DOCUMENT_NOT_EXIST);
            } else {
                log.debug("数据更新前后一致");
            }
        }
        BsonValue insert = updateResult.getUpsertedId();
        if (insert == null) return new ArrayList<>();
        return updateResult.getUpsertedId()
                .asArray().stream()
                .map(s -> String.valueOf(s.asObjectId().getValue()))
                .collect(Collectors.toList());
    }

    protected List<String> handleResult(BulkWriteResult bulkWriteResult) {
        if (bulkWriteResult == null) return List.of();
        int modifiedCount = Math.max(bulkWriteResult.getModifiedCount(), bulkWriteResult.getInsertedCount());
        if (modifiedCount == 0) {
            if (bulkWriteResult.getMatchedCount() == 0) {
                log.info(MongoExceptionConstant.DOCUMENT_NOT_EXIST);
            } else {
                log.debug("数据更新前后一致");
            }
        }
        //插入的文档
        List<BulkWriteInsert> inserts = bulkWriteResult.getInserts();
        //更新导致插入的文档
        List<BulkWriteUpsert> upsets = bulkWriteResult.getUpserts();
        List<String> resultId = new ArrayList<>();
        if (inserts != null) inserts.forEach(s -> resultId.add(String.valueOf(s.getId().asObjectId().getValue())));
        if (upsets != null) upsets.forEach(s -> resultId.add(String.valueOf(s.getId().asObjectId().getValue())));
        return resultId;
    }

    /**
     * 插入单个的结果处理逻辑
     *
     * @param insertOneResult 插入数据
     * @return 插入的id
     */
    protected String handleResult(InsertOneResult insertOneResult) {
        if (insertOneResult.wasAcknowledged()) {
            return Objects.requireNonNull(insertOneResult.getInsertedId()).asObjectId().getValue().toString();
        }
        throw new BaseDaoException(MongoExceptionConstant.DOCUMENT_INSERT_ERROR);
    }

    /**
     * 批量插入的结果处理逻辑
     *
     * @param insertManyResult 插入数据
     * @return 插入的id集合
     */
    protected List<String> handleResult(InsertManyResult insertManyResult) {
        if (insertManyResult.wasAcknowledged()) {
            return Objects.requireNonNull(insertManyResult.getInsertedIds())
                    .values().stream()
                    .map(s -> String.valueOf(s.asObjectId().getValue()))
                    .collect(Collectors.toList());
        }
        throw new BaseDaoException(MongoExceptionConstant.DOCUMENT_INSERT_ERROR);
    }

    /**
     * 删除的结果处理逻辑
     *
     * @param deleteResult 删除数据
     * @return 删除的数量
     */
    protected Long handleResult(DeleteResult deleteResult) {
        if (!deleteResult.wasAcknowledged()) throw new BaseDaoException(MongoExceptionConstant.DOCUMENT_DELETE_ERROR);
        return deleteResult.getDeletedCount();
    }


    public Optional<String> getCurrentUserId() {
        //从Header中获取用户信息
        ServletRequestAttributes attributes = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
        if (attributes != null && ObjectUtil.isNotEmpty(attributes.getRequest().getHeader("user"))) {
            try {
                HttpServletRequest request = attributes.getRequest();
                String userStr = request.getHeader("user");
                return Optional.of(Convert.toStr(new JSONObject(userStr).get(AuthConstant.USER_ID)));
            } catch (Exception e) {
                e.printStackTrace();
                log.error("Request中无法获取user，mongo用户审计失败...");
                return Optional.empty();
            }
        } else {
            try {
                return Optional.of(Convert.toStr(new JSONObject(ThreadLocalUtil.get("user")).get(AuthConstant.USER_ID)));
            } catch (Exception e) {
                e.printStackTrace();
                log.error("ThreadLocal中无法获取user，mongo用户审计失败...");
                return Optional.empty();
            }

        }
    }
}
