package org.irm.lab.common.entity;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
public class ApiMessage {

    @ApiModelProperty("controller路径")
    private String classURL;

    @ApiModelProperty("controller名称")
    private String className;

    @ApiModelProperty("controller信息")
    private String classDesc;

    @ApiModelProperty("service路径")
    private String methodURL;

    @ApiModelProperty("请求方式")
    private String requestType;

    @ApiModelProperty("接口信息")
    private String methodDesc;

    @ApiModelProperty("接口名称")
    private String methodName;

    @ApiModelProperty("是否隐藏")
    private boolean hidden;
}
