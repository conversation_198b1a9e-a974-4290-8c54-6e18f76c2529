package org.irm.lab.common.annotation;

import org.springframework.core.annotation.AliasFor;
import org.springframework.stereotype.Component;

import java.lang.annotation.*;

/**
 * <AUTHOR>
 * @date 2022/4/5
 * @description
 */
@Target({ElementType.TYPE})
@Retention(RetentionPolicy.RUNTIME)
@Documented
@Component
public @interface MongoDao {

    @AliasFor(annotation = Component.class)
    String value() default "";
}
