package org.irm.lab.common.support;

import cn.hutool.core.util.ObjectUtil;
import com.mongodb.client.model.Filters;
import org.bson.conversions.Bson;
import org.irm.lab.common.constant.FilterTypeConst;


/**
 * <AUTHOR>
 * @date 2023/1/29 15:43
 * @description MongoDB常用Filter
 */
public class FilterType {

    public static Bson getMethod(String key, String value, String method) {
        if (ObjectUtil.isNotEmpty(value)) {
            if (FilterTypeConst.EQ.equalsIgnoreCase(method)) {
                return Filters.eq(key, value);
            } else if (FilterTypeConst.NE.equalsIgnoreCase(method)) {
                return Filters.ne(key, value);
            } else if (FilterTypeConst.GT.equalsIgnoreCase(method)) {
                return Filters.gt(key, value);
            } else if (FilterTypeConst.LT.equalsIgnoreCase(method)) {
                return Filters.lt(key, value);
            } else if (FilterTypeConst.GTE.equalsIgnoreCase(method)) {
                return Filters.gte(key, value);
            } else if (FilterTypeConst.LTE.equalsIgnoreCase(method)) {
                return Filters.lte(key, value);
            }
        }
        // 默认使用模糊查询
        return Filters.regex(key, value);
    }
}
