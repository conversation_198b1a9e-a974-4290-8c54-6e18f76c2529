/*
 * @Author: <PERSON><PERSON> lich<PERSON>
 * @Date: 2020-11-13 23:26:51
 * @LastEditors: liu lichao
 * @LastEditTime: 2021-12-08 15:15:41
 * @Description: 角色变量
 */
package org.irm.lab.common.constant;

public interface AuthConstant {

    /**
     * JWT存储权限前缀
     */
    String AUTHORITY_PREFIX = "ROLE_";

    /**
     * JWT存储权限属性
     */
    String AUTHORITY_CLAIM_NAME = "authorities";

    /**
     * 后台管理client_id
     */
    String ADMIN_CLIENT_ID = "k-unify";

    String ADMIN_CLIENT_SECRET = "RUCLAB#2022";

    /**
     * 前台client_id
     */
    String PORTAL_CLIENT_ID = "k-unify-app";

    String PORTAL_CLIENT_SECRET = "RUCLAB#2022";



    /**
     * 后台管理接口路径匹配
     */
    String ADMIN_URL_PATTERN = "/admin/**";


    /**
     * 前台接口路径匹配
     */
    String APP_URL_PATTERN = "/" + AppConstant.APPLICATION_FRONT_NAME + "/**";

    String SYS_URL_PATTERN = "/" + AppConstant.APPLICATION_SYS_NAME + "/**";

    String USER_URL_PATTERN = "/" + AppConstant.APPLICATION_USER_NAME + "/**";

    String RESOURCE_URL_PATTERN = "/" + AppConstant.APPLICATION_RESOURCE_NAME + "/**";

    String CONFIG_URL_PATTERN = "/" + AppConstant.APPLICATION_CONFIG_NAME + "/**";

    String REPOSITORY_URL_PATTERN = "/" + AppConstant.APPLICATION_REPOSITORY_NAME + "/**";

    String CMS_URL_PATTERN = "/" + AppConstant.APPLICATION_CMS_NAME + "/**";

    String LOG_PATTERN = "/" + AppConstant.APPLICATION_LOG_NAME + "/**";

    String KG_PATTERN = "/" + AppConstant.APPLICATION_KG_NAME + "/**";

    String FRONT_PATTERN = "/" + AppConstant.APPLICATION_FRONT_NAME + "/**";


    /**
     * 后台接口数组
     */
    String[] ADMIN_URL_PATTERNS = new String[]{
            ADMIN_URL_PATTERN,
            RESOURCE_URL_PATTERN,
            SYS_URL_PATTERN,
            CONFIG_URL_PATTERN,
            USER_URL_PATTERN,
            REPOSITORY_URL_PATTERN,
            CMS_URL_PATTERN,
            LOG_PATTERN,
            KG_PATTERN,
            FRONT_PATTERN
    };

    /**
     * Redis缓存权限规则key
     */
    String RESOURCE_ROLES_MAP_KEY = "unify:auth:resourceRolesMap";

    /**
     * 认证信息Http请求头
     */
    String JWT_TOKEN_HEADER = "Authorization";

    /**
     * JWT令牌前缀
     */
    String JWT_TOKEN_PREFIX = "Bearer ";

    /**
     * 用户信息Http请求头
     */
    String USER_TOKEN_HEADER = "user";

    /**
     * 认证请求头前缀
     */
    String BASIC_HEADER_PREFIX = "Basic ";

    /**
     * 认证请求头前缀
     */
    String BASIC_HEADER_PREFIX_EXT = "Basic%20";

    String CAPTCHA_KEY = "unify:auth::captcha:";

    public final static String CAPTCHA_HEADER_KEY = "Captcha-Key";
    public final static String CAPTCHA_HEADER_CODE = "Captcha-Code";
    public final static String CAPTCHA_NOT_CORRECT = "验证码不正确";
    public final static String USER_TYPE_HEADER_KEY = "User-Type";
    public final static String DEFAULT_USER_TYPE = "admin";

    String USER_ID = "user_id";
    String ACCOUNT = "account";
    String ROLE_ID = "role_id";
    String ROLES = "roles";
    String USER_NAME = "user_name";
    String ROLE_NAME = "role_name";
    String CLIENT_ID = "client_id";
    String DEFAULT_AVATAR = "https://gw.alipayobjects.com/zos/rmsportal/BiazfanxmamNRoxxVxka.png";
    String EXPIRES_IN = "expires_in";
    String ACCESS_TOKEN = "access_token";
    String REFRESH_TOKEN = "refresh_token";
    String TOKEN_TYPE = "token_type";
    String NAME = "name";
    String EMAIL = "email";
    String AVATAR = "avatar";
    String CLIENT_AUTH_HEADER_KEY = "CLIENT_AUTH";
    String TENANT_ID = "tenant_id";

    String LOCKED_PREFIX = "locked_";

    String MARK_PREFIX = "mark_";

}