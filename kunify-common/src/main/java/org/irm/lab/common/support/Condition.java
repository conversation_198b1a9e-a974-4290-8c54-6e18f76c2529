package org.irm.lab.common.support;


import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.util.BooleanUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.ReflectUtil;
import cn.hutool.core.util.StrUtil;
import com.mongodb.client.model.Filters;
import lombok.extern.log4j.Log4j2;
import org.bson.conversions.Bson;
import org.bson.types.ObjectId;
import org.irm.lab.common.constant.AbstractBaseEntityFieldConstant;
import org.irm.lab.common.pojo.BaseStatus;

import java.util.*;

/**
 * <AUTHOR>
 * @date 2022/4/7
 * @description
 */
@Log4j2
public class Condition {

    public static <T> Bson getFilter(T t) {
        return getFilter(BeanUtil.beanToMap(t, false, true), t.getClass());
    }

    public static <T> Bson getFilter(Map<String, Object> query, Class<T> tClass) {
        List<Bson> bsonList = new ArrayList<>();

        for (Map.Entry<String, Object> entry : query.entrySet()) {
            String key = entry.getKey();
            Object value = entry.getValue();
            // 只添加 tClass 中存在的字段
            if (ReflectUtil.hasField(tClass, key)) {
                // 获取字段数据类型
                Class<?> type = ReflectUtil.getField(tClass, key).getType();
                bsonList.add(wrapperBson(key, value, type));
            }
        }
        return bsonList.isEmpty() ? null : Filters.and(bsonList);
    }

    public static <T> Bson getFilter(Map<String, Object> query, Class<T> tClass, Map<String, Object> queryType) {
        // 创建集合，存储多个Bson条件
        List<Bson> bsonList = new ArrayList<>();
        // 添加默认条件 isDeleted="正常"
        bsonList.add(Filters.eq(AbstractBaseEntityFieldConstant.IS_DELETED, BaseStatus.OK));
        // 遍历查询条件
        for (Map.Entry<String, Object> entry : query.entrySet()) {
            // 获取字段名
            String key = entry.getKey();
            // 获取字段值
            Object value = entry.getValue();
            // 只添加 tClass 中存在的字段
            if (ReflectUtil.hasField(tClass, key)) {
                // 获取字段数据类型
                Class<?> type = ReflectUtil.getField(tClass, key).getType();
                // 封装条件
                bsonList.add(wrapperBson(key, value, type, queryType));
            }
        }
        return Filters.and(bsonList);
    }


    private static Bson wrapperBson(String key, Object value, Class<?> type) {
        Bson f1 = null;
        String fieldType = type.getSimpleName();
        if (AbstractBaseEntityFieldConstant.ID.equals(key)) {
            f1 = Filters.eq(new ObjectId(value.toString()));
        } else if (fieldType.equals("String")) {
            Bson bson = valueIsList(key, value);
            f1 = Objects.requireNonNullElseGet(bson, () -> Filters.regex(key, String.valueOf(value)));
        } else if (fieldType.equals("boolean")) {
            boolean booleanValue = BooleanUtil.toBoolean(String.valueOf(value));
            f1 = Filters.ne(key, !booleanValue);
        } else {
            f1 = WrapperListBson(key, value, null, fieldType);
        }
        return f1;
    }

    /**
     * 封装Filter查询条件，指定特定字段的查询方式
     *
     * @param key       参数名
     * @param value     参数值
     * @param type      实体类字段类型
     * @param queryType 查询方式
     * @return Bson
     */
    private static Bson wrapperBson(String key, Object value, Class<?> type, Map<String, Object> queryType) {
        // 创建默认条件对象
        Bson defaultFilter = null;
        // 获取字段名
        String fieldType = type.getSimpleName();
        // 判断是否为ID
        if (AbstractBaseEntityFieldConstant.ID.equals(key)) {
            defaultFilter = Filters.eq(new ObjectId(value.toString()));
        } else if (fieldType.equals("String")) { // 判断实体类字段类型是否为String
            // 判断传递的参数是否为List（参数：List，字段：String。 就是in操作）
            Bson bson = valueIsList(key, value);
            // 再判断是否指定的查询方式
            if (ObjectUtil.isNotEmpty(queryType.get(key))) {
                defaultFilter = FilterType.getMethod(key, Convert.toStr(value), Convert.toStr(queryType.get(key)));
            } else {
                // 如果不是in查询，就默认模糊查询
                defaultFilter = Objects.requireNonNullElseGet(bson, () -> Filters.regex(key, String.valueOf(value)));
            }
        } else if (fieldType.equals("boolean")) {
            boolean booleanValue = BooleanUtil.toBoolean(String.valueOf(value));
            defaultFilter = Filters.ne(key, !booleanValue);
        } else defaultFilter = WrapperListBson(key, value, null, fieldType);
        // 返回条件
        return defaultFilter;
    }


    /**
     * 如果实体类类型为List，统一处理
     *
     * @param key       参数名
     * @param value     参数值
     * @param f1        bson条件
     * @param fieldType 字段类型
     * @return Bson
     */
    private static Bson WrapperListBson(String key, Object value, Bson f1, String fieldType) {
        if (fieldType.equals("List")) {
            Class<?> aClass = value.getClass();
            boolean isEntity = aClass.getPackageName().contains("org.irm.lab") || value instanceof Map;
            boolean isList = value instanceof Iterable;
            if (isEntity) {
                //传参为实体
                Bson bson = wrapperBson(value);
                if (bson != null) f1 = Filters.elemMatch(key, bson);
            } else if (isList) {
                //传参为集合
                List<Object> list = new ArrayList<>();
                for (Object o : (Iterable<?>) value) {
                    Bson bson = wrapperBson(o);
                    list.add(bson == null ? o : bson);
                }
                f1 = Filters.all(key, list);
            } else {
                f1 = Filters.eq(key, value);
            }
        } else {
            log.info("未知的类型：【{}】", value.getClass());
            f1 = Filters.regex(key, String.valueOf(value));
        }
        return f1;
    }


    /**
     * 封装对象，不递归
     *
     * @param value 值
     * @return 封装后的bson
     */
    private static Bson wrapperBson(Object value) {
        boolean isEntity = value.getClass().getPackageName().contains("org.irm.lab") || value instanceof Map;
        if (!isEntity) return null;
        Map<String, Object> objectMap = value instanceof Map ? (Map<String, Object>) value : BeanUtil.beanToMap(value);
        Bson f1 = null;
        for (Map.Entry<String, Object> entity : objectMap.entrySet()) {
            if (ObjectUtil.isEmpty(entity.getValue())) continue;
            String key1 = entity.getKey();
            Object value1 = entity.getValue();
            Class<?> type1 = value1.getClass();
            //自定义实体类类型
            Bson f2;
            if (type1.getPackageName().contains("org.irm.lab")) {
                log.error("不支持多层嵌套对象查询");
                continue;
            } else {
                f2 = wrapperBson(key1, value1, type1);
            }
            f1 = f1 == null ? f2 : Filters.and(f1, f2);
        }
        return f1;
    }

    public static List<Bson> wrapperBson(Map<String, Object> bean) {
        ArrayList<Bson> param = new ArrayList<>();
        for (Map.Entry<String, Object> objectEntry : bean.entrySet()) {
            Object value = objectEntry.getValue();
            if ("id".equals(objectEntry.getKey())) {
                param.add(Filters.eq(new ObjectId(String.valueOf(value))));
                continue;
            }
            if (value instanceof String) {
                String s = String.valueOf(value);
                if (StrUtil.isBlank(s)) continue;
                param.add(Filters.regex(objectEntry.getKey(), s));
            } else {
                param.add(Filters.eq(objectEntry.getKey(), value));
            }
        }
        return param;
    }

    /**
     * 判断value1是否是List类型
     *
     * @param key1   属性名
     * @param value1 属性值
     * @return 封装的bson
     */
    private static Bson valueIsList(String key1, Object value1) {
        if (!(value1 instanceof List)) return null;
        List<?> list = ((List<?>) value1);
        return Filters.in(key1, list);
    }

}
