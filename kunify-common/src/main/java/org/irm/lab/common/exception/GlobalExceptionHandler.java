package org.irm.lab.common.exception;

import org.irm.lab.common.api.R;
import org.irm.lab.common.config.EnvironmentConfig;
import org.springframework.validation.BindException;
import org.springframework.validation.BindingResult;
import org.springframework.validation.FieldError;
import org.springframework.web.bind.MethodArgumentNotValidException;
import org.springframework.web.bind.annotation.ControllerAdvice;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.annotation.Resource;

@ControllerAdvice
public class GlobalExceptionHandler {

    @Resource
    private EnvironmentConfig environmentConfig;

    @ResponseBody
    @ExceptionHandler(value = ServiceException.class)
    public R handle(ServiceException e) {
        if (e.getMessage() != null) {
            return R.failed(e.getMessage());
        }
        return R.failed(e.getErrorCode());
    }

    @ResponseBody
    @ExceptionHandler(value = SecureException.class)
    public R handleSecureException(SecureException e) {
        if (e.getMessage() != null) {
            return R.unauthorized(e.getMessage());
        }
        return R.unauthorized(e.getErrorCode());
    }

    @ResponseBody
    @ExceptionHandler(value = MethodArgumentNotValidException.class)
    public R handleValidException(MethodArgumentNotValidException e) {
        BindingResult bindingResult = e.getBindingResult();
        String message = null;
        if (bindingResult.hasErrors()) {
            FieldError fieldError = bindingResult.getFieldError();
            if (fieldError != null) {
                message = fieldError.getField() + fieldError.getDefaultMessage();
            }
        }
        return R.validateFailed(message);
    }

    @ResponseBody
    @ExceptionHandler(value = BindException.class)
    public R handleValidException(BindException e) {
        BindingResult bindingResult = e.getBindingResult();
        String message = null;
        if (bindingResult.hasErrors()) {
            FieldError fieldError = bindingResult.getFieldError();
            if (fieldError != null) {
                message = fieldError.getField() + fieldError.getDefaultMessage();
            }
        }
        return R.validateFailed(message);
    }


    @ResponseBody
    @ExceptionHandler(value = FileAuthException.class)
    public R handlerServiceException(FileAuthException fe) {
        return R.failed(fe.getMessage());
    }


    @ResponseBody
    @ExceptionHandler(value = {Exception.class, BaseDaoException.class})
    public R handleOtherException(Exception e) {
        e.printStackTrace();
        if (environmentConfig.getUnKnowErrorInfo()) {
            return R.success();
        } else {
            // 未知错误信息
            return R.failed(e.getMessage());
        }
    }

}