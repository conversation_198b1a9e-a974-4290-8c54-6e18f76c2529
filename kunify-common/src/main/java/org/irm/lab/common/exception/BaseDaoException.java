package org.irm.lab.common.exception;

import lombok.Getter;
import org.irm.lab.common.api.IErrorCode;
import org.irm.lab.common.api.ResultCode;

public class BaseDaoException extends RuntimeException {

    private static final long serialVersionUID = 2359767895161832954L;

    @Getter
    private IErrorCode errorCode;

    public BaseDaoException(IErrorCode errorCode) {
        super(errorCode.getMessage());
        this.errorCode = errorCode;
    }

    public BaseDaoException(String message) {
        super(message);
        this.errorCode = ResultCode.FAILED;
    }

    public BaseDaoException(Throwable cause) {
        super(cause);
    }

    public BaseDaoException(String message, Throwable cause) {
        super(message, cause);
    }


    /**
     * 提高性能
     *
     * @return Throwable
     */
    @Override
    public Throwable fillInStackTrace() {
        return this;
    }

    public Throwable doFillInStackTrace() {
        return super.fillInStackTrace();
    }
}
