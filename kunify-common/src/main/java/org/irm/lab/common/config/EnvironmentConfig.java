package org.irm.lab.common.config;

import lombok.Getter;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Configuration;

/**
 * 用于区分环境的配置
 */
@Configuration
@Getter
public class EnvironmentConfig {

    // Exception异常隐藏,现场环境默认隐藏；
    // 若需要开启，在自己的-xxx配置文件中，设置unKnowErrorInfo=false
    @Value("${unKnowErrorInfo:true}")
    private Boolean unKnowErrorInfo;

    // 现场环境默认租户028641
    // 若需要配置公司环境，在自己的-xxx配置文件中，设置tenantId=028639
    @Value("${tenantId:028641}")
    private String tenantId;

}
