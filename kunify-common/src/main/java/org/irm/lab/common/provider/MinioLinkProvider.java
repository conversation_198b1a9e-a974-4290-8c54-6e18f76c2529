package org.irm.lab.common.provider;

import lombok.RequiredArgsConstructor;
import org.irm.lab.common.constant.AttachUrlConstant;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @date 2023/6/19 13:52
 * @description
 */
@Component
@RequiredArgsConstructor
public class MinioLinkProvider {
    private final TenantProvider tenantProvider;

    /**
     * 获取minio上的文件访问地址(外网)
     *
     */
    public String getMinioLink(String attachName){
       return AttachUrlConstant.DOMAIN + AttachUrlConstant.BUCKET +tenantProvider.getTenantId()+"/"+ attachName;
    }

    /**
     * 获取minio上的文件访问地址(内网)
     *
     */
    public String getMinioLinkIntranet(String attachName){
        return  AttachUrlConstant.INTRANET_DOMAIN + AttachUrlConstant.BUCKET +tenantProvider.getTenantId()+"/"+ attachName;
    }

}
