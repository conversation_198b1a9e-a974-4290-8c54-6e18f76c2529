package org.irm.lab.common.annotation;


import org.irm.lab.common.constant.LogConstant;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

@Target(ElementType.METHOD)
@Retention(RetentionPolicy.RUNTIME)
public @interface MyLog {

    String menu();  //顶部菜单

    String dataType();  //数据类型

    String logRank() default LogConstant.RANK_OPERATION; //日志级别   默认是操作日志

    String system() default LogConstant.SYSTEM_BACKEND; //系统前后台   默认是后台

    String operation() default "";  //操作方式
}
