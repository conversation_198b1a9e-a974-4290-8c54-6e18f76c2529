package org.irm.lab.common.constant;

/**
 * <AUTHOR>
 * @date 2022/12/29 17:02
 * @description 实体类字段常量池
 */
public interface FiledNameConst extends AbstractBaseEntityFieldConstant {
    String NAME = "name";
    String TITLE = "title";
    String IDENTIFIER = "identifier";
    String VALUE = "value";
    String SORT = "sort";
    String SET_ID = "setId";
    String ROLE_ID = "roleId";
    String PARENT_ID = "parentId";
    String LABEL = "label";
    String NAME_EN = "nameEn";
    String METADATA = "metadata";
    String SOURCE_METADATA_ID = "sourceMetadataId";
    String SOURCE_ID = "sourceId";
    String DICT_ID = "dictId";
    String USER_NAME = "username";
    String TENANT_ID = "tenantId";
    String USER_ID = "userId";
    String PATH = "path";
    String CLASSIFICATION_SET_ID = "classificationSetId";
    String LABEL_IDS = "labelIds";
    String EMAIL = "email";
    String METADATA_SCHEMA_ID = "metadataSchemaId";
    String TYPE = "type";
    String STAGE = "stageName";
    String PROCESS_ID = "processId";
    String MODEL_ID = "modelId";
    String AUTO_DESCRIBE_ALGORITHM = "autoDescribeAlgorithm";
    String CHECK_ALGORITHMS = "checkAlgorithms";
    String DATA_SET_ID = "dataSetId";
    String DEPLOYED = "deployed";
    String RELATION_ID = "relationId";

    String CONCEPT_ONE = "conceptOne";

    String CONCEPT_TWO = "conceptTwo";

    String PREDICATE_ID = "predicateId";

    String WORK_TASK_ID = "workTaskId";

    String CONCEPT_ID = "conceptId";

    String POINT_ONE = "pointOne";

    String POINT_TWO = "pointTwo";

    String CLASSIFY = "classify";

    String INTERFACE_ID = "interfaceId";

    String DATA_SOURCE_ID = "datasourceId";

    String GROUP = "group";

    String WORK_STAGE = "workStage";

    String DATA_TYPE = "datatype";

    String QUERYABLE = "queryable";

    String FILTERABLE = "filterable";

    String RESOURCE_ID = "resourceId";

    String TASK_STATUS = "taskStatus";

}
