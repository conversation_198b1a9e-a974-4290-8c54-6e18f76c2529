package org.irm.lab.common.constant;

public interface AppConstant {

    /**
     * 应用版本
     */
    String APPLICATION_VERSION = "0.0.1";

    /**
     * 基础包
     */
    String BASE_PACKAGES = "org.irm.lab";

    String DB_PRIMARY_KEY = "id";

    String DB_PRIMARY_KEY_METHOD = "getId";

    /**
     * 应用名前缀
     */
    String APPLICATION_NAME_PREFIX = "kunify-";

    String TENANT_DB_PREFIX = "kunify-hn-";

    /**
     * 网关模块名称
     */
    String APPLICATION_GATEWAY_NAME = APPLICATION_NAME_PREFIX + "gateway";

    /**
     * 授权模块名称
     */
    String APPLICATION_AUTH_NAME = APPLICATION_NAME_PREFIX + "auth";

    /**
     * 用户模块名称
     */
    String APPLICATION_USER_NAME = APPLICATION_NAME_PREFIX + "user";

    /**
     * 后台管理模块名称
     */
    String APPLICATION_ADMIN_NAME = APPLICATION_NAME_PREFIX + "admin";


    /**
     * 后台管理模块名称
     */
    String APPLICATION_SYS_NAME = APPLICATION_NAME_PREFIX + "system";

    /**
     * 资源库配置模块，kunify-config
     */
    String APPLICATION_CONFIG_NAME = APPLICATION_NAME_PREFIX + "config";

    /**
     * 资源库资源管理模块，kunify-repository
     */
    String APPLICATION_REPOSITORY_NAME = APPLICATION_NAME_PREFIX + "repository";

    /**
     * 日志模块，kunify-log
     */
    String APPLICATION_LOG_NAME = APPLICATION_NAME_PREFIX + "log";


    /**
     * CMS 展示库模块，kunify-cms
     */
    String APPLICATION_CMS_NAME = APPLICATION_NAME_PREFIX + "cms";

    /**
     * CMS 展示库模块，kunify-kg
     */
    String APPLICATION_KG_NAME = APPLICATION_NAME_PREFIX + "kg";

    /**
     * CMS 展示库模块，kunify-front
     */
    String APPLICATION_FRONT_NAME = APPLICATION_NAME_PREFIX + "front";


    /**
     * 资源模块名称
     */
    String APPLICATION_RESOURCE_NAME = APPLICATION_NAME_PREFIX + "resource";

    /**
     * 资源库存储模块
     */
    String APPLICATION_DOC_STORAGE_NAME = APPLICATION_NAME_PREFIX + "doc-storage";

    /**
     * 知识模型模块
     */
    String APPLICATION_KG_MODEL_NAME = APPLICATION_NAME_PREFIX + "kg-model";

    /**
     * 租户默认账号额度KEY
     */
    String ACCOUNT_NUMBER_KEY = "tenant.default.accountNumber";

    /**
     * 租户默认账号额度
     */
    Integer DEFAULT_ACCOUNT_NUMBER = -1;

    /**
     * 开发环境
     */
    String DEV_CODE = "dev";
    /**
     * 生产环境
     */
    String PROD_CODE = "prod";
    /**
     * 测试环境
     */
    String TEST_CODE = "test";

    /**
     * 代码部署于 linux 上，工作默认为 mac 和 Windows
     */
    String OS_NAME_LINUX = "LINUX";

    /**
     * 顶级父节点id
     */
    Long TOP_PARENT_ID = 0L;

    String TOP_PARENT_STRING_ID = "0";

    /**
     * 顶级父节点名称
     */
    String TOP_PARENT_NAME = "顶级";

    String NLP_TEXT_SEP = "[SEP]";


}