package org.irm.lab.common.constant;

/**
 * <AUTHOR>
 * @date 2022-12-26
 */
//通用字段
public interface AbstractBaseEntityFieldConstant {

    String UNDERLINE_ID = "_id";

    String ID = "id";

    String CREATE_TIME = "createTime";

    String UPDATE_TIME = "updateTime";

    String CREATE_USER = "createUser";

    String UPDATE_USER = "updateUser";

    String IS_DELETED = "isDeleted";

    String STATUS = "status";

    String NAME = "name";

    String SORT = "sort";

}
