package org.irm.lab.common.support;

import java.util.List;
import java.util.stream.Collectors;

import cn.hutool.core.collection.IterUtil;

public abstract class BaseEntityWrapper<E, V> {

    /**
     * 单个实体类包装
     *
     * @param entity 实体类
     * @return V
     */
    public abstract V entityVO(E entity);

    /**
     * 实体类集合包装
     *
     * @param list 列表
     * @return List V
     */
    public List<V> listVO(List<E> list) {
        long startTime = System.currentTimeMillis();
        List<V> collect = list.stream().map(this::entityVO).collect(Collectors.toList());
        long endTime = System.currentTimeMillis();
        System.out.println("实体类集合包装用时 = " + (endTime - startTime) / 1000.0);
        return collect;
    }

    public List<V> listVOIter(Iterable<E> iterable) {
        return listVO(IterUtil.toList(iterable));
    }

}