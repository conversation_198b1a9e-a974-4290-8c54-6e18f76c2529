package org.irm.lab.common.config.resolve;

import org.irm.lab.common.provider.TenantProvider;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @date 2023-06-28
 */
//es的动态索引解析器
@Component("esIndexResolver")
public class EsIndexResolver {

    public static final String ES_INDEX_PREFIX = "hn_document_es";

    @Resource
    private TenantProvider tenantProvider;

    public String resolveEsIndexByTenant() {
        return ES_INDEX_PREFIX + "_" + tenantProvider.getTenantId();
    }
}
