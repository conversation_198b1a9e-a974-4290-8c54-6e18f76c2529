package org.irm.lab.common.config;

import java.util.Enumeration;

import javax.servlet.http.HttpServletRequest;

import org.irm.lab.common.utils.ThreadLocalUtil;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import feign.RequestInterceptor;
import feign.RequestTemplate;
import lombok.extern.slf4j.Slf4j;

/**
 * Feign请求拦截器 - 负责传播HTTP请求头到Feign客户端调用
 *
 * 这个拦截器会自动将原始HTTP请求的所有头信息传播到Feign客户端调用中，
 * 确保用户上下文（如"user"头）在微服务调用链中得到正确传递。
 *
 * 注意：这个类通过FeignConfiguration进行Bean注册，不需要@Configuration注解
 */
@Slf4j
public class FeignAuthRequestInterceptor implements RequestInterceptor {

    @Override
    public void apply(RequestTemplate requestTemplate) {
        log.debug("FeignAuthRequestInterceptor.apply() 开始处理Feign请求头传播");

        boolean headerPropagated = false;

        // 获取当前的ServletRequestAttributes
        ServletRequestAttributes attributes = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
        if (attributes != null) {
            // 获取原始的HttpServletRequest
            HttpServletRequest request = attributes.getRequest();
            log.debug("成功获取到HttpServletRequest，开始传播请求头");

            // 遍历所有的请求头
            Enumeration<String> headerNames = request.getHeaderNames();
            int headerCount = 0;
            while (headerNames.hasMoreElements()) {
                String headerName = headerNames.nextElement();
                String headerValue = request.getHeader(headerName);

                // 跳过content-length头，因为它会在Feign中自动计算
                if (headerName.equals("content-length")) {
                    continue;
                }

                // 将请求头添加到Feign的RequestTemplate
                requestTemplate.header(headerName, headerValue);
                headerCount++;

                // 特别记录user头的传播情况
                if ("user".equals(headerName)) {
                    log.debug("成功传播user头: {}", headerValue);
                    headerPropagated = true;
                }
            }
            log.debug("从HTTP请求中传播了 {} 个请求头", headerCount);
        } else {
            log.debug("无法获取ServletRequestAttributes，尝试从ThreadLocal获取用户信息");
        }

        // 如果有ThreadLocal中存储的user信息，也添加到请求头
        String userFromThreadLocal = ThreadLocalUtil.get("user");
        if (userFromThreadLocal != null) {
            requestTemplate.header("user", userFromThreadLocal);
            log.debug("从ThreadLocal传播user头: {}", userFromThreadLocal);
            headerPropagated = true;
        }

        // 记录最终的传播结果
        if (headerPropagated) {
            log.debug("用户上下文已成功传播到Feign请求");
        } else {
            log.warn("未能传播用户上下文到Feign请求 - 请检查请求头或ThreadLocal中是否包含用户信息");
        }

        // 记录目标URL（用于调试）
        log.debug("Feign请求目标: {}", requestTemplate.url());
    }
}