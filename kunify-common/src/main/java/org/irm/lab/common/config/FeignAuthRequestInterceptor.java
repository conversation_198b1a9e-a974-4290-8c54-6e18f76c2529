package org.irm.lab.common.config;

import java.util.Enumeration;

import javax.servlet.http.HttpServletRequest;

import org.irm.lab.common.utils.ThreadLocalUtil;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import feign.RequestInterceptor;
import feign.RequestTemplate;
import lombok.extern.slf4j.Slf4j;

/**
 * Feign请求拦截器 - 负责传播HTTP请求头到Feign客户端调用
 *
 * 这个拦截器会自动将原始HTTP请求的所有头信息传播到Feign客户端调用中，
 * 确保用户上下文（如"user"头）在微服务调用链中得到正确传递。
 *
 * 注意：这个类通过FeignConfiguration进行Bean注册，不需要@Configuration注解
 */
@Slf4j
public class FeignAuthRequestInterceptor implements RequestInterceptor {

    @Override
    public void apply(RequestTemplate requestTemplate) {
        log.info("=== FeignAuthRequestInterceptor.apply() 开始处理Feign请求头传播 ===");
        log.info("目标URL: {}", requestTemplate.url());

        boolean headerPropagated = false;
        String userHeaderValue = null;

        // 获取当前的ServletRequestAttributes
        ServletRequestAttributes attributes = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
        if (attributes != null) {
            // 获取原始的HttpServletRequest
            HttpServletRequest request = attributes.getRequest();
            log.info("✅ 成功获取到HttpServletRequest，开始传播请求头");

            // 遍历所有的请求头
            Enumeration<String> headerNames = request.getHeaderNames();
            int headerCount = 0;
            while (headerNames.hasMoreElements()) {
                String headerName = headerNames.nextElement();
                String headerValue = request.getHeader(headerName);

                // 跳过content-length头，因为它会在Feign中自动计算
                if (headerName.equals("content-length")) {
                    continue;
                }

                // 将请求头添加到Feign的RequestTemplate
                requestTemplate.header(headerName, headerValue);
                headerCount++;

                // 特别记录user头的传播情况
                if ("user".equals(headerName)) {
                    log.info("✅ 成功从HTTP请求传播user头: {}", headerValue);
                    userHeaderValue = headerValue;
                    headerPropagated = true;
                }
            }
            log.info("从HTTP请求中传播了 {} 个请求头", headerCount);
        } else {
            log.warn("❌ 无法获取ServletRequestAttributes，尝试从ThreadLocal获取用户信息");
        }

        // 如果有ThreadLocal中存储的user信息，也添加到请求头（作为备选方案）
        String userFromThreadLocal = ThreadLocalUtil.get("user");
        if (userFromThreadLocal != null) {
            // 如果HTTP请求头中没有user信息，或者ThreadLocal中的信息更新，则使用ThreadLocal的
            if (userHeaderValue == null) {
                requestTemplate.header("user", userFromThreadLocal);
                log.info("✅ 从ThreadLocal传播user头: {}", userFromThreadLocal);
                headerPropagated = true;
            } else {
                log.info("HTTP请求头中已有user信息，ThreadLocal作为备选: {}", userFromThreadLocal);
            }
        } else {
            log.warn("❌ ThreadLocal中也没有用户信息");
        }

        // 强制检查最终的请求头设置
        if (requestTemplate.headers().containsKey("user")) {
            log.info("✅ 最终确认：Feign请求模板中包含user头");
            headerPropagated = true;
        } else {
            log.error("❌ 最终确认：Feign请求模板中不包含user头！");
        }

        // 记录最终的传播结果
        if (headerPropagated) {
            log.info("✅ 用户上下文已成功传播到Feign请求");
        } else {
            log.error("❌ 未能传播用户上下文到Feign请求 - 请检查请求头或ThreadLocal中是否包含用户信息");
        }

        log.info("=== FeignAuthRequestInterceptor.apply() 处理完成 ===");
    }
}