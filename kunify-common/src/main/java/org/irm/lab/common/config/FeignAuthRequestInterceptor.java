package org.irm.lab.common.config;

import cn.hutool.json.JSONUtil;
import java.util.Enumeration;

import javax.servlet.http.HttpServletRequest;

import org.irm.lab.common.utils.ThreadLocalUtil;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import feign.RequestInterceptor;
import feign.RequestTemplate;

@Configuration
public class FeignAuthRequestInterceptor implements RequestInterceptor {

    @Override
    public void apply(RequestTemplate requestTemplate) {
        // 获取当前的ServletRequestAttributes
        ServletRequestAttributes attributes = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
        if (attributes != null) {
            // 获取原始的HttpServletRequest
            HttpServletRequest request = attributes.getRequest();

            // 遍历所有的请求头
            Enumeration<String> headerNames = request.getHeaderNames();
            while (headerNames.hasMoreElements()) {
                String headerName = headerNames.nextElement();
                String headerValue = request.getHeader(headerName);

                if (headerName.equals("content-length")) {
                    continue;
                }
//                if (headerName.equals("accept")) {
//                    headerValue = "application/json";
//                }
                // 将请求头添加到Feign的RequestTemplate
                if (headerName.equals("user")) {
                    requestTemplate.header(headerName, JSONUtil.toJsonStr(headerValue));
                }else {
                    requestTemplate.header(headerName, headerValue);
                }
            }
        }

        // 如果有ThreadLocal中存储的user信息，也添加到请求头
        if (ThreadLocalUtil.get("user") != null) {
            String user = ThreadLocalUtil.get("user");
            requestTemplate.header("user", user);
        }
    }
}