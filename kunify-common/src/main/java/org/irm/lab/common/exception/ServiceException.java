package org.irm.lab.common.exception;

import org.irm.lab.common.api.IErrorCode;
import org.irm.lab.common.api.ResultCode;

import lombok.Getter;

public class ServiceException extends RuntimeException {

    private static final long serialVersionUID = 2359767895161832954L;

    @Getter
    private IErrorCode errorCode;

    public ServiceException(IErrorCode errorCode) {
        super(errorCode.getMessage());
        this.errorCode = errorCode;
    }

    public ServiceException(String message) {
        super(message);
        this.errorCode = ResultCode.FAILED;
    }

    public ServiceException(Throwable cause) {
        super(cause);
    }

    public ServiceException(String message, Throwable cause) {
        super(message, cause);
    }


    /**
     * 提高性能
     *
     * @return Throwable
     */
    @Override
    public Throwable fillInStackTrace() {
        return this;
    }

    public Throwable doFillInStackTrace() {
        return super.fillInStackTrace();
    }
}