package org.irm.lab.common.support;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/4/5
 * @description
 */
@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
public class MyPage<T> {

    private static final int DEFAULT_PAGE_SIZE = 10;

    /**
     * 当前页，0表示第一页
     */
    private int page;
    /**
     * 每页数量
     */
    private int size;
    /**
     * 符合条件的结果总数
     */
    private long totalElements;
    /**
     * 当页所有元素
     */
    private List<T> content;

//    public int getPage() {
//        return page;
//    }
//
//    public void setPage(int page) {
//        this.page = page;
//    }
//
//    public int getSize() {
//        return size;
//    }
//
//    public void setSize(int size) {
//        this.size = size;
//    }
//
//    public long getTotalElements() {
//        return totalElements;
//    }
//
//    public void setTotalElements(long totalElements) {
//        this.totalElements = totalElements;
//    }
//
//    public List<T> getContent() {
//        return content;
//    }
//
//    public void setContent(List<T> content) {
//        this.content = content;
//    }
}
