package org.irm.lab.common.annotation;

import java.lang.reflect.Field;
import java.lang.reflect.InvocationHandler;
import java.lang.reflect.Method;
import java.lang.reflect.Proxy;
import java.util.Map;

public class AnnotationUtil {

    //获取动态代理的注解，并设置属性值
    public static Map getMemberValues(Class cls, String methodName, Class... methodParam) throws NoSuchMethodException, NoSuchFieldException, IllegalAccessException {
        Method thisMethod = cls.getDeclaredMethod(methodName, methodParam);
        MyLog myLogAnno = thisMethod.getAnnotation(MyLog.class);
        //获取 myLogAnno 这个代理实例所持有的 InvocationHandler
        InvocationHandler h = Proxy.getInvocationHandler(myLogAnno);
        // 获取 AnnotationInvocationHandler 的 memberValues 字段
        Field hField = h.getClass().getDeclaredField("memberValues");
        // 因为这个字段事 private final 修饰，所以要打开权限
        hField.setAccessible(true);
        // 获取 memberValues，这个map中存储的就是注解的属性值，可以修改这里面的值
        Map memberValues = (Map) hField.get(h);
        return memberValues;
    }
}
