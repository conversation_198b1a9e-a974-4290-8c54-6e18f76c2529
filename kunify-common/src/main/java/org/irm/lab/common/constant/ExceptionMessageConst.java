package org.irm.lab.common.constant;

/**
 * <AUTHOR>
 * @date 2022/12/29 16:57
 * @description 异常信息常量类
 */
public interface ExceptionMessageConst {

    String UN_KNOW = "未知错误";
    String NAME_EXISTS = "操作失败，已存在同名名称";
    String IDENTIFIER_EXISTS = "操作失败,已存在同名标识";
    String VALUE_EXISTS = "操作失败，已存在同名键值";
    String LABEL_EXISTS = "操作失败，已存在同名标签";
    String NAME_EN_EXISTS = "操作失败，已存在同名英文名";
    String METADATA_EXISTS = "不可重复添加元数据项";
    String METADATA_ITEM_NOT_EXISTS = "该元数据项复制体不存在";
    String METADATA_ITEM_TYPE_RANGE_NOT_UPDATE = "操作失败，不可对元数据复制体的类型、值域进行修改";
    String METADATA_LABEL_NOT_EXISTS = "该元数据方案下，无可用元数据标签";
    String DICT_FAILED = "操作失败，有字典正在被使用";
    String CLASSIFICATION_SET_FAILED = "操作失败，有类目正在被使用";
    String METADATA_LABEL_FAILED = "操作失败，有标签正在被使用";
    String ALGORITHM_FAILED = "操作失败，有算法正在被使用";
    String UNIT_FAILED = "操作失败，有单位正在被使用";
    String DEPARTMENT_FAILED = "操作失败，有部门正在被使用";
    String PROCESS_ALREADY_DEPLOYED = "操作失败，不可对已部署的处理流程进行操作";
    String MODEL_ALREADY_DEPLOYED = "操作失败，不可对已部署模型执行当前操作";
    String PREDICATE_HAVE_RELATION = "操作失败，当前对象已存在多条关系实例";
    String PROPERTY_ALREADY_BIND = "操作失败，当前属性已已被绑定";
    String RELATION_ALREADY_EXIST = "操作失败，当前关系已存在";
    String JUST_INHERIT_DEPLOYED = "操作失败,只可对已部署的模型进行继承";
    String PROCESS_NODE_ALREADY_BIND = "请选择尚未绑定的流程节点进行添加";
    String NODE_NOT_EXIST = "操作失败，绑定实例不存在";
    String NOT_HAVE_PERMISSION = "暂无权限查看当前文件";
    String OBJECT_ID_ERROR = "操作失败，主键格式有误";
    String REQUEST_BODY_IS_NOT_JSON = "操作失败，body参数只能为json格式";
    String DATASOURCE_RESOURCE_ALREADY_JOIN_WORK = "操作失败，已加入任务的资源不可重复加入";
    String RESOURCE_NOT_PREVIEW = "操作失败，当前资源不支持预览";
    String RESOURCE_NOT_EXIST = "操作失败，当前资源不存在";
    String RESOURCE_ALREADY_CONFIRM = "操作失败，当前文件已确认";
    String CONFIG_MENU_MAX_FOUR = "操作失败，最多可配置四个快捷按钮";
    String PERMISSION_FORMAT_ERROR = "操作失败，权限格式错误";
    String EXTERNAL_IMPORTS_ATTACHMENT_NOT_UPDATE = "操作失败，不可以对外部导入的资源的附件进行当前操作";
    String NOT_REMOVE_ALREADY_JOIN_WORK_RESOURCE = "操作失败，不可删除已加入任务的资源";
    String EXISTING_IDENTICAL = "操作失败，已存在相同路径";
    String TASK_INITIATION_NOT_VIEWABLE_RESOURCE_SUBMISSIONS = "操作失败，任务发起后不可查看资源提交环节";

    /**
     * 资源上传
     */
    String UPLOAD_FAIL = "上传失败";

    /**
     * 资源下载
     */
    String DOWNLOAD_FAIL = "下载失败";

    /**
     * 知识库
     */
    String WORD_TO_PDF = "操作失败，word转pdf失败";
    String PDF_STRIPPER = "pdf内容抽取失败";
    String PDF_STRIPPER_SUCCESS = "pdf内容抽取成功";
    String PDF_IMAGES_GEN = "pdf图片生成失败";

    String OCR_GEN = "OCR接口调用失败";
    String OCR_GEN_SUCCESS = "OCR接口调用成功";
    String OCR_GEN_BEGIN = "OCR接口调用开启";


}
