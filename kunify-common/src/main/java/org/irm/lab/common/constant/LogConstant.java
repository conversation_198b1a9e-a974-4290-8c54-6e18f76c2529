package org.irm.lab.common.constant;

/**
 * <AUTHOR> @date
 * @description 日志信息常量池
 */
public interface LogConstant {
    String RANK_LOGIN = "登录日志";
    String RANK_OPERATION = "操作日志";

    String SYSTEM_BACKEND = "后台";
    String SYSTEM_FRONT = "前台";

    String OPERATION_LOGIN = "登录";
    String OPERATION_REMOVE = "删除";
    String OPERATION_LIFT = "提出";
    String OPERATION_JOIN = "加入";
    String OPERATION_ADD = "添加";
    String OPERATION_SEARCH = "搜索";
    String OPERATION_ADD_MARK = "添加标注";
    String OPERATION_CITE = "引用属性";
    String OPERATION_DEREFERENCE = "取消引用属性";
    String OPERATION_ADD_OR_ALTER = "添加或修改";
    String OPERATION_ALTER = "更新";
    String OPERATION_RECORD = "著录";
    String OPERATION_MIXTURE = "综合操作";
    String OPERATION_RESET = "重置";
    String OPERATION_SYNC = "同步";
    String OPERATION_CONFIGURE = "配置";
    String OPERATION_CREAT = "创建";
    String OPERATION_FINISH = "结束";
    String OPERATION_INITIATE = "发起";
    String OPERATION_DEPLOY = "部署";
    String OPERATION_CONNECT = "交接";
    String OPERATION_PASS = "通过";
    String OPERATION_REJECT = "驳回";
    String OPERATION_ANALYSIS = "解析";
    String OPERATION_REANALYSIS = "重新解析";
    String OPERATION_ANALYSIS_TRUE = "确认解析";
    String OPERATION_REIDENTIFICATION = "重新识别";
    String OPERATION_MARK_CONFIRMATION = "标注确认";
    String OPERATION_RULE_REANALYSIS = "重新规则解析";
    String OPERATION_RULE_ANALYSIS_TRUE = "确认规则解析";
    String OPERATION_REMATCH = "重新匹配";
    String OPERATION_CONFIRM_MATCH = "匹配确认";
    String OPERATION_COLLECT = "外部导入";
    String OPERATION_NATIVE = "本地上传";
    String OPERATION_PREVIEW = "预览";
    String OPERATION_DETAILS = "查看详情";

    String MENU_VIEW = "知识模型";
    String MENU_RESOURCE = "资源库";
    String MENU_KNOWLEDGE = "知识库";
    String MENU_WORK_CENTER = "任务中心";
    String MENU_DATASOURCE = "数据源";
    String MENU_SYSTEM = "系统管理";
    String MENU_CONTENT_MATCH = "全文检索";
    String MENU_KG_MATCH = "知识查询";
    String MENU_META_MATCH = "元数据检索";

    String MENU_QUESTION_SMART = "智能问答";
    String MENU_DRAFT_SMART = "智能拟稿";
    String MENU_KG_MAP = "知识地图";

    String MENU_ANALYSE_MEETING = "会议纪要";
    String MENU_ANALYSE_HONOUR = "表彰荣誉";
    String MENU_ANALYSE_LEADER = "领导批示";
    String MENU_ANALYSE_REGULATORY = "规章制度";


    String DOCUMENT_TEMPLATE = "文档模板";
    String CONFIGURE_THE_HUB = "配置中心";

    String DATA_ALGORITHM = "算法";
    String DATA_DATA_SET = "数据集";
    String DATA_LABEL = "数据集标签";
    String DATA_DATA_CONVERTER = "数据转化器";
    String DATA_DICT = "数据字典";
    String DATA_DICT_ITEM = "字典条目";
    String DATA_CLASSIFICATION_SET = "类目集";
    String DATA_CLASSIFICATION_ITEM = "类目项";
    String DATA_CONVERTER = "数据转化器";
    String DATA_METADATA_LABEL = "元数据标签";
    String DATA_METADATA = "元数据";
    String DATA_METADATA_SCHEMA = "元数据方案";
    String DATA_RESOURCE = "资源";
    String DATA_RESOURCE_CATALOGUE = "资源目录";
    String DATA_RESOURCE_FILE = "资源文件";
    String DATA_IMPORT_RESOURCE = "外部导入的资源";
    String DATA_RESOURCE_POWER = "权限";
    String DATA_WORK_RESOURCE = "任务资源";
    String DATA_RESOURCE_ANNEX = "资源附件";
    String DATA_RESOURCE_ANNEX_FILE = "资源附件文件";
    String DATA_BATCH = "任务";
    String DATA_MODEL_LIST = "模型列表";
    String DATA_PROCESS = "处理流程";
    String DATA_WORK_TASK = "任务";
    String DATA_DATASOURCE = "数据源";
    String DATA_DATASOURCE_CLASSIFICATION = "数据源分类";
    String DATA_DATASOURCE_RESOURCE = "数据源资源";
    String DATA_DATASOURCE_METADATA = "数据源元数据";
    String DATA_DRAINAGE = "自动分流";
    String DATA_MODEL = "模型";
    String DATA_CONCEPT = "概念";
    String DATA_PROPERTY = "属性";
    String DATA_RELATION = "关系";
    String DATA_RELATION_PROPERTY = "关系属性";
    String DATA_ENTITY = "实例";
    String DATA_ENTITY_RELATION = "实例关系";
    String DATA_DOCUMENT_UNIT = "语料";
    String DATA_ANNEX = "附件";
    String DATA_FILE = "文件";
    String DATA_ROLE = "角色";
    String DATA_UNIT = "单位";
    String DATA_DEPARTMENT = "部门";
    String DATA_ROLE_STATUS = "角色状态";
    String DATA_USER = "用户";
    String DATA_USER_PHOTO = "用户头像";
    String DATA_USER_STATUS = "用户状态";
    String DATA_USER_PASSWORD = "用户密码";
    String DATA_MENU = "菜单";
    String DATA_TENANT = "租户";
    String DATA_TENANT_STATUS = "租户状态";
    String DATA_ENTITY_LABEL = "实例标签";
    String DATA_HIGHLIGHT_WORD = "高亮词";
    String DATA_INTERFACE = "资源接口";
    String DATA_INTERFACE_AUTH = "权限接口";
    String DATA_FAST_BUTTON = "快捷按钮";

    String DATA_KEY_WORK = "检索词";
    String DATA_QA_QUESTION = "问题";

    String DATA_META = "元数据";

    String STATUS_SUCCESS = "操作成功";
    String STATUS_ERROR = "操作失败";
    String STATUS_LONGIN_SUCCESS = "登录成功";
    String STATUS_LOGIN_ERROR = "登录失败";


}
