package org.irm.lab.common.config;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Configuration;

import javax.annotation.PostConstruct;
import java.util.HashMap;
import java.util.Map;

//默认的dataset是测试环境下的数据集
@Configuration
public class QaDataSetConfig {

    private static final Map<String, String> idMap = new HashMap<>();

    //通用QA数据集ID
    @Value("${qa.dataset.all-id:1783409423943733250}")
    private String allId;

    //表彰荣誉QA数据集ID
    @Value("${qa.dataset.bzry-id:1783409342967508994}")
    private String bzryId;

    //会议纪要QA数据集ID
    @Value("${qa.dataset.hyjy-id:1787673080802955266}")
    private String hyjyId;

    //规章制度QA数据集ID
    @Value("${qa.dataset.gzzd-id:1787424937336684545}")
    private String gzzdId;

    //领导批示QA数据集ID
    @Value("${qa.dataset.ldps-id:1783409270379823106}")
    private String ldpsId;

    //后续数据统一数据集ID
    @Value("${qa.dataset.after-id:}")
    private String afterId;


    @PostConstruct
    public void init() {
        idMap.put("ALL", allId);
        idMap.put("GE_REN_RONG_YU", bzryId);
        idMap.put("HUI_YI", hyjyId);
        idMap.put("BIAO_ZHUN_ZHI_DU", gzzdId);
        idMap.put("LING_DAO_PI_FU", ldpsId);
        idMap.put("AFTER", afterId);
    }

    public static String getId(String enumName) {
        return idMap.get(enumName);
    }
}
