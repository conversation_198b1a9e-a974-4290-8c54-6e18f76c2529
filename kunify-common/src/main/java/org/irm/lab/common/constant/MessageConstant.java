/*
 * @Author: <PERSON><PERSON> <PERSON>ich<PERSON>
 * @Date: 2020-11-12 21:22:00
 * @LastEditors: liu lichao
 * @LastEditTime: 2020-12-25 09:10:28
 * @Description: 认证服务消息常量
 */
package org.irm.lab.common.constant;

public interface MessageConstant {

    String LOGIN_SUCCESS = "登录成功!";

    String USERNAME_PASSWORD_ERROR = "用户名或密码错误!";


    String PASSWORD_VALID_INTEGRITY_FAIL = "密码完整性校验未通过！";

    String CAPTCHA_NOT_CORRECT = "验证码不正确";
    String ACCESS_FORBIDDEN_BACK_SYSTEM = "该账号不具备后台访问权限";

    String CREDENTIALS_EXPIRED = "该账户的登录凭证已过期，请重新登录!";

    String ACCOUNT_DISABLED = "该账户已被禁用，请联系管理员!";

    String ACCOUNT_LOCKED = "该账号已被锁定，请联系管理员!";

    String ACCOUNT_EXPIRED = "该账号已过期，请联系管理员!";

    String PERMISSION_DENIED = "没有访问权限，请联系管理员!";

    String DENIED_OPERATION_PERMISSION = "没有操作权限，请联系管理员!";

    String TENANT_IVALIDE = "无效的租户ID";

    String ACCOUNT_IVALIDE = "无效的用户ID";

    String TENANT_ACCESS_DENIED = "无此租户权限";

    String AUTH_SERVER_ERROR = "远程服务器错误！";

    String CLIENT_AUTH_FAILUIRE = "客户端认证失败！";

    /**
     * 默认成功消息
     */
    String DEFAULT_SUCCESS_MESSAGE = "操作成功";
    /**
     * 默认失败消息
     */
    String DEFAULT_FAILURE_MESSAGE = "操作失败";

    /**
     * 默认失败消息
     */
    String DEFAULT_FAILURE_RESOLVE = "未知错误，请联系管理员！！！";

    /**
     * 页面不存在
     */
    String NOT_PAGE_RESOLVE = "标签页不存在，请关闭此标签页并清除缓存！！！";


}