package org.irm.lab.common.config;

import feign.RequestInterceptor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * Feign客户端配置类
 * 
 * 确保FeignAuthRequestInterceptor被正确注册为Bean，
 * 以便在所有Feign客户端调用中生效。
 */
@Slf4j
@Configuration
public class FeignConfiguration {

    /**
     * 注册Feign请求拦截器
     * 
     * @return FeignAuthRequestInterceptor实例
     */
    @Bean
    public RequestInterceptor feignAuthRequestInterceptor() {
        log.info("注册FeignAuthRequestInterceptor - 用于传播HTTP请求头到Feign调用");
        return new FeignAuthRequestInterceptor();
    }
}
