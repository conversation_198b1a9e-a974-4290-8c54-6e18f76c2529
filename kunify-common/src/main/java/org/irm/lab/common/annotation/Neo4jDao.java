package org.irm.lab.common.annotation;

import org.springframework.core.annotation.AliasFor;
import org.springframework.stereotype.Component;

import java.lang.annotation.*;

/**
 * <AUTHOR>
 * @date 2023-01-04
 */
@Target({ElementType.TYPE})
@Retention(RetentionPolicy.RUNTIME)
@Documented
@Component
public @interface Neo4jDao {
    @AliasFor(annotation = Component.class)
    String value() default "";

    boolean checkTenant() default true;
}
