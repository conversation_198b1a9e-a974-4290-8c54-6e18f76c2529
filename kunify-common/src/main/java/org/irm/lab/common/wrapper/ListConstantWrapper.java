package org.irm.lab.common.wrapper;


import cn.hutool.core.util.ReflectUtil;
import org.irm.lab.common.vo.ListConstantVO;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2023-01-06
 */

//将列表常量池，转换为指定返回格式的包装器
public class ListConstantWrapper {

    /**
     * 将列表常量池，转为指定格式的返回结果
     *
     * @param clazz        常量池类
     * @param defaultCheck 默认项
     * @return 集合
     */
    public static List<ListConstantVO> listVO(Class clazz, String... defaultCheck) {
        ArrayList<ListConstantVO> listConstantVOS = new ArrayList<>();
        List<String> fieldsValue = Arrays.stream(ReflectUtil.getFieldsValue(clazz)).map(Object::toString).collect(Collectors.toList());
        fieldsValue.forEach(obj -> {
            ListConstantVO listConstantVO = new ListConstantVO().setName(obj);
            if (Arrays.asList(defaultCheck).contains(obj))
                listConstantVO.setIsDefault(true);
            listConstantVOS.add(listConstantVO);
        });
        return listConstantVOS;
    }
}
