package org.irm.lab.common.config.tenant;

import com.mongodb.client.MongoClient;
import com.mongodb.client.MongoDatabase;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.bson.codecs.configuration.CodecRegistry;
import org.irm.lab.common.constant.AppConstant;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.context.annotation.DependsOn;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.HashMap;

/**
 * <AUTHOR>
 * @date 2022-12-27
 */
@Component("mongoTenantFactory")
@Slf4j
@DependsOn("baseMongoConfig")
public class MongoTenantFactory implements InitializingBean {
    @Resource
    private CodecRegistry codecRegistry;
    @Resource
    private MongoClient mongoClient;
    @Resource
    private MongoDatabase mongoDatabase;

    private final HashMap<String, MongoDatabase> tenantMongoDatabaseMap = new HashMap<>();

    public MongoDatabase buildTenantDatabase(String tenantId) {
        String dbName = tenantId == null ? StringUtils.chop(AppConstant.TENANT_DB_PREFIX) : AppConstant.TENANT_DB_PREFIX + tenantId;
        if (!tenantMongoDatabaseMap.containsKey(dbName)) {
            tenantMongoDatabaseMap.put(dbName,
                    mongoClient.getDatabase(dbName).withCodecRegistry(codecRegistry)
            );
            log.info("为当前模块构建mongo租户数据库：【{}】", dbName);
        }
        return tenantMongoDatabaseMap.get(dbName);
    }

    public MongoDatabase selectDatabase(String tenantId) {
        String dbName = tenantId == null ? StringUtils.chop(AppConstant.TENANT_DB_PREFIX) : AppConstant.TENANT_DB_PREFIX + tenantId;
//        log.info("当前操作选择租户数据库为【{}】", dbName);
        MongoDatabase mongoDatabase = tenantMongoDatabaseMap.get(dbName);
        if (mongoDatabase == null) {
            return buildTenantDatabase(tenantId);
        }
        return mongoDatabase;
    }

    @Override
    public void afterPropertiesSet() throws Exception {
        String dbName = StringUtils.chop(AppConstant.TENANT_DB_PREFIX);
        tenantMongoDatabaseMap.put(dbName, mongoDatabase);
    }
}
