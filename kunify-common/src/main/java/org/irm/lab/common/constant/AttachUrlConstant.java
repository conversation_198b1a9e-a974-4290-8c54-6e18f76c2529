package org.irm.lab.common.constant;

import org.springframework.beans.factory.InitializingBean;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

@Component
public class AttachUrlConstant implements InitializingBean {

    /**
     * minio对内IP
     * 华能数据库服务器  私有IP
     */
    @Value("${img-constant.internet-domain:http://************:9000}")
    private String internet_domain;
    public static String INTRANET_DOMAIN;

    /**
     * minio对外IP
     * 华能数据库服务器  弹性IP
     */
    @Value("${img-constant.domain:http://************:9000}")
    private String domain;
    public static String DOMAIN;

    /**
     * minio桶前缀
     */
    @Value("${img-constant.bucket:/kunify-hn-}")
    private String bucket;
    public static String BUCKET;

    @Override
    public void afterPropertiesSet() throws Exception {
        INTRANET_DOMAIN = internet_domain;
        DOMAIN = domain;
        BUCKET = bucket;
    }

    //公司环境
//    /**
//     * minio对外IP
//     * 华能数据库服务器  弹性IP
//     */
//    public static String DOMAIN = "http://************:9000";
//    /**
//     * minio对内IP
//     * 华能数据库服务器  私有IP
//     */
//    public static String INTRANET_DOMAIN = "http://************:9000";
//    /**
//     * minio桶前缀
//     */
//    public static String BUCKET = "/kunify-hn-";


    //测试环境
//    /**
//     * minio对内IP
//     * 华能数据库服务器  私有IP
//     */
//    public static String INTRANET_DOMAIN = "http://***********:9000";
//
//    /**
//     * minio对外IP
//     * 华能数据库服务器  弹性IP
//     */
//    public static String DOMAIN = "http://**************:9000";
//
//    /**
//     * minio桶前缀
//     */
//    public static String BUCKET = "/kunify-hn-";


//    //生产环境-http
//    /**
//     * minio对内IP
//     * 华能数据库服务器  私有IP
//     */
//    public static String INTRANET_DOMAIN = "http://************:9000";
//
//    /**
//     * minio对外IP
//     * 华能数据库服务器  弹性IP
//     */
//    public static String DOMAIN = "http://**************:9000";
//
//    /**
//     * minio桶前缀
//     */
//    public static String BUCKET = "/kunify-hn-";

//    //生产环境-https
//    /**
//     * minio对内IP
//     * 华能数据库服务器  私有IP
//     */
//    public static String INTRANET_DOMAIN = "http://************:9000";
//
//    /**
//     * minio对外IP
//     * 华能数据库服务器  弹性IP
//     */
//    public static String DOMAIN = "https://edocarchive.chng.com.cn:9000";
//
//    /**
//     * minio桶前缀
//     */
//    public static String BUCKET = "/kunify-hn-";

}
