package org.irm.lab.common.constant;

import lombok.AllArgsConstructor;
import lombok.Data;

/**
 * 文件权限常量
 */
public interface FileAuthConstant {

    String META_NAME_KNOW_RANGE = "知悉范围";
    String META_NAME_SECRET_LEVEL = "密级";

    //bean名称
    String BEAN_NAME_PUBLIC = "publicStrategy";
    String BEAN_NAME_META = "metaStrategy";
    String BEAN_NAME_DISABLE = "disableStrategy";

    //知悉范围
    String KNOW_RANGE_PUBLIC = "1";
    String KNOW_RANGE_DISABLE = "0";
    String KNOW_RANGE_META = "3";
    String KNOW_RANGE_DEPART = "2";

    //密级（公开or不公开）
    String[] SECRET_LEVEL_PUBLIC = {"无密级", "公开"};

    //文件可见状态
    String FILE_VISIBLE_PUBLIC = "文件可见";
    String FILE_VISIBLE_DISABLE = "文件不可见";
    String FILE_VISIBLE_META = "元数据项可见";

}
