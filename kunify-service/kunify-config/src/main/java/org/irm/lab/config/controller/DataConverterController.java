package org.irm.lab.config.controller;

import cn.hutool.core.convert.Convert;
import cn.hutool.core.util.ObjectUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.irm.lab.common.annotation.MyLog;
import org.irm.lab.common.api.R;
import org.irm.lab.common.constant.LogConstant;
import org.irm.lab.common.support.MyPage;
import org.irm.lab.common.utils.Func;
import org.irm.lab.config.constant.DataConverterType;
import org.irm.lab.config.entity.DataConverter;
import org.irm.lab.config.feign.DataConverterFeign;
import org.irm.lab.config.service.IDataConverterService;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.Arrays;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2023/1/12 18:23
 * @description 数据转化器控制器
 */
@Api("数据转化器")
@RestController
@RequestMapping("/data-converter")
public class DataConverterController implements DataConverterFeign {

    @Resource
    private IDataConverterService iDataConverterService;


    /**
     * 新增一个数据转化器
     *
     * @param dataConverter {@link DataConverter}
     * @return 数据转化器Id
     */
    @MyLog(menu = LogConstant.MENU_VIEW, dataType = LogConstant.DATA_CONVERTER, operation = LogConstant.OPERATION_ADD)
    @PostMapping("/save")
    @ApiOperation(value = "新增数据转化器")
    public R<String> save(@Validated @RequestBody DataConverter dataConverter) {
        return R.data(iDataConverterService.saveOrUpdate(dataConverter));
    }


    /**
     * 删除一个/多个数据转化器
     *
     * @param ids 数据转化器ID列表
     * @return {@link R<String>}
     */
    @MyLog(menu = LogConstant.MENU_VIEW, dataType = LogConstant.DATA_CONVERTER, operation = LogConstant.OPERATION_REMOVE)
    @PostMapping("/remove")
    @ApiOperation(value = "删除数据转化器", notes = "删除一个或多个数据转化器")
    public R<String> remove(@RequestBody String ids) {
        // 数据转换
        List<String> dataConverterIdList = Func.objToStrList(ids);
        // 删除单个/多个数据转化器
        iDataConverterService.remove(dataConverterIdList);
        return R.success();
    }

    /**
     * 根据Id查询指定数据转化器
     *
     * @param id 数据转化器Id
     * @return {@link DataConverter}
     */
    @GetMapping("/info")
    @ApiOperation(value = "数据转化器详情", notes = "根据Id获取指定数据转化器")
    public R<DataConverter> info(@RequestParam("id") String id) {
        return R.data(iDataConverterService.info(id));
    }

    @MyLog(menu = LogConstant.MENU_VIEW, dataType = LogConstant.DATA_CONVERTER, operation = LogConstant.OPERATION_CONFIGURE)
    @PostMapping("/config")
    @ApiOperation(value = "转化器配置")
    public R<DataConverter> config(@Validated @RequestBody DataConverter dataConverter) {
        return R.data(iDataConverterService.config(dataConverter));
    }

    /**
     * 更新数据转化器
     *
     * @param dataConverter {@link DataConverter}
     * @return 数据转化器Id
     */
    @MyLog(menu = LogConstant.MENU_VIEW, dataType = LogConstant.DATA_CONVERTER, operation = LogConstant.OPERATION_ALTER)
    @PostMapping("/update")
    @ApiOperation(value = "更新数据转化器")
    public R<String> update(@Validated @RequestBody DataConverter dataConverter) {
        return R.data(iDataConverterService.saveOrUpdate(dataConverter));
    }

    /**
     * 条件分页查询
     *
     * @param pageMap 分页对象
     * @return 分页数据
     */
    @GetMapping("/page")
    @ApiOperation(value = "条件分页查询")
    public R<MyPage<DataConverter>> page(@RequestParam(required = false) Map<String, Object> pageMap) {
        // 获取分页条件
        Integer page = Convert.toInt(pageMap.getOrDefault("page", 1));
        Integer size = Convert.toInt(pageMap.getOrDefault("size", 10));
        // 分页查询
        return R.data(iDataConverterService.page(pageMap, page, size));
    }

    @GetMapping("/schema-and-converter")
    @ApiOperation(value = "获取指定方案和数据集下的转化器", hidden = true)
    public R<List<DataConverter>> listBySchemaAndConverter(@RequestParam String schemaId, @RequestParam String dataSetId) {
        return R.data(iDataConverterService.listBySchemaAndConverter(schemaId, dataSetId));
    }

    /**
     * 获取所有数据转化器
     *
     * @return {@link  DataConverter}
     */
    @GetMapping("/list")
    @ApiOperation(value = "获取所有数据转化器")
    public R<List<DataConverter>> list() {
        return R.data(iDataConverterService.list());
    }

    /**
     * 条件查询数据转化器
     *
     * @param filterMap 查询条件
     * @return {@link DataConverter}
     */
    @GetMapping("/find-by-condition")
    @ApiOperation(value = "条件查询")
    public R<List<DataConverter>> listByCondition(@RequestParam(required = false) Map<String, Object> filterMap) {
        return R.data(iDataConverterService.listByCondition(filterMap));
    }

    /**
     * 动态获取元数据转化器
     *
     * @param metadataSchemaId 元数据方案Id
     * @param modelId          模型Id
     * @param dataSetId        数据集Id
     * @return {@link DataConverter}
     */
    @GetMapping("list-by-multi-condition")
    @ApiOperation(value = "动态获取元数据转化器")
    public R<List<DataConverter>> listByMultiCondition(String metadataSchemaId, String modelId, String dataSetId, String type) {
        if (ObjectUtil.isEmpty(metadataSchemaId)) {
            return R.failed("请配置元数据方案!");
        }
        if (ObjectUtil.isEmpty(modelId)) {
            return R.failed("请配置知识模型!");
        }
        if (ObjectUtil.isEmpty(dataSetId)) {
            return R.failed("请配置数据集!");
        }
        return R.data(iDataConverterService.listByCondition(Map.of("metadataSchemaId", metadataSchemaId, "dataSetId", dataSetId, "type", type)));
    }

    /**
     * 获取数据转化器类型
     *
     * @return 转化器类型列表
     */
    @GetMapping("/list-converter-type")
    @ApiOperation(value = "获取数据转化器类型列表")
    public R<List<String>> listConverterType() {
        return R.data(Arrays.asList(DataConverterType.ALL_CONVERTER_TYPE));
    }

}
