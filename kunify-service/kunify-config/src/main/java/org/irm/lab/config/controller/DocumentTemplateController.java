package org.irm.lab.config.controller;

import cn.hutool.core.convert.Convert;
import cn.hutool.json.JSONObject;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.irm.lab.common.annotation.MyLog;
import org.irm.lab.common.api.R;
import org.irm.lab.common.constant.LogConstant;
import org.irm.lab.common.support.MyPage;
import org.irm.lab.common.utils.Func;
import org.irm.lab.config.entity.DocumentTemplate;
import org.irm.lab.config.service.IDocumentTemplateService;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.util.Map;

@Api(value = "文档模板")
@RestController
@RequestMapping("/document-Template")
@RequiredArgsConstructor
public class DocumentTemplateController {

    private final IDocumentTemplateService templateService;

    /**
     * 新增或修改文档模板
     *
     * @param template {@link DocumentTemplate}
     * @return {@link DocumentTemplate}
     */
    @MyLog(menu = LogConstant.CONFIGURE_THE_HUB, dataType = LogConstant.DOCUMENT_TEMPLATE, operation = LogConstant.OPERATION_ADD)
    @ApiOperation(value = "文档模板增改")
    @PostMapping("/save")
    public R<DocumentTemplate> save(@Validated @RequestBody DocumentTemplate template) {
        return R.data(templateService.save(template));
    }


    /**
     * 删除文档模板
     *
     * @param ids 文档模板Id
     */
    @MyLog(menu = LogConstant.CONFIGURE_THE_HUB, dataType = LogConstant.DOCUMENT_TEMPLATE, operation = LogConstant.OPERATION_REMOVE)
    @ApiOperation(value = "文档模板删除")
    @PostMapping(value = "/remove")
    public R<Void> remove(@RequestBody String ids) {
        templateService.remove(Func.objToStrList(ids));
        return R.success();
    }


    /**
     * 当前文档模板分页查询
     *
     * @param param 查询条件
     * @return {@link DocumentTemplate}
     */
    @ApiOperation(value = "文档模板分页")
    @GetMapping("/page")
    public R<MyPage<DocumentTemplate>> page(@RequestParam Map<String, Object> param) {
        // 获取分页条件
        Integer page = Convert.toInt(param.getOrDefault("page", 1));
        Integer size = Convert.toInt(param.getOrDefault("size", 20));
        // 分页查询
        return R.data(templateService.page(param, page, size));
    }

    /**
     * 文档模板预览地址
     *
     * @param id
     * @return
     */
    @ApiOperation(value = "文档模板预览地址")
    @GetMapping("/preview")
    public R<JSONObject> preview(@RequestParam String id) {
        return R.data(templateService.preview(id));
    }


    /**
     * 文档模板上传模板
     *
     * @param file
     * @param templateId
     * @return
     */
    @ApiOperation(value = "文档模板上传")
    @PostMapping("/upload")
    public R<DocumentTemplate> upload(@RequestParam("file") MultipartFile file, String templateId) {
        return R.data(templateService.upload(file, templateId));
    }



}
