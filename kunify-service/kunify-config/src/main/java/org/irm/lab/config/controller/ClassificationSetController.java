package org.irm.lab.config.controller;

import cn.hutool.core.convert.Convert;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.irm.lab.common.annotation.MyLog;
import org.irm.lab.common.api.R;
import org.irm.lab.common.constant.LogConstant;
import org.irm.lab.common.support.MyPage;
import org.irm.lab.common.utils.Func;
import org.irm.lab.config.dto.resp.ClassificationSetRespDTO;
import org.irm.lab.config.entity.ClassificationSet;
import org.irm.lab.config.service.IClassificationSetService;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2023/1/2 17:54
 * @description 类目集控制器
 */
@Api("类目集管理")
@RestController
@RequestMapping("/classification-set")
public class ClassificationSetController {

    @Resource
    private IClassificationSetService iClassificationSetService;


    /**
     * 新增一个类目集
     *
     * @param classificationSet {@link ClassificationSet}
     * @return 类目集ID
     */
    @MyLog(menu = LogConstant.MENU_VIEW, dataType = LogConstant.DATA_CLASSIFICATION_SET, operation = LogConstant.OPERATION_ADD)
    @PostMapping("/save")
    @ApiOperation(value = "新增类目集",notes = "新增一个类目集")
    public R<String> saveClassificationSet(@Validated @RequestBody ClassificationSet classificationSet) {
        return R.data(iClassificationSetService.saveOrUpdateClassificationSet(classificationSet));
    }


    /**
     * 删除一个/多个类目集
     *
     * @param ids 类目集列表
     * @return {@link R<String>}
     */
    @MyLog(menu = LogConstant.MENU_VIEW, dataType = LogConstant.DATA_CLASSIFICATION_SET, operation = LogConstant.OPERATION_REMOVE)
    @PostMapping("/remove")
    @ApiOperation(value = "删除类目集",notes = "删除一个或多个类目集")
    public R<String> removeClassificationSets(@RequestBody String ids) {
        // 数据转换
        List<String> ClassificationSetIdList = Func.objToStrList(ids);
        // 删除
        iClassificationSetService.removeClassificationSets(ClassificationSetIdList);
        return R.success();
    }

    /**
     * 根据ID查询指定类目集
     *
     * @param id 类目集ID
     * @return {@link ClassificationSet}
     */
    @GetMapping("/info")
    @ApiOperation(value = "类目集详情",notes = "根据Id获取指定的类目集")
    public R<ClassificationSet> info(@RequestParam("id") String id) {
        return R.data(iClassificationSetService.info(id));
    }

    /**
     * 修改类目集
     *
     * @param classificationSet {@link ClassificationSet}
     * @return 类目集ID
     */
    @MyLog(menu = LogConstant.MENU_VIEW, dataType = LogConstant.DATA_CLASSIFICATION_SET, operation = LogConstant.OPERATION_ALTER)
    @PostMapping("/update")
    @ApiOperation(value = "更新类目集",notes = "更新一个类目集或修改类目集状态")
    public R<String> updateClassificationSet(@Validated @RequestBody ClassificationSet classificationSet) {
        iClassificationSetService.saveOrUpdateClassificationSet(classificationSet);
        return R.success();
    }


    /**
     * 条件分页查询
     *
     * @param pageMap 分页对象
     * @return 分页数据
     */
    @GetMapping("/page")
    @ApiOperation(value = "条件分页查询")
    public R<MyPage<ClassificationSetRespDTO>> page(@RequestParam(required = false) Map<String, Object> pageMap) {
        // 获取分页条件
        Integer page = Convert.toInt(pageMap.getOrDefault("page", 1));
        Integer size = Convert.toInt(pageMap.getOrDefault("size", 10));
        // 分页查询
        return R.data(iClassificationSetService.page(pageMap, page, size));
    }

    /**
     * 获取全部可用的类目集
     *
     * @return {@link ClassificationSet}
     */
    @GetMapping("/list")
    @ApiOperation(value = "获取全部可用的类目集",notes = "状态为'正常'")
    public R<List<ClassificationSet>> list(){
        // 返回可用的类目集列表
        return R.data(iClassificationSetService.list());
    }


}
