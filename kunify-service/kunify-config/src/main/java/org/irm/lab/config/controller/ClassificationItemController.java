package org.irm.lab.config.controller;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.irm.lab.common.annotation.MyLog;
import org.irm.lab.common.api.R;
import org.irm.lab.common.constant.LogConstant;
import org.irm.lab.common.tool.INode;
import org.irm.lab.common.utils.Func;
import org.irm.lab.config.entity.ClassificationItem;
import org.irm.lab.config.feign.ClassificationItemFeign;
import org.irm.lab.config.service.IClassificationItemService;
import org.irm.lab.config.vo.ClassificationItemVO;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2023/1/3 13:44
 * @description 类目项控制器
 */
@Api("类目项管理")
@RestController
@RequestMapping("/classification-item")
public class ClassificationItemController implements ClassificationItemFeign {

    @Resource
    private IClassificationItemService iClassificationItemService;

    /**
     * 新增类目项
     *
     * @param classificationItem {@link ClassificationItem}
     * @return 类目项ID
     */
    @MyLog(menu = LogConstant.MENU_VIEW, dataType = LogConstant.DATA_CLASSIFICATION_ITEM, operation = LogConstant.OPERATION_ADD)
    @PostMapping("/save")
    @ApiOperation(value = "新增类目项", notes = "新增一个类目项")
    public R<String> saveClassificationItem(@RequestBody ClassificationItem classificationItem) {
        return R.data(iClassificationItemService.saveOrUpdateClassificationSet(classificationItem));
    }

    /**
     * 删除一个/多个类目项
     *
     * @param ids 类目集列表
     * @return {@link R<String>}
     */
    @MyLog(menu = LogConstant.MENU_VIEW, dataType = LogConstant.DATA_CLASSIFICATION_ITEM, operation = LogConstant.OPERATION_REMOVE)
    @PostMapping("/remove")
    @ApiOperation(value = "删除类目项", notes = "删除一个或多个类目项")
    public R<String> removeClassificationItems(@RequestBody String ids) {
        // 数据转换
        List<String> ClassficationItemList = Func.objToStrList(ids);
        // 删除
        iClassificationItemService.removeClassficationItems(ClassficationItemList);
        return R.success();
    }

    /**
     * 根据ID查询指定类目项
     *
     * @param id 类目项Id
     * @return {@link ClassificationItem}
     */
    @GetMapping("/info")
    @ApiOperation(value = "类目项详情", notes = "根据Id获取指定类目项")
    public R<ClassificationItem> info(@RequestParam("id") String id) {
        return R.data(iClassificationItemService.info(id));
    }

    /**
     * 修改类目项
     *
     * @param classificationItem {@link ClassificationItem}
     * @return 类目项ID
     */
    @MyLog(menu = LogConstant.MENU_VIEW, dataType = LogConstant.DATA_CLASSIFICATION_ITEM, operation = LogConstant.OPERATION_ALTER)
    @PostMapping("/update")
    @ApiOperation(value = "更新类目项", notes = "更新一个类目项或修改类目项状态")
    public R<String> updateClassificationItem(@Validated @RequestBody ClassificationItem classificationItem) {
        iClassificationItemService.saveOrUpdateClassificationSet(classificationItem);
        return R.success();
    }

    /**
     * 类目项树结构查询
     *
     * @return {@link ClassificationItemVO}
     */
    @GetMapping("/tree")
    @ApiOperation(value = "类目项树结构查询")
    public R<List<? extends INode>> tree(@RequestParam(required = false) Map<String, Object> pageMap) {
        return R.data(iClassificationItemService.tree(pageMap));
    }

    /**
     * 根据类目集Id，查询该类目集下的所有可用类目项
     *
     * @param setId 类目集Id
     * @return {@link ClassificationItem}
     */
    @GetMapping("/enable-list")
    @ApiOperation(value = "根据类目集Id获取所有可用类目项 ")
    public R<List<ClassificationItem>> enableList(@RequestParam("setId") String setId) {
        return R.data(iClassificationItemService.enableList(setId));
    }

    @GetMapping("/enable-list-by-name")
    @ApiOperation(value = "根据类目集名称获取所有可用类目项 ")
    public R<List<ClassificationItem>> enableListByName(@RequestParam String setName) {
        return R.data(iClassificationItemService.enableListByName(setName));
    }

    @PostMapping("/sort-tree")
    @ApiOperation(value = "获取类目集树 ")
    public R<List<ClassificationItemVO>> sortTree(@RequestBody List<ClassificationItemVO> classificationItemList) {
        return R.data(iClassificationItemService.sortTree(classificationItemList));
    }

    @GetMapping("/gw-list")
    @ApiOperation(value = "获取公文下一级类目项")
    public R<List<ClassificationItem>> gwList() {
        return R.data(iClassificationItemService.gwList());
    }

    @GetMapping("/zd-list")
    @ApiOperation(value = "获取制度下一级类目项")
    public R<List<ClassificationItem>> zdList() {
        return R.data(iClassificationItemService.zdList());
    }
}
