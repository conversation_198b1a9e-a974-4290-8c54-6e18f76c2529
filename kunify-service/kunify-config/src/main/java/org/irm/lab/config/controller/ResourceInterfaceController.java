package org.irm.lab.config.controller;

import cn.hutool.core.convert.Convert;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.irm.lab.common.annotation.MyLog;
import org.irm.lab.common.api.R;
import org.irm.lab.common.constant.LogConstant;
import org.irm.lab.common.support.MyPage;
import org.irm.lab.common.utils.Func;
import org.irm.lab.config.entity.ResourceInterface;
import org.irm.lab.config.service.IResourceInterface;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2023/1/17 11:19
 * @description 资源接口控制器
 */
@Api(value = "资源接口")
@RestController
@RequestMapping("/resource-interface")
@RequiredArgsConstructor
public class ResourceInterfaceController {

    private final IResourceInterface iResourceInterface;

    /**
     * 新增一个资源接口
     *
     * @param resourceInterface {@link ResourceInterface}
     * @return 资源接口ID
     */
    @MyLog(menu = LogConstant.MENU_DATASOURCE, dataType = LogConstant.DATA_INTERFACE, operation = LogConstant.OPERATION_ADD)
    @PostMapping("/save")
    @ApiOperation(value = "新增资源接口")
    public R<String> save(@Validated @RequestBody ResourceInterface resourceInterface) {
        return R.data(iResourceInterface.saveOrUpdate(resourceInterface));
    }

    /**
     * 删除一个/多个资源接口
     *
     * @param ids 资源接口ID
     * @return {@link R}
     */
    @MyLog(menu = LogConstant.MENU_DATASOURCE, dataType = LogConstant.DATA_INTERFACE, operation = LogConstant.OPERATION_REMOVE)
    @PostMapping("/remove")
    @ApiOperation(value = "删除资源接口", notes = "删除一个或多个")
    public R<String> remove(@RequestBody String ids) {
        // 数据转换
        List<String> resourceInterfaceIdList = Func.objToStrList(ids);
        // 删除一个或多个
        iResourceInterface.remove(resourceInterfaceIdList);
        return R.success();
    }

    /**
     * 根据ID获取资源接口
     *
     * @param id 资源接口ID
     * @return {@link ResourceInterface}
     */
    @GetMapping("/info")
    @ApiOperation("根据ID获取资源接口")
    public R<ResourceInterface> info(@RequestParam("id") String id) {
        return R.data(iResourceInterface.info(id));
    }

    /**
     * 更新资源接口
     *
     * @param resourceInterface {@link ResourceInterface}
     * @return 资源接口ID
     */
    @MyLog(menu = LogConstant.MENU_DATASOURCE, dataType = LogConstant.DATA_INTERFACE, operation = LogConstant.OPERATION_ALTER)
    @PostMapping("/update")
    @ApiOperation(value = "新增资源接口")
    public R<String> update(@Validated @RequestBody ResourceInterface resourceInterface) {
        return R.data(iResourceInterface.saveOrUpdate(resourceInterface));
    }

    /**
     * 条件分页查询
     *
     * @param pageMap 分页对象
     * @return 分页数据
     */
    @GetMapping("/page")
    @ApiOperation(value = "条件分页查询")
    public R<MyPage<ResourceInterface>> page(@RequestParam(required = false) Map<String, Object> pageMap) {
        // 获取分页条件
        Integer page = Convert.toInt(pageMap.getOrDefault("page", 1));
        Integer size = Convert.toInt(pageMap.getOrDefault("size", 10));
        // 分页查询
        return R.data(iResourceInterface.page(pageMap, page, size));
    }

    /**
     * 获取全部资源接口
     *
     * @return {@link ResourceInterface}
     */
    @GetMapping("/list")
    @ApiOperation(value = "获取全部资源接口")
    public R<List<ResourceInterface>> list() {
        return R.data(iResourceInterface.list());
    }
}
