package org.irm.lab.config.controller;

import cn.hutool.core.convert.Convert;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.irm.lab.common.annotation.MyLog;
import org.irm.lab.common.api.R;
import org.irm.lab.common.constant.LogConstant;
import org.irm.lab.common.support.MyPage;
import org.irm.lab.common.utils.Func;
import org.irm.lab.config.entity.Metadata;
import org.irm.lab.config.entity.MetadataItem;
import org.irm.lab.config.entity.MetadataLabel;
import org.irm.lab.config.entity.MetadataSchema;
import org.irm.lab.config.feign.MetadataSchemaFeign;
import org.irm.lab.config.service.IMetadataSchemaService;
import org.irm.lab.config.vo.MetadataItemVO;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2023/1/6 14:43
 * @description 元数据方案控制器
 */
@Api(value = "元数据方案")
@RestController
@RequestMapping("/metadata-schema")
public class MetadataSchemaController implements MetadataSchemaFeign {


    @Resource
    private IMetadataSchemaService iMetadataSchemaService;

    /**
     * 新增元数据方案
     *
     * @param metadataSchema {@link MetadataSchema}
     * @return 元数据方案ID
     */
    @MyLog(menu = LogConstant.MENU_VIEW, dataType = LogConstant.DATA_METADATA_SCHEMA, operation = LogConstant.OPERATION_ADD)
    @ApiOperation(value = "元数据方案新增")
    @PostMapping("/save")
    public R<String> saveMetadataSchema(@Validated @RequestBody MetadataSchema metadataSchema) {
        return R.data(iMetadataSchemaService.saveOrUpdate(metadataSchema));
    }

    /**
     * 删除一个/多个元数据方案
     *
     * @param ids 元数据方案ID
     * @return {@link R}
     */
    @MyLog(menu = LogConstant.MENU_VIEW, dataType = LogConstant.DATA_METADATA_SCHEMA, operation = LogConstant.OPERATION_REMOVE)
    @ApiOperation(value = "元数据方案删除")
    @PostMapping("/remove")
    public R<String> removeMetadataSchemas(@RequestBody String ids) {
        // 数据转换
        List<String> metadataSchemaIdList = Func.objToStrList(ids);
        // 删除
        iMetadataSchemaService.removeMetadataSchemas(metadataSchemaIdList);
        return R.success();
    }


    /**
     * 根据ID查询指定的元数据方案
     *
     * @param id 元数据方案ID
     * @return {@link MetadataSchema}
     */
    @ApiOperation(value = "元数据方案详情")
    @GetMapping("/info")
    public R<MetadataSchema> info(@RequestParam("id") String id) {
        return R.data(iMetadataSchemaService.info(id));
    }

    /**
     * 修改元数据方案
     *
     * @param metadataSchema {@link MetadataSchema}
     * @return 元数据方案ID
     */
    @MyLog(menu = LogConstant.MENU_VIEW, dataType = LogConstant.DATA_METADATA_SCHEMA, operation = LogConstant.OPERATION_ALTER)
    @ApiOperation(value = "元数据方案修改")
    @PostMapping("/update")
    public R<String> updateMetadataSchema(@Validated @RequestBody MetadataSchema metadataSchema) {
        return R.data(iMetadataSchemaService.saveOrUpdate(metadataSchema));
    }


    /**
     * 分页查询
     *
     * @param pageMap 条件参数
     * @return 分页数据
     */
    @ApiOperation(value = "元数据方案分页")
    @GetMapping("/page")
    public R<MyPage<MetadataSchema>> page(@RequestParam(required = false) Map<String, Object> pageMap) {
        // 获取分页条件
        Integer page = Convert.toInt(pageMap.getOrDefault("page", 1));
        Integer size = Convert.toInt(pageMap.getOrDefault("size", 10));
        // 分页查询
        return R.data(iMetadataSchemaService.page(pageMap, page, size));
    }


    /**
     * 元数据方案配置 ===> 获取所有可用的元数据项
     *
     * @param metadataSchemaId 元数据方案Id
     * @return 元数据项列表
     */
    @ApiOperation(value = "元数据方案列表")
    @GetMapping("/list-metadata")
    public R<List<Metadata>> listMetadata(@RequestParam("metadataSchemaId") String metadataSchemaId) {
        return R.data(iMetadataSchemaService.listMetadata(metadataSchemaId));
    }


    /**
     * 元数据方案配置 ===> 新增元数据(元数据项复制体)
     *
     * @param metadataSchemaId 元数据方案Id
     * @param ids              元数据项Id列表
     * @return 元数据项ID
     */
    @MyLog(menu = LogConstant.MENU_VIEW, dataType = LogConstant.DATA_METADATA, operation = LogConstant.OPERATION_ADD)
    @ApiOperation(value = "新增元数据")
    @PostMapping("/add-metadata-item")
    public R<String> addMetadataItem(@RequestParam("metadataSchemaId") String metadataSchemaId, @RequestBody String ids) {
        // 获取元数据项Id列表
        List<String> metadataIdList = Func.objToStrList(ids);
        return R.data(iMetadataSchemaService.addMetadataItem(metadataSchemaId, metadataIdList));
    }


    /**
     * 元数据方案配置 ===> 删除元数据(元数据复制体)
     *
     * @param paramMap 参数
     * @return {@link R}
     */
    @MyLog(menu = LogConstant.MENU_VIEW, dataType = LogConstant.DATA_METADATA, operation = LogConstant.OPERATION_REMOVE)
    @ApiOperation(value = "删除元数据")
    @PostMapping("/remove-metadata-item")
    public R<String> removeMetadataItem(@RequestBody Map<String, Object> paramMap) {
        // 获取元数据方案Id
        String metadataSchemaId = Convert.toStr(paramMap.get("metadataSchemaId"));
        // 获取元数据复制体Id
        List<?> metadataItemIdList = Convert.toList(paramMap.get("metadataItemId"));
        for (Object o : metadataItemIdList) {
            String metadataItemId = Convert.toStr(o);
            // 删除
            iMetadataSchemaService.removeMetadataItem(metadataSchemaId, metadataItemId);
        }
        return R.success();
    }


    /**
     * 元数据方案配置 ===> 根据元数据方案Id和元数据项复制体Id查询
     *
     * @param metadataSchemaId 元数据方案ID
     * @param sourceMetadataId 元数据项复制体的源Id
     * @return {@link MetadataItem}
     */
    @ApiOperation(value = "根据元数据方案Id和元数据项复制体Id查询", hidden = true)
    @GetMapping("/info-metadata-item")
    public R<MetadataItemVO> infoMetadataItem(String metadataSchemaId, String sourceMetadataId) {
        return R.data(iMetadataSchemaService.infoMetadataItem(metadataSchemaId, sourceMetadataId));
    }


    /**
     * 元数据方案配置 ===> 获取当前方案下可用的元数据标签
     *
     * @param metadataSchemaId 元数据方案Id
     * @return {@link MetadataLabel}
     */
    @ApiOperation(value = "获取当前方案下可用的元数据标签", hidden = true)
    @GetMapping("/list-metadata-item")
    public R<List<MetadataLabel>> listMetadataItem(String metadataSchemaId) {
        return R.data(iMetadataSchemaService.listMetadataItem(metadataSchemaId));
    }


    /**
     * 元数据方案配置 ===> 修改指定元数据方案下的元数据项复制体
     *
     * @param metadataSchemaId 元数据方案Id
     * @param metadataItem     {@link MetadataItem}
     * @return {@link R}
     */
    @MyLog(menu = LogConstant.MENU_VIEW, dataType = LogConstant.DATA_METADATA, operation = LogConstant.OPERATION_ALTER)
    @ApiOperation(value = "修改指定元数据方案下的元数据项复制体", hidden = true)
    @PostMapping("/update-metadata-item")
    public R<String> updateMetadataItem(@RequestParam("metadataSchemaId") String metadataSchemaId, @RequestBody MetadataItem metadataItem) {
        iMetadataSchemaService.updateMetadataItem(metadataSchemaId, metadataItem);
        return R.success();
    }


    /**
     * 元数据方案配置 ===> 条件分页查询
     *
     * @param pageMap 分页对象
     * @return 分页数据
     */
    @ApiOperation(value = "元数据方案配置-条件分页查询", hidden = true)
    @GetMapping("/page-metadata-item")
    public R<MyPage<MetadataItem>> pageMetadataItem(@RequestParam(required = false) Map<String, Object> pageMap) {
        // 获取分页条件
        Integer page = Convert.toInt(pageMap.getOrDefault("page", 1));
        Integer size = Convert.toInt(pageMap.getOrDefault("size", 10));
        // 分页查询
        return R.data(iMetadataSchemaService.pageMetadataItem(pageMap, page, size));
    }

    /**
     * 根据元数据方案id集合，元数据项id，获取每一个元数据meta
     */
    @ApiOperation(value = "获取元数据metadata", hidden = true)
    @PostMapping("/meta-by-schema-and-meta-id")
    public R<List<MetadataItem>> metaBySchemaAndMetaId(@RequestBody Map<String, Object> body) {
        List<String> schemaIds = Convert.toList(String.class, body.getOrDefault("schemaIds", new ArrayList<>()));
        String metaId = Convert.toStr(body.getOrDefault("metaId", ""));
        return R.data(iMetadataSchemaService.allMetaBySchemaAndMetaId(schemaIds, metaId));
    }

    /**
     * 查询所有可用的元数据方案
     *
     * @return {@link MetadataSchema}
     */
    @ApiOperation(value = "元数据方案列表")
    @GetMapping("/list")
    public R<List<MetadataSchema>> list(@RequestParam(required = false) Map<String, Object> queryParam) {
        return R.data(iMetadataSchemaService.list(queryParam));
    }

    /**
     * 根据Id列表查询元数据方案列表
     *
     * @param ids 元数据方案Id
     * @return {@link MetadataSchema}
     */
    @ApiOperation(value = "根据Id列表查询元数据方案列表", hidden = true)
    @PostMapping("/list-by-ids")
    public R<List<MetadataSchema>> listByIds(@RequestBody String ids) {
        return R.data(iMetadataSchemaService.listByIds(Func.objToStrList(ids)));
    }


    /**
     * 为元数据项设置分组标签
     *
     * @param paramMap 参数map
     */
    @MyLog(menu = LogConstant.MENU_VIEW, dataType = LogConstant.DATA_METADATA, operation = LogConstant.OPERATION_CONFIGURE)
    @ApiOperation(value = "为元数据项设置分组标签")
    @PostMapping("/update-label")
    public R<Void> updateLabel(@RequestBody Map<String, Object> paramMap){
        String metadataSchemaId = Convert.toStr(paramMap.get("metadataSchemaId"));
        List<?> metadataItemIdList = Convert.toList(paramMap.get("metadataItemId"));
        String labelId = Convert.toStr(paramMap.get("labelId"));
        iMetadataSchemaService.updateLabel(metadataSchemaId,metadataItemIdList,labelId);
        return R.success();
    }


}
