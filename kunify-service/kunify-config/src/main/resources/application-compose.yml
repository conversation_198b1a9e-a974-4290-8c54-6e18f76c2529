spring:
  cloud:
    nacos:
      discovery:
        server-addr: kunify-nacos:8848
  redis:
    database: 3
    port: 6379
    host: kunify-redis
    password: PsOsGgN0rfXfIPbz
mongodb:
  host: kunify-mongodb
  port: 27017
  database: kunify-hn
  username: root
  password: mongo2022
  authenticationDatabase: admin
neo4j:
  url: bolt://kunify-neo4j:7687
  username: neo4j
  password: neo4j123

logging:
  level:
    root: INFO
  file:
    name: /data/kunify-config.log

unKnowErrorInfo: false
tenantId: "028639"