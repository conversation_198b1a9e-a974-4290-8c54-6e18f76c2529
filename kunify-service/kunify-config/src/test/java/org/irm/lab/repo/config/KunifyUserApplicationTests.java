package org.irm.lab.repo.config;

import cn.hutool.core.util.RandomUtil;
import org.irm.lab.config.entity.WorkTask;
import org.irm.lab.config.entity.WorkTaskNode;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

@SpringBootTest
class KunifyUserApplicationTests {

	@Test
	void contextLoads() {
	}

	@Test
	void contextLoads2() {


	}

//	@Test
//	void contextLoads1() {
//		WorkTask workTask = new WorkTask();
//		WorkTaskNode workTaskNode = new WorkTaskNode();
//		workTaskNode.setIdentifier(RandomUtil.randomString(15));
//		workTaskNode.setName("121");
//		WorkTaskNode workTaskNode2 = new WorkTaskNode();
//		workTaskNode2.setIdentifier(RandomUtil.randomString(15));
//		workTaskNode2.setName("222");
//
//		workTask.setWorkTaskNodes(List.of(workTaskNode,workTaskNode2));
//		workTask.setId("63db66f1e7e639b6ea7defb5");
//		workTaskRepository.save(workTask);
//	}

}
