server:
  port: 1004
router:
  name:
spring:
  application:
    name: kunify-resource
  cloud:
    nacos:
      discovery:
        heart-beat-timeout: 60000
        server-addr: http://*************:8848
        username: nacos
        password: 'Nacosabc'
        namespace: zsl
        # 添加连接稳定性配置
        heart-beat-interval: 30000
        ip-delete-timeout: 60000
        instance-enabled: true
      config:
        file-extension: yml
        name: ${spring.application.name}.${spring.cloud.nacos.config.file-extension}
        namespace: zsl
        config-long-poll-timeout: 60000 # 默认30000;长轮询时间 1分钟
        config-retry-time: 5000 # 默认2000;重试5秒
        max-retry: 6000 # 默认3;最大重试次数
        server-addr: http://*************:8848
        username: nacos
        password: 'Nacosabc'
        # 添加配置刷新和连接稳定性配置
        refresh-enabled: true
        enabled: true