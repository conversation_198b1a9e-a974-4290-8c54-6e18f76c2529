package org.irm.lab.kg;

import com.mongodb.client.model.Filters;
import org.bson.conversions.Bson;

import org.irm.lab.repository.KunifyRepositoryApplication;
import org.irm.lab.user.repository.DataPermissionRepository;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.config.BeanDefinition;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.context.annotation.ClassPathScanningCandidateComponentProvider;
import org.springframework.core.type.filter.AnnotationTypeFilter;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.lang.reflect.Method;
import java.util.*;

@ActiveProfiles("gcy")
@SpringBootTest(classes = KunifyRepositoryApplication.class)
class KunifyRepositoryApplicationTests {
    @Resource
    DataPermissionRepository dataPermissionRepository;
    @Test
    public void testGetAllControllersRequestPaths() throws ClassNotFoundException {
        // 包路径：指定要扫描的 controller 包路径（例如 "org.irm.lab.front.controller"）
        String basePackage = "org.irm.lab.repository.controller";
        long size = 0 ;
        // 扫描指定包下所有的类
        ClassPathScanningCandidateComponentProvider scanner = new ClassPathScanningCandidateComponentProvider(false);
        // 添加过滤器，仅扫描带有 @RestController 注解的类
        scanner.addIncludeFilter(new AnnotationTypeFilter(RestController.class));

        // 获取扫描到的所有候选类
        Set<BeanDefinition> beanDefinitions = scanner.findCandidateComponents(basePackage);

        // 遍历每个控制器类
        for (BeanDefinition beanDefinition : beanDefinitions) {
            String className = beanDefinition.getBeanClassName();
            Class<?> controllerClass = Class.forName(className);

            // 获取类上的 @RequestMapping 注解
            RequestMapping classRequestMapping = controllerClass.getAnnotation(RequestMapping.class);
            String rootPath = classRequestMapping != null ? classRequestMapping.value()[0] : "";

            // 存储该控制器的所有请求路径
            List<String> controllerPaths = new ArrayList<>();

            // 遍历控制器中的所有方法
            for (Method method : controllerClass.getDeclaredMethods()) {
                String methodPath = null;

                if (method.isAnnotationPresent(GetMapping.class)) {
                    GetMapping getMapping = method.getAnnotation(GetMapping.class);
                    methodPath = (getMapping.value().length > 0) ? getMapping.value()[0] : "";
                } else if (method.isAnnotationPresent(PostMapping.class)) {
                    PostMapping postMapping = method.getAnnotation(PostMapping.class);
                    methodPath = (postMapping.value().length > 0) ? postMapping.value()[0] : "";
                } else if (method.isAnnotationPresent(PutMapping.class)) {
                    PutMapping putMapping = method.getAnnotation(PutMapping.class);
                    methodPath = (putMapping.value().length > 0) ? putMapping.value()[0] : "";
                } else if (method.isAnnotationPresent(DeleteMapping.class)) {
                    DeleteMapping deleteMapping = method.getAnnotation(DeleteMapping.class);
                    methodPath = (deleteMapping.value().length > 0) ? deleteMapping.value()[0] : "";
                } else if (method.isAnnotationPresent(RequestMapping.class)) {
                    RequestMapping requestMapping = method.getAnnotation(RequestMapping.class);
                    methodPath = (requestMapping.value().length > 0) ? requestMapping.value()[0] : "";
                }

                // 组合类路径和方法路径，生成完整的请求路径
                if (methodPath != null) {
                    String fullPath = rootPath + methodPath;
                    Bson eq = Filters.eq("children.path", fullPath);
                    try {
                        dataPermissionRepository.findOne(eq);
                    } catch (Exception e) {
                        controllerPaths.add(fullPath);

                    }
//					if (dataPermissionFeign.infoByPath(fullPath).getCode() == 500) {
//						controllerPaths.add(fullPath);
//					}




                }
            }

            // 输出该控制器的所有路径
            System.out.println("Controller: " + controllerClass.getName());
            controllerPaths.forEach(System.out::println);
            size += controllerPaths.size();
        }
        System.out.println("需要添加的接口数量" + size);
    }
}
