package org.irm.lab.repository.controller;

import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.irm.lab.common.api.R;
import org.irm.lab.common.enums.QaDataSetEnum;
import org.irm.lab.common.support.Condition;
import org.irm.lab.config.entity.SortConfig;
import org.irm.lab.config.repository.SortConfigRepository;
import org.irm.lab.repository.dto.QaDTO;
import org.irm.lab.repository.service.IQaProcessService;
import org.springframework.web.bind.annotation.*;

import java.util.Arrays;
import java.util.Map;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * <AUTHOR>
 * @date 2023/5/20 14:23
 */
@Api(value = "问答", hidden = true)
@RestController
@RequestMapping("/qa")
@RequiredArgsConstructor
public class QAController {

    private final IQaProcessService iQaProcessService;

    private final SortConfigRepository sortConfigRepository;

    @ApiOperation("文档问答")
    @PostMapping("/doc-qa")
    public R<Void> save(@RequestBody QaDTO qaDTO) {
        iQaProcessService.qa(qaDTO.getDatasetId(), qaDTO.getToken(), qaDTO.getQuestion());
        return R.success();
    }

    @ApiOperation(value = "文档数据集")
    @GetMapping("/qa-sets")
    public R<JSONArray> qaSets() {

        final SortConfig sortConfig = sortConfigRepository.findByCondition(Condition.getFilter(Map.of("type", "AI问答配置"), SortConfig.class)).stream().findFirst().get();

        return R.data(JSONUtil.parseArray(sortConfig.getOrderJson()));
        /*JSONArray array = JSONUtil.createArray();
        for (QaDataSetEnum qaDataSetEnum : Arrays.stream(QaDataSetEnum.values()).filter(QaDataSetEnum::getIsShow).collect(Collectors.toList())) {
            array.add(JSONUtil.createObj().putOpt("name", qaDataSetEnum.getLogInfo())
                    .putOpt("id", qaDataSetEnum.getDataSetId())
            );
        }
        return R.data(array);*/
    }

    /**
     * 资源预览
     *
     * @param originId 资源原始id
     * @param type     资源类型 "1":主文件 "0":附件
     */
    @GetMapping("/preview")
    public R<String> preview(@RequestParam("originId") String originId, @RequestParam("selectSource") String type) {
        return R.data(iQaProcessService.preview(originId, type));
    }
}
