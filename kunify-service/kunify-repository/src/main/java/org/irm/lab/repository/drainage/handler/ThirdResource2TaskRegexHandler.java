package org.irm.lab.repository.drainage.handler;

import cn.hutool.core.collection.ListUtil;
import lombok.extern.slf4j.Slf4j;
import org.irm.lab.common.pojo.BaseStatus;
import org.irm.lab.config.entity.WorkTask;
import org.irm.lab.repository.drainage.IDrainageStrategy;
import org.irm.lab.repository.entity.datasource.DatasourceResource;
import org.irm.lab.repository.repository.DatasourceResourceRepository;
import org.irm.lab.repository.service.IDatasourceResourceService;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;
import java.util.Set;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

@Component
@Slf4j
//该处理器用于：匹配后的资源，分发到任务中
public class ThirdResource2TaskRegexHandler extends ResourceRegexHandler {
    @Resource
    private IDatasourceResourceService datasourceResourceService;
    @Resource
    private DatasourceResourceRepository datasourceResourceRepository;

    @Override
    public ResourceRegexHandler handle(IDrainageStrategy drainageStrategy, List<DatasourceResource> resources) {
        log.info("第3链：开始分发资源到任务中...");
        Set<WorkTask> regexTask = resources.isEmpty() ? drainageStrategy.getRegexTask() : drainageStrategy.getRegexTask(resources.get(0));
        int countByTask = (int) Math.ceil((double) resources.size() / regexTask.size());
        List<List<DatasourceResource>> split = ListUtil.split(resources, countByTask);

        if (!split.isEmpty()) {
            AtomicInteger i = new AtomicInteger(0);
            regexTask.forEach(task -> {
                datasourceResourceService.joinWork(split.get(i.get()).stream().map(DatasourceResource::getId).collect(Collectors.toList()), task.getId());
                i.getAndIncrement();
            });

        }

        //链条结束
        log.info("第3链：资源分发结束...");
        //设置已处理的resource,并更新状态
        resources.forEach(datasourceResource -> {
            datasourceResource.setDrainageStage(BaseStatus.OK);
            datasourceResourceRepository.updateOne(datasourceResource);
        });
        return this.setProcessResource(resources);
    }
}
