package org.irm.lab.repository.controller.datasource;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.irm.lab.common.api.R;
import org.irm.lab.common.utils.Func;
import org.irm.lab.repository.entity.datasource.DatasourceDataStruct;
import org.irm.lab.repository.service.IDatasourceDataStructService;
import org.irm.lab.repository.vo.DatasourceDataStructVO;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2023-02-23
 */

@RestController
@RequestMapping("/datasource-data-struct")
@Api(value = "数据结构")
public class DatasourceDataStructController {
    @Resource
    private IDatasourceDataStructService datasourceDataStructService;

    @ApiOperation("树形式返回")
    @GetMapping("/tree-list")
    public R<List<DatasourceDataStructVO>> treeList(String interfaceId, String type) {
        return R.data(datasourceDataStructService.listTree(interfaceId, type));
    }

    @ApiOperation("自动解析json")
    @PostMapping("/parse-json")
    public R<List<DatasourceDataStructVO>> parseJson(@RequestParam String interfaceId, @RequestBody String json) {
        return R.data(datasourceDataStructService.parseJson(interfaceId, json));
    }


    @ApiOperation("数据结构详情")
    @GetMapping("/info")
    public R<DatasourceDataStruct> info(@RequestParam String id) {
        return R.data(datasourceDataStructService.info(id));
    }


    @ApiOperation("数据结构增改")
    @PostMapping("/save")
    public R<DatasourceDataStruct> save(@RequestBody DatasourceDataStruct datasourceDataStruct) {
        return R.data(datasourceDataStructService.save(datasourceDataStruct));
    }


    @ApiOperation("数据结构删除")
    @PostMapping("/remove")
    public R<Void> remove(@RequestBody String ids) {
        datasourceDataStructService.remove(Func.objToStrList(ids));
        return R.success();
    }

}
