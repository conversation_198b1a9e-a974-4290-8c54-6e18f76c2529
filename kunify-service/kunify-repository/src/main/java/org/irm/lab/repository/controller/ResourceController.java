package org.irm.lab.repository.controller;

import cn.hutool.json.JSONArray;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.irm.lab.common.annotation.MyLog;
import org.irm.lab.common.api.R;
import org.irm.lab.common.constant.LogConstant;
import org.irm.lab.common.utils.Func;
import org.irm.lab.common.utils.ThreadLocalUtil;
import org.irm.lab.repository.entity.Resource;
import org.irm.lab.repository.service.IResourceService;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import java.util.List;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.LinkedBlockingQueue;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;


@RestController
@RequestMapping("/resource")
@RequiredArgsConstructor
@Api(value = "资源对象")
@Slf4j
public class ResourceController {


    private final IResourceService resourceService;

    private final HttpServletRequest request;

    @ApiOperation(value = "获取文件预览地址")
    @GetMapping("/resource-preview")
    public R<String> resourcePreview(@RequestParam String id) {
        return R.data(resourceService.resourcePreview(id));
    }

    /**
     * 根据资源Id获取资源对象
     *
     * @param id 资源Id
     * @return {@link Resource}
     */
    @GetMapping("/info")
    @ApiOperation(value = "根据资源Id获取资源对象")
    public R<Resource> info(@RequestParam String id) {
        return R.data(resourceService.info(id));
    }


    /**
     * 资源加工全部文件一键通过
     *
     * @param workTaskId 工作任务Id
     */
    @ApiOperation(value = "一键同步智能问答数据集")
    @GetMapping("/pass-resource-all-qa")
    private R<Void> passAllResourceQA(@RequestParam String workTaskId) {
        resourceService.passAllResourceQA(workTaskId);
        return R.success();
    }

    /**
     * 删除资源对象
     *
     * @param ids 资源id
     */
    @MyLog(menu = LogConstant.MENU_RESOURCE, dataType = LogConstant.DATA_RESOURCE, operation = LogConstant.OPERATION_REMOVE)
    @PostMapping("/remove")
    public R<Void> remove(@RequestBody List<String> ids) {
        ThreadLocalUtil.set("user", request.getHeader("user"));
        log.info("当前线程{}",Thread.currentThread().getId());
        CompletableFuture.runAsync(() -> resourceService.remove(ids), new ThreadPoolExecutor(10, 10,
                0L, TimeUnit.MILLISECONDS,
                new LinkedBlockingQueue<>()));

        return R.success();
    }

    /**
     * 删除资源的文件对象（不删除资源对象）
     *
     * @param ids 资源id
     */
    @MyLog(menu = LogConstant.MENU_RESOURCE, dataType = LogConstant.DATA_RESOURCE_FILE, operation = LogConstant.OPERATION_REMOVE)
    @PostMapping("/remove-file")
    public R<Void> removeFile(@RequestBody String ids) {
        List<String> idList = Func.objToStrList(ids);
        resourceService.removeNotResource(idList);
        return R.success();
    }


    /**
     * 附件替换正文
     *
     * @param archiveId 正文ID
     * @param annexId   附件ID
     * @return {@link Resource}
     */
    @GetMapping("/annex-replace-archive")
    @ApiOperation(value = "附件替换正文", hidden = true)
    public R<Void> info(@RequestParam String archiveId, @RequestParam String annexId) {
        resourceService.annexReplaceArchive(archiveId, annexId);
        return R.success();
    }

    /**
     * 获取主文件和其附件列表
     *
     * @param id 主文件Id
     * @return {@link JSONArray}
     */
    @GetMapping("/resource-with-annex")
    @ApiOperation(value = "获取主文件和其附件", hidden = true)
    public R<JSONArray> resourceWithAnnex(@RequestParam String id) {
        return R.data(resourceService.resourceWithAnnex(id));
    }

    @MyLog(menu = LogConstant.MENU_RESOURCE, dataType = LogConstant.DATA_RESOURCE_POWER, operation = LogConstant.OPERATION_CONFIGURE)
    @PostMapping("/update-front-status")
    @ApiOperation(value = "前台状态修改", hidden = true)
    public R<Void> updateFrontStatus(@RequestBody String ids, @RequestParam String status) {
        List<String> idList = Func.objToStrList(ids);
        resourceService.updateFrontStatus(idList, status);
        return R.success();
    }

}
