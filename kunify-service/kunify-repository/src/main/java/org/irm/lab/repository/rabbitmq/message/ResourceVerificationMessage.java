package org.irm.lab.repository.rabbitmq.message;

import lombok.Data;
import org.irm.lab.repository.entity.Resource;

import java.io.Serializable;


/**
 * <AUTHOR>
 * @date 2023/1/30 14:47
 * @description RabbitMq消息配置
 */
@Data
public class ResourceVerificationMessage implements Serializable {
    /**
     * 交换机
     */
    public static final String EXCHANGE = "KUNIFY4_EXCHANGE_RESOURCE_VERIFICATION";
    /**
     * 消息队列
     */
    public static final String QUEUE = "KUNIFY4_QUEUE_RESOURCE_VERIFICATION";
    /**
     * routingKey
     */
    public static final String ROUTING_KEY = "KUNIFY4_ROUTING_RESOURCE_VERIFICATION";

    /**
     * 当前用户信息
     */
    private String user;
    /**
     * 资源对象
     */
    private Resource resource;

}
