package org.irm.lab.repository.controller.datasource;

import cn.hutool.core.convert.Convert;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.irm.lab.common.api.R;
import org.irm.lab.common.support.MyPage;
import org.irm.lab.common.utils.ThreadLocalUtil;
import org.irm.lab.repository.entity.datasource.SpecifySyncExcel;
import org.irm.lab.repository.entity.datasource.SpecifySyncRow;
import org.irm.lab.repository.service.ISpecifySyncExcelService;
import org.irm.lab.repository.service.ISpecifySyncRowService;
import org.irm.lab.repository.sync.builder.ExcelAddress;
import org.irm.lab.repository.vo.SpecifySyncExcelVO;
import org.springframework.core.io.FileSystemResource;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.io.File;
import java.io.IOException;
import java.util.List;
import java.util.Map;

/**
 * 拉取指定文件接口
 */
@RestController
@RequestMapping("/specify")
@Api(value = "拉取指定文件接口")
@Slf4j
public class SpecifySyncController {

    @Resource
    private ISpecifySyncExcelService syncExcelService;

    @Resource
    private HttpServletRequest request;

    @Resource
    private ISpecifySyncRowService syncRowService;


    @ApiOperation(value = "上传excel文件", notes = "用于之后根据该excel文件记录的id，拉取数据")
    @PostMapping("/upload")
    public R<SpecifySyncExcel> uploadExcel(@RequestParam String datasourceId, @RequestParam("file") MultipartFile file) throws IOException {
        //校验（文件格式，md5，文件名称）
        syncExcelService.validate(file, datasourceId);
        //上传文件
        return R.data(syncExcelService.uploadExcel(datasourceId, file));
    }

    @ApiOperation(value = "删除excel")
    @PostMapping("/delete")
    public R<Void> deleteExcel(@RequestBody List<String> excelId) {
        syncExcelService.deleteExcel(excelId);
        return R.success();
    }


    @ApiOperation(value = "导出excel中的row")
    @GetMapping("/export-row")
    public ResponseEntity<?> scriptExportCatalogId(@RequestParam String excelId) {
        File file = syncExcelService.exportRow(excelId);
        HttpHeaders headers = new HttpHeaders();
        headers.add(HttpHeaders.CONTENT_DISPOSITION, "attachment; filename=" + file.getName());
        headers.add(HttpHeaders.CONTENT_LENGTH, String.valueOf(file.length()));
        headers.setContentType(MediaType.APPLICATION_OCTET_STREAM);

        return ResponseEntity.ok()
                .headers(headers)
                .body(new FileSystemResource(file));
    }


    @ApiOperation(value = "同步excel row中待同步的文件")
    @PostMapping("/sync")
    public R<Void> syncByExcel(@RequestParam String excelId) {
        ThreadLocalUtil.set("user", request.getHeader("user"));
        syncRowService.sync(excelId);
        return R.success("拉取数据中...");
    }

    @ApiOperation(value = "获取excel信息")
    @GetMapping("/page-excel")
    public R<MyPage<SpecifySyncExcelVO>> excelPage(@RequestParam Map<String, Object> pageMap) {
        // 获取分页条件
        Integer page = Convert.toInt(pageMap.getOrDefault("page", 1));
        Integer size = Convert.toInt(pageMap.getOrDefault("size", 10));
        return R.data(syncExcelService.page(pageMap, page, size));
    }

    @ApiOperation(value = "获取excel row信息")
    @GetMapping("/page-row")
    public R<MyPage<SpecifySyncRow>> rowPage(@RequestParam Map<String, Object> pageMap) {
        // 获取分页条件
        Integer page = Convert.toInt(pageMap.getOrDefault("page", 1));
        Integer size = Convert.toInt(pageMap.getOrDefault("size", 10));
        return R.data(syncRowService.page(pageMap, page, size));
    }


}
