package org.irm.lab.repository.controller.datasource;

import cn.hutool.core.convert.Convert;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.ReUtil;
import cn.hutool.json.JSONUtil;
import cn.hutool.log.Log;
import com.mongodb.client.model.Filters;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.irm.lab.common.annotation.MyLog;
import org.irm.lab.common.api.R;
import org.irm.lab.common.api.ResultCode;
import org.irm.lab.common.config.resolve.EsIndexResolver;
import org.irm.lab.common.constant.FiledNameConst;
import org.irm.lab.common.constant.LogConstant;
import org.irm.lab.common.pojo.BaseStatus;
import org.irm.lab.common.support.Condition;
import org.irm.lab.common.support.MyPage;
import org.irm.lab.common.utils.Func;
import org.irm.lab.common.utils.ThreadLocalUtil;
import org.irm.lab.common.vo.ListConstantVO;
import org.irm.lab.common.wrapper.ListConstantWrapper;
import org.irm.lab.config.entity.WorkTask;
import org.irm.lab.config.feign.WorkTaskFeign;
import org.irm.lab.repository.constant.DatasourceResourceConstant;
import org.irm.lab.repository.drainage.constant.DrainageType;
import org.irm.lab.repository.drainage.regex.ResourceRegex;
import org.irm.lab.repository.entity.BaseResource;
import org.irm.lab.repository.entity.ResourceAnnex;
import org.irm.lab.repository.entity.datasource.DatasourceAttach;
import org.irm.lab.repository.entity.datasource.DatasourceResource;
import org.irm.lab.repository.feign.DatasourceResourceFeign;
import org.irm.lab.repository.repository.DatasourceResourceRepository;
import org.irm.lab.repository.repository.ResourceAnnexRepository;
import org.irm.lab.repository.repository.ResourceRepository;
import org.irm.lab.repository.service.IDatasourceResourceService;
import org.irm.lab.repository.service.IPeripheralInterfaceService;
import org.irm.lab.repository.service.IResourceAfterProcessService;
import org.irm.lab.repository.service.IResourceService;
import org.irm.lab.repository.utils.IpUtil;
import org.irm.lab.resource.entity.Attach;
import org.irm.lab.resource.feign.AttachFeign;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.*;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.io.*;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2023-02-22
 */
@RestController
@RequestMapping("/datasource-resource")
@Api(value = "远程-资源列表")
@Slf4j
public class DatasourceResourceController implements DatasourceResourceFeign {
    @Resource
    private IDatasourceResourceService datasourceResourceService;
    @Resource
    private DatasourceResourceRepository datasourceResourceRepository;
    @Resource
    private IPeripheralInterfaceService peripheralInterfaceService;
    @Resource
    private HttpServletRequest request;

    @Resource
    private ResourceRepository resourceRepository;
    @Resource
    private ResourceAnnexRepository annexRepository;

    @Resource
    private IResourceAfterProcessService resourceAfterProcessService;
    @Resource
    private WorkTaskFeign workTaskFeign;
    @Resource
    private IResourceService resourceService;
    @Autowired
    private AttachFeign attachFeign;

    @MyLog(menu = LogConstant.MENU_DATASOURCE, dataType = LogConstant.DATA_DATASOURCE_RESOURCE, operation = LogConstant.OPERATION_SYNC)
    @ApiOperation(value = "资源数据同步")
    @GetMapping("/sync")
    public R<List<DatasourceResource>> syncResource(@RequestParam String datasourceId) {
        datasourceResourceService.syncResource(datasourceId, request.getHeader("user"));
        return R.success();
    }

    @MyLog(menu = LogConstant.MENU_DATASOURCE, dataType = LogConstant.DATA_DRAINAGE, operation = LogConstant.OPERATION_INITIATE)
    @ApiOperation(value = "自动分流")
    @GetMapping("/drainage")
    public R<Void> drainage(@RequestParam String datasourceId, @RequestParam String year) {
        datasourceResourceService.drainage(datasourceId, year);
        return R.success("后台正在默默分流中...");
    }

    /**
     * 自执行，从通用任务中，筛选文件分发到其他任务
     */
    public void script() {
        //从通用任务中，筛选文件分发到其他任务
        HashMap<String, List<org.irm.lab.repository.entity.Resource>> collectMap = new HashMap<>();
        //1.匹配通用任务中的所有资源
        List<org.irm.lab.repository.entity.Resource> all = resourceRepository.findByCondition(Filters.eq(FiledNameConst.WORK_TASK_ID, DrainageType.DEFAULT_DRAINAGE.getCode()));
        log.info("通用任务资源共有【{}】份", all.size());
        //2.筛选各类型文件
        Arrays.stream(DrainageType.values()).forEach(drainageType -> {
            if (!drainageType.getDrainageName().equals(DrainageType.DEFAULT_DRAINAGE.getDrainageName())) {
                List<org.irm.lab.repository.entity.Resource> collect = all.stream().filter(resource -> drainageType.getResourceRegexRule().stream().anyMatch(regex -> ReUtil.isMatch(regex, resource.getName()))).collect(Collectors.toList());
                collectMap.put(drainageType.getCode(), collect);

                log.info("筛选【{}】文件，策略为【{}】，共有{}份......", drainageType.getDrainageName(), drainageType.getResourceRegexRule(), collect.size());
            }
        });
        log.info("筛选完成，开始分发到其他任务中......");

        //3.分到各自任务中
        collectMap.forEach((taskID, value) -> value.forEach(resource -> {
            log.info("开始分发【{}】", taskID);
            //获取该资源的所有附件
            List<ResourceAnnex> allAnnex = annexRepository.findByCondition(Filters.eq(FiledNameConst.RESOURCE_ID, resource.getId()));
            //修改所有附件的taskID
            allAnnex.forEach(annex -> {
                annex.setWorkTaskId(taskID);
                annexRepository.updateOne(annex);
                log.info("主文件【{}】，附件【{}】分发完成", resource.getName(), annex.getName());
            });
            //修改所有正文的taskID
            resource.setWorkTaskId(taskID);
            resourceRepository.updateOne(resource);
            log.info("主文件【{}】分发完成", resource.getName());
        }));

        log.info("分发完成.......");
    }


    @ApiOperation(value = "资源数据分页")
    @GetMapping("/page")
    public R<MyPage<? extends DatasourceResource>> page(@RequestParam Map<String, Object> pageMap) {
        // 获取分页条件
        Integer page = Convert.toInt(pageMap.getOrDefault("page", 1));
        Integer size = Convert.toInt(pageMap.getOrDefault("size", 20));
        String datasourceId = pageMap.get("datasourceId").toString();

        // 分页查询
//        //单例map维护上一次的异步操作是否结束，并且存在未就绪的资源，可以点击自动分流，否则不允许点击分流
//        boolean drainageAble = true;
//        try {
//            datasourceResourceRepository.findOne(Condition.getFilter(Map.of("drainageStage", BaseStatus.UNREADY,
//                    "datasourceId", datasourceId), DatasourceResource.class));
//
//        } catch (Exception e) {
//            drainageAble = false;
//        }
//
//        drainageAble = drainageAble && CacheSingletonMapAsyncListener.getInstance().getCacheData(datasourceId) == null;
//        if (drainageAble) return R.data(datasourceResourceService.page(page, size, pageMap));
//        return R.data(222, ResultCode.SUCCESS.getMessage(), datasourceResourceService.page(page, size, pageMap));
        return R.data(datasourceResourceService.page(page, size, pageMap));
    }

    @ApiOperation(value = "分类资源列表")
    @GetMapping("/get-resource-by-classify")
    public R<List<DatasourceResource>> getResourceByClassify(@RequestParam String datasourceId, @RequestParam String classify) {
        return R.data(datasourceResourceService.getResourceByClassify(datasourceId, classify));
    }

    @MyLog(menu = LogConstant.MENU_DATASOURCE, dataType = LogConstant.DATA_DATASOURCE_RESOURCE, operation = LogConstant.OPERATION_JOIN)
    @ApiOperation(value = "资源加入任务")
    @PostMapping("/join-work")
    public R<Void> joinWork(@RequestBody String ids, @RequestParam String workId) throws IOException {
        ThreadLocalUtil.set("user", request.getHeader("user"));
        ThreadLocalUtil.set("ip", IpUtil.getClientIp(request));
        datasourceResourceService.joinWork(Func.objToStrList(ids), workId);
        return R.success();
    }

    //读取txt文件，获取文件的id
    private List<String> getFileId(String path) throws IOException {
        log.info("============开始加入【{}】中的文件============", path);
        ArrayList<String> lists = new ArrayList<>();
        File file = new File(path);
        if (!file.exists()) {
            log.error("==========未读取到文件【{}】=========", path);
            return lists;
        }
        BufferedReader br = new BufferedReader(new FileReader(file));
        String line;
        while ((line = br.readLine()) != null) {
            try {
//            List<org.irm.lab.repository.entity.Resource> existResource = resourceRepository.findByCondition(Filters.eq("datasourceCatalogId", line));
                String datasourceResourceId = datasourceResourceRepository.findOne(Filters.eq("fileId", line)).getId();
//            String datasourceResourceId = datasourceResourceRepository.findById(line).getId();
                lists.add(datasourceResourceId);
                log.info("{}====待加入id为：{}", line, datasourceResourceId);
            } catch (Exception e) {
                log.error("==========文件【{}】中id【{}】不存在=========", path, line);
            }

        }
        log.info("==================已拿到所有id，开始加入任务================={}", lists);
        return lists;
    }

    @MyLog(menu = LogConstant.MENU_DATASOURCE, dataType = LogConstant.DATA_DATASOURCE_RESOURCE, operation = LogConstant.OPERATION_REMOVE)
    @ApiOperation(value = "数据源资源删除")
    @PostMapping("/remove")
    public R<Void> remove(@RequestBody String ids) throws IOException {
        datasourceResourceService.remove(Func.objToStrList(ids));

        //同步分类下资源脚本
//        syncAllResource();

        //智能问答同步脚本
//        WorkTask workTask = workTaskFeign.info("66178064987f997086dc707a").getData();
//        List<String> resourceIds = resourceService.listByWorkTaskId(workTask.getId())
//                .stream().map(BaseResource::getId).collect(Collectors.toList());
//        resourceAfterProcessService.qaFileUpload(resourceIds);


        //细分任务脚本
//        String path1 = "/home/<USER>/2021-zbh.txt";
//        String path2 = "/home/<USER>/2022-zbh.txt";
//        String path3 = "/home/<USER>/2023-zbh.txt";
//
//        String path4 = "/home/<USER>/2021-dzh.txt";
//        String path5 = "/home/<USER>/2022-dzh.txt";
//        String path6 = "/home/<USER>/2023-dzh.txt";
//
//        String path7 = "/home/<USER>/2021-dsh.txt";
//        String path8 = "/home/<USER>/2022-dsh.txt";
//        String path9 = "/home/<USER>/2023-dsh.txt";
//
//        String path10 = "/home/<USER>/2021-zth.txt";
//        String path11 = "/home/<USER>/2022-zth.txt";
//        String path12 = "/home/<USER>/2023-zth.txt";
//
//        String path13 = "/home/<USER>/2021-dzzxz.txt";
//        String path14 = "/home/<USER>/2022-dzzxz.txt";
//        String path15 = "/home/<USER>/2023-dzzxz.txt";
//
//        ThreadLocalUtil.set("user", request.getHeader("user"));
//        ThreadLocalUtil.set("ip", IpUtil.getClientIp(request));
//        log.info("==========开始同步【总办会】==========");
//        datasourceResourceService.joinWork(getFileId(path1), "6687ce8e2da0f33cf10f2ae8");
//        datasourceResourceService.joinWork(getFileId(path2), "6687ce9c2da0f33cf10f2ae9");
//        datasourceResourceService.joinWork(getFileId(path3), "6687cea72da0f33cf10f2aea");
//        log.info("==========【总办会】同步完成==========");


//        log.info("==========开始同步【董专会】==========");
//        datasourceResourceService.joinWork(getFileId(path4), "6687cec12da0f33cf10f2aeb");
//        datasourceResourceService.joinWork(getFileId(path5), "6687cec92da0f33cf10f2aec");
//        datasourceResourceService.joinWork(getFileId(path6), "6687cedc2da0f33cf10f2aed");
//        log.info("==========【董专会】同步完成==========");


//        log.info("==========开始同步【董事会】==========");
//        datasourceResourceService.joinWork(getFileId(path7), "6687cef82da0f33cf10f2aee");
//        datasourceResourceService.joinWork(getFileId(path8), "6687cf022da0f33cf10f2aef");
//        datasourceResourceService.joinWork(getFileId(path9), "6687cf092da0f33cf10f2af0");
//        log.info("==========【董事会】同步完成==========");


//        log.info("==========开始同步【专题会】==========");
//        datasourceResourceService.joinWork(getFileId(path10), "6687cf202da0f33cf10f2af1");
//        datasourceResourceService.joinWork(getFileId(path11), "6687cf282da0f33cf10f2af2");
//        datasourceResourceService.joinWork(getFileId(path12), "6687cf352da0f33cf10f2af3");
//        log.info("==========【专题会】同步完成==========");

//        log.info("==========开始同步【党组中心组】==========");
//        datasourceResourceService.joinWork(getFileId(path13), "6687cf5c2da0f33cf10f2af4");
//        datasourceResourceService.joinWork(getFileId(path14), "6687cf642da0f33cf10f2af5");
//        datasourceResourceService.joinWork(getFileId(path15), "6687cf6b2da0f33cf10f2af6");
//        log.info("==========【党组中心组】同步完成==========");


        return R.success();
    }

    private void syncAllResource() throws IOException {
        BufferedReader br = new BufferedReader(new FileReader("/home/<USER>/sync.txt"));
        String datasourceId = br.readLine();
        String classify = br.readLine();
        String workId = br.readLine();
        log.info("读到的数据源id为：【{}】", datasourceId);
        log.info("读到的分类为：【{}】", classify);
        log.info("读到的任务id为：【{}】", workId);
        List<String> ids = getResourceByClassify(datasourceId, classify).getData()
                .stream().map(DatasourceResource::getId).collect(Collectors.toList());
        log.info("待加入资源个数为：【{}】", ids.size());

        StringBuilder idsStr = new StringBuilder();
        for (int i = 0; i < ids.size(); i++) {
            idsStr.append(ids.get(i));
            if (i != ids.size() - 1) {
                idsStr.append(",");
            }
        }

        joinWork(JSONUtil.createObj().putOpt("ids", idsStr.toString()).toString(), workId);
    }

    @MyLog(menu = LogConstant.MENU_DATASOURCE, dataType = LogConstant.DATA_DATASOURCE_RESOURCE, operation = LogConstant.OPERATION_DETAILS)
    @ApiOperation(value = "数据源资源详情")
    @GetMapping("/info")
    public R<DatasourceResource> info(@RequestParam String id) {
        return R.data(datasourceResourceService.info(id));
    }

    @MyLog(menu = LogConstant.MENU_DATASOURCE, dataType = LogConstant.DATA_DATASOURCE_METADATA, operation = LogConstant.OPERATION_PREVIEW)
    @ApiOperation(value = "数据源元数据预览")
    @GetMapping("/metadata-preview")
    public R<Map<String, Object>> metadataPreview(@RequestParam String id) {
        Map<String, Object> metadataPreviewMap = datasourceResourceService.metadataPreview(id);
        metadataPreviewMap.remove("附件");
        return R.data(metadataPreviewMap);
    }

    @ApiOperation(value = "获取资源附件信息")
    @GetMapping("/datasource-attach")
    public R<List<DatasourceAttach>> getDatasourceAttachList(@RequestParam String id) {
        return R.data(datasourceResourceService.getDatasourceAttachList(id));
    }

    /**
     * 获取资源预览的地址
     *
     * @param id 甲方维护的资源id
     * @return 资源预览地址
     */
    @ApiOperation(value = "外部导入资源预览")
    @GetMapping("/file-preview")
    public R<String> filePreview(@RequestParam String id) {
        return R.data(peripheralInterfaceService.filePreview(id));
    }

    @ApiOperation(value = "数据库映射", hidden = true)
    @GetMapping("/mapping-field")
    public R<List<ListConstantVO>> mappingField() {
        return R.data(ListConstantWrapper.listVO(DatasourceResourceConstant.class));
    }

    @ApiOperation(value = "模糊查询", hidden = true)
    @GetMapping("/get-filter")
    public R<List<DatasourceResource>> getFilter(@RequestParam Map<String, Object> queryParam) {
        return R.data(datasourceResourceService.getFilter(queryParam));
    }

    @ApiOperation(value = "模糊查询总数", hidden = true)
    @GetMapping("/get-filter-count")
    public R<Long> getFilterCount(@RequestParam Map<String, Object> queryParam) {
        return R.data(datasourceResourceService.getFilterCount(queryParam));
    }

    @ApiOperation(value = "时间区间查询", hidden = true)
    @PostMapping("/list-by-time")
    public R<List<DatasourceResource>> listByCreateTime(@RequestParam Date startTime, @RequestParam Date endTime) {
        return R.data(datasourceResourceService.listByCreateTime(startTime, endTime));
    }

    @ApiOperation(value = "时间区间查询总数", hidden = true)
    @PostMapping("/list-by-time-count")
    public R<Long> listByCreateTimeCount(@RequestParam Date startTime, @RequestParam Date endTime) {
        return R.data(datasourceResourceService.listByCreateTimeCount(startTime, endTime));
    }

    @ApiOperation(value = "文件批量下载", hidden = true)
    @PostMapping("/download-datasource-resource")
    public R<Void> downloadDatasourceResource(@RequestParam String datasourceId) {
        datasourceResourceService.downloadDatasourceResource(datasourceId, request.getHeader("user"));
        return R.success();
    }


}
