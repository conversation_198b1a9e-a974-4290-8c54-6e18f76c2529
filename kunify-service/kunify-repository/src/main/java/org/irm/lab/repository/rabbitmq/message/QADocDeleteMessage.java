package org.irm.lab.repository.rabbitmq.message;

import lombok.Data;
import org.irm.lab.repository.entity.ResourceTemp;

import java.io.Serializable;



@Data
public class QADocDeleteMessage implements Serializable {
    /**
     * 交换机
     */
    public static final String EXCHANGE = "KUNIFY4_EXCHANGE_RESOURCE_QA_DOC_DELETE_";
    /**
     * 消息队列
     */
    public static final String QUEUE = "KUNIFY4_QUEUE_RESOURCE_QA_DOC_DELETE_";
    /**
     * routingKey
     */
    public static final String ROUTING_KEY = "KUNIFY4_ROUTING_RESOURCE_QA_DOC_DELETE_";

    /**
     * 当前用户信息
     */
    private String user;

    private ResourceTemp resourceTemp;

}
