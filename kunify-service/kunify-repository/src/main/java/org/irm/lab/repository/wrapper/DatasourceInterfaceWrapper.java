package org.irm.lab.repository.wrapper;

import cn.hutool.core.bean.BeanUtil;
import org.irm.lab.common.support.BaseEntityWrapper;
import org.irm.lab.common.utils.SpringUtil;
import org.irm.lab.repository.constant.DatasourceConstant;
import org.irm.lab.repository.dto.datasource.DatasourceInterfaceDTO;
import org.irm.lab.repository.entity.datasource.DatasourceInterface;
import org.irm.lab.repository.service.IDatasourceDataStructService;
import org.irm.lab.repository.service.IDatasourceMetaMappingService;

import java.util.List;

public class DatasourceInterfaceWrapper extends BaseEntityWrapper<DatasourceInterface, DatasourceInterfaceDTO> {

    public static DatasourceInterfaceWrapper build() {
        return new DatasourceInterfaceWrapper();
    }

    private static final IDatasourceDataStructService dataStructService = SpringUtil.getBean(IDatasourceDataStructService.class);

    private static final IDatasourceMetaMappingService metaMappingService = SpringUtil.getBean(IDatasourceMetaMappingService.class);

    @Override
    public DatasourceInterfaceDTO entityVO(DatasourceInterface remoteInterface) {
        DatasourceInterfaceDTO remoteInterfaceVO = BeanUtil.copyProperties(remoteInterface, DatasourceInterfaceDTO.class);
        remoteInterfaceVO.setRequestDataStructs(dataStructService.listTree(remoteInterface.getId(), DatasourceConstant.PARAM_REQUEST));
        remoteInterfaceVO.setResponseDataStructs(dataStructService.listTree(remoteInterface.getId(), DatasourceConstant.PARAM_RESPONSE));
        remoteInterfaceVO.setDatasourceMetaMappings(metaMappingService.getListByInterfaceId(remoteInterface.getId()));
        return remoteInterfaceVO;
    }
}
