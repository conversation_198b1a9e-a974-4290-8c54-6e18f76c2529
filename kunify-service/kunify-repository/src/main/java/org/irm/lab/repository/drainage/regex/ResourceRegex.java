package org.irm.lab.repository.drainage.regex;

public class ResourceRegex {

    public static final String[] REGEX_DEFAULT = {""};

    public static final String[] REGEX_MEETING = {".*会议.*", ".*纪要.*", ".*专题.*", ".*董事长专题会.*", ".*党组会.*", ".*总经理办公会.*", ".*专题会.*"};

    public static final String[] REGEX_AWARDS = {".*表彰.*", ".*奖项.*", ".*优秀.*", ".*奖章.*", ".*荣誉.*", ".*奖金.*", ".*先进个人.*", ".*四有共产党员.*"};

    public static final String[] REGEX_SYSTEM = {".*制度.*"};

    public static final String[] REGEX_LEADER = {".*批复.*", ".*批示.*", ".*报告.*", ".*意见.*", ".*通知.*"};
}
