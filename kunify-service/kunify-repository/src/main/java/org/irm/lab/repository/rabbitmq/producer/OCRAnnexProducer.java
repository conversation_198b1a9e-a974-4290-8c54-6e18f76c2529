package org.irm.lab.repository.rabbitmq.producer;

import lombok.RequiredArgsConstructor;
import org.irm.lab.kg.entity.annex.AnnexDocumentImage;
import org.irm.lab.kg.message.OCRAnnexMessage;
import org.irm.lab.repository.entity.ResourceAnnex;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.stereotype.Component;

import java.io.Serializable;
import java.util.HashMap;

/**
     
 * @date 2024/2/27 <br/>
      
 */
@Component
@RequiredArgsConstructor
public class OCRAnnexProducer implements Serializable {
    private final RabbitTemplate rabbitTemplate;

    /**
     * 发送消息
     */
    public void sendMessage(String user, ResourceAnnex resourceAnnex, AnnexDocumentImage annexDocumentImage, Integer page, Boolean isEnd, String ...flag){
        OCRAnnexMessage documentParsingMessage = new OCRAnnexMessage();
        documentParsingMessage.setUser(user);
        documentParsingMessage.setResourceAnnex(resourceAnnex);
        documentParsingMessage.setAnnexDocumentImage(annexDocumentImage);
        documentParsingMessage.setPage(page);
        documentParsingMessage.setIsEnd(isEnd);
        if (flag.length>0) {
            final HashMap<String, Object> objectObjectHashMap = new HashMap<>();

            objectObjectHashMap.put("isES", flag[0]);
            documentParsingMessage.setMap(objectObjectHashMap);
        }
        // 发送消息
        rabbitTemplate.convertAndSend(OCRAnnexMessage.EXCHANGE, OCRAnnexMessage.ROUTING_KEY + "kunify-kg", documentParsingMessage);
    }
}
