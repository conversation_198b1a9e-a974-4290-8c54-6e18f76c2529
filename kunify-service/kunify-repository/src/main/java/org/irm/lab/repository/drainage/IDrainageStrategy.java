package org.irm.lab.repository.drainage;

import lombok.extern.slf4j.Slf4j;
import org.irm.lab.config.entity.Process;
import org.irm.lab.config.entity.WorkTask;
import org.irm.lab.config.feign.ProcessFeign;
import org.irm.lab.config.feign.WorkTaskFeign;
import org.irm.lab.repository.drainage.constant.DrainageType;
import org.irm.lab.repository.entity.datasource.DatasourceResource;
import org.irm.lab.repository.drainage.handler.ResourceRegexHandlerManager;

import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * 引流策略
 */
@Slf4j
public abstract class IDrainageStrategy {

    protected ProcessFeign processFeign;
    protected WorkTaskFeign workTaskFeign;
    protected ResourceRegexHandlerManager resourceRegexHandlerManager;

    /**
     * 获取引流类型
     *
     * @return
     */
    public abstract DrainageType getType();

    /**
     * 获取引流的处理流程
     *
     * @return
     */
    public Set<Process> getRegexProcess() {
        return processFeign.listByProcessNameRegex(getType().getProcessRegexRule()).getData();
    }

    /**
     * 获取引流的任务（根据年度）
     *
     * @return
     */
    public Set<WorkTask> getRegexTask(DatasourceResource dr) {
        return workTaskFeign.findAllByProcessIds(getRegexProcess().stream()
                        .map(Process::getId)
                        .collect(Collectors.toList())).getData().stream()
                .filter(workTask -> workTask.getName().contains(getDrainageYear(dr))).collect(Collectors.toSet());
    }

    /**
     * 获取引流的任务
     *
     * @return
     */
    public Set<WorkTask> getRegexTask() {
        return workTaskFeign.findAllByProcessIds(getRegexProcess().stream()
                .map(Process::getId)
                .collect(Collectors.toList())).getData();
    }

    /**
     * 获取引流的年度
     *
     * @param dr
     * @return
     */
    public String getDrainageYear(DatasourceResource dr) {
        return dr.getFields().get("C_YEAR").toString();
    }

    /**
     * 单文件引流
     *
     * @param dr
     */
    public void singleResourceDrainage(DatasourceResource dr) {

    }


    /**
     * 多文件引流
     *
     * @param drs 需要引流的所有资源
     * @return 已处理资源
     */
    public List<DatasourceResource> multiResourceDrainage(List<DatasourceResource> drs) {
        log.info("正在执行【{}】分流策略...", getType().getDrainageName());
        return resourceRegexHandlerManager.initResourceHandler(drs).
                handle(this, drs).getProcessResource();
    }

}
