package org.irm.lab.repository.controller;

import lombok.RequiredArgsConstructor;
import org.irm.lab.common.api.R;
import org.irm.lab.repository.service.IResourceAfterProcessService;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 * @date 2023/5/25 17:36
 * @description
 */
@RestController
@RequestMapping("/resource-after")
@RequiredArgsConstructor
public class ResourceAfterProcessController {
    private final IResourceAfterProcessService iResourceAfterProcessService;


    /**
     * 同步指定工作任务下的 已经审核通过的资源到QA中
     *
     * @param workTaskId 工作任务Id
     */
    @PostMapping("/qa-file-sync")
    public R<Void> qaFileSync(@RequestParam("workTaskId") String workTaskId){
        iResourceAfterProcessService.qaFileSync(workTaskId);
        return R.success();
    }
}
