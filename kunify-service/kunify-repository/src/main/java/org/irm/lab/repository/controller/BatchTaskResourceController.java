package org.irm.lab.repository.controller;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.irm.lab.common.api.R;
import org.irm.lab.repository.entity.BatchTaskResource;
import org.irm.lab.repository.service.IBatchTaskResourceService;
import org.irm.lab.repository.service.IRecordFileService;
import org.springframework.web.bind.annotation.*;
import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/2/1 17:20
 * @description 批量提交任务控制器
 */
@RestController
@RequestMapping("/task")
@Api(value = "批量提交任务", hidden = true)
@RequiredArgsConstructor
public class BatchTaskResourceController {


    private final IBatchTaskResourceService iBatchTaskResourceService;
    private final IRecordFileService iRecordFileService;

    /**
     * 打包提交 ===> 导出excel模板
     *
     * @param workTaskId 工作任务Id
     * @param response   响应
     */
    @ApiOperation(value = "打包提交")
    @GetMapping("/excel-export")
    public R<Void> exportExcel(@RequestParam("workTaskId") String workTaskId, HttpServletResponse response) {
        try {
            iBatchTaskResourceService.exportExcel(workTaskId, response);
            return R.success();
        } catch (Exception e) {
            e.printStackTrace();
            return R.failed();
        }
    }

    /**
     * 开启批量上传任务
     *
     * @param batchTaskResource {@link BatchTaskResource}
     * @return {@link BatchTaskResource}
     */
    @ApiOperation(value = "开启批量上传任务")
    @PostMapping("/save")
    public R<? extends BatchTaskResource> save(@RequestBody BatchTaskResource batchTaskResource) {
        return R.data(iBatchTaskResourceService.save(batchTaskResource));
    }

    /**
     * 开启文件的批量上传和解析
     *
     * @param taskId 任务ID
     * @return {@link BatchTaskResource}
     */
    @ApiOperation(value = "开启文件的批量上传和解析")
    @GetMapping("/batch")
    public R<BatchTaskResource> batchFileUpload(@RequestParam("taskId") String taskId) {
        // 文件切片上传成功后会调用该方法,此时修改任务状态
        iBatchTaskResourceService.setTask2Success(taskId);
        // 开启解析压缩包文件
        iRecordFileService.parseBatchFile(taskId);
        return R.success();
    }


    /**
     * 查询所有批量提交任务
     *
     * @return {@link BatchTaskResource}
     */
    @ApiOperation(value = "查询所有批量提交任务")
    @GetMapping("/list")
    public R<List<BatchTaskResource>> list() {
        return R.data(iBatchTaskResourceService.list());
    }


}
