package org.irm.lab.repository.controller.datasource;

import cn.hutool.core.convert.Convert;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.irm.lab.common.annotation.MyLog;
import org.irm.lab.common.api.R;
import org.irm.lab.common.constant.LogConstant;
import org.irm.lab.common.support.MyPage;
import org.irm.lab.common.utils.Func;
import org.irm.lab.repository.entity.datasource.DataSource;
import org.irm.lab.repository.service.IDataSourceService;
import org.irm.lab.repository.vo.DataSourceVO;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2023-02-17
 */

@RestController
@RequestMapping("/datasource")
@Api(value = "数据源")
public class DataSourceManagerController {

    @Resource
    private IDataSourceService dataSourceService;

    @ApiOperation("数据源分页")
    @GetMapping("/page")
    public R<MyPage<DataSourceVO>> page(@RequestParam(required = false) Map<String, Object> queryParam) {
        // 获取分页条件
        Integer page = Convert.toInt(queryParam.getOrDefault("page", 1));
        Integer size = Convert.toInt(queryParam.getOrDefault("size", 20));
        // 分页查询
        return R.data(dataSourceService.page(queryParam, page, size));
    }

    @ApiOperation("数据源详情")
    @GetMapping("/info")
    public R<DataSource> info(@RequestParam String id) {
        return R.data(dataSourceService.info(id));
    }

    @MyLog(menu = LogConstant.MENU_DATASOURCE, dataType = LogConstant.DATA_DATASOURCE, operation = LogConstant.OPERATION_ADD)
    @ApiOperation("数据源增改")
    @PostMapping("/save")
    public R<DataSource> save(@RequestBody DataSource dataSource) {
        return R.data(dataSourceService.save(dataSource));
    }

    @ApiOperation(value = "开启/关闭数据源")
    @GetMapping("/status")
    public R<DataSource> changeStatus(@RequestParam String dataSourceId) {
        return R.data(dataSourceService.changeStart(dataSourceId));
    }

    @MyLog(menu = LogConstant.MENU_DATASOURCE, dataType = LogConstant.DATA_DATASOURCE, operation = LogConstant.OPERATION_REMOVE)
    @ApiOperation("数据源删除")
    @PostMapping("/remove")
    public R<Void> remove(@RequestBody String ids) {
        dataSourceService.remove(Func.objToStrList(ids));
        return R.success();
    }
}
