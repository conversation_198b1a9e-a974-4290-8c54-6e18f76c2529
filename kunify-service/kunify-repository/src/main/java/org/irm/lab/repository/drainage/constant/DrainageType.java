package org.irm.lab.repository.drainage.constant;

import lombok.Getter;
import org.irm.lab.config.constant.ProcessConstant;
import org.irm.lab.repository.drainage.regex.ResourceRegex;

import java.util.Arrays;
import java.util.List;

/**
 * 引流枚举类
 */
@Getter
public enum DrainageType {
    DEFAULT_DRAINAGE("默认文件", Arrays.asList(ResourceRegex.REGEX_DEFAULT),
            "default", 0, Arrays.asList(ProcessConstant.DRAINAGE_DEFAULT)),

    MEETING_DRAINAGE("会议文件", Arrays.asList(ResourceRegex.REGEX_MEETING),
            "meeting", 4, Arrays.asList(ProcessConstant.DRAINAGE_MEETING)),

    AWARDS_DRAINAGE("表彰文件", Arrays.asList(ResourceRegex.REGEX_AWARDS),
            "awards", 3, Arrays.asList(ProcessConstant.DRAINAGE_AWARDS)),

    //制度文件不参与分流，由另一个制度接口导入数据
    SYSTEM_DRAINAGE("制度文件", Arrays.asList(ResourceRegex.REGEX_SYSTEM),
            "system", 2, Arrays.asList(ProcessConstant.DRAINAGE_SYSTEM)),

    LEADER_DRAINAGE("领导批示文件", Arrays.asList(ResourceRegex.REGEX_LEADER),
            "leader", 1, Arrays.asList(ProcessConstant.DRAINAGE_LEADER));

    //暂时执行一段脚本(从通用任务中，筛选文件分发到其他任务，code设置为任务ID)
//    DEFAULT_DRAINAGE("默认文件", Arrays.asList(ResourceRegex.REGEX_DEFAULT),
//            "65f2b9ea7d6fdb753b75242b", 0, Arrays.asList(ProcessConstant.DRAINAGE_DEFAULT)),
//
//    MEETING_DRAINAGE("会议文件", Arrays.asList(ResourceRegex.REGEX_MEETING),
//            "65f7dbfc7d6fdb753b75242c", 4, Arrays.asList(ProcessConstant.DRAINAGE_MEETING)),
//
//    AWARDS_DRAINAGE("表彰文件", Arrays.asList(ResourceRegex.REGEX_AWARDS),
//            "65f29ce87d6fdb753b752428", 3, Arrays.asList(ProcessConstant.DRAINAGE_AWARDS)),
//
//    SYSTEM_DRAINAGE("制度文件", Arrays.asList(ResourceRegex.REGEX_SYSTEM),
//            "65f29cf67d6fdb753b752429", 2, Arrays.asList(ProcessConstant.DRAINAGE_SYSTEM)),
//
//    LEADER_DRAINAGE("领导批示文件", Arrays.asList(ResourceRegex.REGEX_LEADER),
//            "65f29d037d6fdb753b75242a", 1, Arrays.asList(ProcessConstant.DRAINAGE_LEADER));

    //分流名称
    private final String drainageName;

    //分流编码
    private final String code;

    //权重
    private final Integer score;

    //处理流程的匹配策略
    private final List<String> processRegexRule;

    //文件资源的匹配策略
    private final List<String> resourceRegexRule;


    DrainageType(String drainageName, List<String> resourceRegexRule, String code, int score, List<String> processRegexRule) {
        this.drainageName = drainageName;
        this.code = code;
        this.score = score;
        this.processRegexRule = processRegexRule;
        this.resourceRegexRule = resourceRegexRule;
    }
}
