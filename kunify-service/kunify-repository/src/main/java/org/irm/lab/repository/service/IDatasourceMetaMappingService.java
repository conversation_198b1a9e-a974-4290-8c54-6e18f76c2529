package org.irm.lab.repository.service;

import org.irm.lab.repository.entity.datasource.DatasourceMetaMapping;

import java.util.List;

public interface IDatasourceMetaMappingService {
    DatasourceMetaMapping save(DatasourceMetaMapping datasourceMetaMapping);

    DatasourceMetaMapping info(String id);

    void remove(List<String> ids);

    void saveAll(List<DatasourceMetaMapping> datasourceMetaMappings);

    List<DatasourceMetaMapping> getListByInterfaceId(String interfaceId);
}
