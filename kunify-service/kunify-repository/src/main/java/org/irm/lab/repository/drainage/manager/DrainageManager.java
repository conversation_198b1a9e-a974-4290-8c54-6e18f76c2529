package org.irm.lab.repository.drainage.manager;

import cn.hutool.extra.spring.SpringUtil;
import lombok.extern.slf4j.Slf4j;
import org.irm.lab.common.pojo.BaseStatus;
import org.irm.lab.repository.controller.datasource.CacheSingletonMapAsyncListener;
import org.irm.lab.repository.drainage.IDrainageStrategy;
import org.irm.lab.repository.entity.datasource.DatasourceResource;
import org.irm.lab.repository.repository.DatasourceResourceRepository;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;
import java.util.concurrent.atomic.AtomicReference;


//用于将所有的分流策略，根据权重依次执行
@Component
@Slf4j
public class DrainageManager {

    /**
     * @param resources
     * @return
     */
    @Async
    public void handleByDrainageScore(List<DatasourceResource> resources, String datasourceId) {
        //动态resources，一个资源被分配走之后，就从集合列表中删除
        AtomicReference<List<DatasourceResource>> dynamicResources = new AtomicReference<>(resources);
        SpringUtil.getBeansOfType(IDrainageStrategy.class).values()
                .stream()
                .sorted(((o1, o2) -> o2.getType().getScore().compareTo(o1.getType().getScore())))
                .forEach(drainageStrategy -> {
                    //每个策略执行完后，返回该策略下已处理资源，更新动态资源
                    resources.removeAll(drainageStrategy.multiResourceDrainage(dynamicResources.get()));
                    dynamicResources.set(resources);
                    log.info("【{}】策略下资源分发结束，还剩余【{}】资源待分发", drainageStrategy.getType().getDrainageName(), resources.size());
                });
        log.info("所有策略下资源全部分发结束...");
        //设置资源状态，并解除封禁
        resources.forEach(datasourceResource -> {
            datasourceResource.setDrainageStage(BaseStatus.FALSE);
        });
        CacheSingletonMapAsyncListener.getInstance().removeCacheData(datasourceId);
    }

}
