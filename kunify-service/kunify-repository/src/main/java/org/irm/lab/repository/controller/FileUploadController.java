package org.irm.lab.repository.controller;

import cn.hutool.core.thread.ThreadUtil;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.ObjectUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import lombok.SneakyThrows;
import lombok.Synchronized;
import lombok.extern.slf4j.Slf4j;
import org.irm.lab.common.annotation.MyLog;
import org.irm.lab.common.api.R;
import org.irm.lab.common.constant.ExceptionMessageConst;
import org.irm.lab.common.constant.LogConstant;
import org.irm.lab.common.exception.ServiceException;
import org.irm.lab.common.utils.RedisUtil;
import org.irm.lab.common.utils.ResourceType;
import org.irm.lab.common.utils.ThreadLocalUtil;
import org.irm.lab.common.utils.ZipFileUtil;
import org.irm.lab.config.entity.Process;
import org.irm.lab.config.entity.WorkTask;
import org.irm.lab.config.feign.WorkTaskFeign;
import org.irm.lab.repository.config.TempPathConfig;
import org.irm.lab.repository.constant.FileTypeConstant;
import org.irm.lab.repository.constant.RecordResourceType;
import org.irm.lab.repository.constant.RedisConstant;
import org.irm.lab.repository.constant.ResourceConstant;
import org.irm.lab.repository.entity.Resource;
import org.irm.lab.repository.entity.ResourceAnnex;
import org.irm.lab.repository.service.*;
import org.irm.lab.resource.entity.Attach;
import org.jetbrains.annotations.NotNull;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletRequest;
import java.io.File;
import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.concurrent.Executors;
import java.util.concurrent.LinkedBlockingDeque;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicInteger;


/**
 * <AUTHOR>
 * @date 2023/2/1 13:44
 * @description 文件分布提交控制器
 */
@RestController
@RequestMapping("/upload")
@Slf4j
@Api(value = "文件上传")
@RequiredArgsConstructor
public class FileUploadController {

    private final IFileUploadService iFileUploadService;
    private final IResourceAnnexService iResourceAnnexService;
    private final IScalePicService iScalePicService;
    private final IAsyncService iAsyncService;
    private final HttpServletRequest request;
    private final RedisUtil redisUtil;
    private final WorkTaskFeign workTaskFeign;
    private final IAutoRecordMetadataService iAutoRecordMetadataService;
    private final ILinkVerificationService iLinkVerificationService;
    private final IResourceService iResourceService;

    /**
     * 分布提交 ==> 单文件上传
     *
     * @param file       文件
     * @param workTaskId 工作任务ID
     * @param resourceId 资源Id
     * @return {@link Attach}
     */
    @MyLog(menu = LogConstant.MENU_RESOURCE, dataType = LogConstant.DATA_WORK_RESOURCE, operation = LogConstant.OPERATION_NATIVE)
    @ApiOperation(value = "分布提交")
    @PostMapping("/single-file")
    public R<Resource> singleFile(@RequestParam("file") MultipartFile file, String workTaskId, @RequestParam(required = false) String resourceId) {
        Attach attach;
        String metadataSchemaId;
        try {
            // 上传pdf文件时，设置文件类型（保证能够在线预览）
            if (ResourceType.PDF.equals(ResourceType.autoJudge(file.getOriginalFilename()))) {
                attach = iFileUploadService.uploadSinglePdf(file);
            } else {
                // 上传其他文件时，直接上传到Minio中
                attach = iFileUploadService.uploadSingleFile(file, file.getOriginalFilename());
            }
        } catch (Exception e) {
            e.printStackTrace();
            throw new ServiceException("文件服务器过载，请重新上传!");
        }
        if (attach == null) return R.failed("文件服务器过载，请重新上传!");
        // 远程调用获取工作任务对象
        R<WorkTask> workTaskR = workTaskFeign.info(workTaskId);
        // 获取工作任务对象
        WorkTask workTask = workTaskR.getData();
        if (!workTaskR.isSuccess() || workTask == null) {
            return R.failed("当前工作任务不存在!");
        }
        Process process = workTask.getProcess();
        metadataSchemaId = process.getMetadataSchemaId();
        // 生成资源对象
        Resource resource = generateResource(workTaskId, resourceId, attach, metadataSchemaId);
        // 判断该工作任务是否设置了元数据项算法自动著录
        if (iLinkVerificationService.isMetadataAutoFill(workTask)) {
            // 设置请求头，防止异步任务的租户问题
            ThreadLocalUtil.set("user", request.getHeader("user"));
            // TODO 模拟开启算法自动识别，对元数据项著录，著录结束后，状态为“待人工著录”
            iAutoRecordMetadataService.algorithmRecordMetadata(workTaskId, resource);
        } else {
            log.info("该资源所在工作任务暂未设置元数据算法自动填充!!!");
        }
        // 对文件类型进行判断，如果是word需要转换为pdf，图片或pdf无需转换
        if (ResourceType.WORD.equals(ResourceType.autoJudge(file.getOriginalFilename()))) {
            iScalePicService.wordToPdf(resource, attach);
            return R.data(iResourceService.info(resource.getId()));
        } else if (ResourceType.PDF.equals(ResourceType.autoJudge(file.getOriginalFilename()))) {
            // 如果是pdf资源，则直接设置资源的pdfAttachName和pdfAttachId
            resource.setPdfAttachId(attach.getId());
            resource.setPdfAttachName(attach.getOssObjectName());
            return R.data(iResourceService.save(resource));
        }
        // 保存文件资源数据
        return R.data(resource);
    }

    @GetMapping("/script")
    public R<Void> uploadScriptController(@RequestParam String filePath) {
        log.info("当前文件路径为：{}", filePath);
        File file = new File(filePath);

        ThreadPoolExecutor threadPool = new ThreadPoolExecutor(
                3,                      //核心线程池大小
                5,                      //最大线程池大小
                2,                      //超时了没有人调用，就会释放
                TimeUnit.SECONDS,                       //超时单位
                new LinkedBlockingDeque<>(3),           //阻塞队列
                Executors.defaultThreadFactory(),       //线程工厂：用来创建线程，默认即可
                new ThreadPoolExecutor.AbortPolicy()    //拒绝策略
        );

        AtomicInteger atomicInteger = new AtomicInteger(10000);

        for (int i = 0; i < 3; i++) {
            ThreadLocalUtil.set("user", request.getHeader("user"));
            threadPool.execute(() -> {
                while (atomicInteger.decrementAndGet() > 0) {
                    log.info("线程{}====开始执行上传文件，还剩{}份文件待上传", Thread.currentThread().getName(), atomicInteger.get());
                    MultipartFile multipartFile = ZipFileUtil.fileToMultipartFile(file);
                    singleFile(multipartFile, "66442b6936126f031ca916f1", null);
                }
            });
        }

        threadPool.shutdown();
        return R.success();
    }

    /**
     * 生成资源对象
     *
     * @param workTaskId       工作任务Id
     * @param resourceId       资源Id
     * @param attach           附件
     * @param metadataSchemaId 元数据方案Id
     * @return {@link Resource}
     */
    @NotNull
    private Resource generateResource(String workTaskId, String resourceId, Attach attach, String metadataSchemaId) {
        Resource resource;
        // 判断resourceId是否存在，并根据他查询出资源对象
        resource = iResourceService.info(resourceId);
        if (ObjectUtil.isEmpty(resource)) {
            resource = new Resource();
        }
        // 设置资源的相关属性
        resource.setName(attach.getFileOriginalName());
        resource.setWorkTaskId(workTaskId);
        resource.setPrimaryFileId(attach.getId());
        resource.setPrimaryFileName(attach.getOssObjectName());
        resource.setMediaType(attach.getFileExtension());
        // 主文件
        resource.setFileType(FileTypeConstant.MAIN_FILE);
        resource.setMetadataSchemaId(metadataSchemaId);
        // 保存文件资源,此时资源状态为 “待著录”
        resource.setStage(RecordResourceType.STAGE_1);
        resource = iResourceService.save(resource);
        // 初始化元数据项
        iResourceService.initMetadataItem(resource);
        // 把资源对象保存到Redis中，状态改为“待著录_识别中”
        resource.setStage(RecordResourceType.STAGE_2);
        // 对于分步提交的任务，存储到Redis中，后续删除任务进度展示时，要从Redis中删除
        redisUtil.hmset(RedisConstant.SINGLE_FILE_UPLOAD_PREFIX + workTaskId, Map.of(resource.getId(), resource));
        return resource;
    }


    /**
     * 目录提交
     *
     * @param file 文件对象
     * @return {@link R}
     */
    @MyLog(menu = LogConstant.MENU_RESOURCE, dataType = LogConstant.DATA_RESOURCE_CATALOGUE, operation = LogConstant.OPERATION_NATIVE)
    @ApiOperation(value = "目录提交")
    @SneakyThrows
    @PostMapping("/catalog")
    public R<String> catalog(@RequestParam(required = false, name = "file") MultipartFile file, @RequestParam("workTaskId") String workTaskId) {
        // 把excel文件存储到本地临时目录
        final HashSet<String> fileType = new HashSet<>();
        fileType.add("pdf");
        fileType.add("doc");
        fileType.add("docx");
        String catalogPath = TempPathConfig.currentBatchPath();
        // 获取原文件名称
        String fileName = file.getOriginalFilename();
        if (fileName != null && fileType.contains(removeExtension(fileName))) {
            throw new ServiceException("文件格式错误");
        }
        // 生成新的文件名
        String newFileName = IdUtil.simpleUUID() + fileName.substring(fileName.indexOf("."));
        File saveFile = new File(catalogPath + newFileName);
        if (!saveFile.getParentFile().exists()) {
            boolean result = saveFile.getParentFile().mkdirs();
            if (!result) throw new ServiceException("目录创建失败!");
        }
        // 存储到本地临时目录
        file.transferTo(saveFile);
        // 异步著录
        // 设置请求头，防止异步任务的租户问题
        ThreadLocalUtil.set("user", request.getHeader("user"));
        iAsyncService.parseCatalog(saveFile, workTaskId);
        return R.success();
    }

    public static String removeExtension(String fullPath) {
        int dotIndex = fullPath.lastIndexOf('.');
        if (dotIndex == -1) {
            return fullPath; // 没有后缀
        }
        int dirSeparatorIndex = fullPath.lastIndexOf(File.separator);
        if (dirSeparatorIndex > dotIndex) {
            return fullPath; // 最后一个点在文件夹名中
        }
        return fullPath.substring(0, dotIndex);
    }


    /**
     * 附件上传，允许空附件
     *
     * @param file       文件对象
     * @param resourceId 主文件的资源Id
     * @param annexName  空附件名称
     * @return {@link R}
     */
    @MyLog(menu = LogConstant.MENU_RESOURCE, dataType = LogConstant.DATA_RESOURCE_ANNEX_FILE, operation = LogConstant.OPERATION_NATIVE)
    @ApiOperation(value = "附件上传")
    @PostMapping("/file-annex")
    public R<List<ResourceAnnex>> fileAttach(
            @RequestParam(required = false, name = "file") MultipartFile[] file
            , @RequestParam("resourceId") String resourceId
            , @RequestParam(required = false) List<String> annexName) {
        Resource resource = iResourceService.info(resourceId);
        //若当前资源是外部导入资源，则不能执行当前操作
        if (ResourceConstant.EXTERNAL_IMPORTS.equals(resource.getImportMethod()))
            throw new ServiceException(ExceptionMessageConst.EXTERNAL_IMPORTS_ATTACHMENT_NOT_UPDATE);
        // 创建附件列表
        List<ResourceAnnex> resourceAnnexList = new ArrayList<>();
        // 如果存在attachName就是空附件
        if (ObjectUtil.isNotEmpty(annexName)) {
            // 主文件与空附件进行绑定
            for (String noneAnnexName : annexName) {
                ResourceAnnex resourceAttach = annexBind(resourceId, null, noneAnnexName, resource.getWorkTaskId());
                resourceAnnexList.add(resourceAttach);
            }
            // 最后返回所有空附件的资源附件对象
            return R.data(resourceAnnexList);
        }
        for (MultipartFile multipartfile : file) {
            // 不是空附件，就上传文件
            Attach attach = iFileUploadService.uploadSingleFile(multipartfile, multipartfile.getOriginalFilename());
            // 附件绑定
            ResourceAnnex resourceAttach = annexBind(resourceId, attach, null, resource.getWorkTaskId());
            // 添加附件到集合中
            resourceAnnexList.add(resourceAttach);
        }
        // 返回附件名称
        return R.data(resourceAnnexList);
    }


    /**
     * 主文件与真实附件绑定
     *
     * @param resourceId 主文件资源Id
     * @param attach     附件文件
     * @param workTaskId 工作任务Id
     */
    @ApiOperation(value = "主文件与真实附件绑定")
    @Synchronized
    private ResourceAnnex annexBind(String resourceId, Attach attach, String attachName, String workTaskId) {
        // 创建资源附件对象
        ResourceAnnex resourceAnnex = new ResourceAnnex();
        resourceAnnex.setResourceId(resourceId);
        resourceAnnex.setWorkTaskId(workTaskId);
        // 设置附件扩展名
        resourceAnnex.setMediaType(attach.getFileExtension());
        // 判断是否为空附件
        if (ObjectUtil.isEmpty(attach)) {
            resourceAnnex.setName(attachName);
            resourceAnnex.setFileType(FileTypeConstant.NONE_ANNEX);
        } else {
            resourceAnnex.setName(attach.getFileOriginalName());
            resourceAnnex.setPrimaryFileId(attach.getId());
            resourceAnnex.setPrimaryFileName(attach.getOssObjectName());
            resourceAnnex.setFileSize(attach.getFileSize().toString());
            resourceAnnex.setFileType(FileTypeConstant.ANNEX_FILE);
            // 获取上传后的文件名称
            String ossObjectName = attach.getOssObjectName();
            // 如果附件本身是pdf,则直接设置相关字段
            if (ResourceType.PDF.equals(ResourceType.autoJudge(attach.getFileOriginalName()))) {
                resourceAnnex.setPdfAttachId(attach.getId());
                resourceAnnex.setPdfAttachName(ossObjectName);
                // 生成资源附件对象
                resourceAnnex = iResourceAnnexService.save(resourceAnnex);
            } else if (ResourceType.WORD.equals(ResourceType.autoJudge(attach.getFileOriginalName()))) {
                // doc、docx，则需要转化为pdf
                iScalePicService.annexWordToPdf(resourceAnnex, attach);
            } else {
                // 如果附件既不是 word 又不是 pdf 则直接保存
                resourceAnnex = iResourceAnnexService.save(resourceAnnex);
            }
        }
        log.info("成功绑定附件 {}", resourceAnnex.getName() != null ? resourceAnnex.getName() : attach.getFileOriginalName());
        // 返回资源附件对象
        return resourceAnnex;
    }


}
