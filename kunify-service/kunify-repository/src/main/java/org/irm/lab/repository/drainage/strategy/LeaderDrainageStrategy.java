//package org.irm.lab.repository.drainage.strategy;
//
//
//import lombok.extern.slf4j.Slf4j;
//import org.irm.lab.config.feign.ProcessFeign;
//import org.irm.lab.config.feign.WorkTaskFeign;
//import org.irm.lab.repository.drainage.IDrainageStrategy;
//import org.irm.lab.repository.drainage.constant.DrainageType;
//import org.irm.lab.repository.drainage.handler.ResourceRegexHandlerManager;
//import org.springframework.beans.factory.InitializingBean;
//import org.springframework.stereotype.Component;
//
//import javax.annotation.Resource;
//
//@Component
//@Slf4j
//public class LeaderDrainageStrategy extends IDrainageStrategy implements InitializingBean {
//    @Resource
//    private ProcessFeign processFeign;
//    @Resource
//    private WorkTaskFeign workTaskFeign;
//    @Resource
//    private ResourceRegexHandlerManager resourceRegexHandlerManager;
//
//    @Override
//    public DrainageType getType() {
//        return DrainageType.LEADER_DRAINAGE;
//    }
//
//
//    @Override
//    public void afterPropertiesSet() throws Exception {
//        super.processFeign = processFeign;
//        super.workTaskFeign = workTaskFeign;
//        super.resourceRegexHandlerManager = resourceRegexHandlerManager;
//    }
//}
