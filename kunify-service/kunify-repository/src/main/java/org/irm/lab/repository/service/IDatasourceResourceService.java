package org.irm.lab.repository.service;

import org.irm.lab.common.support.MyPage;
import org.irm.lab.repository.entity.Resource;
import org.irm.lab.repository.entity.datasource.DatasourceAttach;
import org.irm.lab.repository.entity.datasource.DatasourceResource;
import org.irm.lab.resource.entity.Attach;

import java.io.File;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2023-02-22
 */

public interface IDatasourceResourceService {
    DatasourceResource info(String id);

    void syncResource(String datasourceId, String user);

    List<DatasourceResource> getResourceByClassify(String datasourceId, String classify);

    MyPage<? extends DatasourceResource> page(Integer page, Integer size, Map<String, Object> map);

    void remove(List<String> ids);

    void joinWork(List<String> ids, String workId);

    void gzzdJoinWork(List<String> ids, String workId);

    void resourceTrans(Resource resource, Attach attach);

    List<DatasourceResource> getResourceByDatasourceId(String datasourceId);

    List<DatasourceResource> getFilter(Map<String, Object> queryParam);

    Long getFilterCount(Map<String, Object> queryParam);

    List<DatasourceResource> listByCreateTime(Date startTime, Date endTime);

    Long listByCreateTimeCount(Date startTime, Date endTime);

    Map<String, Object> metadataPreview(String id);

    Map<String, Object> metadataPreview(String classify, Map<String, Object> fields);

    List<DatasourceAttach> getDatasourceAttachList(String id);

    String filePreview(String id);

    void drainage(String datasourceId, String year);

    void downloadDatasourceResource(String datasourceId, String user);

    File downloadFile(String url, String filePath, String fileName, String method);

    List<String> saveAll(List<DatasourceResource> documents);

    String save(DatasourceResource documents);
}
