package org.irm.lab.repository.rabbitmq.producer;

import org.irm.lab.kg.message.EsSyncMessage;
import org.irm.lab.repository.entity.ResourceAnnex;
import org.irm.lab.repository.rabbitmq.message.DatasourceResourceSyncMessage;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;



//TODO 数据同步暂时使用redis+cron的方式解决，有时间在改成rabbit
/**
 * 同步数据页码
 */
@Component
public class DatasourceResourceSyncProducer {

    @Resource
    private RabbitTemplate rabbitTemplate;


    /**
     * 发送消息
     */
    public void sendMessage(DatasourceResourceSyncMessage message) {
        // 发送消息
        rabbitTemplate.convertAndSend(DatasourceResourceSyncMessage.EXCHANGE,
                DatasourceResourceSyncMessage.ROUTING_KEY,
                message);
    }

}
