package org.irm.lab.repository.drainage.handler;


import org.irm.lab.repository.entity.datasource.DatasourceResource;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;

@Component
public class ResourceRegexHandlerManager {
    @Resource
    private FirstTaskExistRegexHandler firstTaskExistRegexHandler;
    @Resource
    private SecondResourceMatchRegexHandler secondResourceMatchRegexHandler;
    @Resource
    private ThirdResource2TaskRegexHandler thirdResource2TaskRegexHandler;


    public ResourceRegexHandler initResourceHandler(List<DatasourceResource> resources) {
        firstTaskExistRegexHandler.setProcessResource(resources);
        firstTaskExistRegexHandler.setNext(secondResourceMatchRegexHandler);
        secondResourceMatchRegexHandler.setNext(thirdResource2TaskRegexHandler);
        return firstTaskExistRegexHandler;
    }
}
