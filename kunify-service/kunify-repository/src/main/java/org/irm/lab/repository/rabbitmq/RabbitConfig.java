package org.irm.lab.repository.rabbitmq;

import org.irm.lab.repository.rabbitmq.message.DatasourceResourceSyncMessage;
import org.irm.lab.repository.rabbitmq.message.QADocDeleteMessage;
import org.irm.lab.repository.rabbitmq.message.ResourceRepeatDeleteMessage;
import org.irm.lab.repository.rabbitmq.message.ResourceVerificationMessage;
import org.springframework.amqp.core.Binding;
import org.springframework.amqp.core.BindingBuilder;
import org.springframework.amqp.core.DirectExchange;
import org.springframework.amqp.core.Queue;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * <AUTHOR>
 * @date 2023/1/30 14:47
 * @description RabbitMq配置类
 */
@Configuration
public class RabbitConfig {

    /**
     * 数据校验消息队列配置
     */
    public static class ResourceVerificationConfiguration {
        /**
         * 获取RoutingKey尾缀
         */
        @Value("${spring.application.name}")
        private String KEY_SELF;

        /**
         * 创建 Queue
         */
        @Bean
        public Queue directQueue() {
            return new Queue(ResourceVerificationMessage.QUEUE + KEY_SELF, // Queue 名字
                    true, // durable: 是否持久化
                    false, // exclusive: 是否排它
                    false); // autoDelete: 是否自动删除
        }

        /**
         * 创建 Direct Exchange
         */
        @Bean
        public DirectExchange directExchange() {
            return new DirectExchange(ResourceVerificationMessage.EXCHANGE,
                    true,  // durable: 是否持久化
                    false);  // exclusive: 是否排它
        }

        /**
         * 创建 Binding
         * Exchange：Demo01Message.EXCHANGE
         * Routing key：Demo01Message.ROUTING_KEY
         * Queue：Demo01Message.QUEUE
         */
        @Bean
        public Binding directBindingQueue() {
            return BindingBuilder.bind(directQueue()).to(directExchange()).with(ResourceVerificationMessage.ROUTING_KEY + KEY_SELF);
        }
    }


    /**
     * 数据源数据同步消息队列配置
     */
    public static class DatasourceSyncConfiguration {
        /**
         * 获取RoutingKey尾缀
         */
        @Value("${spring.application.name}")
        private String KEY_SELF;

        /**
         * 创建 Queue
         */
        @Bean
        public Queue directSyncQueue() {
            return new Queue(DatasourceResourceSyncMessage.QUEUE + KEY_SELF, // Queue 名字
                    true, // durable: 是否持久化
                    false, // exclusive: 是否排它
                    false); // autoDelete: 是否自动删除
        }

        /**
         * 创建 Direct Exchange
         */
        @Bean
        public DirectExchange directSyncExchange() {
            return new DirectExchange(DatasourceResourceSyncMessage.EXCHANGE,
                    true,  // durable: 是否持久化
                    false);  // exclusive: 是否排它
        }

        /**
         * 创建 Binding
         * Exchange：Demo01Message.EXCHANGE
         * Routing key：Demo01Message.ROUTING_KEY
         * Queue：Demo01Message.QUEUE
         */
        @Bean
        public Binding directSyncBindingQueue() {
            return BindingBuilder.bind(directSyncQueue()).to(directSyncExchange()).with(DatasourceResourceSyncMessage.ROUTING_KEY);
        }
    }


    /**
     * 重复文件脚本文件删除消息队列配置
     */
    public static class ResourceRepeatDeleteConfiguration {
        /**
         * 获取RoutingKey尾缀
         */
        @Value("${spring.application.name}")
        private String KEY_SELF;

        /**
         * 创建 Queue
         */
        @Bean
        public Queue directResourceRepeatDeleteQueue() {
            return new Queue(ResourceRepeatDeleteMessage.QUEUE + KEY_SELF, // Queue 名字
                    true, // durable: 是否持久化
                    false, // exclusive: 是否排它
                    false); // autoDelete: 是否自动删除
        }

        /**
         * 创建 Direct Exchange
         */
        @Bean
        public DirectExchange directResourceRepeatDeleteExchange() {
            return new DirectExchange(ResourceRepeatDeleteMessage.EXCHANGE,
                    true,  // durable: 是否持久化
                    false);  // exclusive: 是否排它
        }

        /**
         * 创建 Binding
         * Exchange：Demo01Message.EXCHANGE
         * Routing key：Demo01Message.ROUTING_KEY
         * Queue：Demo01Message.QUEUE
         */
        @Bean
        public Binding directResourceRepeatDeleteBindingQueue() {
            return BindingBuilder.bind(directResourceRepeatDeleteQueue()).to(directResourceRepeatDeleteExchange()).with(ResourceRepeatDeleteMessage.ROUTING_KEY + KEY_SELF);
        }
    }


    /**
     * 删除AI中台中doc队列配置
     */
    public static class QADocDeleteConfiguration {
        /**
         * 获取RoutingKey尾缀
         */
        @Value("${spring.application.name}")
        private String KEY_SELF;

        /**
         * 创建 Queue
         */
        @Bean
        public Queue directQADocDeleteQueue() {
            return new Queue(QADocDeleteMessage.QUEUE + KEY_SELF, // Queue 名字
                    true, // durable: 是否持久化
                    false, // exclusive: 是否排它
                    false); // autoDelete: 是否自动删除
        }

        /**
         * 创建 Direct Exchange
         */
        @Bean
        public DirectExchange directQADocDeleteExchange() {
            return new DirectExchange(QADocDeleteMessage.EXCHANGE,
                    true,  // durable: 是否持久化
                    false);  // exclusive: 是否排它
        }

        /**
         * 创建 Binding
         * Exchange：Demo01Message.EXCHANGE
         * Routing key：Demo01Message.ROUTING_KEY
         * Queue：Demo01Message.QUEUE
         */
        @Bean
        public Binding directQADocDeleteBindingQueue() {
            return BindingBuilder.bind(directQADocDeleteQueue()).to(directQADocDeleteExchange()).with(QADocDeleteMessage.ROUTING_KEY + KEY_SELF);
        }
    }

}
