package org.irm.lab.repository.controller;

import cn.hutool.json.JSONObject;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.irm.lab.common.api.R;
import org.irm.lab.common.support.MyPage;
import org.irm.lab.common.utils.Func;
import org.irm.lab.config.entity.MetadataItem;
import org.irm.lab.repository.dto.query.ResourceQueryDTO;
import org.irm.lab.repository.entity.Resource;
import org.irm.lab.repository.service.IResourceManagerService;
import org.springframework.web.bind.annotation.*;

import java.util.List;


/**
 * <AUTHOR>
 * @date 2023-02-11
 */

@RestController
@RequestMapping("/resource")
@RequiredArgsConstructor
@Api("资源管理")
public class ResourceManagerController {

    private final IResourceManagerService resourceManagerService;

    /**
     * 根据元数据方案Id，获取元数据方案下的，用于查询的元数据项
     *
     * @param schemaIds 元数据方案Id
     * @return {@link MetadataItem}
     */
    @ApiOperation(value = "动态查询条件")
    @PostMapping("/dynamic-meta")
    public R<List<MetadataItem>> dynamicMeta(@RequestBody String schemaIds) {
        return R.data(resourceManagerService.dynamicMetaBySchema(Func.objToStrList(schemaIds)));
    }

    /**
     * 根据元数据方案id，和枚举或类目集 的值域并集
     *
     * @return {@link JSONObject}
     */
    @ApiOperation(value = "获取值域并集")
    @PostMapping("/meta-union-value")
    public R<JSONObject> metaUnionValue(@RequestParam("metadataId") String metadataId, @RequestBody String metadataSchemaId) {
        return R.data(resourceManagerService.getMetaUnionValueBySchema(metadataSchemaId, metadataId));
    }


    /**
     * 动态综合查询
     *
     * @return {@link Void}
     */
    @ApiOperation(value = "动态综合查询")
    @PostMapping("/dynamic-query")
    public R<MyPage<Resource>> dynamicQuery(@RequestBody ResourceQueryDTO resourceQueryDTO) {
        return R.data(resourceManagerService.dynamicQuery(resourceQueryDTO));
    }

    /**
     * 根据元数据方案列表获取动态表头
     *
     * @return 元数据方案列表
     */
    @ApiOperation(value = "根据元数据方案列表获取动态表头")
    @PostMapping("/dynamic-header")
    public R<List<String>> dynamicHeader(@RequestBody String ids) {
        return R.data(resourceManagerService.dynamicHeader(ids));
    }


}
