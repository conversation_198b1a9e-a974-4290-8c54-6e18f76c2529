package org.irm.lab.repository.controller;

import cn.hutool.core.convert.Convert;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.irm.lab.common.annotation.MyLog;
import org.irm.lab.common.api.R;
import org.irm.lab.common.constant.LogConstant;
import org.irm.lab.common.entity.AbstractBaseEntity;
import org.irm.lab.common.support.Condition;
import org.irm.lab.common.support.MyPage;
import org.irm.lab.common.utils.Func;
import org.irm.lab.repository.entity.Resource;
import org.irm.lab.repository.repository.ResourceRepository;
import org.irm.lab.repository.service.IResourceAuditService;
import org.irm.lab.repository.service.IResourceService;
import org.irm.lab.repository.vo.ResourceVO;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2023/2/14 18:47
 * @description 资源审核控制器
 */
@RestController
@RequestMapping("/resource-audit")
@Api(value = "资源审核控制器")
@RequiredArgsConstructor
public class ResourceAuditController {
    private final IResourceAuditService iResourceAuditService;
    private final ResourceRepository resourceRepository;
    private final IResourceService resourceService;

    /**
     * 批量通过资源
     *
     * @param ids 资源id
     * @return {@link R}
     */
    @MyLog(menu = LogConstant.MENU_RESOURCE, dataType = LogConstant.DATA_RESOURCE, operation = LogConstant.OPERATION_PASS)
    @ApiOperation(value = "批量通过资源")
    @PostMapping("/pass")
    public R<String> passResource(@RequestBody String ids) {
        List<String> resourceIdList = Func.objToStrList(ids);
        iResourceAuditService.passResource(resourceIdList);
        return R.success();
    }

    /**
     * 批量通过资源全部
     *
     * @return {@link R}
     */
    @ApiOperation(value = "批量通过资源-全部")
    @GetMapping("/all-pass")
    public R<String> passResourceAll(@RequestParam String workTaskId) {
        List<String> resources = resourceRepository.findByCondition(Condition.getFilter(Map.of("workTaskId", workTaskId), Resource.class))
                .parallelStream().map(AbstractBaseEntity::getId).collect(Collectors.toList());
        iResourceAuditService.passResource(resources);
        return R.success();
    }

    /**
     * 驳回资源
     *
     * @param resource {@link Resource}
     * @return {@link R}
     */
    @MyLog(menu = LogConstant.MENU_RESOURCE, dataType = LogConstant.DATA_RESOURCE, operation = LogConstant.OPERATION_REJECT)
    @ApiOperation(value = "驳回资源")
    @PostMapping("/reject")
    public R<String> rejectResource(@RequestBody Resource resource) {
        return R.data(iResourceAuditService.save(resource));
    }


    /**
     * 获取当前工作任务下，所有“待审核”和“驳回_待审核”的资源
     *
     * @param pageMap 查询条件
     * @return {@link ResourceVO}
     */
    @ApiOperation(value = "获取所有资源")
    @GetMapping("/list")
    public R<MyPage<ResourceVO>> list(@RequestParam Map<String, Object> pageMap) {
        // 获取分页条件
        Integer page = Convert.toInt(pageMap.getOrDefault("page", 1));
        Integer size = Convert.toInt(pageMap.getOrDefault("size", 10));
        // 返回结果
        if ("批量通过审核".equals(pageMap.get("name"))) {
            String workTaskId = pageMap.get("workTaskId").toString();
            List<Resource> resources = resourceService.listByWorkTaskId(workTaskId);
            iResourceAuditService.passResource(resources.stream().map(AbstractBaseEntity::getId).collect(Collectors.toList()));
        }
        return R.data(iResourceAuditService.list(pageMap, page, size));
    }

}
