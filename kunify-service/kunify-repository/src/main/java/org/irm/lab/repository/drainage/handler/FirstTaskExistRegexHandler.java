package org.irm.lab.repository.drainage.handler;

import lombok.extern.slf4j.Slf4j;
import org.irm.lab.config.entity.WorkTask;
import org.irm.lab.repository.drainage.IDrainageStrategy;
import org.irm.lab.repository.entity.datasource.DatasourceResource;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.Set;

//该处理器用于：检测是否有匹配到的任务
@Component
@Slf4j
public class FirstTaskExistRegexHandler extends ResourceRegexHandler {


    @Override
    public ResourceRegexHandler handle(IDrainageStrategy drainageStrategy, List<DatasourceResource> resources) {
        log.info("第1链：获取匹配到的任务");
        Set<WorkTask> regexTask = resources.isEmpty() ? drainageStrategy.getRegexTask() : drainageStrategy.getRegexTask(resources.get(0));
        if (!resources.isEmpty() && !regexTask.isEmpty()) {
            //若任务中资源数量大于500，跳过该任务
//            if (drainageStrategy.getRegexTask().stream().mapToLong(WorkTask::getTotalResourceNum).count() >= 500) {
//                //设置当前流程已处理资源为0
//                return this.setProcessResource(new ArrayList<>());
//            }
            log.info("第1链：任务获取成功");
            return getNext().handle(drainageStrategy, resources);
        }
        log.warn("当前策略【{}】没有可匹配任务或资源，无法分流...", drainageStrategy.getType().getDrainageName());
        //设置当前流程已处理资源为0
        return this.setProcessResource(new ArrayList<>());
    }


}