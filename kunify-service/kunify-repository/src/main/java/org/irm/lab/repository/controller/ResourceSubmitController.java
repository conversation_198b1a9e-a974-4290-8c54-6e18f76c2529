package org.irm.lab.repository.controller;

import cn.hutool.core.convert.Convert;
import cn.hutool.core.lang.Opt;
import cn.hutool.core.text.StrSplitter;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.google.gson.Gson;
import com.google.gson.GsonBuilder;
import com.google.gson.JsonElement;
import com.google.gson.JsonObject;
import com.mongodb.client.model.Filters;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.pdfbox.pdmodel.PDDocument;
import org.apache.pdfbox.rendering.PDFRenderer;
import org.apache.pdfbox.text.PDFTextStripper;
import org.bson.Document;
import org.bson.conversions.Bson;
import org.bson.types.ObjectId;
import org.dom4j.Element;
import org.dom4j.io.SAXReader;
import org.irm.lab.common.annotation.MyLog;
import org.irm.lab.common.api.R;
import org.irm.lab.common.constant.*;
import org.irm.lab.common.enums.MediaType;
import org.irm.lab.common.exception.ServiceException;
import org.irm.lab.common.pojo.BaseStatus;
import org.irm.lab.common.provider.MinioLinkProvider;
import org.irm.lab.common.support.Condition;
import org.irm.lab.common.support.MyPage;
import org.irm.lab.common.utils.*;
import org.irm.lab.config.entity.Label;
import org.irm.lab.config.entity.MetadataItem;
import org.irm.lab.config.entity.MetadataSchema;
import org.irm.lab.config.entity.WorkTask;
import org.irm.lab.config.feign.MetadataSchemaFeign;
import org.irm.lab.config.feign.WorkTaskFeign;
import org.irm.lab.config.repository.LabelRepository;
import org.irm.lab.config.repository.WorkTaskRepository;
import org.irm.lab.config.vo.WorkTaskVO;
import org.irm.lab.front.feign.DocumentElasticSearchFeign;
import org.irm.lab.kg.algorithm.DocumentImage;
import org.irm.lab.kg.algorithm.DocumentUnit;
import org.irm.lab.kg.constant.RuleMatchingConstant;
import org.irm.lab.kg.entity.KnowledgeConcept;
import org.irm.lab.kg.entity.annex.AnnexDocumentImage;
import org.irm.lab.kg.entity.annex.AnnexDocumentUnit;
import org.irm.lab.kg.entity.annex.processing.AnnexLabelData;
import org.irm.lab.kg.entity.annex.processing.AnnexTripleLabelData;
import org.irm.lab.kg.entity.processing.LabelData;
import org.irm.lab.kg.entity.processing.LabelPropertyVO;
import org.irm.lab.kg.entity.processing.TripleLabelData;
import org.irm.lab.kg.entity.processing.process.ProcessRecords;
import org.irm.lab.kg.repository.DocumentImageRepository;
import org.irm.lab.kg.repository.DocumentUnitRepository;
import org.irm.lab.kg.repository.KnowledgeConceptRepository;
import org.irm.lab.kg.repository.annex.AnnexLabelDataRepository;
import org.irm.lab.kg.repository.annex.AnnexTripleLabelDataRepository;
import org.irm.lab.kg.repository.annex.processing.AnnexDocumentImageRepository;
import org.irm.lab.kg.repository.annex.processing.AnnexDocumentUnitRepository;
import org.irm.lab.kg.repository.processing.LabelDataRepository;
import org.irm.lab.kg.repository.processing.TripleLabelDataRepository;
import org.irm.lab.kg.repository.processing.process.ProcessRecordsRepository;
import org.irm.lab.repository.config.TempPathConfig;
import org.irm.lab.repository.constant.DocumentResolvedStatus;
import org.irm.lab.repository.constant.RecordResourceType;
import org.irm.lab.repository.constant.ResourceProcessStatus;
import org.irm.lab.repository.dto.RecordMetadataBatchDTO;
import org.irm.lab.repository.dto.SearchDTO;
import org.irm.lab.repository.dto.TaskMonitoringDTO;
import org.irm.lab.repository.entity.BaseResource;
import org.irm.lab.repository.entity.Resource;
import org.irm.lab.repository.entity.ResourceAnnex;
import org.irm.lab.repository.entity.ResourceVerification;
import org.irm.lab.repository.feign.ResourceAnnexFeign;
import org.irm.lab.repository.feign.ResourceFeign;
import org.irm.lab.repository.rabbitmq.consumer.DatasourceResourceSyncConsumer;
import org.irm.lab.repository.rabbitmq.message.DatasourceResourceSyncMessage;
import org.irm.lab.repository.rabbitmq.producer.*;
import org.irm.lab.repository.repository.ResourceAnnexRepository;
import org.irm.lab.repository.repository.ResourceRepository;
import org.irm.lab.repository.service.*;
import org.irm.lab.repository.service.impl.SchemaServiceImpl;
import org.irm.lab.repository.vo.ResourceVO;
import org.irm.lab.repository.websocket.TaskMonitoringCenter;
import org.irm.lab.resource.entity.Attach;
import org.irm.lab.resource.feign.AttachFeign;
import org.irm.lab.resource.feign.IOssEndPoint;
import org.irm.lab.user.entity.User;
import org.springframework.mock.web.MockMultipartFile;
import org.springframework.web.bind.annotation.*;

import javax.imageio.ImageIO;
import javax.servlet.http.HttpServletRequest;
import java.awt.image.BufferedImage;
import java.io.*;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.LinkedBlockingQueue;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.concurrent.atomic.AtomicReference;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2023/1/30 14:47
 * @description 资源提交控制器
 */
@Slf4j
@RestController
@RequestMapping("/resource-submit")
@Api(value = "任务资源")
@RequiredArgsConstructor
public class ResourceSubmitController implements ResourceFeign {

    public static final String[] NOT_SYNC_FILE = {"办理单", "办理过程", "稿纸", "痕迹", "发文单"};


    private final IResourceSubmitService iResourceSubmitService;
    private final WorkTaskFeign workTaskFeign;
    private final MetadataSchemaFeign metadataSchemaFeign;
    private final IResourceVerificationService iResourceVerificationService;
    private final TaskMonitoringCenter taskMonitoringCenter;
    private final IResourceService resourceService;

    private final AttachFeign attachFeign;
    private final IScalePicService scalePicService;
    private final HttpServletRequest httpServletRequest;
    private final DocumentElasticSearchFeign documentElasticSearchFeign;
    private final KnowledgeConceptRepository knowledgeConceptRepository;

    private final ResourceAnnexFeign resourceAnnexFeign;
    private final DatasourceResourceSyncProducer datasourceResourceSyncProducer;
    private final DatasourceResourceSyncConsumer datasourceResourceSyncConsumer;
    private final AnnexDocumentImageRepository annexDocumentImageRepository;

    private final OCRAnnexProducer ocrAnnexProducer;

    private final ResourceRepeatDeleteProducer resourceRepeatDeleteProducer;

    /**
     * 获取当前工作任务下，资源提交阶段的所有用户
     *
     * @param workTaskId 工作任务Id
     * @return {@link R}
     */
    @ApiOperation(value = "获取资源提交阶段的所有用户", hidden = true)
    @GetMapping("/list-user")
    public R<List<User>> listUser(@RequestParam("workTaskId") String workTaskId) {
        return R.data(iResourceSubmitService.listUser(workTaskId));
    }


    /**
     * 资源条件分页查询
     *
     * @param pageMap 条件
     * @return {@link MyPage}
     */
    @ApiOperation(value = "任务资源分页")
    @GetMapping("/page")
    public R<MyPage<ResourceVO>> page(@RequestParam Map<String, Object> pageMap) throws InterruptedException {
        long startTime = System.nanoTime();
        // 获取分页条件
        Integer page = Convert.toInt(pageMap.getOrDefault("page", 1));
        Integer size = Convert.toInt(pageMap.getOrDefault("size", 20));
        // 合并语料脚本
        if (ObjectUtil.equals("99999", pageMap.getOrDefault("name", ""))) {
            log.info("触发合并脚本");
            pageMap.remove("name");
            mergeScript(pageMap);
        }

        if (ObjectUtil.equals("999999", pageMap.getOrDefault("name", ""))) {
            log.info("触发合并附件脚本");
            pageMap.remove("name");
            mergeScriptAnnex(pageMap);
        }

        if (ObjectUtil.equals("导出语料", pageMap.getOrDefault("name", ""))) {
            log.info("触发导出语料脚本");
            pageMap.remove("name");
            exportJsonData(pageMap);
        }

        if (ObjectUtil.equals("重新同步ES", pageMap.getOrDefault("name", ""))) {
            log.info("触发重新同步ES");
            pageMap.remove("name");
            upes(pageMap);
        }
        if (ObjectUtil.equals("刷新领导批示", pageMap.getOrDefault("name", ""))) {
            log.info("触发刷新领导批示");
            pageMap.remove("name");
            processUp(pageMap);
        }
        String name = (String) pageMap.getOrDefault("name", "");

        if (StrUtil.contains(name, "删除esmongo数据145")) {
            log.info("触发删除es数据");
            String param = (String) pageMap.getOrDefault("name", "");
            String[] split = param.split("[:]");
            log.info(split[1]);
            List<String> ids = StrUtil.split(split[1], ",");
            pageMap.remove("name");
            log.info("删除掉的数据为:{}", ids.toString());
            deleteES(ids);
        }

        if (StrUtil.contains(name, "修改状态进资源加工步骤")) {
            log.info("修改资源状态进资源加工");
            String param = (String) pageMap.getOrDefault("name" +
                    "", "");
            String[] split = param.split("[:]");
            log.info(split[1]);
            List<String> ids = StrUtil.split(split[1], ",");
            pageMap.remove("name");
            log.info("输出修改的文件ids:{}", ids.toString());
            updateResource(ids);
        }
        if (StrUtil.contains(name, "手动对重复节点进行对齐")) {
            String param = (String) pageMap.getOrDefault("name" +
                    "", "");
            String[] split = param.split("[:]");

            log.info("开始对齐{}", split[1]);
            KnowledgeConcept people = knowledgeConceptRepository.findOne(Filters.eq("name", split[1]));
            DatasourceResourceSyncMessage datasourceResourceSyncMessage = new DatasourceResourceSyncMessage();
            datasourceResourceSyncMessage.setUser(request.getHeader("user"));
            datasourceResourceSyncMessage.setConceptId(people.getId());
            datasourceResourceSyncProducer.sendMessage(datasourceResourceSyncMessage);
            return R.data(new MyPage<>(1, 10, 0, new ArrayList<ResourceVO>()));
        }

        if (StrUtil.contains(name, "手动对重复节点进行两个实体对齐")) {
            log.info("开始对齐");
            String param = (String) pageMap.getOrDefault("name" +
                    "", "");
            String[] split = param.split("[:]");
            log.info(split[1]);
            List<String> ids = StrUtil.split(split[1], ",");
            pageMap.remove("name");
            log.info("输出当前操作的节点ids:{}", ids.toString());
            ThreadLocalUtil.set("user", request.getHeader("user"));
            CompletableFuture.runAsync(() -> {
                datasourceResourceSyncConsumer.updateNodeEntity(ids);
            }, new ThreadPoolExecutor(10, 10,
                    0L, TimeUnit.MILLISECONDS,
                    new LinkedBlockingQueue<Runnable>()));

            return R.data(new MyPage<>(1, 10, 0, new ArrayList<ResourceVO>()));
        }

        if (StrUtil.contains(name, "所有未进入es数据")) {
            List<ResourceVO> resources = resourceService.listByWorkTaskIdWithoutES(pageMap.get("workTaskId").toString());
            List<ResourceVO> collect = resources.stream()
                    .filter(resourceVO -> resourceVO.getMediaType() != null)
                    .filter(resourceVO -> resourceVO.getName() != null)
                    .filter(resourceVO -> Arrays.stream(NOT_SYNC_FILE).noneMatch(ele -> resourceVO.getName().contains(ele)))
                    .collect(Collectors.toList());

            return R.data(new MyPage<>(0, 1, collect.size(), collect));
        }
        if (StrUtil.contains(name, "过滤未进入es数据")) {
            pageMap.remove("name");
            MyPage<ResourceVO> page1 = resourceService.listByWorkTaskIdWithoutESByPage(iResourceSubmitService.page(pageMap, page, size).getContent());
            List<ResourceVO> collect = page1.getContent().stream()
                    .filter(resourceVO -> resourceVO.getMediaType() != null)
                    .filter(resourceVO -> resourceVO.getName() != null)
                    .filter(resourceVO -> Arrays.stream(NOT_SYNC_FILE).noneMatch(ele -> resourceVO.getName().contains(ele)))
                    .collect(Collectors.toList());
            page1.setContent(collect);
            return R.data(page1);
        }

        if (StrUtil.contains(name, "一键同步智能问答数据集")) {
            pageMap.remove("name");
            String workTaskId = pageMap.get("workTaskId").toString();
            resourceService.passAllResourceQA(workTaskId);
            return R.success();
        }

        if (StrUtil.contains(name, "手动OCR并同步es")) {
            String param = (String) pageMap.getOrDefault("name", "");
            String[] split = param.split(":");
            List<String> ids = StrSplitter.split(split[1], ',', 0, true, true);
            ThreadLocalUtil.set("user",request.getHeader("user"));
            synES(ids);
            return R.data(new MyPage<>(1, 10, 0, new ArrayList<ResourceVO>()));
        }

        if (StrUtil.contains(name, "重复文件删除")) {
            ThreadLocalUtil.set("user", request.getHeader("user"));
            CompletableFuture.runAsync(resourceService::deleteResource, new ThreadPoolExecutor(10, 10,
                    0L, TimeUnit.MILLISECONDS,
                    new LinkedBlockingQueue<Runnable>()));


            return R.data(new MyPage<>(1, 10, 0, new ArrayList<ResourceVO>()));
        }

        if (StrUtil.contains(name, "根据excel删除文件")) {
            ThreadLocalUtil.set("user", request.getHeader("user"));
            CompletableFuture.runAsync(resourceService::deleteResourceInExcel, new ThreadPoolExecutor(10, 10,
                    0L, TimeUnit.MILLISECONDS,
                    new LinkedBlockingQueue<Runnable>()));

            return R.data(new MyPage<>(1, 10, 0, new ArrayList<ResourceVO>()));
        }
        long endTime = System.nanoTime();
        log.info("跳过判断逻辑耗时【{}】", (endTime - startTime) / 1000000000.00000);
        return R.data(iResourceSubmitService.page(pageMap, page, size));
    }

    private void synES(List<String> ids) {
        ArrayList<Bson> bsons = new ArrayList<>();
        for (String id : ids) {
            Bson eq = Filters.eq(new ObjectId(id));
            bsons.add(eq);
        }
        Bson and = Filters.or(bsons);
        List<Resource> byCondition = resourceRepository.findByCondition(and);
        List<ResourceAnnex> byCondition1 = resourceAnnexRepository.findByCondition(and);

        byCondition.forEach(resource -> {

            deleteResourceInEs(resource);
            List<DocumentImage> documentImageList = documentImageRepository.findByCondition(Filters.eq("resourceId", resource.getId()));
            if (ObjectUtil.isEmpty(documentImageList)) {
                log.info(">>>>>>>>>>【主文件】开始生成图片<<<<<<<<<<");
                pdfToPic(resource);
                log.info("<<<<<<<<<<【主文件】图片生成结束<<<<<<<<<<");
            } else {
                log.info("【主文件】 {} 图片已生成，无需重复生成", resource.getName());
            }
            final List<DocumentImage> documentImages = documentImageRepository.findByConditionAndSorted(Filters.eq("resourceId", resource.getId()), Filters.eq("page", 1));



            int page = 1;
            for (int i = 0; i < documentImages.size(); i++) {
                if (i == documentImages.size() - 1) {

                    ocrProducer.sendMessage(httpServletRequest.getHeader("user"), resource, documentImages.get(i), page, true, "YES");
                } else {
                    ocrProducer.sendMessage(httpServletRequest.getHeader("user"), resource, documentImages.get(i), page, false, "YES");
                }

                page++;
            }

        });
        log.info("同步resource个数{}",byCondition.size());

        ArrayList<String> annexName = new ArrayList<>();
        for (ResourceAnnex resourceAnnex : byCondition1) {
            annexName.add(resourceAnnex.getName());
        }
        log.info("同步附件{}",annexName);
        byCondition1.forEach(annex -> {
           deleteResourceInEs(annex);
            List<AnnexDocumentImage> annexDocumentImages = annexDocumentImageRepository.findByConditionAndSorted(Filters.eq("annexId", annex.getId()),
                    Filters.eq("page", 1));
            if (ObjectUtil.isEmpty(annexDocumentImages)) {
                log.info("annexDocumentImages is empty,开始生成附件图片");
                annexPdfToPic(annex);
                annexDocumentImages = annexDocumentImageRepository.findByConditionAndSorted(Filters.eq("annexId", annex.getId()),Filters.eq("page", 1));

            }else {
                log.info("附件图片已生成无需重复生成");
            }


            int page = 1;
            for (int i= 0; i < annexDocumentImages.size(); i++) {
                if (i == annexDocumentImages.size()-1) {
                    ocrAnnexProducer.sendMessage(ThreadLocalUtil.get("user"), annex, annexDocumentImages.get(i), page, true);
                }else {
                    ocrAnnexProducer.sendMessage(ThreadLocalUtil.get("user"), annex, annexDocumentImages.get(i), page,false);
                }

                page++;
            }

        });
    }

    private void deleteResourceInEs(BaseResource resource) {
        log.info("开始清除ES原始内容");
        final R<org.irm.lab.front.model.Document> documentById = documentElasticSearchFeign.info(resource.getId());
        final org.irm.lab.front.model.Document data1 = documentById.getData();
        if (Opt.of(data1).isPresent()) {
            data1.setContent("");
            documentElasticSearchFeign.updateMeta(data1);
        }

        log.info("清除ES原始内容结束");
    }

    public void annexPdfToPic(ResourceAnnex resourceAnnex) {
        String pdfAttachName = resourceAnnex.getPdfAttachName();
        if (pdfAttachName == null) return;

        //获取资源文件路径
        String pdfLink = minioLinkProvider.getMinioLinkIntranet(resourceAnnex.getPdfAttachName());
        if (pdfLink == null) return;
        try (InputStream inputStream = NetWorkFileUtil.urlToInputStream(pdfLink)) {
            List<AnnexDocumentImage> imageList = AnnexPdfToPic(inputStream);
            imageList.forEach(m -> {
                m.setResourceId(resourceAnnex.getResourceId());
                m.setAnnexId(resourceAnnex.getId());
            });
            annexDocumentImageRepository.saveAll(imageList);
            resourceAnnex.setDocImagesStatus(BaseStatus.OK);
        } catch (IOException e) {
            resourceAnnex.setDocImagesStatus(ExceptionMessageConst.PDF_IMAGES_GEN);
            throw new ServiceException(ExceptionMessageConst.PDF_IMAGES_GEN + "【" + e.getMessage() + "】");
        } finally {
            resourceAnnexFeign.save(resourceAnnex);
        }

    }

    private void updateResource(List<String> ids) {
        for (String id : ids) {
            Resource byId = resourceRepository.findById(id);
            byId.setProcessStatus(ResourceProcessStatus.PROCESSING);
            byId.setRuleStatus(DocumentResolvedStatus.UN_RESOLVED);
            byId.setProcessed(false);
            byId.setDocRuleParsingConfirm(false);
            byId.setKnowledgeTagConfirm(false);
            resourceRepository.save(byId);
        }
    }

    private final ProcessRecordsRepository processRecordsRepository;

    private void processUp(Map<String, Object> pageMap) {

        Bson filter = Condition.getFilter(pageMap, Resource.class, Map.of(FiledNameConst.WORK_TASK_ID, FilterTypeConst.EQ));
        // 获取开始和结束时间
        Date startTime = Convert.toDate(pageMap.getOrDefault("startTime", Integer.MIN_VALUE));
        Date endTime = Convert.toDate(pageMap.getOrDefault("endTime", Long.MAX_VALUE));
        // 构建条件
        Bson timeFilter = Filters.and(Filters.gte(AbstractBaseEntityFieldConstant.CREATE_TIME, startTime), Filters.lte(AbstractBaseEntityFieldConstant.CREATE_TIME, endTime));
        // 构建最新条件
        Bson newFilter = Filters.and(filter, timeFilter);
        List<Resource> resourcesList = resourceRepository.findByConditionAndSorted(newFilter, Filters.eq(AbstractBaseEntityFieldConstant.UPDATE_TIME, -1));

        log.info("开始重新同步es======数据量为：「{}」", resourcesList.size());

        final ArrayList<ProcessRecords> records = new ArrayList<>();
        resourcesList.stream().forEach(resource -> {
            R<List<ResourceAnnex>> resourceAnnexListR = resourceAnnexFeign.listByResourceId(resource.getId());
            List<ResourceAnnex> resourceAnnexList = resourceAnnexListR.getData();
            if (!resourceAnnexListR.isSuccess() || ObjectUtil.isEmpty(resourceAnnexList)) {
                log.info("该资源不存在附件，无需附件规则解析!");
                return;
            }
            final List<ProcessRecords> processRecords = processRecordsRepository.findByCondition(Filters.eq("resourceId", resource.getId()));
            if (ObjectUtil.isNotEmpty(processRecords)) {
                log.info("文件存在批示信息，开始删除批示信息，批示个数是「{}」", processRecords.size());
                final List<String> strings = processRecordsRepository.deleteByIdFake(processRecords.stream().map(ProcessRecords::getId).collect(Collectors.toList()));
                log.info("删除批示信息，批示个数是「{}」", strings.size());
            }

            log.info("一共「{}」件数据", resourceAnnexList.size());
            resourceAnnexList.stream().forEach(annexResource -> {
                //获取附件attach
                Attach attach = attachFeign.info(annexResource.getPrimaryFileId()).getData();
                // 只处理xml类型办理单文件
                if (attach != null && !"xml".equals(attach.getFileExtension())) return;
                log.info(">>>>>>>>>>【附件】【办理单】规则解开启>>>>>>>>>>");


                final List<ProcessRecords> processRecordsByResource = processPlus(attach.getLink(), resource.getId());


                records.addAll(processRecordsByResource);
            });


        });
        processRecordsRepository.saveAll(records);
    }

    public List<ProcessRecords> processPlus(String link, String resourceId) {


        org.dom4j.Document doc = null;
        try {
            SAXReader reader = new SAXReader();
            doc = reader.read(link);
        } catch (org.dom4j.DocumentException e) {
            e.printStackTrace();
        }

        assert doc != null;

        Element root = doc.getRootElement();

        List<ProcessRecords> processRecordsByResource;

        // 检查根元素或子元素的名称，以判断结构
        if ("办理过程字段内容".equals(root.getName())) {
            // 处理带有ItemInfo作为子元素的结构
            log.info("存在办理过程字段内容");
            processRecordsByResource = processItemInfo(root.element("ItemInfo"), resourceId);
        } else {
            // 如果根元素就是ItemInfo，直接处理
            log.info("不存在办理过程字段内容");
            processRecordsByResource = processItemInfo(root, resourceId);
        }
        return processRecordsByResource;
        /*Element root2 = elements.get(0);
        List<Element> elements1 = root2.elements();*/


    }

    private List<ProcessRecords> processItemInfo(Element itemInfo, String resourceId) {
        List<Element> items = itemInfo.elements();
        List<ProcessRecords> processRecordsList = new ArrayList<>();
        for (Element item : items) {

            final ProcessRecords processRecords = processItem(item, resourceId);
            processRecordsList.add(processRecords);
        }
        return processRecordsList;
    }

    private ProcessRecords processItem(Element item, String resourceId) {
        final ProcessRecords processRecords = new ProcessRecords();
        processRecords.setResourceId(resourceId);

        List<Element> children = item.elements();
        if (!children.isEmpty() && children.get(0).getName().startsWith("Field")) {
            // 假设是第一种格式，每个子元素都是Field
            // 处理当前节点办理人员

            final JSONObject attribute = JSONUtil.createObj();

            boolean foundCurrentNode = false;  // 标志位，用于检查是否找到当前节点
            String currentNodeHandler = null;  // 用于存储当前节点办理人员
            for (Element field : children) {
                String name = field.attributeValue("Name");
                String value = field.attributeValue("Value");

                // 检查当前字段是否为“当前节点”，如果是，则设置标志位
                if (ObjectUtil.equals(name, "当前节点")) {
                    foundCurrentNode = true;  // 将标志位设为 true，表示已找到当前节点
                } else if (foundCurrentNode && ObjectUtil.equals(name, "办理人员") && !ObjectUtil.isEmpty(value)) {
                    currentNodeHandler = value;  // 如果找到了当前节点并且下一个办理人员字段不为空，则记录办理人员
                    processRecords.setHandler(value);
                    foundCurrentNode = false;  // 重置标志位
                }

                // 其他逻辑处理，如获取办理意见
                if (ObjectUtil.equals(name, RuleMatchingConstant.HANDLING_ADVICE)) {
                    processRecords.setContent(ObjectUtil.isEmpty(value) ? RuleMatchingConstant.NO_CHANCE : value);
                    attribute.putOpt(RuleMatchingConstant.HANDLING_ADVICE, ObjectUtil.isEmpty(value) ? RuleMatchingConstant.NO_CHANCE : value);
                }

                // 处理完办理人员后，如果是当前节点的办理人员，进行相应的处理
                if (currentNodeHandler != null) {

                    currentNodeHandler = null;  // 重置当前节点办理人员变量
                }

                // 获取办理序号
                if (ObjectUtil.equals(name, RuleMatchingConstant.SERIAL_NUMBER_NEW)) {
                    processRecords.setSort(value);
                    attribute.putOpt(RuleMatchingConstant.SERIAL_NUMBER, value);
                }
                // 获取发送时间
                if (ObjectUtil.equals(name, RuleMatchingConstant.SEND_TIME)) {
                    processRecords.setSendTime(value);
                    attribute.putOpt(RuleMatchingConstant.START_TIME, value);
                }
                // 获取结束时间
                if (ObjectUtil.equals(name, RuleMatchingConstant.COMPLETION_TIME)) {
                    processRecords.setFinishTime(value);
                    attribute.putOpt(RuleMatchingConstant.FINISH_TIME, value);
                }
            }

        } else {

            // 假设是第二种格式，直接是具体的字段
            for (Element field : children) {

                if (ObjectUtil.equals(field.getName(), RuleMatchingConstant.CURRENT_NODE_HANDLER)) {

                    final String name = field.getText();
                    processRecords.setHandler(name);

                }


                // 获取办理意见
                if (ObjectUtil.equals(field.getName(), RuleMatchingConstant.HANDLING_ADVICE)) {

                    final String nineValue = field.getText();
                    processRecords.setContent(ObjectUtil.isEmpty(nineValue) ? RuleMatchingConstant.NO_CHANCE : nineValue);
                }
                // 获取办理序号
                if (ObjectUtil.equals(field.getName(), RuleMatchingConstant.SERIAL_NUMBER_NEW)) {
                    processRecords.setSort(field.getText());
                }
                // 获取发送时间
                if (ObjectUtil.equals(field.getName(), RuleMatchingConstant.SEND_TIME)) {
                    processRecords.setSendTime(field.getText());
                }
                // 获取结束时间
                if (ObjectUtil.equals(field.getName(), RuleMatchingConstant.COMPLETION_TIME)) {
                    processRecords.setFinishTime(field.getText());
                }


            }

        }
        return processRecords;
    }

    private final DocumentImageRepository documentImageRepository;
    private final MinioLinkProvider minioLinkProvider;

    private void upes(Map<String, Object> pageMap) {

        Bson filter = Condition.getFilter(pageMap, Resource.class, Map.of(FiledNameConst.WORK_TASK_ID, FilterTypeConst.EQ));
        // 获取开始和结束时间
        Date startTime = Convert.toDate(pageMap.getOrDefault("startTime", Integer.MIN_VALUE));
        Date endTime = Convert.toDate(pageMap.getOrDefault("endTime", Long.MAX_VALUE));
        // 构建条件
        Bson timeFilter = Filters.and(Filters.gte(AbstractBaseEntityFieldConstant.CREATE_TIME, startTime), Filters.lte(AbstractBaseEntityFieldConstant.CREATE_TIME, endTime));
        // 构建最新条件
        Bson newFilter = Filters.and(filter, timeFilter);
        List<Resource> resources = resourceRepository.findByConditionAndSorted(newFilter, Filters.eq(AbstractBaseEntityFieldConstant.UPDATE_TIME, -1));

        log.info("开始重新同步es======数据量为：「{}」", resources.size());

        final int[] count = {0};
        resources.forEach(resource -> {

            log.info("开始清除ES原始内容");
            final R<org.irm.lab.front.model.Document> documentById = documentElasticSearchFeign.info(resource.getId());
            final org.irm.lab.front.model.Document data1 = documentById.getData();
            if (Opt.of(data1).isPresent()) {
                data1.setContent("");
                documentElasticSearchFeign.updateMeta(data1);
            }

            log.info("清除ES原始内容结束");
            List<DocumentImage> documentImageList = documentImageRepository.findByCondition(Filters.eq("resourceId", resource.getId()));
            if (ObjectUtil.isEmpty(documentImageList)) {
                log.info(">>>>>>>>>>【主文件】开始生成图片<<<<<<<<<<");
                pdfToPic(resource);
                log.info("<<<<<<<<<<【主文件】图片生成结束<<<<<<<<<<");
            } else {
                log.info("【主文件】 {} 图片已生成，无需重复生成", resource.getName());
            }
            final List<DocumentImage> documentImages = documentImageRepository.findByConditionAndSorted(Filters.eq("resourceId", resource.getId()), Filters.eq("page", 1));

            count[0] = count[0] + documentImages.size();

            int page = 1;
            for (int i = 0; i < documentImages.size(); i++) {
                if (i == documentImages.size() - 1) {

                    ocrProducer.sendMessage(httpServletRequest.getHeader("user"), resource, documentImages.get(i), page, true, "YES");
                } else {
                    ocrProducer.sendMessage(httpServletRequest.getHeader("user"), resource, documentImages.get(i), page, false, "YES");
                }

                page++;
            }

        });

        log.debug("一共{}页数据", count[0]);
    }

    private final OCRProducer ocrProducer;

    public void pdfToPic(Resource resource) {
        String pdfAttachName = resource.getPdfAttachName();
        if (pdfAttachName == null) return;
        resource.setDocImagesStatus(BaseStatus.OK);
        //获取资源文件路径
        String pdfLink = minioLinkProvider.getMinioLinkIntranet(resource.getPdfAttachName());
        if (pdfLink == null) return;
        try (InputStream inputStream = NetWorkFileUtil.urlToInputStream(pdfLink)) {
            List<DocumentImage> imageList = pdfToPic(inputStream);
            imageList.forEach(m -> m.setResourceId(resource.getId()));
            documentImageRepository.saveAll(imageList);
        } catch (IOException e) {
            resource.setDocImagesStatus(ExceptionMessageConst.PDF_IMAGES_GEN);
            throw new ServiceException(ExceptionMessageConst.PDF_IMAGES_GEN + "【" + e.getMessage() + "】");
        } finally {
            resourceService.save(resource);
        }
    }

    private List<AnnexDocumentImage> AnnexPdfToPic(InputStream inputStream) throws IOException {
        PDDocument document = PDDocument.load(inputStream);
        PDFRenderer renderer = new PDFRenderer(document);
        int pageCount = document.getNumberOfPages();
        List<AnnexDocumentImage> images = new ArrayList<>(pageCount);
        for (int i = 0; i < pageCount; i++) {
            try (ByteArrayOutputStream outputStream = new ByteArrayOutputStream()) {
                Attach attach = getAttach(renderer, i, outputStream);
                images.add(new AnnexDocumentImage(attach.getId(), attach.getOssObjectName(), i + 1));
            } catch (Exception e) {
                log.info(e.getMessage());
            }

        }
        document.close();
        return images;
    }

    @javax.annotation.Resource
    private IOssEndPoint iOssEndPoint;

    private List<DocumentImage> pdfToPic(InputStream inputStream) throws IOException {
        PDDocument document = PDDocument.load(inputStream);
        PDFRenderer renderer = new PDFRenderer(document);
        int pageCount = document.getNumberOfPages();
        List<DocumentImage> images = new ArrayList<>(pageCount);
        for (int i = 0; i < pageCount; i++) {
            try (ByteArrayOutputStream outputStream = new ByteArrayOutputStream()) {
                Attach attach = getAttach(renderer, i, outputStream);
                images.add(new DocumentImage(attach.getId(), attach.getOssObjectName(), i + 1));
            } catch (Exception e) {
                log.info(e.getMessage());
            }

        }
        document.close();
        return images;
    }

    private Attach getAttach(PDFRenderer renderer, int i, ByteArrayOutputStream outputStream) throws IOException {
        BufferedImage image = renderer.renderImageWithDPI(i, 296);
        String pdfName = Math.random() * 10000 + "-" + i + ".jpg";
        ImageIO.write(image, "JPG", outputStream);
        MockMultipartFile mockMultipartFile = new MockMultipartFile(pdfName, pdfName, null, outputStream.toByteArray());
        return iOssEndPoint.putFile(mockMultipartFile).getData();
    }


    private final WorkTaskRepository workTaskRepository;

    private final SchemaServiceImpl schemaServiceImpl;

    private final DocumentUnitRepository documentUnitRepository;

    private final AnnexLabelDataRepository annexLabelDataRepository;

    private void exportJsonData(Map<String, Object> pageMap) {

        Bson filter = Condition.getFilter(pageMap, Resource.class, Map.of(FiledNameConst.WORK_TASK_ID, FilterTypeConst.EQ));
        // 获取开始和结束时间
        Date startTime = Convert.toDate(pageMap.getOrDefault("startTime", Integer.MIN_VALUE));
        Date endTime = Convert.toDate(pageMap.getOrDefault("endTime", Long.MAX_VALUE));
        // 构建条件
        Bson timeFilter = Filters.and(Filters.gte(AbstractBaseEntityFieldConstant.CREATE_TIME, startTime), Filters.lte(AbstractBaseEntityFieldConstant.CREATE_TIME, endTime));
        // 构建最新条件
        Bson newFilter = Filters.and(filter, timeFilter);
        List<Resource> resources = resourceRepository.findByConditionAndSorted(newFilter, Filters.eq(AbstractBaseEntityFieldConstant.UPDATE_TIME, -1));

        final Map<String, List<DocumentUnit>> documentUnitMap = documentUnitRepository.findByCondition(Filters.in("resourceId", resources.stream().map(Resource::getId).collect(Collectors.toList()))).stream().collect(Collectors.groupingBy(DocumentUnit::getResourceId));

        documentUnitMap.replaceAll((resourceId, documentUnits) ->
                documentUnits.stream()
                        .sorted(Comparator.comparingInt(DocumentUnit::getPage)
                                .thenComparingInt(DocumentUnit::getSortInCurrentPage))
                        .collect(Collectors.toList())
        );

        Map<String, String> contentMap = documentUnitMap.entrySet().stream()
                .collect(Collectors.toMap(
                        Map.Entry::getKey,
                        entry -> entry.getValue().stream()
                                .map(DocumentUnit::getContent)
                                .collect(Collectors.joining())
                ));

        final ArrayList<SchemaServiceImpl.JsonSchema> resultJson = new ArrayList<>();
        resources.forEach(resource -> {

            final String workTaskId = resource.getWorkTaskId();

            final String dataSetId = workTaskRepository.findById(workTaskId).getProcess().getDataSetId();
            final JSONArray objects = relevanceShow(resource.getId(), dataSetId);

            final String classifyByResourceOrAnnexId = "领导批示";//getClassifyByResourceOrAnnexId(resource.getId());

            if (ObjectUtil.equals(classifyByResourceOrAnnexId, "表彰荣誉")) {
                final List<ResourceAnnex> resourceId1 = resourceAnnexRepository.findByCondition(Filters.eq("resourceId", resource.getId()));

                resourceId1.forEach(resourceAnnex -> {
                    final JSONArray objects1 = relevanceShowAnnex(resourceAnnex.getId(), dataSetId);
                    final String schema = schemaServiceImpl.transformJson(objects1.toString(), classifyByResourceOrAnnexId);
                    final SchemaServiceImpl.JsonSchema build = SchemaServiceImpl.JsonSchema.builder().query(JSONUtil.toJsonStr("根据schema:" + getSchema(classifyByResourceOrAnnexId, null) + "对以下文本进行知识图谱抽取: " + contentMap.get(resource.getId()))).result(schema).build();

                    resultJson.add(build);
                });
            }

            final String schema = schemaServiceImpl.transformJson(objects.toString(), classifyByResourceOrAnnexId);
            final SchemaServiceImpl.JsonSchema build = SchemaServiceImpl.JsonSchema.builder().query(JSONUtil.toJsonStr("根据schema:" + getSchema(classifyByResourceOrAnnexId, null) + "对以下文本进行知识图谱抽取: " + contentMap.get(resource.getId()))).result(schema).build();

            resultJson.add(build);
        });

        try (FileWriter file = new FileWriter("output.json")) {

            resultJson.forEach(data -> {
                try {
                    Gson prettyGson = new GsonBuilder().setPrettyPrinting().create();
                    final JsonElement jsonTree = prettyGson.toJsonTree(data);
                    final JsonObject asJsonObject = jsonTree.getAsJsonObject();

                    file.write(asJsonObject.toString());
                    file.write("\n");
                } catch (IOException e) {
                    throw new RuntimeException(e);
                }

            });


        } catch (IOException e) {
            throw new RuntimeException(e);
        }

    }

    private final ResourceAnnexRepository annexRepository;

    public String getClassifyByResourceOrAnnexId(String id) {
        AtomicReference<List<String>> classify = new AtomicReference<>(new ArrayList<>());
        try {
            //正文
            setClassifyName(resourceRepository.findById(id), classify);
        } catch (Exception e) {
            //附件
            setClassifyName(resourceRepository.findById(
                            annexRepository.findById(id).getResourceId()),
                    classify);
        }
        return classify.get().isEmpty() ? "" : classify.get().get(0);
    }

    public static final String META_NAME = "公文分类";

    private void setClassifyName(org.irm.lab.repository.entity.Resource resource, AtomicReference<List<String>> classify) {
        resource.getMetadata()
                .stream().filter(meta -> META_NAME.equals(meta.getName()))
                .findFirst().ifPresent(metaVO -> classify.set(metaVO.getValue()));
    }

    /**
     * 根据文件类型获取对应的schema
     *
     * @param type 文件类型
     * @return schema
     */

    public static String getSchema(String type, String annex) {

        switch (type) {
            case "规章制度":
                return "[{\"制度文件\":[\"制定目的\",\"适用范围\",\"制定依据\",\"废止文件\"]},{\"机构\":[\"职责\"]},{\"概念术语\":[\"定义内容\"]}]";
            case "会议纪要":
                return "[{\"会议\": [\"会议期数\", \"会议时间\", \"会议议题\",\"参会人员\", \"涉及机构/部门\",\"审议文件\", \"涉及项目\"]}]";
            case "领导批示":
                return "[{\"公文\":[\"机构/部门\",\"时间\",\"文件\",\"请示\"]},{\"机构/部门\":[\"发起请示\"]}]";
            case "表彰荣誉":
            case "奖章荣誉":
                if (ObjectUtil.isNotEmpty(annex)) {
                    return "[{\"荣誉称号\": [\"授予人员\", \"授予机构\"]},{\"人员\": [\"机构/部门\", \"职务\"]}]";
                }
                return "[{\"表彰荣誉\": [\"表彰年度\", \"表彰时间\", \"奖项寄语\", \"授奖机构\",\"获奖人员\", \"获奖机构\", \"获奖工程项目\"]},{\"人员\": [\"机构/部门\", \"职务\"]}]";
        }

        return "";
    }

    private final TripleLabelDataRepository tripleLabelDataRepository;

    private final LabelDataRepository labelDataRepository;

    public Map<String, List<JSONObject>> entitySortByKeyValue(String dataSetId) {
        Map<String, Integer> labelSort = new HashMap<>();
        List<Label> labelList = labelRepository.findByCondition(new Document("dataSetId", dataSetId));
        labelList.forEach(m -> labelSort.put(m.getName(), m.getSort()));
        return new TreeMap<>(((o1, o2) -> {
            if (!(labelSort.containsKey(o1) && labelSort.containsKey(o2))) return 1;
            if (!Objects.equals(o1, o2)) {
                Integer o1Sort = labelSort.get(o1);
                Integer o2Sort = labelSort.get(o2);
                return Objects.equals(o1Sort, o2Sort) ? 0 : (o1Sort > o2Sort ? 1 : -1);
            }
            return 0;
        }));
    }

    public JSONArray relevanceShow(String resourceId, String dataSetId) {
        HashMap<String, Label> labelMap = new HashMap<>();
        Map<String, List<JSONObject>> labelDatamap = entitySortByKeyValue(dataSetId);
        // 获取该资源的所有标签数据·
        List<LabelData> labelDataList = labelDataRepository.findByCondition(Filters.eq("resourceId", resourceId));
        List<TripleLabelData> allTripleLabelDataList = tripleLabelDataRepository.findByCondition(Filters.eq("resourceId", resourceId));
        Map<String, List<TripleLabelData>> collect = allTripleLabelDataList.stream()
                .filter(data -> data.getStartLabelDataId() != null) // 过滤掉getStartLabelDataId返回null的项
                .collect(Collectors.groupingBy(TripleLabelData::getStartLabelDataId));        // 遍历所以标签数据
        for (LabelData labelData : labelDataList) {
            // 创建数组存储关系
            List<JSONObject> relations = new ArrayList<>();
            // 把标签数据的属性放在和关系平级的位置
            // 给属性封装名称
            for (LabelPropertyVO labelProperty : labelData.getLabelProperties()) {
                // 获取属性标签
                String labelPropertyId = labelProperty.getLabelPropertyId();
                Label propertyLabel = getLabel(labelMap, labelPropertyId);
                if (propertyLabel == null)
                    throw new ServiceException("数据集标签不存在，请检查数据集，并修改后重新加工该资源！");
                // 为属性设置标签名称
                labelProperty.setLabelPropertyName(propertyLabel.getName());
                // 把属性封装为指定JSONObject
                JSONObject labelDataPropertyObject = new JSONObject(labelProperty);
                // 添加层级
                labelDataPropertyObject.putOpt("level", 3);
                // 把属性放在关系前面
                if (ObjectUtil.isNotEmpty(labelDataPropertyObject)) relations.add(labelDataPropertyObject);
            }
            // 获取每个数据标签 作为主语的 关系三元组
            List<TripleLabelData> tripleLabelDataList = collect.get(labelData.getId());
            if (ObjectUtil.isNotEmpty(tripleLabelDataList)) {
                for (TripleLabelData tripleLabelData : tripleLabelDataList) {
                    // 生成关系对象
                    generateRelationRelevance(relations, tripleLabelData, labelMap);
                }
            }
            // 给标签数据的标签Id封装为labelName
            String conceptLabelId = labelData.getLabelId();
            Label conceptLabel = getLabel(labelMap, conceptLabelId);
            if (conceptLabel == null)
                throw new ServiceException("数据集标签不存在，请检查数据集，并修改后重新加工该资源！");
            labelData.setLabelName(conceptLabel.getName());
            // 转换为JSONObject
            JSONObject labelDataJsonObject = new JSONObject(labelData);
            // 添加 层级
            labelDataJsonObject.putOpt("level", 2);
            // 添加关系
            labelDataJsonObject.putOpt("children", relations);
            // 根据标签Id查询标签对象，并获取标签名称
            String labelId = labelData.getLabelId();
            Label label = getLabel(labelMap, labelId);
            if (label == null) throw new ServiceException("数据集标签不存在，请检查数据集，并修改后重新加工该资源！");
            // 添加结果
            List<JSONObject> list = labelDatamap.getOrDefault(label.getName(), new ArrayList<>());
            list.add(labelDataJsonObject);
            labelDatamap.put(label.getName(), list);

        }
        // 封装标签数据
        JSONArray jsonArray = new JSONArray();
        Set<String> keySet = labelDatamap.keySet();
        for (String key : keySet) {
            JSONObject jsonObject = new JSONObject();
            // 获取标签ID
            String labelId = labelDatamap.get(key).get(0).getStr("labelId");
            jsonObject.putOpt("labelId", labelId);
            jsonObject.putOpt("labelName", key);
            jsonObject.putOpt("children", labelDatamap.get(key));
            jsonObject.putOpt("level", 1);
            jsonArray.add(jsonObject);
        }
        // 返回结果
        return jsonArray;
    }

    private final LabelRepository labelRepository;

    private final AnnexTripleLabelDataRepository annexTripleLabelDataRepository;


    /**
     * 关系生成
     *
     * @param relations       关系JSONObject
     * @param tripleLabelData 关系三元组
     */
    private void generateRelationRelevance(List<JSONObject> relations, TripleLabelData tripleLabelData, Map<String, Label> labelMap) {

        if (ObjectUtil.isEmpty(tripleLabelData.getEndLabelDataId())) return;
        // 创建关系对象
        JSONObject relationJsonObject = new JSONObject();
        // 添加关系三元组的Id
        relationJsonObject.putOpt("id", tripleLabelData.getId());
        // 如果关系有属性的话就添加
        if (ObjectUtil.isNotEmpty(tripleLabelData.getRelationProperties())) {
            JSONArray relationArray = new JSONArray();
            for (LabelPropertyVO relationProperty : tripleLabelData.getRelationProperties()) {
                JSONObject relationPropertyObject = new JSONObject(relationProperty);
                // 根据关系标签Id获取关系标签名称
                String labelPropertyId = relationProperty.getLabelPropertyId();
                Label label = getLabel(labelMap, labelPropertyId);
                if (label == null) throw new ServiceException("数据集标签不存在，请检查数据集，并修改后重新加工该资源！");
                relationPropertyObject.putOpt("labelName", label.getName());
                // 层级三
                relationPropertyObject.putOpt("level", 4);
                // 添加关系的属性
                relationArray.add(relationPropertyObject);
            }
            relationJsonObject.putOpt("children", relationArray);
        }

        // 谓词标签Id
        relationJsonObject.putOpt("relationLabelId", tripleLabelData.getRelationLabelId());
        // 谓词标签名称
        String relationLabelId = tripleLabelData.getRelationLabelId();
        Label relationLabel = getLabel(labelMap, relationLabelId);
        if (relationLabel == null) throw new ServiceException("数据集标签不存在，请检查数据集，并修改后重新加工该资源！");
        relationJsonObject.putOpt("relationLabelName", relationLabel.getName());
        // 宾语的标签Id
        relationJsonObject.putOpt("labelId", tripleLabelData.getEndLabelId());
        // 宾语的标签名称
        String endLabelId = tripleLabelData.getEndLabelId();
        Label label = getLabel(labelMap, endLabelId);
        if (label == null) throw new ServiceException("数据集标签不存在，请检查数据集，并修改后重新加工该资源！");
        relationJsonObject.putOpt("labelName", label.getName());
        // 宾语的标签数据Id
        relationJsonObject.putOpt("labelDataId", tripleLabelData.getEndLabelDataId());
        // 宾语的标签数据名称
        log.info(" -------------------  「{}」", tripleLabelData);
        LabelData endLabelData = Optional.ofNullable(labelDataRepository.findById(tripleLabelData.getEndLabelDataId()))
                .orElseThrow(() -> new ServiceException("当前标签数据不存在!"));
        relationJsonObject.putOpt("labelDataName", endLabelData.getContent());
        // 添加层级
        relationJsonObject.putOpt("level", 3);
        // 添加关系
        relations.add(relationJsonObject);
    }

    private Label getLabel(Map<String, Label> labelMap, String labelId) {
        Label label = labelMap.get(labelId);
        if (label == null) {
            try {
                label = labelRepository.findById(labelId);
            } catch (Exception e) {
                return null;
            }
            labelMap.put(labelId, label);
        }
        return label;
    }

    public JSONArray relevanceShowAnnex(String annexId, String dataSetId) {
        Map<String, List<JSONObject>> labelDatamap = entitySortByKeyValue(dataSetId);
        // 获取该资源的所有标签数据·
        List<AnnexLabelData> annexLabelDataList = annexLabelDataRepository.findByCondition(Filters.eq("annexId", annexId));
        // 遍历所以标签数据
        for (AnnexLabelData annexLabelData : annexLabelDataList) {
            // 创建数组存储关系
            List<JSONObject> relations = new ArrayList<>();
            // 把标签数据的属性放在和关系平级的位置
            // 给属性封装名称
            for (LabelPropertyVO labelProperty : annexLabelData.getLabelProperties()) {
                // 获取属性标签
                Label propertyLabel = Opt.ofNullable(labelRepository.findById(labelProperty.getLabelPropertyId())).orElseThrow(ServiceException::new, "该标签不存在!");
                // 为属性设置标签名称
                labelProperty.setLabelPropertyName(propertyLabel.getName());
                // 把属性封装为指定JSONObject
                JSONObject labelDataPropertyObject = new JSONObject(labelProperty);
                // 添加层级
                labelDataPropertyObject.putOpt("level", 3);
                // 把属性放在关系前面
                if (ObjectUtil.isNotEmpty(labelDataPropertyObject)) relations.add(labelDataPropertyObject);
            }
            // 获取每个数据标签 作为主语的 关系三元组
            List<AnnexTripleLabelData> annexTripleLabelDataList = annexTripleLabelDataRepository.findByConditionAndSorted(Filters.eq("startLabelDataId", annexLabelData.getId()), Filters.eq(AbstractBaseEntityFieldConstant.CREATE_TIME, -1));
            for (AnnexTripleLabelData annexTripleLabelData : annexTripleLabelDataList) {
                // 生成关系对象
                generateRelation(relations, annexTripleLabelData);
            }
            // 给标签数据的标签Id封装为labelName
            Label conceptLabel = Opt.ofNullable(labelRepository.findById(annexLabelData.getLabelId())).orElseThrow(ServiceException::new, "该标签不存在");
            annexLabelData.setLabelName(conceptLabel.getName());
            // 转换为JSONObject
            JSONObject labelDataJsonObject = new JSONObject(annexLabelData);
            // 添加 层级
            labelDataJsonObject.putOpt("level", 2);
            // 添加关系
            labelDataJsonObject.putOpt("children", relations);
            // 根据标签Id查询标签对象，并获取标签名称
            Label label = labelRepository.findById(annexLabelData.getLabelId());
            // 添加结果
            List<JSONObject> list = labelDatamap.getOrDefault(label.getName(), new ArrayList<>());
            list.add(labelDataJsonObject);
            labelDatamap.put(label.getName(), list);

        }
        // 封装标签数据
        JSONArray jsonArray = new JSONArray();
        Set<String> keySet = labelDatamap.keySet();
        for (String key : keySet) {
            JSONObject jsonObject = new JSONObject();
            // 获取标签ID
            String labelId = labelDatamap.get(key).get(0).getStr("labelId");
            jsonObject.putOpt("labelId", labelId);
            jsonObject.putOpt("labelName", key);
            jsonObject.putOpt("children", labelDatamap.get(key));
            jsonObject.putOpt("level", 1);
            jsonArray.add(jsonObject);
        }
        // 返回结果
        return jsonArray;
    }

    private void generateRelation(List<JSONObject> relations, AnnexTripleLabelData annexTripleLabelData) {
        // 创建关系对象
        JSONObject relationJsonObject = new JSONObject();
        // 添加关系三元组的Id
        relationJsonObject.putOpt("id", annexTripleLabelData.getId());
        // 如果关系有属性的话就添加
        if (ObjectUtil.isNotEmpty(annexTripleLabelData.getRelationProperties())) {
            JSONArray relationArray = new JSONArray();
            for (LabelPropertyVO relationProperty : annexTripleLabelData.getRelationProperties()) {
                JSONObject relationPropertyObject = new JSONObject(relationProperty);
                // 根据关系标签Id获取关系标签名称
                Label label = Opt.ofNullable(labelRepository.findById(relationProperty.getLabelPropertyId())).orElseThrow(ServiceException::new, "当前标签不存在!");
                relationPropertyObject.putOpt("labelName", label.getName());
                // 层级三
                relationPropertyObject.putOpt("level", 4);
                // 添加关系的属性
                relationArray.add(relationPropertyObject);
            }
            relationJsonObject.putOpt("children", relationArray);
        }

        // 谓词标签Id
        relationJsonObject.putOpt("relationLabelId", annexTripleLabelData.getRelationLabelId());
        // 谓词标签名称
        Label relationLabel = Optional.ofNullable(labelRepository.findById(annexTripleLabelData.getRelationLabelId()))
                .orElseThrow(() -> new ServiceException("当前标签不存在!"));
        relationJsonObject.putOpt("relationLabelName", relationLabel.getName());
        // 宾语的标签Id
        relationJsonObject.putOpt("labelId", annexTripleLabelData.getEndLabelId());
        // 宾语的标签名称
        Label label = Optional.ofNullable(labelRepository.findById(annexTripleLabelData.getEndLabelId()))
                .orElseThrow(() -> new ServiceException("当前标签不存在!"));
        relationJsonObject.putOpt("labelName", label.getName());
        // 宾语的标签数据Id
        relationJsonObject.putOpt("labelDataId", annexTripleLabelData.getEndLabelDataId());
        // 宾语的标签数据名称
        AnnexLabelData endLabelData = Optional.ofNullable(annexLabelDataRepository.findById(annexTripleLabelData.getEndLabelDataId()))
                .orElseThrow(() -> new ServiceException("当前标签数据不存在!"));
        relationJsonObject.putOpt("labelDataName", endLabelData.getContent());
        // 添加层级
        relationJsonObject.putOpt("level", 3);
        // 添加关系
        relations.add(relationJsonObject);
    }

    private final IResourceAnnexService iResourceAnnexService;

    private void mergeScriptAnnex(Map<String, Object> pageMap) {

        Bson filter = Condition.getFilter(pageMap, Resource.class, Map.of(FiledNameConst.WORK_TASK_ID, FilterTypeConst.EQ));
        // 获取开始和结束时间
        Date startTime = Convert.toDate(pageMap.getOrDefault("startTime", Integer.MIN_VALUE));
        Date endTime = Convert.toDate(pageMap.getOrDefault("endTime", Long.MAX_VALUE));
        // 构建条件
        Bson timeFilter = Filters.and(Filters.gte(AbstractBaseEntityFieldConstant.CREATE_TIME, startTime), Filters.lte(AbstractBaseEntityFieldConstant.CREATE_TIME, endTime));
        // 构建最新条件
        Bson newFilter = Filters.and(filter, timeFilter);
        List<Resource> resources = resourceRepository.findByConditionAndSorted(newFilter, Filters.eq(AbstractBaseEntityFieldConstant.UPDATE_TIME, -1));

        log.info("开始附件合并脚本======数据量为：「{}」", resources.size());


        final List<String> collect = resources.stream().map(org.irm.lab.repository.entity.Resource::getId).collect(Collectors.toList());

        final ArrayList<ResourceAnnex> objects = new ArrayList<>();
        collect.stream().forEach(data -> {
            final List<ResourceAnnex> resourceAnnexes = iResourceAnnexService.listByResourceId(data);
            objects.addAll(resourceAnnexes);
        });
        final ArrayList<String> strings = new ArrayList<>();

        objects.forEach(data -> {
            List<AnnexDocumentUnit> annexDocumentUnitList = annexDocumentUnitRepository.findByCondition(Filters.eq("annexId", data.getId()));
            annexDocumentUnitList.stream().map(AnnexDocumentUnit::getId).forEach(strings::add);
        });

        final List<AnnexDocumentUnit> byId = annexDocumentUnitRepository.findById(strings);

        Map<String, List<AnnexDocumentUnit>> sortedGroupedMap = byId.stream()
                .collect(Collectors.groupingBy(
                        AnnexDocumentUnit::getAnnexId,
                        Collectors.collectingAndThen(
                                Collectors.toList(),
                                list -> {
                                    list.sort(Comparator.comparing(AnnexDocumentUnit::getPage));
                                    return list;
                                }
                        )
                ));
        sortedGroupedMap.forEach((resourceId, documentUnits) -> {

            final StringBuffer stringBuffer = new StringBuffer();
            documentUnits.forEach(data -> {

                stringBuffer.append(data.getContent());
            });
            AnnexDocumentUnit documentUnit = new AnnexDocumentUnit();
            documentUnit.setAnnexId(resourceId);

            documentUnit.setSortInCurrentPage(1);
            documentUnit.setPage(1);
            documentUnit.setType("0");
            documentUnit.setContent(stringBuffer.toString());

            log.info("【附件】{} 开始删除", documentUnits);
            removeAnnex(documentUnits.stream().map(AnnexDocumentUnit::getId).collect(Collectors.toList()));
            annexDocumentUnitRepository.save(documentUnit);

        })
        ;
        ;

    }

    /**
     * 合并语料脚本
     */
    private final HttpServletRequest request;


    @javax.annotation.Resource
    private EsSyncProducer esSyncProducer;
    private final ResourceAnnexRepository resourceAnnexRepository;
    private final AnnexDocumentUnitRepository annexDocumentUnitRepository;
    private final ResourceRepository resourceRepository;

    public void mergeScript(Map<String, Object> pageMap) {

        Bson filter = Condition.getFilter(pageMap, Resource.class, Map.of(FiledNameConst.WORK_TASK_ID, FilterTypeConst.EQ));
        // 获取开始和结束时间
        Date startTime = Convert.toDate(pageMap.getOrDefault("startTime", Integer.MIN_VALUE));
        Date endTime = Convert.toDate(pageMap.getOrDefault("endTime", Long.MAX_VALUE));
        // 构建条件
        Bson timeFilter = Filters.and(Filters.gte(AbstractBaseEntityFieldConstant.CREATE_TIME, startTime), Filters.lte(AbstractBaseEntityFieldConstant.CREATE_TIME, endTime));
        // 构建最新条件
        Bson newFilter = Filters.and(filter, timeFilter);
        List<Resource> resources = resourceRepository.findByConditionAndSorted(newFilter, Filters.eq(AbstractBaseEntityFieldConstant.UPDATE_TIME, -1));

        log.info("开始合并脚本======数据量为：「{}」", resources.size());


        final List<String> collect = resources.stream().map(org.irm.lab.repository.entity.Resource::getId).collect(Collectors.toList());
        final ArrayList<String> strings = new ArrayList<>();
        collect.stream().forEach(data -> {
            final List<DocumentUnit> byId = documentUnitRepository.findByCondition(Condition.getFilter(Map.of("resourceId", data), DocumentUnit.class));
            byId.stream().map(DocumentUnit::getId).forEach(strings::add);

        });
        final List<DocumentUnit> byId = documentUnitRepository.findById(strings);

        Map<String, List<DocumentUnit>> sortedGroupedMap = byId.stream()
                .collect(Collectors.groupingBy(
                        DocumentUnit::getResourceId,
                        Collectors.collectingAndThen(
                                Collectors.toList(),
                                list -> {
                                    list.sort(Comparator.comparing(DocumentUnit::getPage));
                                    return list;
                                }
                        )
                ));
        sortedGroupedMap.forEach((resourceId, documentUnits) -> {

            final StringBuffer stringBuffer = new StringBuffer();
            documentUnits.forEach(data -> {

                stringBuffer.append(data.getContent());
            });
            DocumentUnit documentUnit = new DocumentUnit();
            documentUnit.setResourceId(resourceId);

            documentUnit.setSortInCurrentPage(1);
            documentUnit.setPage(1);
            documentUnit.setType("0");
            documentUnit.setContent(stringBuffer.toString());

            log.info("【主文件】{} 开始删除", documentUnits);
            remove(documentUnits.stream().map(DocumentUnit::getId).collect(Collectors.toList()));
            documentUnitRepository.save(documentUnit);
            esSyncProducer.sendMessage(request.getHeader("user"), resourceRepository.findById(resourceId), null, stringBuffer.toString(), "主文件");

        })
        ;
        ;

    }


    public void deleteES(List<String> ids) {


        List<Resource> resources = resourceRepository.findById(ids);

        log.info("开始合并脚本======数据量为：「{}」", resources.size());

        final List<String> collect = resources.stream().map(org.irm.lab.repository.entity.Resource::getId).collect(Collectors.toList());

        AtomicInteger count = new AtomicInteger();
        AtomicInteger mongo = new AtomicInteger();

        collect.forEach(data -> {
            final R<Integer> integerR = documentElasticSearchFeign.deleteById(data);

            final Long l = resourceRepository.deleteById(data);
            mongo.addAndGet(l.intValue());
            if (integerR.getCode() == 200) {
                count.getAndIncrement();
            }
        });

        log.info("成功删除es数量为：「{}」", count.get());
        log.info("成功删除mongo数量为：「{}」", mongo.get());

    }

    public void remove(List<String> ids) {
        for (String id : ids) {
            documentUnitRepository.deleteByIdFake(id);
        }
    }

    public void removeAnnex(List<String> ids) {
        for (String id : ids) {
            annexDocumentUnitRepository.deleteByIdFake(id);
        }
    }

    /**
     * 资源删除
     *
     * @param ids 资源id
     * @return {@link R}
     */
    @MyLog(menu = LogConstant.MENU_RESOURCE, dataType = LogConstant.DATA_RESOURCE, operation = LogConstant.OPERATION_REMOVE)
    @ApiOperation(value = "资源删除")
    @PostMapping("/remove")
    public R<Void> remove(@RequestBody String ids) {
        iResourceSubmitService.remove(Func.objToStrList(ids));
        return R.success();
    }


    private void script() {
        List<Resource> resources = resourceService.listByWorkTaskId("66178064987f997086dc707a");
        resources.forEach(resource -> {
            try {
                String url = "http://***********:9000/kunify-hn-028641/" + resource.getPdfAttachName();
                File pdfFile = FileUtil.downUrl2File(url, TempPathConfig.currentPath());
                try {
                    PDDocument document = PDDocument.load(pdfFile);
                    PDFTextStripper pdfStripper = new PDFTextStripper();

                    pdfStripper.setStartPage(1);
                    pdfStripper.setEndPage(1);

                    String text = pdfStripper.getText(document);
                    log.info("读取内容为：======{}", text);
                    if (!text.contains("总则") && !text.contains("第一条")) {
                        log.info("当前文件可能乱码：======={}", pdfFile.getName());
                        exceptionFileProcess(pdfFile);
                    }
                } catch (IOException e) {
                    e.printStackTrace();
                    log.info("当前文件读取报错：======{}", pdfFile.getName());
                    errorFileProcess(resource);
                }
            } catch (Exception e) {
                e.printStackTrace();
                log.error("当前文件无法缓存到本地======{}", resource.getName());
                errorFileProcess(resource);
            }

        });
    }

    private static void exceptionFileProcess(File errorFile) {
        File file = new File(TempPathConfig.currentPath() + "/target");
//        File file = new File("/Users/<USER>/Desktop/华能/权限方案/target");
        if (!file.exists()) {
            file.mkdirs();
        }
        cn.hutool.core.io.FileUtil.copy(errorFile.toPath(), file.toPath());
        log.info("copy文件完成：====={}======{}", errorFile.getName(), file.getPath());
    }

    private static void errorFileProcess(Resource resource) {
        try {
            File file = new File(TempPathConfig.currentPath() + "/target/" + "a.txt");
            if (!file.exists()) {
                file.createNewFile();
            }
            BufferedWriter bw = new BufferedWriter(new FileWriter(file, true));
            bw.write(resource + "=============" + resource.getPdfAttachName());
            bw.newLine();
            bw.flush();
            bw.close();
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    public static void main(String[] args) {
        String pdfFilePath = "/Users/<USER>/Desktop/华能/权限方案/系统权限.pdf"; // 替换为你的PDF文件路径
        PDDocument document = null;
        try {
            File file = new File(pdfFilePath);
            document = PDDocument.load(file);
            PDFTextStripper pdfStripper = new PDFTextStripper();

            pdfStripper.setStartPage(1);
            pdfStripper.setEndPage(1);

            String text = pdfStripper.getText(document);

            System.out.println("text = " + text);
            System.out.println(text.contains("系统"));
            exceptionFileProcess(file);

        } catch (IOException e) {
            e.printStackTrace();
        }
    }


    /**
     * 删除外部导入的资源
     *
     * @param resourceId 资源Id
     */
    @MyLog(menu = LogConstant.MENU_RESOURCE, dataType = LogConstant.DATA_IMPORT_RESOURCE, operation = LogConstant.OPERATION_REMOVE)
    @ApiOperation(value = "删除外部导入的资源")
    @GetMapping("/remove-import")
    public R<Void> removeImport(@RequestParam String resourceId) {
        iResourceSubmitService.removeImportResource(resourceId);
        return R.success();
    }

    @ApiOperation(value = "批量统计数量")
    @PostMapping("/batchCount")
    public R<List<WorkTaskVO>> batchCount(@RequestBody SearchDTO searchDTO) {
        return R.data(iResourceSubmitService.batchCount(searchDTO.getWorkTaskVOS(), searchDTO.getStatusList(), searchDTO.getStatus()));
    }

    /**
     * 获取该资源应有的元数据项列表
     *
     * @param workTaskId 工作任务Id
     * @return {@link MetadataItem}
     */
    @ApiOperation(value = "资源元数据列表")
    @GetMapping("/list-metadata")
    public R<MetadataSchema> listMetadata(@RequestParam("workTaskId") String workTaskId) {
        // 根据工作任务Id获取元数据方案Id
        try {
            R<WorkTask> workTaskR = workTaskFeign.info(workTaskId);
            if (ObjectUtil.isNotEmpty(workTaskR)) {
                String metadataSchemaId = workTaskR.getData().getProcess().getMetadataSchemaId();
                // 获取元数据项复制体列表
                MetadataSchema metadataSchema = metadataSchemaFeign.info(metadataSchemaId).getData();
                // 获取元数据项列表，对元数据项列表进行排序
                List<MetadataItem> metadataItemList = metadataSchema.getMetadata();
                metadataItemList.sort(Comparator.comparingInt(MetadataItem::getSort));
                metadataSchema.setMetadata(metadataItemList);
                // 返回排序后的元数据方案
                return R.data(metadataSchema);
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        return R.failed("不存在指定工作任务");
    }

    /**
     * 新增资源对象
     *
     * @param resource {@link Resource}
     * @return {@link Resource}
     */
    @MyLog(menu = LogConstant.MENU_RESOURCE, dataType = LogConstant.DATA_WORK_RESOURCE, operation = LogConstant.OPERATION_ADD)
    @ApiOperation(value = "任务资源新增")
    @PostMapping("/save")
    public R<Resource> save(@RequestBody Resource resource) {
        return R.data(iResourceSubmitService.saveResource(resource));
    }

    @ApiOperation(value = "查询单个资源")
    @GetMapping("/info")
    public R<Resource> info(@RequestParam String id) {
        return R.data(iResourceSubmitService.findById(id));
    }

    /**
     * 手动著录元数据项 / 新增条目
     *
     * @param resource 资源对象
     * @return {@link  R}
     */
    @MyLog(menu = LogConstant.MENU_RESOURCE, dataType = LogConstant.DATA_METADATA, operation = LogConstant.OPERATION_RECORD)
    @ApiOperation(value = "手动著录元数据项", hidden = true)
    @PostMapping("/record-metadata")
    public R<Resource> recordMetadata(@RequestBody Resource resource) {
        if (ObjectUtil.isEmpty(resource.getId())) {
            // 新增的条目，应该默认设置为“待人工著录”
            resource.setStage(RecordResourceType.STAGE_3);
            // 资源类型设置为未知
            resource.setMediaType(MediaType.UN_KNOW.getType());
        }
        return R.data(iResourceSubmitService.recordResource(resource));
    }

    /**
     * 批量著录元数据项
     *
     * @param recordMetadataBatchDTO {@link RecordMetadataBatchDTO}
     */
    @PostMapping("/record-metadata-batch")
    @MyLog(menu = LogConstant.MENU_RESOURCE, dataType = LogConstant.DATA_METADATA, operation = LogConstant.OPERATION_RECORD)
    public R<Void> recordMetadataBatch(@RequestBody RecordMetadataBatchDTO recordMetadataBatchDTO) {
        iResourceSubmitService.recordMetadataBatch(recordMetadataBatchDTO);
        return R.success();
    }


    /**
     * 开启资源元数据校验
     *
     * @param resourceId 资源Id
     * @return {@link R}
     */
    @ApiOperation(value = "开启资源元数据校验", hidden = true)
    @GetMapping("/verification")
    public R<ResourceVerification> verification(@RequestParam("resourceId") String resourceId) {
        // 根据资源id获取资源对象
        Resource resource = iResourceSubmitService.findById(resourceId);
        return R.data(iResourceVerificationService.resourceVerification(resource));
    }

    /**
     * 根据资源Id获取该资源的最新元数据校验版本信息
     *
     * @param resourceId 资源Id
     * @return {@link ResourceVerification}
     */
    @ApiOperation(value = "获取资源最新元数据校验版本信息", hidden = true)
    @GetMapping("/verification-info")
    public R<ResourceVerification> verificationInfo(@RequestParam("resourceId") String resourceId) {
        return R.data(iResourceVerificationService.info(resourceId));
    }

    /**
     * 删除任务监控中心任务
     *
     * @param taskMonitoringDTO {@link TaskMonitoringDTO}
     */
    @ApiOperation(value = "删除任务监控中心任务", hidden = true)
    @PostMapping("/remove-task-monitoring")
    public R<String> removeTaskMonitoring(@RequestBody TaskMonitoringDTO taskMonitoringDTO) {
        // 1、清空任务中心
        // 只需要提交 当前工作任务Id
        // 2、删除指定的 批量提交任务
        // 需要提交指定的 批量提交任务Id
        // 3、删除指定的 分步提交任务
        // 需要提交指定 资源Id
        taskMonitoringCenter.removeTaskMonitoring(taskMonitoringDTO);
        return R.success();
    }

    /**
     * 根据查询条件，获取指定的资源列表
     *
     * @param filterMap 查询条件
     * @return {@link Resource}
     */
    @ApiOperation(value = "条件查询", hidden = true)
    @GetMapping("/list-by-condition")
    public R<List<Resource>> listByCondition(@RequestParam(required = false) Map<String, Object> filterMap) {
        return R.data(iResourceSubmitService.listByCondition(filterMap));
    }

    /**
     * 根据查询条件，获取指定的资源列表总和
     *
     * @param filterMap 查询条件
     * @return {@link Resource}
     */
    @ApiOperation(value = "条件查询", hidden = true)
    @GetMapping("/list-by-condition-count")
    public R<Long> listByConditionCount(@RequestParam(required = false) Map<String, Object> filterMap) {
        return R.data(iResourceSubmitService.listByConditionCount(filterMap));
    }

    @ApiOperation(value = "任务条件查询document", hidden = true)
    @GetMapping("/task_find_document")
    public List<Document> taskFindDocument(@RequestParam(required = false) Map<String, Object> filterMap) {
        return iResourceSubmitService.taskFindDocument(filterMap);
    }

    /**
     * 根据条件统计资源数量
     *
     * @param filterMap 条件Map
     * @return 资源数量
     */
    @ApiOperation(value = "根据条件统计资源数量")
    @GetMapping("/count-by-condition")
    public R<Long> countByCondition(@RequestParam(required = false) Map<String, Object> filterMap) {
        return R.data(iResourceSubmitService.countByCondition(filterMap));
    }

    /**
     * 根据多个id，获取多个资源对象
     *
     * @param ids id列表
     * @return {@link Resource}
     */
    @Override
    @GetMapping("/list-by-ids")
    public R<List<Resource>> listByIds(@RequestParam Set<String> ids) {
        return R.data(iResourceSubmitService.listByIds(ids));
    }

    @ApiOperation(value = "时间区间查询", hidden = true)
    @PostMapping("/list-by-time")
    public R<List<Resource>> listByCreateTime(@RequestParam Date startTime, @RequestParam Date endTime) {
        return R.data(iResourceSubmitService.listByCreateTime(startTime, endTime));
    }

    /**
     * 根据公文分类查询文件ids
     *
     * @param classify 公文分类
     * @return 资源ids
     */
    @ApiOperation(value = "根据公文分类查询文件ids", hidden = true)
    @PostMapping("/document-ids-by-classify")
    public R<List<String>> documentIdsByClassify(String classify) {
        return R.data(iResourceSubmitService.documentIdsByClassify(classify));
    }


    /**
     * 更新资源对象的某个字段
     *
     * @param map 条件Map
     * @return {@link Resource}
     */
    @GetMapping("/update-one")
    @Override
    public R<Resource> updateOne(@RequestParam(required = false) Map<String, Object> map) {
        return R.data(iResourceSubmitService.updateOne(map));
    }

    @PostMapping("/word2pdf-resource")
    @Override
    public R<Attach> word2pdf(@RequestBody org.irm.lab.repository.entity.Resource resource) {
        if (ResourceType.WORD.equals(ResourceType.autoJudge(resource.getMediaType())))
            scalePicService.wordToPdf(resource, attachFeign.info(resource.getPrimaryFileId()).getData());
        return attachFeign.info(resource.getPrimaryFileId());
    }
}
