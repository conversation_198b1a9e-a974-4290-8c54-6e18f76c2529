package org.irm.lab.repository.controller;

import cn.hutool.core.convert.Convert;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONObject;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.irm.lab.common.annotation.MyLog;
import org.irm.lab.common.api.R;
import org.irm.lab.common.constant.LogConstant;
import org.irm.lab.common.entity.AbstractBaseEntity;
import org.irm.lab.common.enums.MediaType;
import org.irm.lab.common.pojo.BaseStatus;
import org.irm.lab.common.support.Condition;
import org.irm.lab.common.support.MyPage;
import org.irm.lab.common.utils.Func;
import org.irm.lab.front.feign.DocumentElasticSearchFeign;
import org.irm.lab.front.model.Document;
import org.irm.lab.kg.dto.ReParsDTO;
import org.irm.lab.kg.feign.DocumentParsingFeign;
import org.irm.lab.kg.feign.NodeEntityFeign;
import org.irm.lab.repository.constant.ResourceStageConstant;
import org.irm.lab.repository.entity.Resource;
import org.irm.lab.repository.entity.datasource.DatasourceClassification;
import org.irm.lab.repository.repository.ResourceRepository;
import org.irm.lab.repository.service.*;
import org.irm.lab.repository.sync.bean.RowMetaBean;
import org.irm.lab.repository.sync.builder.ExcelAddress;
import org.irm.lab.repository.sync.constant.ExcelConstant;
import org.irm.lab.repository.sync.handler.SyncMetaManager;
import org.irm.lab.repository.sync.utils.ExcelUtils;
import org.irm.lab.repository.vo.ResourceStageInfoVO;
import org.irm.lab.repository.vo.ResourceVO;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.*;

import java.io.FileNotFoundException;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2023/2/13 11:34
 * @description 资源著录控制器
 */
@RestController
@RequestMapping("/resource-record")
@Api(value = "资源著录控制器")
@RequiredArgsConstructor
public class ResourceRecordController {
    public static final String[] NOT_SYNC_FILE = {"办理单", "办理过程", "稿纸", "痕迹", "发文单"};

    private static final Logger log = LoggerFactory.getLogger(ResourceRecordController.class);
    private final IResourceRecordService iResourceRecordService;
    private final ResourceRepository resourceRepository;
    private final IResourceService resourceService;
    private final SyncMetaManager syncMetaManager;
    private final IDatasourceClassificationService classificationService;
    private final NodeEntityFeign nodeEntityFeign;
    private final IResourceAfterProcessService resourceAfterProcessService;
    private final DocumentElasticSearchFeign documentElasticSearchFeign;
    private final DocumentParsingFeign documentParsingFeign;

    /**
     * 工作任务绑定的流程上，获取该流程可用的资源类型列表
     * TODO 先返回假数据
     *
     * @return {@link List}
     */
    @ApiOperation(value = "获取流程可用的资源类型列表")
    @GetMapping("/list-media-type")
    public R<JSONObject> listMediaType(@RequestParam("workTaskId") String workTaskId) {
        return R.data(MediaType.list());
    }


    /**
     * 资源著录环节信息展示
     *
     * @param workTaskId 工作任务Id
     * @return {@link ResourceStageInfoVO}
     */
    @ApiOperation(value = "资源著录环节信息展示")
    @GetMapping("/stage-info")
    public R<ResourceStageInfoVO> stageInfo(@RequestParam("workTaskId") String workTaskId) {
        return R.data(iResourceRecordService.stageInfo(workTaskId));
    }

    /**
     * 获取当前工作任务下，所有“待著录”和“已驳回”的资源
     *
     * @param pageMap 查询条件
     * @return {@link ResourceVO}
     */
    @ApiOperation(value = "获取当前任务下的资源")
    @GetMapping("/list")
    public R<MyPage<ResourceVO>> list(@RequestParam Map<String, Object> pageMap) throws FileNotFoundException {
        if ("更新规章制度元数据".equals(pageMap.get("name"))) {
            resourceService.updateGZZDMeta();
        }
        if("更新规章制度生效日期".equals(pageMap.get("name"))) {
            resourceService.updateGZZDSxrq2Graph();
        }
        if ("更新规章制度年度".equals(pageMap.get("name"))) {
            resourceService.updateGZZDND(pageMap.get("workTaskId").toString());
        }
        if ("更新规章制度文号双后缀".equals(pageMap.get("name"))) {
            resourceService.updateGZZDWenHaoAndName(pageMap.get("workTaskId").toString());
        }
        if ("导出文件id到excel".equals(pageMap.get("name"))) {
            syncMetaManager.initExcelEnvironment(new ExcelAddress());
            resourceService.exportFileId(pageMap.get("workTaskId").toString());
        }
        if ("读取excel导入数据".equals(pageMap.get("name"))) {
            syncMetaManager.initExcelEnvironment(new ExcelAddress());
            resourceService.importResourceByExcel(pageMap.get("workTaskId").toString());
        }
        if ("读取excel更新所有元数据".equals(pageMap.get("name"))) {
            syncMetaManager.initExcelEnvironment(new ExcelAddress());
            syncMetaManager.handle(syncMetaManager.initSyncMetaHandler());
        }
        if ("根据任务拆分excel表".equals(pageMap.get("name"))) {
            //将总表拆分，拆出包含当前任务下资源的子表
            syncMetaManager.initExcelEnvironment(new ExcelAddress());
            syncMetaManager.splitExcelByTask(pageMap.get("workTaskId").toString());
        }
        if ("读取excel更新任务下元数据".equals(pageMap.get("name"))) {
            String workTaskId = pageMap.get("workTaskId").toString();
            //根据任务更新元数据，需要先拆分excel表
            syncMetaManager.initExcelEnvironment(new ExcelAddress());
            syncMetaManager.handle(syncMetaManager.initSyncMetaHandler(ExcelConstant.splitMatchDataExcel + "/" + workTaskId + ".xlsx"));
        }
        if ("批量通过著录".equals(pageMap.get("name"))) {
            String workTaskId = pageMap.get("workTaskId").toString();
            List<Resource> resources = resourceService.listByWorkTaskId(workTaskId);
            iResourceRecordService.handover(resources.stream().map(AbstractBaseEntity::getId).collect(Collectors.toList()));
        }
        if ("补充规章制度节点信息".equals(pageMap.get("name"))) {
            resourceService.supplyGZZDNodeInfo();
        }

        if ("补充全量规章制度节点信息".equals(pageMap.get("name"))) {
            resourceService.supplyAllGZZDNodeInfo();
        }

        if ("批量精确文档解析".equals(pageMap.get("name"))) {
            String workTaskId = pageMap.get("workTaskId").toString();
//            List<String> collect = resourceService.listByWorkTaskId(workTaskId).stream().map(AbstractBaseEntity::getId).collect(Collectors.toList());

            List<String> collect = resourceService.listByWorkTaskIdWithoutES(workTaskId).stream()
                    .filter(resourceVO -> resourceVO.getMediaType() != null)
                    .filter(resourceVO -> resourceVO.getName() != null)
                    .filter(resourceVO -> Arrays.stream(NOT_SYNC_FILE).noneMatch(ele -> resourceVO.getName().contains(ele)))
                    .map(AbstractBaseEntity::getId).collect(Collectors.toList());

            ReParsDTO reParsDTO = new ReParsDTO();
            reParsDTO.setResourceIds(collect);
            reParsDTO.setType("精确");
            log.info("共有【{}】份文件", collect.size());
            documentParsingFeign.allReParsing(reParsDTO);
        }


        if("排查未生成图谱制度文件".equals(pageMap.get("name"))) {
            resourceService.issueGzzdNode();
        }

        if("制度更新正文附件".equals(pageMap.get("name"))) {
            resourceService.issueGzzdAnnexReplaceArchive();
        }

        Object name = pageMap.get("name");

        if (ObjectUtil.isNotEmpty(name) && name.toString().contains("更新图谱元数据:")) {
            String nodeId = "";
            String conceptName = name.toString().split(":")[1];
            if (name.toString().split(":").length > 2) {
                nodeId = name.toString().split(":")[2];
            }
            resourceService.updateGraphMeta(conceptName, nodeId);
        }

        if (ObjectUtil.isNotEmpty(name) && name.toString().contains("测试分组全名称-")) {
            String fenzu = name.toString().split("-")[1];
            getFenZuFullName(fenzu);
        }

        if (ObjectUtil.isNotEmpty(name) && name.toString().contains("同步删除AI中台数据-")) {
            String[] split = name.toString().split("-");
            resourceService.deleteResourceWithQADoc(Integer.parseInt(split[1]), Integer.parseInt(split[2]));
        }

        if (ObjectUtil.isNotEmpty(name) && name.toString().contains("补充实体关系-")) {
            String[] split = name.toString().split("-");
            String resourceId = split[1];
            String predicate1 = "提及机构/部门";
            String predicate2 = "";
            if (split.length > 2) {
                predicate1 = split[2];
            }
            if (split.length > 3) {
                predicate2 = split[3];
            }
            nodeEntityFeign.scriptSupplyNodeAndRelation(resourceId, predicate1, predicate2);
        }

        if (ObjectUtil.isNotEmpty(name) && name.toString().contains("通过任务补充实体关系-")) {
            String workTaskId = pageMap.get("workTaskId").toString();
            String[] split = name.toString().split("-");
            String predicate1 = "提及机构/部门";
            String predicate2 = "";
            if (split.length > 1) {
                predicate1 = split[1];
            }
            if (split.length > 2) {
                predicate2 = split[2];
            }
            nodeEntityFeign.scriptSupplyNodeAndRelationWork(workTaskId, predicate1, predicate2);
        }


        if (ObjectUtil.isNotEmpty(name) && name.toString().contains("元数据名称-")) {

            String metaName = name.toString().split("-")[1];
            Map<String, Long> metaValues = documentElasticSearchFeign.getTypeCountVOS(metaName).getData();
            log.info("元数据名称：【{}】", metaName);
            metaValues.forEach((key, value) -> {
                log.info("元数据值域：【{}】", key);
                log.info("元数据个数：【{}】", value);
            });
        } else if (ObjectUtil.isNotEmpty(name) && name.toString().contains("在Mongodb中查询元数据-")) {
            String metaName = name.toString().split("-")[1];
            List<Object> metaValues = iResourceRecordService.getTypeCountVOS(metaName);
            log.info("元数据名称：【{}】", metaName);
            metaValues.forEach(StrUtil::toString);
        }

        if ("补充es数据".equals(pageMap.get("name"))) {
            String workTaskId = pageMap.get("workTaskId").toString();
            List<Resource> resources = resourceService.listByWorkTaskId(workTaskId);
            List<Resource> prepareSupplyResource = resources.stream()
                    .filter(resource -> BaseStatus.TRUE.equals(resource.getStored()))
                    .filter(resource -> ObjectUtil.isEmpty(documentElasticSearchFeign.info(resource.getId()).getData()))
                    .collect(Collectors.toList());
            log.info("待补充es数据个数为：【{}】", prepareSupplyResource.size());
            resourceAfterProcessService.afterProcess(prepareSupplyResource.stream().map(AbstractBaseEntity::getId).collect(Collectors.toList()));
        }

        if (ObjectUtil.isNotEmpty(name) && name.toString().contains("题名与文件名-")) {
            String deviationFactor = name.toString().split("-")[1];
            resourceService.findDeviationTimingAndFileName(Integer.parseInt(deviationFactor));
        }

        // 获取分页条件
        Integer page = Convert.toInt(pageMap.getOrDefault("page", 1));
        Integer size = Convert.toInt(pageMap.getOrDefault("size", 10));
        // 获取其他查询条件
        String workTaskId = Convert.toStr(pageMap.get("workTaskId"));
        // 判断查询类型
        if (ResourceStageConstant.UN_RECORD.equals(Convert.toStr(pageMap.get("type")))) {
            // 返回结果
            return R.data(iResourceRecordService.listNoRecord(pageMap, page, size, workTaskId, Convert.toStr(pageMap.get("type"))));
        }
        return R.data(iResourceRecordService.listRejected(pageMap, page, size, workTaskId, Convert.toStr(pageMap.get("type"))));
    }

    private String getFenZuFullName(String fenZu) {
        Set<String> allByName = classificationService.findAllByName(fenZu);
        log.info("allByName===={}", allByName.toString());
        if (ObjectUtil.isNotEmpty(allByName)) {
            List<DatasourceClassification> allParentClassifies = classificationService.getAllParentClassifies(allByName.iterator().next());
            String collect = allParentClassifies.stream().map(DatasourceClassification::getName).collect(Collectors.joining("/"));
            log.info("collect===={}", collect);
            return collect;
        }
        return fenZu;
    }

    /**
     * 批量交接 ===> 修改资源状态为“待审核”
     *
     * @param ids 资源id
     * @return {@link R}
     */
    @MyLog(menu = LogConstant.MENU_RESOURCE, dataType = LogConstant.DATA_RESOURCE, operation = LogConstant.OPERATION_CONNECT)
    @ApiOperation(value = "批量交接")
    @PostMapping("/handover")
    public R<String> handover(@RequestBody String ids) {
        List<String> resourceIdList = Func.objToStrList(ids);
        iResourceRecordService.handover(resourceIdList);
        return R.success();
    }


    /**
     * 全部批量交接 ===> 修改资源状态为“待审核”
     *
     * @return {@link R}
     */
    @ApiOperation(value = "全部批量交接")
    @GetMapping("/all-handover")
    public R<String> allHandover(@RequestParam String workTaskId) {
        List<String> resources = resourceRepository.findByCondition(Condition.getFilter(Map.of("workTaskId", workTaskId), Resource.class))
                .parallelStream().map(AbstractBaseEntity::getId).collect(Collectors.toList());
        iResourceRecordService.handover(resources);
        return R.success();
    }


}
