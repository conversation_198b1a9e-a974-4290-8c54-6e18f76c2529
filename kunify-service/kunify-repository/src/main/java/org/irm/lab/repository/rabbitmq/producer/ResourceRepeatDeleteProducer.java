package org.irm.lab.repository.rabbitmq.producer;

import org.irm.lab.repository.rabbitmq.message.ResourceRepeatDeleteMessage;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * 删除重复文件生产者
 */
@Component
public class ResourceRepeatDeleteProducer {

    @Resource
    private RabbitTemplate rabbitTemplate;
    /**
     * 获取RoutingKey尾缀
     */
    @Value("${spring.application.name}")
    private String KEY_SELF;

    /**
     * 发送消息
     */
    public void sendMessage(ResourceRepeatDeleteMessage message) {
        // 发送消息
        rabbitTemplate.convertAndSend(ResourceRepeatDeleteMessage.EXCHANGE,
                ResourceRepeatDeleteMessage.ROUTING_KEY + KEY_SELF,
                message);
    }

}
