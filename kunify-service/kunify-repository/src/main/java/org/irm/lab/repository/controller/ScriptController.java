package org.irm.lab.repository.controller;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.irm.lab.common.api.R;
import org.irm.lab.common.utils.ThreadLocalUtil;
import org.irm.lab.repository.service.IResourceService;
import org.irm.lab.repository.sync.builder.ExcelAddress;
import org.irm.lab.repository.sync.constant.ExcelConstant;
import org.irm.lab.repository.sync.handler.SyncMetaManager;
import org.springframework.core.io.FileSystemResource;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.io.File;
import java.io.FileInputStream;
import java.io.FileOutputStream;
import java.io.IOException;
import java.util.Map;
import java.util.zip.ZipEntry;
import java.util.zip.ZipOutputStream;

@RestController
@RequestMapping("/script")
@Api(value = "原有脚本，向外暴露功能")
@RequiredArgsConstructor
@Slf4j
public class ScriptController {
    private final SyncMetaManager syncMetaManager;
    private final IResourceService resourceService;
    @Resource
    private HttpServletRequest request;

    /**
     * 导出任务下非结构化平台id
     */
    @ApiOperation(value = "导出任务下非结构化平台id")
    @GetMapping("/export-catalog")
    public ResponseEntity<?> scriptExportCatalogId(@RequestParam String workTaskId) {
        log.info("导出任务下非结构化平台id：start");
        syncMetaManager.initExcelEnvironment(new ExcelAddress());
        //读取文件返回前端
        try {
            String filePath = resourceService.exportFileId(workTaskId);
            File file = new File(filePath);
            HttpHeaders headers = new HttpHeaders();
            headers.add(HttpHeaders.CONTENT_DISPOSITION, "attachment; filename=" + file.getName());
            headers.add(HttpHeaders.CONTENT_LENGTH, String.valueOf(file.length()));
            headers.setContentType(MediaType.APPLICATION_OCTET_STREAM);

            log.info("导出任务下非结构化平台id：complete");
            return ResponseEntity.ok()
                    .headers(headers)
                    .body(new FileSystemResource(file));
        } catch (Exception e) {
            log.warn("导出任务下非结构化平台id报错：{}", e.getMessage());
        }
        return ResponseEntity.status(HttpStatus.OK).body(Map.of("message", "导出失败"));
    }


    /**
     * 刷新元数据
     */
    @ApiOperation(value = "刷新元数据")
    @PostMapping("/refresh-meta-upload")
    public R<Void> refreshMeta(@RequestParam("file") MultipartFile file) {
        log.info("刷新元数据：start");
        syncMetaManager.initExcelEnvironment(new ExcelAddress());
        try {
            if (file.getOriginalFilename() == null || !file.getOriginalFilename().toLowerCase().endsWith(".xlsx")) {
                return R.failed("仅支持xlsx文件");
            }
            File excelFile = new File(ExcelConstant.excelPath + "/" + file.getOriginalFilename());
            file.transferTo(excelFile);
            ThreadLocalUtil.set("user", request.getHeader("user"));
            syncMetaManager.refreshByFile(excelFile);
            return R.success("后台刷新中...");
        } catch (Exception e) {
            log.warn("刷新元数据-失败：{}", e.getMessage());
        }
        return R.failed("刷新元数据-失败");
    }


    @ApiOperation(value = "获取元数据刷新结果")
    @GetMapping("/get-refresh-result")
    public ResponseEntity<?> getRefreshResult(@RequestParam String fileName) {
        File drFile = new File(ExcelConstant.failedByDatasourceResourceExcelPath + "/" + fileName);
        File rsFile = new File(ExcelConstant.failedByResourceExcelPath + "/" + fileName);

        if (!drFile.exists() || !rsFile.exists()) {
            return ResponseEntity.status(HttpStatus.OK).body(Map.of("message", "暂无元数据刷新结果"));
        }
        try {
            // 创建临时 ZIP 文件
            File zipFile = File.createTempFile("result", ".zip");
            try (ZipOutputStream zos = new ZipOutputStream(new FileOutputStream(zipFile))) {
                addFileToZip(zos, drFile, "dr-" + drFile.getName());
                addFileToZip(zos, rsFile, "rs-" + rsFile.getName());
            }

            HttpHeaders headers = new HttpHeaders();
            headers.add(HttpHeaders.CONTENT_DISPOSITION, "attachment; filename=" + zipFile.getName());
            headers.add(HttpHeaders.CONTENT_LENGTH, String.valueOf(zipFile.length()));
            headers.setContentType(MediaType.APPLICATION_OCTET_STREAM);

            return ResponseEntity.ok()
                    .headers(headers)
                    .body(new FileSystemResource(zipFile));
        } catch (IOException e) {
            e.printStackTrace();
            return ResponseEntity.status(HttpStatus.NOT_FOUND).body(Map.of("message", "暂无元数据刷新结果"));
        }
    }

    private void addFileToZip(ZipOutputStream zos, File file, String fileName) throws IOException {
        try (FileInputStream fis = new FileInputStream(file)) {
            ZipEntry zipEntry = new ZipEntry(fileName);
            zos.putNextEntry(zipEntry);

            byte[] bytes = new byte[1024];
            int length;
            while ((length = fis.read(bytes)) >= 0) {
                zos.write(bytes, 0, length);
            }

            zos.closeEntry();
        }
    }

    /**
     * 刷新任务下元数据
     */
    @ApiOperation(value = "刷新任务下元数据")
    @GetMapping("/refresh-meta-work")
    public R<Void> refreshMetaByWork(@RequestParam String workTaskId) {
        log.info("刷新任务下元数据：start");
        syncMetaManager.initExcelEnvironment(new ExcelAddress());
        try {
            File excelFile = new File(ExcelConstant.splitMatchDataExcel + "/" + workTaskId + ".xlsx");
            if (!excelFile.exists()) {
                return R.failed("待刷新excel不存在，请先切割excel总表");
            }
            syncMetaManager.refreshByFile(excelFile);
            return R.success("刷新任务下元数据-完成");
        } catch (Exception e) {
            log.warn("刷新任务下元数据-失败：{}", e.getMessage());
        }
        return R.failed("刷新任务下元数据-失败");
    }


    /**
     * by 任务 get 数据源更新失败的id
     */
    @ApiOperation(value = "by 任务 get 数据源更新失败的id")
    @GetMapping("/get-dr-failed-by-work")
    public ResponseEntity<FileSystemResource> getDRFailedByWork(@RequestParam String workTaskId) {
        log.info("by 任务 get 数据源更新失败的id：start");
        syncMetaManager.initExcelEnvironment(new ExcelAddress());
        //读取文件返回前端
        File file = new File(ExcelConstant.failedByDatasourceResourceExcelPath + "/" + workTaskId + ".xlsx");

        if (!file.exists()) {
            log.warn("文件不存在，无法导出:{}", file);
            return ResponseEntity.status(HttpStatus.NOT_FOUND).body(null);
        }

        HttpHeaders headers = new HttpHeaders();
        headers.add(HttpHeaders.CONTENT_DISPOSITION, "attachment; filename=" + file.getName());
        headers.add(HttpHeaders.CONTENT_LENGTH, String.valueOf(file.length()));
        headers.setContentType(MediaType.APPLICATION_OCTET_STREAM);

        log.info("by 任务 get 数据源更新失败的id：complete");
        return ResponseEntity.ok()
                .headers(headers)
                .body(new FileSystemResource(file));

    }

    /**
     * by 任务 get 资源更新失败的id
     */
    @ApiOperation(value = "by 任务 get 资源更新失败的id")
    @GetMapping("/get-rs-failed-by-work")
    public ResponseEntity<FileSystemResource> getRSFailedByWork(@RequestParam String workTaskId) {
        log.info("by 任务 get 资源更新失败的id：start");
        syncMetaManager.initExcelEnvironment(new ExcelAddress());
        //读取文件返回前端
        File file = new File(ExcelConstant.failedByResourceExcelPath + "/" + workTaskId + ".xlsx");

        if (!file.exists()) {
            log.warn("文件不存在，无法导出:{}", file);
            return ResponseEntity.status(HttpStatus.NOT_FOUND).body(null);
        }

        HttpHeaders headers = new HttpHeaders();
        headers.add(HttpHeaders.CONTENT_DISPOSITION, "attachment; filename=" + file.getName());
        headers.add(HttpHeaders.CONTENT_LENGTH, String.valueOf(file.length()));
        headers.setContentType(MediaType.APPLICATION_OCTET_STREAM);

        log.info("by 任务 get 资源更新失败的id：complete");
        return ResponseEntity.ok()
                .headers(headers)
                .body(new FileSystemResource(file));

    }


    /**
     * 根据任务切割excel
     */
    @ApiOperation(value = "根据任务切割excel")
    @PostMapping("/split-excel-by-work")
    public R<Void> splitExcelByWork(@RequestParam("file") MultipartFile file, @RequestParam String workTaskId) {
        log.info("根据任务切割excel：start");
        syncMetaManager.initExcelEnvironment(new ExcelAddress());
        try {
            if (file.getOriginalFilename() == null || !file.getOriginalFilename().toLowerCase().endsWith(".xlsx")) {
                return R.failed("仅支持xlsx文件");
            }
            File excelFile = new File(ExcelConstant.totalDataExcel + "/" + workTaskId + ".xlsx");
            file.transferTo(excelFile);
            //将总表拆分，拆出包含当前任务下资源的子表
            syncMetaManager.splitExcelByTask(excelFile, workTaskId);
            log.info("根据任务切割excel：complete");
            return R.success("根据任务切割excel-完成");
        } catch (Exception e) {
            log.warn("根据任务切割excel-失败：{}", e.getMessage());
        }
        return R.failed("根据任务切割excel-失败");

    }

}

