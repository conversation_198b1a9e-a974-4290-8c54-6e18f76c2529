package org.irm.lab.repository.rabbitmq.consumer;

import cn.hutool.core.collection.CollUtil;
import com.mongodb.client.model.Filters;
import com.rabbitmq.client.Channel;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.irm.lab.common.utils.ThreadLocalUtil;
import org.irm.lab.kg.repository.neo4j.node.NodeEntityRepository;
import org.irm.lab.repository.entity.Resource;
import org.irm.lab.repository.entity.ResourceAnnex;
import org.irm.lab.repository.entity.ResourceTemp;
import org.irm.lab.repository.rabbitmq.message.ResourceRepeatDeleteMessage;
import org.irm.lab.repository.repository.ResourceAnnexRepository;
import org.irm.lab.repository.repository.ResourceRepository;
import org.irm.lab.repository.repository.ResourceTempRepository;
import org.irm.lab.repository.service.IResourceService;
import org.jetbrains.annotations.NotNull;
import org.springframework.amqp.core.Message;
import org.springframework.amqp.rabbit.annotation.RabbitHandler;
import org.springframework.amqp.rabbit.annotation.RabbitListener;
import org.springframework.stereotype.Component;

import java.io.IOException;
import java.util.*;
import java.util.stream.Collectors;


/**
 * 重复文件删除消费者
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class ResourceRepeatDeleteConsumer {
    private final ResourceAnnexRepository resourceAnnexRepository;

    private final IResourceService iResourceService;

    private final ResourceTempRepository resourceTempRepository;

    private final NodeEntityRepository nodeEntityRepository;

    private final ResourceRepository resourceRepository;

    /**
     * 消息监听
     */
    @RabbitListener(queues = ResourceRepeatDeleteMessage.QUEUE+"${spring.application.name}",concurrency = "1")
    @RabbitHandler
    public void onMessage(ResourceRepeatDeleteMessage message, Channel channel, Message messageStatus) throws IOException {
        // 获取消息状态信息
        long deliveryTag = messageStatus.getMessageProperties().getDeliveryTag();
//        log.info("[onMessage][线程编号:{} 消息内容：{}]", Thread.currentThread().getId(), message);
        try {
            // 设置用户信息，防止租户问题
            ThreadLocalUtil.set("user",message.getUser());
            ResourceTemp resourceTemp = message.getResourceTemp();
            List<String> docIds = obtainDeleteDocId(resourceTemp.getName(), resourceTemp.getDocIds()); // 看一下获取对不对
            // docId已删除需保留的文件id ，删除重复文件
            List<String> resourceAnnexIds = resourceAnnexRepository.findByCondition(Filters.in("resourceId", docIds)).stream().map(ResourceAnnex::getId).collect(Collectors.toList());
            // 删除主文件相关数据
            iResourceService.deleteResourceAndAnnexResource(docIds);
            resourceTemp.setDeleteMainDocIds(docIds);
            resourceTemp.setDeleteAnnexDocIds(resourceAnnexIds);
            resourceTemp.setDeleteFlag("已删除");
            resourceTempRepository.save(resourceTemp);

            log.info("<<<<<<<<<<<<<<<<<<<<<<<<<<<重复文件[{}]已处理完成,共需要处理文件条目个数：[{}], 已处理文件条目个数：[{}], 待处理文件条目个数：[{}]>>>>>>>>>>>>>>>>>>>>>>>>>", resourceTemp, message.getTotal(), message.getCurrentNum(),  message.getTotal() -  message.getCurrentNum());
        }catch (Exception e){
            e.printStackTrace();
            log.error("消息已处理失败！");
//            if (messageStatus.getMessageProperties().getRedelivered()) {
//                log.error("消息已重复处理失败，拒绝再次接收.....");
//                channel.basicReject(deliveryTag, false);
//            } else {
//                log.info("消息即将再次返回队列中进行处理....");
//                channel.basicNack(deliveryTag, false, true);
//            }
        }
    }

    @NotNull
    private List<String> obtainDeleteDocId(String name, List<String> docIds) {

        log.info("<<<<<<<<<<<<<<<<<<文件：[{}]存在重复资源，重复的文件id列表为{}, 正在筛选其中需删除的文件。。。。。>>>>>>>>>>>>>>>>>>", name, docIds);

        // 判断多个List的id应该保留哪一个
        HashMap<String, Integer> nodeEntityNumInDoc = new HashMap<>();
        String queryNodeEntityNumInDoc = "MATCH (n:ENTITY) " +
                "WHERE any(x IN n.docIds WHERE x IN $docIdsToCheck) ";
        // 1.找到这些id中划词的数量（包含附件的划词数量）
        for (String docId : docIds) {
            Set<String> annexDocIds = resourceAnnexRepository.findByCondition(Filters.eq("resourceId", docId)).stream().map(ResourceAnnex::getId).collect(Collectors.toSet());
            annexDocIds.add(docId);
            int nodeEntityNum = nodeEntityRepository.findEntityCountByCypher(queryNodeEntityNumInDoc, Map.of("docIdsToCheck", annexDocIds));
            nodeEntityNumInDoc.put(docId, nodeEntityNum);
        }
        // 获取划词数量最大的docId
        List<String> maxKeys = getMaxKeys(nodeEntityNumInDoc);
        if (maxKeys.size() == 1) { // 由划词数量判断出应该删除的文件
            docIds.remove(maxKeys.get(0));
            log.info("通过划词数量，对重复的文件[{}]，保留id为[{}]的文件，删除id为{}的文件", name, maxKeys.get(0), docIds);
        } else { // 划词数量相同判断附件最多的
            nodeEntityNumInDoc.clear();
            for (String maxKey : maxKeys) {
                int annexDocSize = resourceAnnexRepository.findByCondition(Filters.eq("resourceId", maxKey)).size();
                nodeEntityNumInDoc.put(maxKey, annexDocSize);
            }
            List<String> maxKeys1 = getMaxKeys(nodeEntityNumInDoc);
            if (maxKeys1.size() == 1) { // 由附件数量判断出应该删除的文件
                docIds.remove(maxKeys1.get(0));
                log.info("通过附件数量，对重复的文件[{}]，保留id为[{}]的文件，删除id为{}的文件", name, maxKeys1.get(0), docIds);
            } else { // 划词数量相同，附件相同时判断元数据最多的文件
                nodeEntityNumInDoc.clear();
                for (String id : maxKeys1) {
                    Resource resource ;
                    try {
                        resource = resourceRepository.findById(id);
                    } catch (Exception e) {
                        resource = null;
                    }
                    int count = 0;
                    if(resource != null){
                        count = (int) resourceRepository.findById(id).getMetadata().stream().filter(metadataVO ->
                                CollUtil.isNotEmpty(metadataVO.getValue())
                        ).count();
                    }

                    nodeEntityNumInDoc.put(id, count);
                }
                List<String> maxKeys2 = getMaxKeys(nodeEntityNumInDoc);
                // 此时默认保留第一个。
                docIds.remove(maxKeys2.get(0));
                log.info("通过元数据数量，对重复的文件[{}]，保留id为[{}]的文件，删除id为{}的文件", name, maxKeys2.get(0), docIds);
            }
        }
        return docIds;
    }


    public static List<String> getMaxKeys(HashMap<String, Integer> map) {
        List<String> maxKeys = new ArrayList<>();
        Integer max = null;

        // 寻找最大值
        for (Map.Entry<String, Integer> entry : map.entrySet()) {
            if (max == null || entry.getValue() > max) {
                max = entry.getValue();
                maxKeys.clear(); // 重置列表
                maxKeys.add(entry.getKey());
            } else if (entry.getValue().equals(max)) {
                maxKeys.add(entry.getKey());
            }
        }

        return maxKeys;
    }
}
