package org.irm.lab.repository.controller.datasource;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.irm.lab.common.api.R;
import org.irm.lab.repository.feign.PeripheralInterfaceFeign;
import org.irm.lab.repository.service.IPeripheralInterfaceService;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

@RestController
@RequestMapping("/peripheral-interface")
@Api(value = "外部调用接口")
public class PeripheralInterfaceController implements PeripheralInterfaceFeign {

    @Resource
    private IPeripheralInterfaceService peripheralInterfaceService;

    @ApiOperation("登录并获取token")
    @GetMapping(value = "/get-token")
    public R<String> getDataSourceToken() {
        return R.data(peripheralInterfaceService.getDataSourceToken());
    }

    /**
     * 获取无水印文件的下载地址
     *
     * @param id 甲方文档id
     * @return 下载地址
     */
    @ApiOperation("获取无水印文件的下载地址")
    @PostMapping(value = "/download-no-water-mark")
    public R<String> downloadNoWaterMark(@RequestBody String id) {
        return R.data(peripheralInterfaceService.downloadNoWaterMark(id));
    }

    /**
     * 获取文件压缩包下载地址
     *
     * @param id 甲方文档id
     * @return 下载地址
     */
    @ApiOperation("获取文件压缩包下载地址")
    @PostMapping(value = "/mark-download-url")
    public R<String> markZipDownloadUrl(@RequestBody String id) {
        return R.data(peripheralInterfaceService.markZipDownloadUrl(id));
    }

    /**
     * 获取资源预览地址
     *
     * @param id 甲方文档id
     * @return 预览地址
     */
    @ApiOperation("获取资源预览地址")
    @PostMapping(value = "/file-preview")
    public R<String> filePreview(String id) {
        return R.data(peripheralInterfaceService.filePreview(id));
    }

}
