package org.irm.lab.repository.rabbitmq.consumer;

import cn.hutool.core.convert.Convert;
import cn.hutool.core.util.ObjUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.ReflectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpResponse;
import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.mongodb.client.model.Filters;
import com.rabbitmq.client.Channel;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import org.irm.lab.common.constant.AuthConstant;
import org.irm.lab.common.constant.FiledNameConst;
import org.irm.lab.common.exception.ServiceException;
import org.irm.lab.common.utils.ThreadLocalUtil;
import org.irm.lab.kg.entity.neo4j.node.NodeEntity;
import org.irm.lab.kg.repository.neo4j.node.NodeEntityRepository;
import org.irm.lab.repository.constant.DatasourceConstant;
import org.irm.lab.repository.constant.DatasourceExceptionConstant;
import org.irm.lab.repository.constant.DatasourceResourceConstant;
import org.irm.lab.repository.entity.datasource.DatasourceAttach;
import org.irm.lab.repository.entity.datasource.DatasourceDataStruct;
import org.irm.lab.repository.entity.datasource.DatasourceInterface;
import org.irm.lab.repository.entity.datasource.DatasourceResource;
import org.irm.lab.repository.rabbitmq.message.DatasourceResourceSyncMessage;
import org.irm.lab.repository.rabbitmq.message.ResourceVerificationMessage;
import org.irm.lab.repository.rabbitmq.producer.DatasourceResourceSyncProducer;
import org.irm.lab.repository.repository.DatasourceResourceRepository;
import org.irm.lab.repository.service.IDatasourceDataStructService;
import org.irm.lab.repository.service.IDatasourceInterfaceService;
import org.irm.lab.repository.service.IPeripheralInterfaceService;
import org.irm.lab.repository.service.IResourceVerificationService;
import org.springframework.amqp.core.Message;
import org.springframework.amqp.rabbit.annotation.RabbitHandler;
import org.springframework.amqp.rabbit.annotation.RabbitListener;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.io.IOException;
import java.text.SimpleDateFormat;
import java.util.*;


//TODO 数据同步暂时使用redis+cron的方式解决，有时间在改成rabbit
@Slf4j
@Component
public class DatasourceResourceSyncConsumer {

    private final SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd hh:mm:ss");

    @Resource
    private IDatasourceInterfaceService interfaceService;

    @Resource
    private IPeripheralInterfaceService peripheralInterfaceService;

    @Resource
    private DatasourceResourceSyncProducer datasourceResourceSyncProducer;

    @Resource
    private IDatasourceDataStructService dataStructService;

    @Resource
    private DatasourceResourceRepository datasourceResourceRepository;

    @Resource
    private NodeEntityRepository nodeEntityRepository;


    /**
     * 消息监听
     */
    @RabbitListener(queues = DatasourceResourceSyncMessage.QUEUE + "${spring.application.name}", concurrency = "3")
    @RabbitHandler
    public void onMessage(DatasourceResourceSyncMessage message, Channel channel, Message messageStatus) throws IOException {
        // 获取消息状态信息
        long deliveryTag = messageStatus.getMessageProperties().getDeliveryTag();
        log.info("[onMessage][线程编号:{} 消息内容：{}]", Thread.currentThread().getId(), message);
        try {
//            // 获取登录token
//            String token = peripheralInterfaceService.getDataSourceToken();
//            // 获取数据同步接口所属的数据源对象
//            DatasourceInterface datasourceInterface = interfaceService.listByDatasourceIdAndGroup(message.getDatasourceId(),
//                    DatasourceConstant.INTERFACE_SYNC_DATA);
//            // 查出当前接口下的响应参数列表项
//            List<DatasourceDataStruct> structs = dataStructService.listByInterfaceId(datasourceInterface.getId(), DatasourceConstant.PARAM_RESPONSE);
            // 设置用户信息，防止租户问题
            long size = 0;
            ThreadLocalUtil.set("user", message.getUser());
            if(StrUtil.isNotEmpty(message.getConceptId())){
                List<String> list = List.of("孟鹏飞", "周楠", "张斌", "王鑫", "于洋", "周宇", "张新宇", "刘鹏");
                Map<String, List<NodeEntity>> sameNodeEntity = nodeEntityRepository.findSameNodeEntity(message.getConceptId());
                for (Map.Entry<String, List<NodeEntity>> stringListEntry : sameNodeEntity.entrySet()) {
                    if(!list.contains(stringListEntry.getKey())){
                        log.info("<<<<<<<<<<<<<<<<<<<<<正在对entityName为{}的实体进行实体对齐>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>",stringListEntry.getKey());
                        List<NodeEntity> value = stringListEntry.getValue();
                        size += value.size();
                        if(value.size() >= 2){
                            for (int i = 1; i < value.size(); i++) {
                                ArrayList<String> ids = new ArrayList<>();
                                ids.add(value.get(0).getId());
                                ids.add(value.get(i).getId());
                                log.info("<<<<<<<<<<<<<<<<<<<<<正在对第一个实体和第[{}]个实体进行对齐>>>>>>>>>>>>>>>>>>>>>>",i+1);
                                updateNodeEntity(ids);
                                log.info("<<<<<<<<<<<<<<<<<<<<完成对第一个实体和第[{}]个实体进行对齐>>>>>>>>>>>>>>>>>>>>>>>",i+1);
                            }
                        }
                    }
                    log.info("<<<<<<<<<<<<<<<<<<<<<完成对entityName为{}的实体进行实体对齐>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>",stringListEntry.getKey());
                }
                log.info("实体对齐,总共对齐了{}组数据,共对齐了{}个节点",sameNodeEntity.entrySet().size(),size);
            }

//
//
//            // 执行业务逻辑：同步当前页码数据
//            log.info("正在消费消息：【{}】，正在同步第【{}】页数据", message.getDatasourceId() + message.getType(), message.getPageIndex());
//            // 甲方传过来的所有字段
//            List<DatasourceResource> datasourceResourceList = new ArrayList<>();
//            // 创建map存储请求体
//            Map<String, Object> postMap = new HashMap<>();
//            postMap.put("startTime", "1900-01-01 00:00:01");
//            postMap.put("endTime", dateFormat.format(new Date()));
//            postMap.put("type", message.getType());
//            postMap.put("pageIndex", message.getPageIndex());
//            postMap.put("pageSize", message.getPageSize());
//            HttpRequest request;
//            if ("post".equals(datasourceInterface.getRequestType())) {
//                request = HttpRequest.post(datasourceInterface.getUri());
//            } else {
//                request = HttpRequest.get(datasourceInterface.getUri());
//            }
//            log.info(datasourceInterface.getUri());
//            HttpResponse execute = request.form(postMap)
//                    .header(AuthConstant.JWT_TOKEN_HEADER, "Bearer " + token)
//                    .execute();
//            String body = execute.body();
//            log.info("获取数据接口响应体：" + body);
//            if (execute.getStatus() != 200)
//                throw new ServiceException(DatasourceExceptionConstant.REMOTE_CONNECTION_FAILURE);
//            JSONObject bodyObject = JSONUtil.parseObj(body);
//            if (Convert.toInt(bodyObject.get("code")) != 200) {
//                log.error("消费消息失败======》【{}】正在同步第【{}】页数据======》失败信息【{}】",
//                        message.getDatasourceId() + message.getType(), message.getPageIndex(), bodyObject);
//                //TODO 自产自销
//            }
//            //调用该接口返回的数据是分页数据
//            Object pageData = bodyObject.get("data");
//            Object result = JSONUtil.parseObj(pageData).get("result");
//            //远程接口中的是数据
//            JSONArray jsonArray = JSONUtil.parseArray(result);
//            //当前页查询不到数据之后说明上一页是最后一页数据，
//            if (ObjectUtil.isEmpty(jsonArray)) {
//                channel.basicReject(deliveryTag, false);
//            }
//            jsonArray.forEach(item -> {
//                try {
//                    try {
//                        //item: 每条资源
//                        DatasourceResource datasourceResource = new DatasourceResource();
//                        datasourceResource.setDatasourceId(message.getDatasourceId());
//                        Map<String, Object> fields = new HashMap<>();
//                        JSONObject jsonObject = JSONUtil.parseObj(item);
//                        Set<Map.Entry<String, Object>> entries = jsonObject.entrySet();
//
//                        entries.forEach(entry -> {
//                            //entry： 每个字段
//                            String key = entry.getKey();
//                            Object value = entry.getValue();
//                            if ("TITLE".equals(key) && ObjectUtil.isNotEmpty(value))
//                                log.info("当前分类为：" + message.getType() + ",当前文件名称为" + value.toString());
//                            if (!"attachments".equalsIgnoreCase(key) && ObjectUtil.isEmpty(value)) {
//                                List<DatasourceAttach> attachList = getAttachByObject(value, structs);
//                                if (ObjectUtil.isNotEmpty(attachList))
//                                    datasourceResource.setAttaches(attachList);
//                            } else {
//                                structs.stream().filter(datasourceDataStruct -> key.equals(datasourceDataStruct.getName())).findFirst().ifPresent(ele -> {
//                                    String dbField = ObjectUtil.isEmpty(ele.getMappingDbField()) ? null : ele.getMappingDbField();
//                                    if (dbField != null)
//                                        ReflectUtil.setFieldValue(datasourceResource, dbField, value);
//                                });
//                                fields.put(key, value.toString());
//                            }
//                        });
//                        datasourceResource.setFields(fields);
//                        //过滤已存在的资源
//                        String fileId = ObjectUtil.isEmpty(datasourceResource.getFileId()) ? "" : datasourceResource.getFileId();
//                        List<DatasourceResource> collect = datasourceResourceRepository.findByCondition(
//                                Filters.and(
//                                        Filters.eq(DatasourceResourceConstant.FILE_ID, fileId),
//                                        Filters.eq(FiledNameConst.DATA_SOURCE_ID, message.getDatasourceId())
//                                )
//                        );
//                        //若不存在当前资源，则新增
//                        if (ObjectUtil.isEmpty(collect)) {
//                            datasourceResource.setAttaches(getDatasourceAttachList(metadataPreview(datasourceResource.getClassify(), datasourceResource.getFields()), structs));
//                            datasourceResourceList.add(datasourceResource);
//                        }
//                    } catch (Exception e) {
//                        e.printStackTrace();
//                    }
//                } catch (Exception e) {
//                    e.printStackTrace();
//                }
//            });
//            //save所有同步后的资源
//            datasourceResourceRepository.saveAll(datasourceResourceList);
            // 手动ack
//            channel.basicAck(deliveryTag, false);
        } catch (Exception e) {
            if (messageStatus.getMessageProperties().getRedelivered()) {
                log.error("消息已重复处理失败，拒绝再次接收.....");
                channel.basicReject(deliveryTag, false);
            } else {
                log.info("消息即将再次返回队列中进行处理....");
                channel.basicNack(deliveryTag, false, true);
            }
        }
    }

    public void updateNodeEntity(List<String> ids) {
        NodeEntity sourceEntity;
        NodeEntity targetEntity;
        try {
            // 获取要对齐的实例对象
            String sourceEntityId = ids.get(0);
            sourceEntity = nodeEntityRepository.findByCondition(Map.of("id", sourceEntityId), 1).get(0);
            // 获取被对齐的实例Id
            String targetEntityId = ids.get(1);
            targetEntity = nodeEntityRepository.findByCondition(Map.of("id", targetEntityId), 1).get(0);
        } catch (Exception e) {
            e.printStackTrace();
            return;
        }
        Map<String, Object> properties = sourceEntity.getProperties();
        for (Map.Entry<String, Object> stringObjectEntry : targetEntity.getProperties().entrySet()) {
            if(ObjUtil.isEmpty(properties.get(stringObjectEntry.getKey()))){
                properties.put(stringObjectEntry.getKey(),stringObjectEntry.getValue());
            }else if(stringObjectEntry.getKey().equals("prop_rylx")){
                Object o = properties.get(stringObjectEntry.getKey());
                if(ObjectUtil.isNotEmpty(o) && !ObjectUtil.equals("高层领导",o)){
                    properties.put(stringObjectEntry.getKey(),stringObjectEntry.getValue());
                }
            }
        }
        // 设置来源文档
        Set<String> docIds = sourceEntity.getDocIds();
        docIds.addAll(targetEntity.getDocIds());
        sourceEntity.setDocIds(docIds);
        NodeEntity save = nodeEntityRepository.save(sourceEntity);
        // 修订指出关系
        nodeEntityRepository.redirectRelations(save.getId(), targetEntity.getId(), docIds);
        nodeEntityRepository.incoming(save.getId(), targetEntity.getId(), docIds);
        nodeEntityRepository.deleteByProperty("id", targetEntity.getId());
    }


    /**
     * 根据Object获取附件信息
     *
     * @param attachObject 附件Object
     * @param structs      参数列表项
     * @return 附件集合
     */
    public List<DatasourceAttach> getAttachByObject(Object attachObject, List<DatasourceDataStruct> structs) {
        List<DatasourceAttach> datasourceAttachList = new ArrayList<>();
        JSONArray jsonArray = JSONUtil.parseArray(attachObject);
        if (ObjectUtil.isNotEmpty(jsonArray)) {
            jsonArray.forEach(item -> {
                //item: 每条资源
                DatasourceAttach datasourceAttach = new DatasourceAttach();
                JSONObject jsonObject = JSONUtil.parseObj(item);
                Set<Map.Entry<String, Object>> entries = jsonObject.entrySet();
                entries.forEach(entry -> {
                    //entry： 每个字段
                    String key = entry.getKey();
                    Object value = entry.getValue();
                    structs.stream().filter(datasourceDataStruct -> key.equals(datasourceDataStruct.getName())).findFirst().ifPresent(ele -> {
                        String dbField = ObjectUtil.isEmpty(ele.getMappingDbField()) ? null : ele.getMappingDbField();
                        if (dbField != null)
                            ReflectUtil.setFieldValue(datasourceAttach, dbField, value);
                    });
                });
                datasourceAttachList.add(datasourceAttach);
            });
        }
        return datasourceAttachList;
    }


    /**
     * 获取资源附件
     *
     * @param map     可用于预览的资源信息  eg： 名称： xxxx
     * @param structs 参数列表项
     * @return 资源附件
     */
    public List<DatasourceAttach> getDatasourceAttachList(Map<String, Object> map, List<DatasourceDataStruct> structs) {
        Object resourceAttach = map.getOrDefault("附件", null);
        return getAttachByObject(resourceAttach, structs);
    }


    public Map<String, Object> metadataPreview(String classify, Map<String, Object> fields) {
        Map<String, Object> newFields = new HashMap<>();
        /*
         * 将元数据字段根据对照表转换成文字对象
         * */
        //获取当前分类的元数据对照表
        List<Map<String, Object>> transitionMetaListMap = peripheralInterfaceService.transitionMeta(classify);
        Map<String, Object> map1 = Map.of("LENGTH", "0", "REQUIRED", "0", "LABEL", "附件", "TYPE", "text", "ACCURACY", "0", "NAME", "attachments");
        Map<String, Object> map2 = Map.of("LENGTH", "0", "REQUIRED", "0", "LABEL", "附件", "TYPE", "text", "ACCURACY", "0", "NAME", "ATTACHMENTS");
        Map<String, Object> map3 = Map.of("LENGTH", "0", "REQUIRED", "0", "LABEL", "附件", "TYPE", "text", "ACCURACY", "0", "NAME", "ATTACHMENT");
        transitionMetaListMap.addAll(List.of(map1, map2, map3));
        transitionMetaListMap.forEach(transitionMetaMap -> {
            //transitionMetaMap：每个字段的基本信息
            String transitionMetaName = transitionMetaMap.getOrDefault("NAME", "").toString();
            String transitionMetaNameLabel = transitionMetaMap.getOrDefault("LABEL", "").toString();
            Set<Map.Entry<String, Object>> entries = fields.entrySet();
            entries.forEach(entry -> {
                //entry ： 当前资源的每个元数据项   例如： CREATOR(key) : 法务同步账号（value）
                String key = entry.getKey();
                Object value = entry.getValue();
                //若当前字段与对照便字段匹配，则生成可查看的元数据信息
                if (transitionMetaName.equals(key))
                    newFields.put(transitionMetaNameLabel, value);
            });

        });
        return newFields;
    }


}
