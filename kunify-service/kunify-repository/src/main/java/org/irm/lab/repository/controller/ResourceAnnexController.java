package org.irm.lab.repository.controller;

import io.swagger.annotations.ApiModelProperty;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.irm.lab.common.annotation.MyLog;
import org.irm.lab.common.api.R;
import org.irm.lab.common.constant.LogConstant;
import org.irm.lab.common.utils.Func;
import org.irm.lab.common.utils.ResourceType;
import org.irm.lab.repository.entity.Resource;
import org.irm.lab.repository.entity.ResourceAnnex;
import org.irm.lab.repository.feign.ResourceAnnexFeign;
import org.irm.lab.repository.service.IResourceAnnexService;
import org.irm.lab.repository.service.IScalePicService;
import org.irm.lab.resource.entity.Attach;
import org.irm.lab.resource.feign.AttachFeign;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2023/4/24 18:32
 * @description 资源附件控制器
 */
@RestController
@RequestMapping("/resource-annex")
@RequiredArgsConstructor
public class ResourceAnnexController implements ResourceAnnexFeign {
    private final IResourceAnnexService iResourceAnnexService;
    private final IScalePicService scalePicService;
    private final AttachFeign attachFeign;

    /**
     * 新增/修改资源附件
     *
     * @param resourceAnnex {@link ResourceAnnex}
     * @return {@link ResourceAnnex}
     */
    @MyLog(menu = LogConstant.MENU_RESOURCE, dataType = LogConstant.DATA_RESOURCE_ANNEX, operation = LogConstant.OPERATION_ADD)
    @PostMapping("/save")
    public R<ResourceAnnex> save(@RequestBody ResourceAnnex resourceAnnex) {
        return R.data(iResourceAnnexService.save(resourceAnnex));
    }

    /**
     * 删除资源附件
     *
     * @param ids 资源附件Id列表
     */
    @MyLog(menu = LogConstant.MENU_RESOURCE, dataType = LogConstant.DATA_RESOURCE_ANNEX, operation = LogConstant.OPERATION_REMOVE)
    @PostMapping("/remove")
    public R<Void> remove(@RequestBody String ids) {
        List<String> idList = Func.objToStrList(ids);
        iResourceAnnexService.remove(idList);
        return R.success();
    }

    /**
     * 删除资源附件文件（不删除附件对象）
     *
     * @param ids 资源附件Id列表
     */
    @MyLog(menu = LogConstant.MENU_RESOURCE, dataType = LogConstant.DATA_RESOURCE_ANNEX_FILE, operation = LogConstant.OPERATION_REMOVE)
    @PostMapping("/remove-file")
    public R<Void> removeFile(@RequestBody String ids) {
        List<String> idList = Func.objToStrList(ids);
        iResourceAnnexService.removeNotResourceAnnex(idList);
        return R.success();
    }

    /**
     * 资源附件详情
     *
     * @param resourceId 资源附件Id
     * @return {@link ResourceAnnex}
     */
    @GetMapping("/info")
    public R<ResourceAnnex> info(@RequestParam String id) {
        return R.data(iResourceAnnexService.info(id));
    }

    /**
     * 获取指定附件的pdf预览地址
     *
     * @param id 资源附件Id
     * @return pdf预览地址
     */
    @ApiOperation(value = "获取指定附件的pdf预览地址")
    @GetMapping("/get-annex-pdf")
    public R<String> getAnnexPdf(@RequestParam String id) {
        return R.data(iResourceAnnexService.getAnnexPdf(id));
    }

    /**
     * 获取指定资源的全部附件
     *
     * @param resourceId 资源Id
     * @return 附件列表
     */
    @ApiModelProperty(value = "根据资源Id获取该资源的全部附件")
    @GetMapping("/list-by-resource")
    public R<List<ResourceAnnex>> listByResourceId(@RequestParam String resourceId) {
        return R.data(iResourceAnnexService.listByResourceId(resourceId));
    }

    /**
     * 更新附件
     *
     * @param map 条件map
     * @return {@link ResourceAnnex}
     */
    @GetMapping("/update-one")
    public R<ResourceAnnex> updateOne(@RequestParam(required = false) Map<String, Object> map) {
        return R.data(iResourceAnnexService.updateOne(map));
    }

    @PostMapping("/word2pdf-annex")
    public R<Attach> word2pdf(@RequestBody ResourceAnnex annex) {
        if (ResourceType.WORD.equals(ResourceType.autoJudge(annex.getMediaType())))
            scalePicService.annexWordToPdf(annex, attachFeign.info(annex.getPrimaryFileId()).getData());
        return attachFeign.info(annex.getPrimaryFileId());
    }
}
