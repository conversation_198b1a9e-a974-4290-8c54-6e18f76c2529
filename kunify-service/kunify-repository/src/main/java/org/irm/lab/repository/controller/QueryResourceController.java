package org.irm.lab.repository.controller;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.irm.lab.common.api.R;
import org.irm.lab.repository.entity.QueryResource;
import org.irm.lab.repository.service.IQueryResourceService;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

@RestController
@RequestMapping("/query-resource")
@Api(value = "资源组配")
public class QueryResourceController {

    @Resource
    private IQueryResourceService queryResourceService;

    @ApiOperation("配置增改")
    @PostMapping("/save")
    public R<QueryResource> save(@RequestBody QueryResource queryResource) {
        return R.data(queryResourceService.save(queryResource));
    }

    @ApiOperation("当前配置")
    @GetMapping("/current-configuration")
    public R<QueryResource> currentConfiguration() {
        return R.data(queryResourceService.currentConfiguration());
    }

    @ApiOperation("配置详情")
    @GetMapping("/info")
    public R<QueryResource> info(@RequestParam String id) {
        return R.data(queryResourceService.info(id));
    }

    @ApiOperation(value = "同步删除元数据项id",hidden = true)
    @GetMapping("/pull-metadata")
    public R<Void> pullMetadata(List<String> metadataIdList){
        queryResourceService.pullMetadata(metadataIdList);
        return R.success();
    }

}
