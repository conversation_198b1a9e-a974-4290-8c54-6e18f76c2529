package org.irm.lab.repository.drainage.handler;

import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.util.ReUtil;
import lombok.extern.slf4j.Slf4j;
import org.irm.lab.common.pojo.BaseStatus;
import org.irm.lab.repository.drainage.IDrainageStrategy;
import org.irm.lab.repository.entity.datasource.DatasourceClassification;
import org.irm.lab.repository.entity.datasource.DatasourceResource;
import org.irm.lab.repository.service.IDatasourceClassificationService;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.HashSet;
import java.util.List;
import java.util.Set;
import java.util.function.Predicate;
import java.util.stream.Collectors;

//该处理器用于：正则匹配资源
@Slf4j
@Component
public class SecondResourceMatchRegexHandler extends ResourceRegexHandler {

    @Resource
    private IDatasourceClassificationService classificationService;

    public static final String LEADER_RESOURCE_CLASSIFY = "收文";

    public static final String MEETING_RESOURCE_CLASSIFY = "会议纪要";


    //该类文档的匹配规则中，获取命中资源
    @Override
    public ResourceRegexHandler handle(IDrainageStrategy drainageStrategy, List<DatasourceResource> resources) {
        log.info("第2链：开始筛选需要分发的资源");
        List<DatasourceResource> readyDrainageResource;
        //获取匹配规则命中的资源列表
        //如果是会议纪要文件，将会议纪要中的所有文件都归为会议纪要
        if ("meeting".equals(drainageStrategy.getType().getCode())) {
            //获取会议下所有分类
            Set<String> allClassifyByHYJY = getAllClassifyByHYJY();
            //TODO 调用获取分类下子分类有bug，先写死分类
            log.info("会议下所有分类为======={}", allClassifyByHYJY);
            readyDrainageResource = resources.stream()
                    .filter(resource -> BaseStatus.FALSE.equals(resource.getWorkStage()) &&
                            List.of("会议纪要", "专题会议纪要", "部门会议纪要").contains(resource.getClassify()) &&
                            drainageStrategy.getType().getResourceRegexRule().stream()
                                    .anyMatch(regex -> ReUtil.isMatch(regex, resource.getName())))
                    .collect(Collectors.toList());
//            readyDrainageResource = resources.stream()
//                    .filter(resource -> drainageStrategy.getType().getResourceRegexRule().stream()
//                            .anyMatch(regex -> ReUtil.isMatch(regex, resource.getName())) && BaseStatus.FALSE.equals(resource.getWorkStage()) && allClassifyByHYJY.contains(resource.getName()))
//                    .collect(Collectors.toList());
        }
        //表彰荣誉类型，不要签报类型
        else if ("awards".equals(drainageStrategy.getType().getCode())) {
            readyDrainageResource = resources.stream()
                    .filter(resource -> !resource.getClassify().contains("签报"))
                    .filter(resource -> drainageStrategy.getType().getResourceRegexRule().stream()
                            .anyMatch(regex -> ReUtil.isMatch(regex, resource.getName())) && BaseStatus.FALSE.equals(resource.getWorkStage()))
                    .collect(Collectors.toList());
        } else {
            readyDrainageResource = resources.stream()
                    .filter(resource -> drainageStrategy.getType().getResourceRegexRule().stream()
                            .anyMatch(regex -> BaseStatus.FALSE.equals(resource.getWorkStage()) && ReUtil.isMatch(regex, resource.getName())))
                    .collect(Collectors.toList());
        }
        log.info("第2链：筛选结束，筛选出需要分流的【{}】个资源", readyDrainageResource.size());
//        List<DatasourceResource> subDrainageResource = ListUtil.sub(readyDrainageResource, 0, 500);
//        log.info("第2链：测试环境，减少同步数量，当前需要分流的【{}】资源", subDrainageResource.size());
        return getNext().handle(drainageStrategy, readyDrainageResource);
    }

    //获取收文下的所有分类名称
    private Set<String> getAllClassifyBySW() {
        Set<String> allClassifyIds = new HashSet<>();
        Set<String> swIds = classificationService.findAllByName(LEADER_RESOURCE_CLASSIFY);
        swIds.forEach(classifyId -> {
            classificationService.getAllChildClassifies(classifyId, allClassifyIds);
        });

        return allClassifyIds.stream().map(classificationService::info).map(DatasourceClassification::getName).collect(Collectors.toSet());
    }

    //获取会议纪要下的所有分类名称
    private Set<String> getAllClassifyByHYJY() {
        Set<String> allClassifyIds = new HashSet<>();
        Set<String> meetingIds = classificationService.findAllByName(MEETING_RESOURCE_CLASSIFY);
        meetingIds.forEach(classifyId -> {
            classificationService.getAllChildClassifies(classifyId, allClassifyIds);
        });

        return allClassifyIds.stream().map(classificationService::info).map(DatasourceClassification::getName).collect(Collectors.toSet());
    }
}
