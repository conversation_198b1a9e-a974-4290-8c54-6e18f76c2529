package org.irm.lab.repository.websocket;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.json.JSONUtil;
import com.nimbusds.jose.JWSObject;
import lombok.RequiredArgsConstructor;
import org.irm.lab.common.api.R;
import org.irm.lab.common.utils.RedisUtil;
import org.irm.lab.common.utils.ThreadLocalUtil;
import org.irm.lab.config.entity.WorkTask;
import org.irm.lab.config.feign.WorkTaskFeign;
import org.irm.lab.repository.constant.RecordResourceType;
import org.irm.lab.repository.constant.RedisConstant;
import org.irm.lab.repository.constant.TransStatusConstant;
import org.irm.lab.repository.dto.TaskMonitoringDTO;
import org.irm.lab.repository.entity.BatchTaskResource;
import org.irm.lab.repository.entity.Resource;
import org.irm.lab.repository.entity.ResourceAnnex;
import org.irm.lab.repository.entity.TaskMonitoring;
import org.irm.lab.repository.service.IBatchTaskResourceService;
import org.irm.lab.repository.service.IResourceAnnexService;
import org.irm.lab.repository.service.IResourceSubmitService;
import org.irm.lab.repository.vo.ResourceVO;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2023/2/8 16:10
 * @description 任务监控中心业务实现类
 */
@Component
@RequiredArgsConstructor
public class TaskMonitoringCenter {
    private final IBatchTaskResourceService iBatchTaskResourceService;
    private final IResourceAnnexService iResourceAnnexService;
    private final IResourceSubmitService iResourceSubmitService;
    private final WorkTaskFeign workTaskFeign;
    private final RedisUtil redisUtil;


    /**
     * 任务中心
     *
     * @return String
     */
    public String taskCenter(String user) {
        String realToken = user.replace("Bearer", "");
        try {
            JWSObject jwsObject = JWSObject.parse(realToken);
            String userStr = jwsObject.getPayload().toString();
            ThreadLocalUtil.set("user", userStr);
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
        // 获取当前登录用户下的工作任务
        R<List<WorkTask>> workTaskR = workTaskFeign.list();
        if (!workTaskR.isSuccess()) return null;
        // 获取当前登录用户下的所有工作任务
        List<WorkTask> workTaskList = workTaskR.getData();
        // 获取当前用户所有的工作任务id
        List<String> workTaskIdList = workTaskR.getData().stream().map(WorkTask::getId).collect(Collectors.toList());
        // 创建集合存储多个TaskMonitoring对象
        ArrayList<TaskMonitoring> taskMonitoringList = new ArrayList<>();
        // 获取所有传输完成的批量提交任务，并过滤出只属于当前登录用户的批量提交任务
        List<BatchTaskResource> batchTaskResourceList = iBatchTaskResourceService.list()
                .stream().filter(batchTaskResource -> batchTaskResource.getTransStatus().equals(TransStatusConstant.TRANS_SUCCESS))
                .filter(batchTaskResource -> workTaskIdList.contains(batchTaskResource.getWorkTaskId()))
                .collect(Collectors.toList());
        // 根据批量提交任务获取每个批量提交任务下的所有资源对象
        initResource(taskMonitoringList, batchTaskResourceList);
        // 创建分布提交的任务监控
        generateTaskMonitoring(workTaskList, taskMonitoringList);
        // 转换为json字符串
        return JSONUtil.toJsonStr(taskMonitoringList);
    }

    /**
     * 生成任务监控
     */
    private void generateTaskMonitoring(List<WorkTask> workTaskList, ArrayList<TaskMonitoring> taskMonitoringList) {
        workTaskList.forEach(workTask -> {
            TaskMonitoring taskMonitoring = new TaskMonitoring();
            taskMonitoring.setWorkTaskName(workTask.getName());
            // 获取所有批量提交任务 k：(String)资源Id， v:(Resource)资源对象
            Map<Object, Object> resourceMap = redisUtil.hmget(RedisConstant.SINGLE_FILE_UPLOAD_PREFIX + workTask.getId());
            // 获取所有资源集合
            List<Resource> resouceList = resourceMap.values()
                    .stream()
                    .map(resource -> BeanUtil.toBean(resource, Resource.class))
                    .collect(Collectors.toList());
            // 设置多个分步提交资源
            taskMonitoring.setSingleFileResource(resouceList);
            // 添加到集合中
            taskMonitoringList.add(taskMonitoring);
        });
    }

    /**
     * 根据批量提交任务获取每个批量提交任务下的所有资源对象
     */
    private void initResource(ArrayList<TaskMonitoring> taskMonitoringList, List<BatchTaskResource> batchTaskResourceList) {
        batchTaskResourceList.forEach(batchTaskResource -> {
            // 获取工作任务对象
            WorkTask workTask = workTaskFeign.info(batchTaskResource.getWorkTaskId()).getData();
            // 创建任务监控对象
            TaskMonitoring taskMonitoring = new TaskMonitoring();
            // 只保留当前批量提交任务下的资源列表
            List<ResourceVO> resourceVOList = iResourceSubmitService.list().stream()
                    .filter(resource -> batchTaskResource.getId().equals(resource.getBatchTaskResourceId()))
                    .map(resource -> {
                        ResourceVO resourceVO = new ResourceVO();
                        BeanUtil.copyProperties(resource, resourceVO);
                        List<ResourceAnnex> resourceAnnexList = iResourceAnnexService.listByResourceId(resource.getId());
                        resourceVO.setResourceAnnexList(resourceAnnexList);
                        return resourceVO;
                    })
                    .collect(Collectors.toList());
            // 设置工作任务Id
            taskMonitoring.setWorkTaskId(workTask.getId());
            // 设置工作任务名称
            taskMonitoring.setWorkTaskName(workTask.getName());
            // 设置批量提交任务名称
            taskMonitoring.setBatchTaskResourceId(batchTaskResource.getId());
            // 设置批量提交任务名称
            taskMonitoring.setBatchTaskResourceName(batchTaskResource.getName());
            // 设置打包提交任务下的资源
            taskMonitoring.setBatchResourceList(resourceVOList);
            // 设置打包提交任务下的资源总数
            taskMonitoring.setTaskResourceTotalSize(batchTaskResource.getParseTotalSize() + batchTaskResource.getAttachTotalSize());
            // 设置需要解析的资源总数（主文件数量)
            taskMonitoring.setParseTotalSize(batchTaskResource.getParseTotalSize());
            // 过滤出解析成功的资源数量
            int parsedSize = (int) resourceVOList.stream()
                    .filter(resource -> resource.getStage().equals(RecordResourceType.STAGE_3) || resource.getStage().equals(RecordResourceType.STAGE_4) || resource.getStage().equals(RecordResourceType.STAGE_5)).count();
            taskMonitoring.setParsedSize(parsedSize);
            // 添加到集合中
            taskMonitoringList.add(taskMonitoring);
        });
    }


    /**
     * 删除任务监控任务
     *
     * @param taskMonitoringDTO {@link TaskMonitoringDTO}
     */
    public void removeTaskMonitoring(TaskMonitoringDTO taskMonitoringDTO) {
        // 获取工作任务Id
        String workTaskId = taskMonitoringDTO.getWorkTaskId();
        // 获取资源Id
        String resourceId = taskMonitoringDTO.getResourceId();
        // 获取批量提交任务Id
        String batchTaskResourceId = taskMonitoringDTO.getBatchTaskResourceId();
        // 删除所有任务监控
        if (ObjectUtil.isNotEmpty(workTaskId) && ObjectUtil.isEmpty(resourceId)) {
            // 1、删除批量删除中的所有指定工作任务下的批量提交任务
            iBatchTaskResourceService.removeByWorkTaskId(workTaskId);
            // 2、删除Redis中所有分布提交的任务监控
            redisUtil.del(RedisConstant.SINGLE_FILE_UPLOAD_PREFIX + workTaskId);
        }
        // 删除指定的批量提交任务
        if (ObjectUtil.isNotEmpty(batchTaskResourceId)) {
            iBatchTaskResourceService.removeByIds(List.of(batchTaskResourceId));
        }
        // 删除指定的分布提交任务
        if (ObjectUtil.isNotEmpty(resourceId) && ObjectUtil.isNotEmpty(workTaskId)) {
            redisUtil.hmdel(RedisConstant.SINGLE_FILE_UPLOAD_PREFIX + workTaskId, resourceId);
        }
    }

}
