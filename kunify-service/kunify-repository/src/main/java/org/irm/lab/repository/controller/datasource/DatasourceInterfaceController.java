package org.irm.lab.repository.controller.datasource;

import cn.hutool.core.bean.BeanUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.irm.lab.common.api.R;
import org.irm.lab.repository.dto.datasource.DatasourceInterfaceDTO;
import org.irm.lab.repository.entity.datasource.DatasourceDataStruct;
import org.irm.lab.repository.entity.datasource.DatasourceInterface;
import org.irm.lab.repository.service.IDatasourceDataStructService;
import org.irm.lab.repository.service.IDatasourceInterfaceService;
import org.irm.lab.repository.service.IDatasourceMetaMappingService;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@RestController
@RequestMapping("/datasource-interface")
@Api(value = "数据接口")
public class DatasourceInterfaceController {

    @Resource
    private IDatasourceInterfaceService datasourceInterfaceService;
    @Resource
    private IDatasourceDataStructService datasourceDataStructService;
    @Resource
    private IDatasourceMetaMappingService metaMappingService;

    @ApiOperation("数据源接口分类查询")
    @GetMapping("/list-by-datasource-group")
    public R<DatasourceInterfaceDTO> listByDatasourceIdAndGroup(@RequestParam String datasourceId, @RequestParam String group) {
        return R.data(datasourceInterfaceService.listByDatasourceIdAndGroup(datasourceId, group));
    }

    @ApiOperation("数据接口增改")
    @PostMapping("/save")
    public R<DatasourceInterface> save(@RequestBody DatasourceInterfaceDTO datasourceInterfaceDTO) {
        List<DatasourceDataStruct> requestDataStructList = datasourceInterfaceDTO.getRequestDataStructs().stream().map(
                datasourceDataStructVO -> BeanUtil.copyProperties(datasourceDataStructVO, DatasourceDataStruct.class)).collect(Collectors.toList());
        List<DatasourceDataStruct> responseDataStructList = datasourceInterfaceDTO.getResponseDataStructs().stream().map(
                datasourceDataStructVO -> BeanUtil.copyProperties(datasourceDataStructVO, DatasourceDataStruct.class)).collect(Collectors.toList());
        datasourceDataStructService.saveAll(requestDataStructList);
        datasourceDataStructService.saveAll(responseDataStructList);
        metaMappingService.saveAll(datasourceInterfaceDTO.getDatasourceMetaMappings());
        return R.data(datasourceInterfaceService.save(BeanUtil.copyProperties(datasourceInterfaceDTO, DatasourceInterface.class)));
    }

    @ApiOperation("数据接口基础信息")
    @GetMapping("/info")
    public R<DatasourceInterface> info(@RequestParam String id) {
        return R.data(datasourceInterfaceService.info(id));
    }

    @ApiOperation("数据接口详情")
    @GetMapping("/info-vo")
    public R<DatasourceInterfaceDTO> infoVO(@RequestParam String id) {
        return R.data(datasourceInterfaceService.infoVO(id));
    }
}
