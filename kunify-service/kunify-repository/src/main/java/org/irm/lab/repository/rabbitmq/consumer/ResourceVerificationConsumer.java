package org.irm.lab.repository.rabbitmq.consumer;

import com.rabbitmq.client.Channel;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import org.irm.lab.common.utils.ThreadLocalUtil;
import org.irm.lab.repository.rabbitmq.message.ResourceVerificationMessage;
import org.irm.lab.repository.service.IResourceVerificationService;
import org.springframework.amqp.core.Message;
import org.springframework.amqp.rabbit.annotation.RabbitHandler;
import org.springframework.amqp.rabbit.annotation.RabbitListener;
import org.springframework.stereotype.Component;

import java.io.IOException;


/**
 * <AUTHOR>
 * @date 2023/2/22 11:21
 * @description 消费者
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class ResourceVerificationConsumer {


    private final IResourceVerificationService iResourceVerificationService;

    /**
     * 消息监听
     */
    @RabbitListener(queues = ResourceVerificationMessage.QUEUE+"${spring.application.name}",concurrency = "3")
    @RabbitHandler
    public void onMessage(ResourceVerificationMessage message, Channel channel, Message messageStatus) throws IOException {
        // 获取消息状态信息
        long deliveryTag = messageStatus.getMessageProperties().getDeliveryTag();
        log.info("[onMessage][线程编号:{} 消息内容：{}]", Thread.currentThread().getId(), message);
        try {
            // 设置用户信息，防止租户问题
            ThreadLocalUtil.set("user",message.getUser());
            // 发起数据校验
            iResourceVerificationService.resourceVerification(message.getResource());
            // 手动ack
//            channel.basicAck(deliveryTag, false);
        }catch (Exception e){
            if (messageStatus.getMessageProperties().getRedelivered()) {
                log.error("消息已重复处理失败，拒绝再次接收.....");
                channel.basicReject(deliveryTag, false);
            } else {
                log.info("消息即将再次返回队列中进行处理....");
                channel.basicNack(deliveryTag, false, true);
            }
        }
    }


}
