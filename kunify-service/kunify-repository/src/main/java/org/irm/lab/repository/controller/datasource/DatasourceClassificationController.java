package org.irm.lab.repository.controller.datasource;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.irm.lab.common.annotation.MyLog;
import org.irm.lab.common.api.R;
import org.irm.lab.common.constant.LogConstant;
import org.irm.lab.common.vo.ListConstantVO;
import org.irm.lab.common.wrapper.ListConstantWrapper;
import org.irm.lab.repository.constant.DatasourceClassificationConstant;
import org.irm.lab.repository.entity.datasource.DatasourceClassification;
import org.irm.lab.repository.feign.DatasourceClassificationFeign;
import org.irm.lab.repository.service.IDatasourceClassificationService;
import org.irm.lab.repository.vo.DatasourceClassificationVO;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * <AUTHOR>
 * @date 2023-03-02
 */
@RestController
@RequestMapping("/datasource-classification")
@Api(value = "数据源分类")
public class DatasourceClassificationController implements DatasourceClassificationFeign {

    @Resource
    private IDatasourceClassificationService datasourceClassificationService;

    @MyLog(menu = LogConstant.MENU_DATASOURCE, dataType = LogConstant.DATA_DATASOURCE_CLASSIFICATION, operation = LogConstant.OPERATION_SYNC)
    @ApiOperation(value = "数据源分类同步")
    @GetMapping("/sync")
    public R<List<DatasourceClassificationVO>> syncClassification(@RequestParam String datasourceId) {
        return R.data(datasourceClassificationService.syncClassification(datasourceId));
    }

    @ApiOperation("数据源分类详情")
    @GetMapping("/info")
    public R<DatasourceClassification> info(@RequestParam String id) {
        return R.data(datasourceClassificationService.info(id));
    }

    @ApiOperation("数据源分类树")
    @GetMapping("/tree-list")
    public R<List<DatasourceClassificationVO>> treeList(String datasourceId) {
        return R.data(datasourceClassificationService.treeList(datasourceId));
    }

    @ApiOperation(value = "数据源分类增改", hidden = true)
    @PostMapping("/save")
    public R<DatasourceClassification> syncClassification(@RequestBody DatasourceClassification datasourceClassification) {
        return R.data(datasourceClassificationService.save(datasourceClassification));
    }

    @ApiOperation(value = "获取总对照表", hidden = true)
    @GetMapping("/all-transition")
    public R<Set<Map<String, Object>>> allTransition(@RequestParam String datasourceId)  {
        return R.data(datasourceClassificationService.allTransition(datasourceId));
    }

    @ApiOperation(value = "数据库映射", hidden = true)
    @GetMapping("/mapping-field")
    public R<List<ListConstantVO>> mappingField() {
        return R.data(ListConstantWrapper.listVO(DatasourceClassificationConstant.class));
    }
}
