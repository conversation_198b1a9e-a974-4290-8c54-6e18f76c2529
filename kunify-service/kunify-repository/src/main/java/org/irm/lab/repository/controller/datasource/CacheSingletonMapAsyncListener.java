package org.irm.lab.repository.controller.datasource;

import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.Map;

//单例map，用于维护监听事件
public class CacheSingletonMapAsyncListener {
    private static volatile CacheSingletonMapAsyncListener cacheSingletonMapAsyncListener;
    private static Map<String, Object> cacheSingletonMap;


    private CacheSingletonMapAsyncListener() {
        cacheSingletonMap = new HashMap<String, Object>();
    }

    /*
     * 单例模式有两种类型
     * 懒汉式：在真正需要使用对象时才去创建该单例类对象
     * 饿汉式：在类加载时已经创建好该单例对象，等待被程序使用
     */

    // 懒汉式单例模式
    public static CacheSingletonMapAsyncListener getInstance() {
        if (cacheSingletonMapAsyncListener == null) {// 线程A和线程B同时看到cacheSingletonUtil = null，如果不为null，则直接返回cacheSingletonUtil
            synchronized (CacheSingletonMapAsyncListener.class) {// 线程A或线程B获得该锁进行初始化
                if (cacheSingletonMapAsyncListener == null) {// 其中一个线程进入该分支，另外一个线程则不会进入该分支
                    cacheSingletonMapAsyncListener = new CacheSingletonMapAsyncListener();
                }
            }
        }
        return cacheSingletonMapAsyncListener;
    }

    /**
     * 添加到内存
     */
    public void addCacheData(String key, Object obj) {
        cacheSingletonMap.put(key, obj);
    }

    /**
     * 从内存中取出
     */
    public Object getCacheData(String key) {
        return cacheSingletonMap.get(key);
    }

    /**
     * 从内存中清除
     */
    public void removeCacheData(String key) {
        cacheSingletonMap.remove(key);
    }
}
