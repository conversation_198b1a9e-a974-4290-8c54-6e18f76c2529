package org.irm.lab.repository.controller;

import cn.hutool.core.util.ObjectUtil;
import com.mongodb.client.model.Filters;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.irm.lab.common.api.R;
import org.irm.lab.common.exception.ServiceException;
import org.irm.lab.common.pojo.BaseStatus;
import org.irm.lab.common.utils.ThreadLocalUtil;
import org.irm.lab.config.entity.WorkTask;
import org.irm.lab.config.feign.WorkTaskFeign;
import org.irm.lab.repository.constant.RecordResourceType;
import org.irm.lab.repository.entity.Resource;
import org.irm.lab.repository.repository.ResourceRepository;
import org.irm.lab.repository.service.ILinkVerificationService;
import org.irm.lab.repository.service.IResourceAfterProcessService;
import org.irm.lab.repository.service.IResourceService;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2023/2/22 17:25
 * @description 环节校验控制器
 */
@Slf4j
@RestController
@RequestMapping("/link-verification")
@Api(value = "环节校验控制器")
@RequiredArgsConstructor
public class LinkVerificationController {
    private final ILinkVerificationService iLinkVerificationService;
    private final WorkTaskFeign workTaskFeign;
    private final IResourceService iResourceService;
    private final ResourceRepository resourceRepository;
    private final IResourceAfterProcessService iResourceAfterProcessService;
    private final HttpServletRequest request;

    /**
     * 资源自动交接 ===> 发起任务（修改任务状态为“执行中”）时调用
     *
     * @param workTaskId 工作任务Id
     */
    @ApiOperation(value = "资源自动交接")
    @GetMapping("/auto-pass")
    public R<String> autoPass(@RequestParam("workTaskId") String workTaskId) {
        // 获取工作任务
        R<WorkTask> workTaskR = workTaskFeign.info(workTaskId);
        WorkTask workTask = workTaskR.getData();
        if (!workTaskR.isSuccess() || workTask==null) {
            throw new ServiceException("工作任务不存在!");
        }
        // 获取该工作任务下的全部资源
        List<Resource> resourceList = resourceRepository.findByCondition(Filters.eq("workTaskId", workTaskId));
        if (ObjectUtil.isEmpty(resourceList)) return R.failed("任务发起失败，该工作任务下不存在资源");
        // 判断该工作任务是否开启了自动交接
        if (iLinkVerificationService.isAutoPass(workTask)) {
            // 如果是自动交接的，还要判断是否开启了审核，如果没开启审核的话，资源状态直接修改为“已通过”
            if (!iLinkVerificationService.isAudit(workTask)) {
                // 修改资源状态
                List<Resource> passResourecList = resourceList.stream()
                        .peek(resource -> {
                            resource.setStage(RecordResourceType.STAGE_6);
                            resource.setStored(BaseStatus.TRUE);
                        })
                        .collect(Collectors.toList());
                // 批量更新资源数据
                List<Resource> resources = iResourceService.saveBatch(passResourecList);
                // 1、自动通过的资源 ===> 发送消息，开启资源实例化
                log.info("【发起任务】资源无需交接和审核");
                List<String> resourceIdList = resources.stream().map(Resource::getId).collect(Collectors.toList());
                ThreadLocalUtil.set("user", request.getHeader("user"));
                iResourceAfterProcessService.afterProcess(resourceIdList);
            } else {
                // 把所有资源的状态设置为“待审核”
                List<Resource> newResourceList = resourceList.stream()
                        .map(resource -> resource.setStage(RecordResourceType.STAGE_4))
                        .collect(Collectors.toList());
                // 批量更新资源数据
                iResourceService.saveBatch(newResourceList);
            }
        }
        return R.success();
    }
}
