package org.irm.lab.repository.controller.datasource;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.irm.lab.common.api.R;
import org.irm.lab.common.utils.Func;
import org.irm.lab.repository.entity.datasource.DatasourceMetaMapping;
import org.irm.lab.repository.service.IDatasourceMetaMappingService;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

@RestController
@RequestMapping("/datasource-meta-mapping")
@Api(value = "元数据映射")
public class DatasourceMetaMappingController {

    @Resource
    private IDatasourceMetaMappingService metaMappingService;

    @ApiOperation("元数据映射增改")
    @PostMapping("/save")
    public R<DatasourceMetaMapping> save(@RequestBody DatasourceMetaMapping datasourceMetaMapping) {
        return R.data(metaMappingService.save(datasourceMetaMapping));
    }

    @ApiOperation("元数据映射删除")
    @PostMapping("/remove")
    public R<Void> remove(@RequestBody String ids) {
        metaMappingService.remove(Func.objToStrList(ids));
        return R.success();
    }

    @ApiOperation("元数据映射详情")
    @GetMapping("/info")
    public R<DatasourceMetaMapping> info(@RequestParam String id) {
        return R.data(metaMappingService.info(id));
    }
}
