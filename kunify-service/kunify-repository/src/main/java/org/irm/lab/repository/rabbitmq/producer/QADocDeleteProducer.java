package org.irm.lab.repository.rabbitmq.producer;

import org.irm.lab.repository.rabbitmq.message.QADocDeleteMessage;
import org.irm.lab.repository.rabbitmq.message.ResourceRepeatDeleteMessage;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * AI中台删除文档
 */
@Component
public class QADocDeleteProducer {

    @Resource
    private RabbitTemplate rabbitTemplate;
    /**
     * 获取RoutingKey尾缀
     */
    @Value("${spring.application.name}")
    private String KEY_SELF;

    /**
     * 发送消息
     */
    public void sendMessage(QADocDeleteMessage message) {
        // 发送消息
        rabbitTemplate.convertAndSend(QADocDeleteMessage.EXCHANGE,
                QADocDeleteMessage.ROUTING_KEY + KEY_SELF,
                message);
    }

}
