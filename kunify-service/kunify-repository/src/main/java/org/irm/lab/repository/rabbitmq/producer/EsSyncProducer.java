package org.irm.lab.repository.rabbitmq.producer;

import lombok.RequiredArgsConstructor;
import org.irm.lab.kg.message.EsSyncMessage;
import org.irm.lab.repository.entity.Resource;
import org.irm.lab.repository.entity.ResourceAnnex;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.stereotype.Component;

import java.util.Arrays;

/**
 * <AUTHOR>
 * @date 2023/4/17 16:27
 * @description Es同步消息
 */
@Component
@RequiredArgsConstructor
public class EsSyncProducer {
    private final RabbitTemplate rabbitTemplate;

    /**
     * 发送消息
     */
    public void sendMessage(String user, Resource resource, ResourceAnnex resourceAnnex, String content, String fileType) {
        EsSyncMessage esSyncMessage = new EsSyncMessage();
        esSyncMessage.setUser(user);
        esSyncMessage.setResource(resource);
        esSyncMessage.setResourceAnnex(resourceAnnex);
        esSyncMessage.setContent(content);
        esSyncMessage.setFileType(fileType);
        // 发送消息
        rabbitTemplate.convertAndSend(EsSyncMessage.EXCHANGE, EsSyncMessage.ROUTING_KEY, esSyncMessage);
    }
}
