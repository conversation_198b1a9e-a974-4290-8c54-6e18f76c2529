package org.irm.lab.repository.rabbitmq.producer;

import java.util.HashMap;

import org.irm.lab.kg.algorithm.DocumentImage;

import org.irm.lab.kg.message.OCRMessage;
import org.irm.lab.repository.entity.Resource;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.stereotype.Component;

import lombok.RequiredArgsConstructor;

/**
 * <AUTHOR> <br/>
 * @date 2024/6/26 <br/>
 * &#064;Copyright  博客：<a href="https://eliauku.gitee.io/">...</a>  ||  per aspera and astra <br/>
 */
@Component
@RequiredArgsConstructor
public class OCRProducer {
    private final RabbitTemplate rabbitTemplate;


    /**
     * 发送消息
     */
    public void sendMessage(String user, Resource resourceAnnex, DocumentImage annexDocumentImage, Integer page, Boolean isEnd, String... esup){
        OCRMessage documentParsingMessage = new OCRMessage();
        documentParsingMessage.setUser(user);
        documentParsingMessage.setResource(resourceAnnex);
        documentParsingMessage.setDocumentImage(annexDocumentImage);
        documentParsingMessage.setPage(page);
        documentParsingMessage.setIsEnd(isEnd);
        if (esup.length>0) {
            final HashMap<String, Object> objectObjectHashMap = new HashMap<>();

            objectObjectHashMap.put("isES", esup[0]);
            documentParsingMessage.setMap(objectObjectHashMap);
        }

        // 发送消息
        rabbitTemplate.convertAndSend(OCRMessage.EXCHANGE, OCRMessage.ROUTING_KEY+"kunify-kg",documentParsingMessage);
    }
}
