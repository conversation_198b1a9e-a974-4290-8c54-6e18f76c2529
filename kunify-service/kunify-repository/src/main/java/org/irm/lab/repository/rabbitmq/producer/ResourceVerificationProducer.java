package org.irm.lab.repository.rabbitmq.producer;

import lombok.RequiredArgsConstructor;
import org.irm.lab.repository.entity.Resource;
import org.irm.lab.repository.rabbitmq.message.ResourceVerificationMessage;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;



/**
 * <AUTHOR>
 * @date 2023/2/22 11:20
 * @description 生产者
 */
@Component
@RequiredArgsConstructor
public class ResourceVerificationProducer {
    private final RabbitTemplate rabbitTemplate;

    @Value("${spring.application.name}")
    private String KEY_SELF;

    /**
     * 发送消息
     */
    public void sendMessage(String user,Resource resource){
        ResourceVerificationMessage resourceVerificationMessage = new ResourceVerificationMessage();
        resourceVerificationMessage.setResource(resource);
        resourceVerificationMessage.setUser(user);
        // 发送消息
        rabbitTemplate.convertAndSend(ResourceVerificationMessage.EXCHANGE, ResourceVerificationMessage.ROUTING_KEY + KEY_SELF,resourceVerificationMessage);
    }
}
