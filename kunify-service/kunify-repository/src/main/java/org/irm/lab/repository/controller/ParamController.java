package org.irm.lab.repository.controller;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.irm.lab.common.api.R;
import org.irm.lab.common.entity.ApiMessage;
import org.irm.lab.repository.service.IParamService;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

@Api(value = "数据权限获取", hidden = true)
@RestController
@RequestMapping("/param")
public class ParamController {

    @Resource
    private IParamService paramService;

    @ApiOperation(value = "获取当前模块的数据权限", hidden = true)
    @GetMapping("/getParam")
    public R<List<ApiMessage>> getParam() {
        return R.data(paramService.getParam());
    }

}
