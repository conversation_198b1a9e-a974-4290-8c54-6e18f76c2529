package org.irm.lab.repository.rabbitmq.message;

import lombok.Data;
import lombok.experimental.Accessors;
import org.irm.lab.kg.entity.neo4j.node.NodeEntity;
import org.irm.lab.repository.entity.Resource;

import java.io.Serializable;
import java.util.List;
import java.util.Map;


//TODO 数据同步暂时使用redis+cron的方式解决，有时间在改成rabbit
@Data
@Accessors(chain = true)
public class DatasourceResourceSyncMessage implements Serializable {
    /**
     * 交换机
     */
    public static final String EXCHANGE = "KUNIFY4_EXCHANGE_RESOURCE_RESOURCE_SYNC";
    /**
     * 消息队列
     */
    public static final String QUEUE = "KUNIFY4_QUEUE_RESOURCE_RESOURCE_SYNC";
    /**
     * routingKey
     */
    public static final String ROUTING_KEY = "KUNIFY4_ROUTING_RESOURCE_RESOURCE_SYNC";

    /**
     * 当前用户信息
     */
    private String user;


    /**
     * 公文分类
     */
    private String type;

    /**
     * 数据源ID
     */
    private String datasourceId;

    /**
     * 当前页码
     */
    private Long pageIndex;

    /**
     * 页大小
     */
    private Long pageSize;

    private String conceptId;
}
