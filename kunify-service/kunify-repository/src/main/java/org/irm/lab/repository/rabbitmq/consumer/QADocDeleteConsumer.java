package org.irm.lab.repository.rabbitmq.consumer;

import com.mongodb.client.model.Filters;
import com.rabbitmq.client.Channel;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.irm.lab.common.utils.ThreadLocalUtil;
import org.irm.lab.repository.entity.ResourceAnnex;
import org.irm.lab.repository.entity.ResourceTemp;
import org.irm.lab.repository.rabbitmq.message.QADocDeleteMessage;
import org.irm.lab.repository.repository.ResourceTempRepository;
import org.irm.lab.repository.service.IResourceService;
import org.springframework.amqp.core.Message;
import org.springframework.amqp.rabbit.annotation.RabbitHandler;
import org.springframework.amqp.rabbit.annotation.RabbitListener;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.io.IOException;
import java.util.List;


/**
 * AI中台文档删除
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class QADocDeleteConsumer {

    @Resource
    private IResourceService resourceService;
    @Resource
    private ResourceTempRepository resourceTempRepository;

    /**
     * 消息监听
     */
    @RabbitListener(queues = QADocDeleteMessage.QUEUE + "${spring.application.name}", concurrency = "1")
    @RabbitHandler
    public void onMessage(QADocDeleteMessage message, Channel channel, Message messageStatus) {
        log.info("[onMessage][线程编号:{} 消息内容：{}]", Thread.currentThread().getId(), message);
        // 设置用户信息，防止租户问题
        ThreadLocalUtil.set("user", message.getUser());
        ResourceTemp resourceTemp = message.getResourceTemp();
        try {
            List<String> errorIds = resourceService.deleteQaDoc(resourceTemp.getDeleteMainDocIds());
            resourceTemp.setDeleteErrorDocIds(errorIds);
            log.info("当前批次【{}】，删除文档【{}】个，失败了【{}】个", resourceTemp.getId(), resourceTemp.getDeleteMainDocIds().size(), errorIds.size());
        } catch (Exception e) {
            e.printStackTrace();
            resourceTemp.setDeleteErrorDocIds(resourceTemp.getDeleteMainDocIds());
            log.error("消息已处理失败！");
        }
        resourceTempRepository.save(resourceTemp);
    }


}
