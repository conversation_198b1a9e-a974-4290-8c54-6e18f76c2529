package org.irm.lab.repository.websocket;

import java.util.concurrent.ConcurrentHashMap;

public class SingletonWebSocketMap {

    private SingletonWebSocketMap() {

    }

    private static class concurrentHashMapInstance {
        private static final ConcurrentHashMap<String, WebSocketServer> webSocketMap = new ConcurrentHashMap<>();
    }

    public static ConcurrentHashMap<String, WebSocketServer> getInstance() {
        return concurrentHashMapInstance.webSocketMap;
    }

}
