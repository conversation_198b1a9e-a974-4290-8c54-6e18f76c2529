package org.irm.lab.repository.drainage.handler;

import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;
import org.irm.lab.repository.drainage.IDrainageStrategy;
import org.irm.lab.repository.entity.datasource.DatasourceResource;

import java.util.List;

@Getter
@Setter
@Accessors(chain = true)
public abstract class ResourceRegexHandler {

    //后一个处理器
    private ResourceRegexHandler next;

    //需要处理的外部资源
    private List<DatasourceResource> processResource;


    public abstract ResourceRegexHandler handle(IDrainageStrategy drainageStrategy, List<DatasourceResource> resources);

}
