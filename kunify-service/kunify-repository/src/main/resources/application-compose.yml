spring:
  cloud:
    nacos:
      discovery:
        server-addr: kunify-nacos:8848
  redis:
    database: 3
    port: 6379
    host: kunify-redis
    password: PsOsGgN0rfXfIPbz
  rabbitmq:
    host: kunify-rabbitmq # RabbitMQ 服务的地址
    port: 5672 # RabbitMQ 服务的端口
    username: kunify # RabbitMQ 服务的账号
    password: kunify@411 # RabbitMQ 服务的密码
mongodb:
  host: kunify-mongodb
  port: 27017
  database: kunify-hn
  username: root
  password: mongo2022
  authenticationDatabase: admin
neo4j:
  url: bolt://kunify-neo4j:7687
  username: neo4j
  password: neo4j123
minio:
  endpoint: http://kunify-minio:9000
  access-key: MINIO
  secret-key: RbCWAc9BRWsaHEYzCag7vFx3OWY06b7e
  bucket: kunify-hn-
ai:
  sdkHost: *************:1000
  sdkSecretKey: kc2HMKE9sTFgew0
  sdkApikey: VW3H6uDjgr
  qaHost: **************:1000
  qaDataSet: 1083451845453545472
  qaUrl: http://************:8080/rag/doc/syncUpload
  qaTenant: 100001

logging:
  level:
    root: INFO
  file:
    name: /data/kunify-repository.log

unKnowErrorInfo: false
tenantId: "028639"