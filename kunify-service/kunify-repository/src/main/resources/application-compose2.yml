spring:
  cloud:
    nacos:
      discovery:
        server-addr: ***********:8848
  redis:
    database: 3
    port: 6379
    host: ***********
    password: kunify@411
  rabbitmq:
    host: *********** # RabbitMQ 服务的地址
    port: 5672 # RabbitMQ 服务的端口
    username: root # RabbitMQ 服务的账号
    password: 123456 # RabbitMQ 服务的密码
mongodb:
  host: ***********
  port: 27017
  database: kunify-hn
  username: root
  password: mongo2022
  authenticationDatabase: admin
neo4j:
  url: bolt://***********:27687
  username: neo4j
  password: 123456
minio:
  endpoint: http://***********:9000
  access-key: admin
  secret-key: minio2022
  bucket: kunify-hn-
ai:
  sdkHost: ***********:1000
  sdkSecretKey: kc2HMKE9sTFgew0
  sdkApikey: VW3H6uDjgr
  qaHost: ***********:1000
  qaDataSet: 1083451845453545472
  qaUrl: http://************:8080/rag/doc/syncUpload
  qaTenant: 100001
# 请求接口变量
datasourceUrl:
  ip: **************
  port: 80
  #  访问ip + 端口
  domain: http://**************:80
  #  登录(获取token)
  loginPath: "/auth/login"
  #  登录名称
  userName: dzgwzyk
  #  登陆密码
  password: ASDzxc@2024
  #  获取所有业务类型
  allBizType: /zisecm/metadata/getAllBizType
  #  根据分类获取元数据对照表
  metadataByType: /zisecm/metadata/getMetadataByType
  #  获取文件预览页面
  filePreview: /#/viewDoc
  #  获取甲方短token
  shortToken: /zisecm/view/preview
  #  无水印文件下载
  downloadNoWaterMark: /zisecm/sync/doc/downloadNoWatermark
  downloadMarkZip: /zisecm/sync/doc/download
  getDoc: /zisecm/sync/doc/getdoc
  #  拉取指定文件
  specifySync: /zisecm/sync/single

img-constant:
  internet-domain: http://***********:9000
  domain: http://**************:9000
  bucket: /kunify-hn-

qa:
  dataset:
    all-id: 1793159313529307137
    bzry-id: 1783409342967508994
    hyjy-id: 1787673080802955266
    gzzd-id: 1787424937336684545
    ldps-id: 1783409270379823106
    after-id: 111

logging:
  level:
    root: INFO
  file:
    name: /data/kunify-repository.log