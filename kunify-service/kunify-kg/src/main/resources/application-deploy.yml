spring:
  cloud:
    nacos:
      discovery:
        server-addr: ***********:8848
        metadata:
          preserved.heart.beat.interval: 1000
          preserved.heart.beat.timeout: 3000
          preserved.ip.delete.timeout: 3000
  redis:
    database: 2
    port: 6379
    host: ***********
    password: kunify@411
  rabbitmq:
    host: *********** # RabbitMQ 服务的地址
    port: 5672 # RabbitMQ 服务的端口
    username: kunify # RabbitMQ 服务的账号
    password: kunify@411 # RabbitMQ 服务的密码
  # 配置SpringCache
  cache:
    redis:
      cache-null-values: true
      key-prefix: CACHE_
      use-key-prefix: true
      time-to-live: 3600000
  elasticsearch:
    rest:
      uris: ***********:39200
      username: elastic
      password: 123456
neo4j:
  url: bolt://***********:27687
  username: neo4j
  password: 123456
mongodb:
  host: ***********
  port: 27017
  database: kunify-hn
  username: root
  password: mongo2022
  authenticationDatabase: admin
ai:
  sdkHost: ***********:1000
  sdkSecretKey: kc2HMKE9sTFgew0
  sdkApikey: VW3H6uDjgr
  qaHost: ***********:1000
  qaDataSet: 1083451845453545472
feign:
  client:
    config:
      default:
        connectTimeout: 10000 #单位毫秒
        readTimeout: 60000 #单位毫秒