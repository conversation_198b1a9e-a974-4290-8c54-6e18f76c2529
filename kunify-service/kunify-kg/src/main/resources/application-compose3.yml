spring:
  cloud:
    nacos:
      discovery:
        server-addr: ************:8848
  redis:
    database: 2
    port: 6379
    host: ************
    password: kunify@411

  rabbitmq:
    host: ************ # RabbitMQ 服务的地址
    port: 5672 # RabbitMQ 服务的端口
    username: root # RabbitMQ 服务的账号
    password: 123456 # RabbitMQ 服务的密码

mongodb:
  host: ************
  port: 27017
  database: kunify-hn
  username: root
  password: mongo2022
  authenticationDatabase: admin

mongo-plus:
  data:
    mongodb:
      host: ************   #ip
      port: 27017   #端口
      database: kunify-hn    #数据库名
      username: root    #用户名，没有可不填（若账号中出现@,!等等符号，不需要再进行转码！！！）
      password: mongo2022    #密码，同上（若密码中出现@,!等等符号，不需要再进行转码！！！）
      authenticationDatabase: admin     #验证数据库
      connectTimeoutMS: 50000   #在超时之前等待连接打开的最长时间（以毫秒为单位）

neo4j:
  url: bolt://************:27687
  username: neo4j
  password: 123456

ai:
  sdkHost: ************:1000
  sdkSecretKey: kc2HMKE9sTFgew0
  sdkApikey: VW3H6uDjgr
  qaHost: ***********:1000
  qaDataSet: 1083451845453545472

logging:
  level:
    root: INFO
  file:
    name: /data/kunify-kg.log