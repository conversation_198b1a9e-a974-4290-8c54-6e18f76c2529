package org.irm.lab.kg.wrapper;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.ObjectUtil;
import org.irm.lab.common.support.BaseEntityWrapper;
import org.irm.lab.common.utils.SpringUtil;
import org.irm.lab.kg.entity.KnowledgeConcept;
import org.irm.lab.kg.entity.KnowledgeRelation;
import org.irm.lab.kg.entity.RelationTriple;
import org.irm.lab.kg.repository.KnowledgeConceptRepository;
import org.irm.lab.kg.repository.KnowledgePropertyRepository;
import org.irm.lab.kg.vo.KnowledgeRelationVO;

import java.util.ArrayList;
import java.util.List;

public class KnowledgeRelationWrapper extends BaseEntityWrapper<KnowledgeRelation, KnowledgeRelationVO> {

    public static KnowledgeRelationWrapper build() {
        return new KnowledgeRelationWrapper();
    }

    private static final KnowledgeConceptRepository conceptRepository = SpringUtil.getBean(KnowledgeConceptRepository.class);

    private static final KnowledgePropertyRepository propertyRepository = SpringUtil.getBean(KnowledgePropertyRepository.class);

    public List<KnowledgeRelationVO> listVO(List<KnowledgeRelation> relationList){
        List<KnowledgeRelationVO> listVO = new ArrayList<>();
        List<String> classIfyList = new ArrayList<>();
        relationList.forEach(relation -> {
            KnowledgeRelationVO relationVO = entityVO(relation);
            String classify = relationVO.getConceptTwoObj().getName() + "-" + relationVO.getConceptOneObj().getName() + "关系集";
            if (classIfyList.contains(classify)){
                relationVO.setClassify(classify);
            }else {
                classIfyList.add(relationVO.getClassify());
            }
            listVO.add(relationVO);
        });
        return listVO;
    }
    @Override
    public KnowledgeRelationVO entityVO(KnowledgeRelation relation) {
        KnowledgeRelationVO relationVO = BeanUtil.copyProperties(relation, KnowledgeRelationVO.class);
        //添加概念实体
        KnowledgeConcept conceptOneObj = conceptRepository.findById(relation.getConceptOne());
        KnowledgeConcept conceptTwoObj = conceptRepository.findById(relation.getConceptTwo());
        relationVO.setConceptOneObj(conceptOneObj);
        relationVO.setConceptTwoObj(conceptTwoObj);
        //生成当前关系下的正反向关系的三元组
        RelationTriple forwardTriple = new RelationTriple(conceptOneObj,relation.getForwardPre(),conceptTwoObj);
        RelationTriple inverseTriple = new RelationTriple(conceptTwoObj,relation.getInversePre(),conceptOneObj);
        //添加正反向关系的三元组
        List<RelationTriple> triple = List.of(forwardTriple, inverseTriple);
        relationVO.setTriples(triple);

        if (conceptOneObj.getName().equals(conceptTwoObj.getName())) {
            relationVO.setClassify(conceptOneObj.getName() + "关系集");
        } else {
            relationVO.setClassify(conceptOneObj.getName() + "-" + conceptTwoObj.getName() + "关系集");
        }

        //添加属性到VO
        if (ObjectUtil.isNotEmpty(relation.getPropertyIds())) {
            relationVO.setProperties(propertyRepository.findById(relation.getPropertyIds()));
        }
        return relationVO;
    }
}
