package org.irm.lab.kg.service.annex.impl;

import cn.hutool.core.convert.Convert;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONObject;
import com.mongodb.client.model.Filters;
import lombok.RequiredArgsConstructor;
import org.bson.Document;
import org.bson.conversions.Bson;
import org.irm.lab.common.api.R;
import org.irm.lab.common.exception.ServiceException;
import org.irm.lab.config.entity.Label;
import org.irm.lab.config.repository.LabelRepository;
import org.irm.lab.kg.entity.annex.processing.AnnexCacheLabelData;
import org.irm.lab.kg.entity.annex.processing.AnnexCacheTripleLabelData;
import org.irm.lab.kg.entity.processing.CacheLabelData;
import org.irm.lab.kg.entity.processing.LabelPropertyVO;
import org.irm.lab.kg.kgprocess.RuleParsingStrategy;
import org.irm.lab.kg.repository.annex.AnnexCacheLabelDataRepository;
import org.irm.lab.kg.repository.annex.AnnexCacheTripleLabelDataRepository;
import org.irm.lab.kg.service.annex.IAnnexRuleParsingGraphService;
import org.irm.lab.repository.constant.DocumentResolvedStatus;
import org.irm.lab.repository.constant.ResourceProcessStatus;
import org.irm.lab.repository.entity.ResourceAnnex;
import org.irm.lab.repository.feign.ResourceAnnexFeign;
import org.irm.lab.repository.feign.ResourceFeign;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2023/5/9 14:39
 * @description 附件规则解析业务实现类
 */
@Service
@RequiredArgsConstructor
public class AnnexRuleParsingGraphServiceImpl implements IAnnexRuleParsingGraphService {

    private final LabelRepository labelRepository;
    private final AnnexCacheLabelDataRepository annexCacheLabelDataRepository;
    private final AnnexCacheTripleLabelDataRepository annexCacheTripleLabelDataRepository;
    private final ResourceAnnexFeign resourceAnnexFeign;
    private final RuleParsingStrategy ruleParsingStrategy;
    private final ResourceFeign resourceFeign;

    /**
     * 附件规则解析 ===> 获取标签数据树
     *
     * @param annexId   附件Id
     * @param dataSetId 数据集Id
     * @return 标签数据
     */
    @Override
    public JSONArray relevanceShow(String annexId, String dataSetId) {
        HashMap<String, Label> labelMap = new HashMap<>();
        Map<String, List<JSONObject>> labelDatamap = entitySortByKeyValue(dataSetId);
        Bson and = Filters.and(Filters.eq("annexId", annexId), Filters.eq("type", "规则解析"));
        Bson and1 = Filters.and(Filters.eq("annexId", annexId), Filters.eq("topLabel", true));
        // 获取该附件的所有关系三元组
        List<AnnexCacheTripleLabelData> allAnnexCacheTripleLabelDataList = annexCacheTripleLabelDataRepository.findByCondition(and);
        Map<String, List<AnnexCacheTripleLabelData>> collect = allAnnexCacheTripleLabelDataList.stream().collect(Collectors.groupingBy(AnnexCacheTripleLabelData::getStartLabelDataId));
        // 获取该附件的所有标签数据
        List<AnnexCacheLabelData> annexCacheLabelDataList = annexCacheLabelDataRepository.findByCondition(Filters.or(and, and1));
        // 遍历所以标签数据
        for (AnnexCacheLabelData annexCacheLabelData : annexCacheLabelDataList) {
            // 创建数组存储关系
            List<JSONObject> relations = new ArrayList<>();
            // 把标签数据的属性放在和关系平级的位置
            // 给属性封装名称
            for (LabelPropertyVO labelProperty : annexCacheLabelData.getLabelProperties()) {
                // 获取属性标签
                String labelPropertyId = labelProperty.getLabelPropertyId();
                Label propertyLabel = getLabel(labelMap, labelPropertyId);
                if (propertyLabel == null)
                    throw new ServiceException("数据集标签不存在，请检查数据集，并修改后重新解析！");
                // 为属性设置标签名称
                labelProperty.setLabelPropertyName(propertyLabel.getName());
                // 把属性封装为指定JSONObject
                JSONObject labelDataPropertyObject = new JSONObject(labelProperty);
                // 添加层级
                labelDataPropertyObject.putOpt("level", 3);
                // 把属性放在关系前面
                if (ObjectUtil.isNotEmpty(labelDataPropertyObject)) relations.add(labelDataPropertyObject);
            }
            // 获取每个数据标签 作为主语的 关系三元组
            List<AnnexCacheTripleLabelData> annexCacheTripleLabelDataList = collect.get(annexCacheLabelData.getId());
            if (ObjectUtil.isNotEmpty(annexCacheTripleLabelDataList)) {
                for (AnnexCacheTripleLabelData annexCacheTripleLabelData : annexCacheTripleLabelDataList) {
                    // 生成关系对象
                    generateRelation(relations, annexCacheTripleLabelData, labelMap);
                }
            }
            // 给标签数据的标签Id封装为labelName
            String conceptLabelId = annexCacheLabelData.getLabelId();
            Label conceptLabel = getLabel(labelMap, conceptLabelId);
            if (conceptLabel == null) throw new ServiceException("数据集标签不存在，请检查数据集，并修改后重新解析！");
            annexCacheLabelData.setLabelName(conceptLabel.getName());
            // 转换为JSONObject
            JSONObject labelDataJsonObject = new JSONObject(annexCacheLabelData);
            // 添加 层级
            labelDataJsonObject.putOpt("level", 2);
            // 添加关系
            labelDataJsonObject.putOpt("children", relations);
            // 根据标签Id查询标签对象，并获取标签名称
            String labelId = annexCacheLabelData.getLabelId();
            Label label = getLabel(labelMap, labelId);
            if (label == null) throw new ServiceException("数据集标签不存在，请检查数据集，并修改后重新解析！");
            // 添加结果
            List<JSONObject> list = labelDatamap.getOrDefault(label.getName(), new ArrayList<>());
            list.add(labelDataJsonObject);
            list = list.stream().sorted(Comparator.comparing(json -> Convert.toStr(json.getOrDefault("content", "1")))).collect(Collectors.toList());
            labelDatamap.put(label.getName(), list);

        }
        // 封装标签数据
        JSONArray jsonArray = new JSONArray();
        Set<String> keySet = labelDatamap.keySet();
        for (String key : keySet) {
            JSONObject jsonObject = new JSONObject();
            // 获取标签ID
            String labelId = labelDatamap.get(key).get(0).getStr("labelId");
            jsonObject.putOpt("labelId", labelId);
            jsonObject.putOpt("labelName", key);
            jsonObject.putOpt("children", labelDatamap.get(key));
            jsonObject.putOpt("level", 1);
            jsonArray.add(jsonObject);
        }
        // 返回结果
        return jsonArray;

    }

    /**
     * 获取标签对象
     *
     * @param labelMap 标签缓存Map
     * @param labelId  标签Id
     * @return 标签对象
     */
    private Label getLabel(Map<String, Label> labelMap, String labelId) {
        Label label = labelMap.get(labelId);
        if (label == null) {
            try {
                label = labelRepository.findById(labelId);
            } catch (Exception e) {
                return null;
            }
            labelMap.put(labelId, label);
        }
        return label;
    }


    /**
     * 获取该附件下的所有标签数据
     *
     * @param annexId 附件Id
     * @return {@link CacheLabelData}
     */
    @Override
    public List<AnnexCacheLabelData> listCacheLabelData(String annexId) {
        Bson and = Filters.and(Filters.eq("annexId", annexId), Filters.eq("type", "规则解析"));
        return annexCacheLabelDataRepository.findByCondition(and);
    }

    /**
     * 新增或修改标签数据对象
     *
     * @param annexCacheLabelData {@link AnnexCacheLabelData}
     */
    @Override
    public void saveOrUpdateLabelData(AnnexCacheLabelData annexCacheLabelData) {
        // 新增属性 和 修改属性
        if (ObjectUtil.isNotEmpty(annexCacheLabelData.getLabelProperties())) {
            // 新增属性 没有 identifier
            List<LabelPropertyVO> labelProperties = annexCacheLabelData.getLabelProperties();
            for (LabelPropertyVO labelProperty : labelProperties) {
                // 构建属性对象
                HashMap<String, Object> filterMap = new HashMap<>();
                filterMap.put("labelPropertyId", labelProperty.getLabelPropertyId());
                filterMap.put("value", labelProperty.getValue());
                // 如果不存在identifier 就是新增属性
                if (ObjectUtil.isEmpty(labelProperty.getIdentifier())) {
                    // 设置唯一标识
                    filterMap.put("identifier", IdUtil.simpleUUID());
                } else {
                    // 修改属性，先删除原来的属性
                    annexCacheLabelDataRepository.pullDistinct(annexCacheLabelData.getId(), Map.of("labelProperties", new Document("identifier", labelProperty.getIdentifier())));
                    // 设置原来的唯一标识
                    filterMap.put("identifier", labelProperty.getIdentifier());
                }
                // 添加新的属性
                annexCacheLabelDataRepository.pushDistinct(annexCacheLabelData.getId(), Map.of("labelProperties", new Document(filterMap)));
            }
            return;
        }
        // 如果属性为空，新增或修改实例
        if (ObjectUtil.isEmpty(annexCacheLabelData.getLabelProperties())) {
            // 新增实例时,把属性字段置空
            annexCacheLabelData.setLabelProperties(null);
        }
        // 新增或修改标签数据
        annexCacheLabelDataRepository.save(annexCacheLabelData);
    }

    /**
     * 删除实例/实例的属性
     *
     * @param annexCacheLabelData 附件缓存标签数据实例
     */
    @Override
    public void removeLabelData(AnnexCacheLabelData annexCacheLabelData) {
        // 获取缓存标签数据ID
        String cacheLabelDataId = annexCacheLabelData.getId();
        AnnexCacheLabelData deleteCacheLabelData = annexCacheLabelDataRepository.findById(cacheLabelDataId);
        // 删除实例
        if (annexCacheLabelData.getLabelProperties() == null || annexCacheLabelData.getLabelProperties().isEmpty()) {
            if (deleteCacheLabelData.isTopLabel()) {
                throw new ServiceException("顶级附件标签无法删除!");
            }
            // 删除标签数据
            annexCacheLabelDataRepository.deleteByIdFake(cacheLabelDataId);
            // 删除该标签数据涉及的关系三元组
            Bson or = Filters.or(Filters.eq("startLabelDataId", cacheLabelDataId), Filters.eq("endLabelDataId", cacheLabelDataId));
            List<AnnexCacheTripleLabelData> annexCacheTripleLabelDataList = annexCacheTripleLabelDataRepository.findByCondition(or);
            // 删除多个三元组
            if (ObjectUtil.isNotEmpty(annexCacheTripleLabelDataList)) {
                List<String> cacheTripleLabelDataId = annexCacheTripleLabelDataList.stream().map(AnnexCacheTripleLabelData::getId).collect(Collectors.toList());
                annexCacheTripleLabelDataRepository.deleteByIdFake(cacheTripleLabelDataId);
            }
        }
        // 删除属性
        if (ObjectUtil.isNotEmpty(annexCacheLabelData.getLabelProperties())) {
            for (LabelPropertyVO labelProperty : annexCacheLabelData.getLabelProperties()) {
                // 删除属性
                annexCacheLabelDataRepository.pullDistinct(cacheLabelDataId, Map.of("labelProperties", new Document("identifier", labelProperty.getIdentifier())));
            }
        }
    }

    /**
     * 新增/修改关系三元组
     *
     * @param annexCacheTripleLabelDataList {@link AnnexCacheTripleLabelData}
     */
    @Override
    public void saveOrUpdateTripleLabelData(List<AnnexCacheTripleLabelData> annexCacheTripleLabelDataList) {
        for (AnnexCacheTripleLabelData annexCacheTripleLabelData : annexCacheTripleLabelDataList) {
            // 新增或修改属性
            if (ObjectUtil.isNotEmpty(annexCacheTripleLabelData.getRelationProperties())) {
                for (LabelPropertyVO relationProperty : annexCacheTripleLabelData.getRelationProperties()) {
                    // 构建属性对象
                    Map<String, Object> map = new HashMap<>();
                    map.put("labelPropertyId", relationProperty.getLabelPropertyId());
                    map.put("value", relationProperty.getValue());
                    // 如果不存在identifier 就是新增属性
                    if (ObjectUtil.isEmpty(relationProperty.getIdentifier())) {
                        // 设置唯一标识
                        map.put("identifier", IdUtil.simpleUUID());
                    } else {
                        // 修改属性，先删除原来的属性
                        annexCacheTripleLabelDataRepository.pullDistinct(annexCacheTripleLabelData.getId(), Map.of("relationProperties", new Document("identifier", relationProperty.getIdentifier())));
                        // 设置原来的唯一标识
                        map.put("identifier", relationProperty.getIdentifier());
                    }
                    // 添加新的属性
                    annexCacheTripleLabelDataRepository.pushDistinct(annexCacheTripleLabelData.getId(), Map.of("relationProperties", new Document(map)));
                }
                return;
            }
            // 如果属性为空，就是新增或修改关系
            if (ObjectUtil.isEmpty(annexCacheTripleLabelData.getRelationProperties())) {
                annexCacheTripleLabelData.setRelationProperties(null);
            }
            // 新增或修改关系
            annexCacheTripleLabelDataRepository.save(annexCacheTripleLabelData);
        }
    }

    /**
     * 删除关系三元组/属性
     *
     * @param annexCacheTripleLabelData 附件关系三元组
     */
    @Override
    public void removeTripleLabelData(AnnexCacheTripleLabelData annexCacheTripleLabelData) {
        // 获取关系三元组Id
        String tripleLabelDataId = annexCacheTripleLabelData.getId();
        // 删除关系
        if (annexCacheTripleLabelData.getRelationProperties() == null || annexCacheTripleLabelData.getRelationProperties().isEmpty()) {
            annexCacheTripleLabelDataRepository.deleteByIdFake(tripleLabelDataId);
        }
        // 删除属性
        if (ObjectUtil.isNotEmpty(annexCacheTripleLabelData.getRelationProperties())) {
            for (LabelPropertyVO relationProperty : annexCacheTripleLabelData.getRelationProperties()) {
                annexCacheTripleLabelDataRepository.pullDistinct(tripleLabelDataId, Map.of("relationProperties", new Document("identifier", relationProperty.getIdentifier())));
            }
        }
    }

    /**
     * 关系生成
     *
     * @param relations                 关系JSONObject
     * @param annexCacheTripleLabelData 关系三元组
     */
    private void generateRelation(List<JSONObject> relations, AnnexCacheTripleLabelData annexCacheTripleLabelData, Map<String, Label> labelMap) {
        // 创建关系对象
        JSONObject relationJsonObject = new JSONObject();
        // 添加关系三元组的Id
        relationJsonObject.putOpt("id", annexCacheTripleLabelData.getId());
        // 如果关系有属性的话就添加
        if (ObjectUtil.isNotEmpty(annexCacheTripleLabelData.getRelationProperties())) {
            JSONArray relationArray = new JSONArray();
            for (LabelPropertyVO relationProperty : annexCacheTripleLabelData.getRelationProperties()) {
                JSONObject relationPropertyObject = new JSONObject(relationProperty);
                // 根据关系标签Id获取关系标签名称
                String labelPropertyId = relationProperty.getLabelPropertyId();
                Label label = getLabel(labelMap, labelPropertyId);
                if (label == null) throw new ServiceException("数据集标签不存在，请检查数据集，并修改后重新解析！");
                relationPropertyObject.putOpt("labelName", label.getName());
                // 层级三
                relationPropertyObject.putOpt("level", 4);
                // 添加关系的属性
                relationArray.add(relationPropertyObject);
            }
            relationJsonObject.putOpt("children", relationArray);
        }

        // 谓词标签Id
        relationJsonObject.putOpt("relationLabelId", annexCacheTripleLabelData.getRelationLabelId());
        // 谓词标签名称
        String relationLabelId = annexCacheTripleLabelData.getRelationLabelId();
        Label relationLabel = getLabel(labelMap, relationLabelId);
        if (relationLabel == null) throw new ServiceException("数据集标签不存在，请检查数据集，并修改后重新解析！");
        relationJsonObject.putOpt("relationLabelName", relationLabel.getName());
        // 宾语的标签Id
        relationJsonObject.putOpt("labelId", annexCacheTripleLabelData.getEndLabelId());
        // 宾语的标签名称
        String endLabelId = annexCacheTripleLabelData.getEndLabelId();
        Label label = getLabel(labelMap, endLabelId);
        if (label == null) throw new ServiceException("数据集标签不存在，请检查数据集，并修改后重新解析！");
        relationJsonObject.putOpt("labelName", label.getName());
        // 宾语的标签数据Id
        relationJsonObject.putOpt("labelDataId", annexCacheTripleLabelData.getEndLabelDataId());
        // 宾语的标签数据名称
        AnnexCacheLabelData endCacheLabelData = Optional.ofNullable(annexCacheLabelDataRepository.findById(annexCacheTripleLabelData.getEndLabelDataId()))
                .orElseThrow(() -> new ServiceException("当前标签数据不存在!"));
        relationJsonObject.putOpt("labelDataName", endCacheLabelData.getContent());
        // 添加层级
        relationJsonObject.putOpt("level", 3);
        // 添加关系
        relations.add(relationJsonObject);
    }


    /**
     * 结果排序TreeMap
     *
     * @param dataSetId 数据集Id
     */
    private Map<String, List<JSONObject>> entitySortByKeyValue(String dataSetId) {
        Map<String, Integer> labelSort = new HashMap<>();
        List<Label> labelList = labelRepository.findByCondition(new Document("dataSetId", dataSetId));
        labelList.forEach(m -> labelSort.put(m.getName(), m.getSort()));
        return new TreeMap<>(((o1, o2) -> {
            if (!(labelSort.containsKey(o1) && labelSort.containsKey(o2))) return 1;
            if (!Objects.equals(o1, o2)) {
                Integer o1Sort = labelSort.get(o1);
                Integer o2Sort = labelSort.get(o2);
                return Objects.equals(o1Sort, o2Sort) ? 0 : (o1Sort > o2Sort ? 1 : -1);
            }
            return 0;
        }));
    }

    /**
     * 解析确认 ===> 规则解析结果确认
     *
     * @param annexId 附件Id
     */
    @Override
    public void parsingConfirm(String annexId) {
        // 附件解析确认，只修改状态，不进行知识匹配，需要主文件确认后才开始知识匹配
        R<ResourceAnnex> resourceAnnexR = resourceAnnexFeign.info(annexId);
        ResourceAnnex resourceAnnex = resourceAnnexR.getData();
        if (!resourceAnnexR.isSuccess() || resourceAnnex == null) {
            throw new ServiceException("该附件不存在!");
        }
        // 修改附件状态
        resourceAnnex.setDocRuleParsingConfirm(true);
        resourceAnnex.setProcessStatus(ResourceProcessStatus.PROCESSED);
        resourceAnnex.setProcessed(true);
        resourceAnnexFeign.save(resourceAnnex);
    }

    /**
     * 重新解析  ===> 附件重新进行规则解析
     *
     * @param annexId 附件Id
     */
    @Override
    public void reRuleParsing(String annexId) {
        R<ResourceAnnex> resourceAnnexR = resourceAnnexFeign.info(annexId);
        ResourceAnnex resourceAnnex = resourceAnnexR.getData();
        if (!resourceAnnexR.isSuccess() || resourceAnnex == null) {
            throw new ServiceException("该附件不存在!");
        }
        // 改为待解析
        resourceAnnex.setRuleStatus(DocumentResolvedStatus.UN_RESOLVED);
        resourceAnnex.setDocRuleParsingConfirm(false);
        resourceAnnexFeign.save(resourceAnnex);
        // 删除附件规则解析生成的缓存标签
        Bson and = Filters.and(Filters.eq("annexId", annexId), Filters.eq("type", "规则解析"));
        List<String> idList = annexCacheLabelDataRepository.findByCondition(and)
                .stream()
                .map(AnnexCacheLabelData::getId)
                .collect(Collectors.toList());
        if (ObjectUtil.isNotEmpty(idList)) {
            annexCacheLabelDataRepository.deleteByIdFake(idList);
        }
        // 删除附件规则解析生成的缓存关系标签
        List<String> idLists = annexCacheTripleLabelDataRepository.findByCondition(and)
                .stream()
                .map(AnnexCacheTripleLabelData::getId)
                .collect(Collectors.toList());
        if (ObjectUtil.isNotEmpty(idLists)) {
            annexCacheLabelDataRepository.deleteByIdFake(idLists);
        }
        // 重新进行附件规则解析
        ruleParsingStrategy.process(resourceFeign.info(resourceAnnex.getResourceId()).getData());
    }
}
