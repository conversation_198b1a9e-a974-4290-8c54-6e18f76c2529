package org.irm.lab.kg.service.annex;

import cn.hutool.json.JSONArray;
import org.irm.lab.kg.entity.annex.processing.AnnexCacheLabelData;
import org.irm.lab.kg.entity.annex.processing.AnnexCacheTripleLabelData;
import org.irm.lab.kg.entity.processing.CacheLabelData;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/5/9 14:38
 * @description 附件规则解析业务接口
 */
public interface IAnnexRuleParsingGraphService {

    /**
     * 附件规则解析 ===> 获取标签数据树
     *
     * @param annexId   附件Id
     * @param dataSetId 数据集Id
     * @return 标签数据
     */
    JSONArray relevanceShow(String annexId, String dataSetId);


    /**
     * 获取该附件下的所有标签数据
     *
     * @param annexId 附件Id
     * @return {@link CacheLabelData}
     */
    List<AnnexCacheLabelData> listCacheLabelData(String annexId);

    /**
     * 新增或修改标签数据对象
     *
     * @param annexCacheLabelData {@link AnnexCacheLabelData}
     */
    void saveOrUpdateLabelData(AnnexCacheLabelData annexCacheLabelData);

    /**
     * 删除实例/实例的属性
     *
     * @param annexCacheLabelData 附件缓存标签数据实例
     */
    void removeLabelData(AnnexCacheLabelData annexCacheLabelData);

    /**
     * 新增/修改关系三元组
     *
     * @param annexCacheTripleLabelData {@link AnnexCacheTripleLabelData}
     */
    void saveOrUpdateTripleLabelData(List<AnnexCacheTripleLabelData> annexCacheTripleLabelData);

    /**
     * 删除关系三元组/属性
     *
     * @param annexCacheTripleLabelData 附件关系三元组
     */
    void removeTripleLabelData(AnnexCacheTripleLabelData annexCacheTripleLabelData);

    /**
     * 解析确认 ===> 规则解析结果确认
     *
     * @param annexId 附件Id
     */
    void parsingConfirm(String annexId);

    /**
     * 重新解析  ===> 附件重新进行规则解析
     *
     * @param annexId 附件Id
     */
    void reRuleParsing(String annexId);
}
