package org.irm.lab.kg.strategy.annexOcr.impl;

import java.io.IOException;
import java.io.InputStream;
import java.util.HashMap;
import java.util.Locale;
import java.util.Map;

import org.irm.lab.kg.strategy.annexOcr.AnnexOcrStrategy;
import org.irm.lab.kg.strategy.ocr.OcrStrategy;
import org.irm.lab.kg.strategy.ocr.impl.AbstractFileOcrStrategyImpl;
import org.irm.lab.kg.utils.SplitterUtil;
import org.irm.lab.repository.constant.DocumentResolvedStatus;
import org.irm.lab.repository.entity.Resource;
import org.irm.lab.repository.entity.ResourceAnnex;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.amqp.core.Message;

import com.aspose.pdf.TextAbsorber;
import com.aspose.words.Document;
import com.aspose.words.FontSettings;
import com.aspose.words.LayoutCollector;
import com.aspose.words.NodeType;
import com.aspose.words.Paragraph;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.system.OsInfo;
import cn.hutool.system.SystemUtil;

/**
 * <AUTHOR> <br/>
 * @date 2024/5/1 <br/>
 * @Copyright 博客：https://eliauku.gitee.io/  ||  per aspera and astra <br/>
 */
public abstract class AbstractAnnexFileOcrStrategyImpl implements AnnexOcrStrategy {


    private final Logger logger = LoggerFactory.getLogger(AbstractAnnexFileOcrStrategyImpl.class);

    /**
     * 得到每页的OCR识别结果
     */
    abstract Map<Integer, String> getOcrResult(String strategy, org.irm.lab.repository.entity.ResourceAnnex message);

    @Override
    public boolean ocr(String strategy, org.irm.lab.repository.entity.ResourceAnnex message) throws IOException{
        OsInfo osInfo = SystemUtil.getOsInfo();
        if (osInfo.isLinux()) {
            FontSettings.getDefaultInstance().setFontsFolder("/usr/share/fonts/chinese", true);
        }
        final Map<Integer, String> ocrResult = getOcrResult(strategy, message);

        if (ObjectUtil.isEmpty(ocrResult)) {
            logger.error("ocrResult is null");
            return false;
        }else {
            message.setResolveStatus(DocumentResolvedStatus.RESOLVED);

            saveOcrResult(strategy, message, ocrResult);

            return true;
        }

    }

    /**
     * 保存每页的OCR识别结果
     */
    abstract void saveOcrResult(String strategy, org.irm.lab.repository.entity.ResourceAnnex message, Map<Integer, String> ocrResult);

    public Map<Integer, String> processWordDocument(InputStream inputStream,  org.irm.lab.repository.entity.ResourceAnnex message) throws Exception {
        Document doc = new Document(inputStream);
        LayoutCollector layoutCollector = new LayoutCollector(doc);
        Map<Integer, StringBuilder> pagesText = new HashMap<>();

        for (Paragraph para : (Iterable<Paragraph>) doc.getChildNodes(NodeType.PARAGRAPH, true)) {
            int pageNumber = layoutCollector.getStartPageIndex(para);
            pagesText.putIfAbsent(pageNumber, new StringBuilder());
            final String text = para.getText();
            pagesText.get(pageNumber).append(SplitterUtil.secondaryProcessing(text)).append("");
            logger.info("当前资源名称为：{}，解析到当前资源的第{}页", message.getName(), pageNumber);

        }

        Map<Integer, String> textByPages = new HashMap<>();
        pagesText.forEach((key, value) -> textByPages.put(key, value.toString()));
        return textByPages;
    }

    public Map<Integer, String> processPDFDocument(InputStream inputStream, ResourceAnnex resource) throws Exception {
        final Locale originalLocale = Locale.getDefault();
        Locale.setDefault(new Locale("zh", "cn"));

        com.aspose.pdf.Document pdfDoc = new com.aspose.pdf.Document(inputStream);
        Map<Integer, String> pagesText = new HashMap<>();
        int totalPages = pdfDoc.getPages().size();

        try {
            for (int pageNum = 1; pageNum <= totalPages; pageNum++) {

                TextAbsorber textAbsorber = new TextAbsorber();
                pdfDoc.getPages().get_Item(pageNum).accept(textAbsorber);
                String pageText = textAbsorber.getText();
                pagesText.put(pageNum, SplitterUtil.secondaryProcessing(pageText));
                logger.info("当前资源名称为{}, 解析到当前资源的第{}页", resource.getName(), pageNum);
            }
        } finally {
            pdfDoc.close();
            Locale.setDefault(originalLocale); // 恢复原始的Locale设置
        }

        return pagesText;
    }

}
