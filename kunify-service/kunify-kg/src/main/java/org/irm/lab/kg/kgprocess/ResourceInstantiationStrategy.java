package org.irm.lab.kg.kgprocess;



import lombok.extern.slf4j.Slf4j;
import org.irm.lab.kg.enums.KgProcessEnum;
import org.irm.lab.kg.kgprocess.strategy.ProcessingStrategy;
import org.irm.lab.kg.service.IResourceInstantiationService;
import org.irm.lab.kg.service.impl.kgprocess.CommonKgProcess;
import org.irm.lab.repository.entity.Resource;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @date 2023/4/25 18:27
 * @description 资源实例化
 */
@Slf4j
@Component
public class ResourceInstantiationStrategy extends CommonKgProcess implements ProcessingStrategy {

    private final static String CODE = KgProcessEnum.RESOURCE_INSTANTIATION.getCode();
    private final static String NAME = KgProcessEnum.RESOURCE_INSTANTIATION.getName();

    @javax.annotation.Resource
    private IResourceInstantiationService iResourceInstantiationService;

    @Override
    public String getCode() {
        return CODE;
    }

    @Override
    public String getName() {
        return NAME;
    }

    @Override
    public void process(Resource resource) {
        // 调用资源实例化
        iResourceInstantiationService.resourceInstance(resource);
    }
}
