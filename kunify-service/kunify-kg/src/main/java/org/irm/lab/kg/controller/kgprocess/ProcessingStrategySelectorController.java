package org.irm.lab.kg.controller.kgprocess;

import cn.hutool.core.util.ObjectUtil;
import lombok.RequiredArgsConstructor;
import org.irm.lab.common.api.R;
import org.irm.lab.common.exception.ServiceException;
import org.irm.lab.common.utils.Func;
import org.irm.lab.common.utils.ThreadLocalUtil;
import org.irm.lab.kg.kgprocess.strategy.ProcessingStrategySelector;
import org.irm.lab.repository.entity.Resource;
import org.irm.lab.kg.feign.ProcessingStrategySelectorFeign;
import org.irm.lab.repository.feign.ResourceFeign;
import org.irm.lab.repository.repository.ResourceRepository;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import java.util.HashSet;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/4/26 11:40
 * @description 资源加工选择器
 */
@RestController
@RequestMapping("/process-selector")
@RequiredArgsConstructor
public class ProcessingStrategySelectorController implements ProcessingStrategySelectorFeign {
    private final ProcessingStrategySelector processingStrategySelector;
    private final ResourceFeign resourceFeign;
    private final HttpServletRequest request;
    private final ResourceRepository resourceRepository;

    /**
     * 开启资源加工
     *
     * @param ids 多个资源Id
     */
    @PostMapping("/start")
    public R<Void> start(@RequestBody String ids) {
        List<String> resorceIdList = Func.objToStrList(ids);
        List<Resource> resourceList = resourceRepository.findById(new HashSet<>(resorceIdList));
        if ( ObjectUtil.isEmpty(resourceList)) {
            throw new ServiceException("资源不存在!");
        }
        if (ThreadLocalUtil.get("user") == null)
            ThreadLocalUtil.set("user", request.getHeader("user"));
        for (Resource resource : resourceList) {
            // 开启资源加工
            processingStrategySelector.process(resource);
        }
        return R.success();
    }
}
