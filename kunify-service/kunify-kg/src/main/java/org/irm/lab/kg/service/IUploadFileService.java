package org.irm.lab.kg.service;

import org.irm.lab.resource.entity.Attach;
import org.springframework.web.multipart.MultipartFile;

/**
 * <AUTHOR>
 * @date 2023/1/30 15:49
 */
public interface IUploadFileService {

    Attach uploadSingleFile(MultipartFile multipartFile);

    /**
     * 上传可预览式的pdf
     */
    Attach uploadSinglePdf(MultipartFile multipartFile);

    Attach uploadSingleFile(MultipartFile multipartFile,String fileName);
}
