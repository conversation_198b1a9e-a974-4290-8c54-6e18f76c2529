package org.irm.lab.kg.strategy.ocr.impl;

import java.io.IOException;
import java.io.InputStream;
import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Locale;
import java.util.Map;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

import org.irm.lab.common.utils.NetWorkFileUtil;
import org.irm.lab.kg.strategy.ocr.OcrStrategy;
import org.irm.lab.kg.utils.PDFUtils;
import org.irm.lab.repository.constant.DocumentResolvedStatus;
import org.irm.lab.repository.entity.Resource;
import org.irm.lab.resource.entity.Attach;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;



import cn.hutool.core.io.FileUtil;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.system.OsInfo;
import cn.hutool.system.SystemUtil;

/**
 * <AUTHOR> <br/>
 * @date 2024/5/1 <br/>
 * @Copyright 博客：https://eliauku.gitee.io/  ||  per aspera and astra <br/>
 */
public abstract class AbstractFileOcrStrategyImpl implements OcrStrategy {
    private static final int minSize = 250;
    private static final int maxSize = 1280;

    final Logger logger = LoggerFactory.getLogger(AbstractFileOcrStrategyImpl.class);

    /**
     * 得到每页的OCR识别结果
     */
    abstract Map<Integer, Map<Integer, String>> getOcrResult(String strategy, org.irm.lab.repository.entity.Resource message);

    @Override
    public boolean ocr(String strategy, org.irm.lab.repository.entity.Resource message) throws IOException{

        final Map<Integer, Map<Integer, String>> ocrResult = getOcrResult(strategy, message);

        if (ObjectUtil.isEmpty(ocrResult)) {
            logger.error("ocrResult is null");
            return false;
        }else {
            message.setResolveStatus(DocumentResolvedStatus.RESOLVED);
            saveOcrResult(strategy, message, ocrResult);
            return true;
        }
    }

    /**
     * 保存每页的OCR识别结果
     */
    abstract void saveOcrResult(String strategy, org.irm.lab.repository.entity.Resource message, Map<Integer, Map<Integer, String>> ocrResult);






}
