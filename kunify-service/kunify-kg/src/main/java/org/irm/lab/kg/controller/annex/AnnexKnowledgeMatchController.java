package org.irm.lab.kg.controller.annex;

import cn.hutool.json.JSONArray;
import com.mongodb.client.model.Filters;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.irm.lab.common.api.R;
import org.irm.lab.kg.entity.annex.processing.AnnexLabelData;
import org.irm.lab.kg.entity.annex.processing.AnnexTripleLabelData;
import org.irm.lab.kg.repository.annex.AnnexLabelDataRepository;
import org.irm.lab.kg.service.annex.IAnnexLabelDataConsolidationService;
import org.irm.lab.kg.service.annex.IAnnexLabelGraphService;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/5/10 14:45
 * @description 附件知识匹配控制器
 */
@RestController
@RequestMapping("/annex-knowledge-match")
@RequiredArgsConstructor
public class AnnexKnowledgeMatchController {

    private final IAnnexLabelGraphService iAnnexLabelGraphService;
    private final IAnnexLabelDataConsolidationService iAnnexLabelDataConsolidationService;
    private final AnnexLabelDataRepository annexLabelDataRepository;



    /**
     * 知识匹配 ===> 获取标签数据树
     *
     * @param annexId   附件Id
     * @param dataSetId 数据集Id
     * @return 标签数据
     */
    @ApiOperation(value = "获取标签数据树")
    @GetMapping("/relevance")
    public R<JSONArray> relevance(@RequestParam String annexId, @RequestParam String dataSetId) {
        return R.data(iAnnexLabelGraphService.relevanceShow(annexId, dataSetId));
    }


    /**
     * 获取该资源下的所有标签数据
     *
     * @param annexId 资源Id
     * @return {@link AnnexLabelData}
     */
    @ApiOperation(value = "获取该附件下的所有标签数据", hidden = true)
    @GetMapping("/list-label-data")
    public R<List<AnnexLabelData>> listLabelData(@RequestParam String annexId) {
        return R.data(iAnnexLabelGraphService.listLabelData(annexId));
    }

    /**
     * 获取指定概念下的所有实例标签
     *
     * @param labelId 标签Id（概念Id）
     * @return {@link AnnexLabelData}
     */
    @ApiOperation(value = "获取指定概念下的所有实例标签", hidden = true)
    @GetMapping("/list-label-data-condition")
    public R<List<AnnexLabelData>> listLabelDataCondition(@RequestParam String annexId, @RequestParam String labelId) {
        return R.data(annexLabelDataRepository.findByCondition(Filters.and(Filters.eq("annexId", annexId), Filters.eq("labelId", labelId))));
    }

    /**
     * 新增或修改附件标签数据对象
     *
     * @param annexLabelData {@link AnnexLabelData}
     */
    @ApiOperation(value = "新增或修改标签数据对象", hidden = true)
    @PostMapping("/save-label-data")
    public R<String> saveOrUpdateLabelData(@RequestBody AnnexLabelData annexLabelData) {
        iAnnexLabelGraphService.saveOrUpdateLabelData(annexLabelData);
        return R.success();
    }

    /**
     * 删除实例/实例的属性
     *
     * @param annexLabelData 附件标签数据
     */
    @ApiOperation(value = "删除实例/实例的属性", hidden = true)
    @PostMapping("/remove-label-data")
    public R<String> removeLabelData(@RequestBody AnnexLabelData annexLabelData) {
        iAnnexLabelGraphService.removeLabelData(annexLabelData);
        return R.success();
    }

    /**
     * 新增/修改附件关系三元组
     *
     * @param annexTripleLabelDataList {@link AnnexTripleLabelData}
     */
    @ApiOperation(value = "新增/修改关系三元组", hidden = true)
    @PostMapping("/save-triple-label-data")
    public R<String> saveOrUpdateTripleLabelData(@RequestBody List<AnnexTripleLabelData> annexTripleLabelDataList) {
        iAnnexLabelGraphService.saveOrUpdateTripleLabelData(annexTripleLabelDataList);
        return R.success();
    }

    /**
     * 删除附件关系三元组/属性
     *
     * @param annexTripleLabelData 附件关系三元组
     */
    @ApiOperation(value = "删除关系三元组/属性", hidden = true)
    @PostMapping("/remove-triple-label-data")
    public R<String> removeTripleLabelData(@RequestBody AnnexTripleLabelData annexTripleLabelData) {
        iAnnexLabelGraphService.removeTripleLabelData(annexTripleLabelData);
        return R.success();
    }


    /**
     * 重新匹配
     *
     * @param annexId 资源Id
     */
    @ApiOperation(value = "重新匹配")
    @PostMapping("/re-match")
    public R<String> reMatch(@RequestParam String annexId) {
        iAnnexLabelDataConsolidationService.reConsolidation(annexId);
        return R.success();
    }



}
