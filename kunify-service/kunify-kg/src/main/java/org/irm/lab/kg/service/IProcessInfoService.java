package org.irm.lab.kg.service;

import org.irm.lab.kg.vo.ResourceProcessInfoVo;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/3/9 16:38
 * @description 加工信息业务接口
 */
public interface IProcessInfoService {
    /**
     * 根据工作任务Id和资源Id获取详情信息
     *
     * @param workTaskId 工作任务Id
     * @param resourceId 资源Id
     * @return {@link ResourceProcessInfoVo}
     */
    ResourceProcessInfoVo processInfo(String workTaskId, String resourceId);

    /**
     * 一键通过资源加工
     *
     * @param resourceId 资源Id
     */
    void passResource(String resourceId);

    /**
     * 批量通过资源加工
     */
    void passResourceMulti(List<String> resourceIds);

    /**
     * 资源加工全部文件一键通过
     *
     * @param workTaskId 工作任务Id
     */
    void passAllResource(String workTaskId);
}
