package org.irm.lab.kg.rabbitmq.ieInfer;

import org.irm.lab.kg.dto.ie.req.IeInferReqDTO;
import org.irm.lab.kg.rabbitmq.MessageHelper;
import org.springframework.amqp.rabbit.connection.CorrelationData;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.DependsOn;
import org.springframework.stereotype.Component;

import cn.hutool.core.lang.Opt;
import cn.hutool.core.lang.UUID;

@Component
public class IeInferProducer {

    @Autowired
    private RabbitTemplate rabbitTemplate;

    @Value("${spring.application.name}")
    private String APP_KEY;

    public void syncSend(IeInferMessage ieInferReqDTO){

        rabbitTemplate.convertAndSend(
                IeInferMessage.EXCHANGE ,
                IeInferMessage.ROUTING_KEY + APP_KEY ,
                ieInferReqDTO
        );
    }

}
