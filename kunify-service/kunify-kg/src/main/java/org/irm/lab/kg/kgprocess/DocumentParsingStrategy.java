package org.irm.lab.kg.kgprocess;

import org.irm.lab.kg.enums.KgProcessEnum;
import org.irm.lab.kg.kgprocess.strategy.ProcessingStrategy;
import org.irm.lab.kg.rabbitmq.producer.DocumentParsingProducer;
import org.irm.lab.repository.entity.Resource;
import org.irm.lab.repository.feign.ResourceFeign;
import org.springframework.stereotype.Component;

import javax.servlet.http.HttpServletRequest;


/**
 * <AUTHOR>
 * @date 2023/4/25 18:28
 * @description 文档解析
 */
@Component
public class DocumentParsingStrategy implements ProcessingStrategy {

    private final static String CODE = KgProcessEnum.DOCUMENT_PARSING.getCode();
    private final static String NAME = KgProcessEnum.DOCUMENT_PARSING.getName();

    @javax.annotation.Resource
    private DocumentParsingProducer documentParsingProducer;
    @javax.annotation.Resource
    private HttpServletRequest request;
    @javax.annotation.Resource
    private ResourceFeign resourceFeign;

    @Override
    public void process(Resource resource) {
        resource = resourceFeign.info(resource.getId()).getData();
        // 文档解析mq消息
        documentParsingProducer.sendMessage(request.getHeader("user"), resource.getId());
    }

    @Override
    public String getCode() {
        return CODE;
    }

    @Override
    public String getName() {
        return NAME;
    }
}
