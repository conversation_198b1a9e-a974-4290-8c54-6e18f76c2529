package org.irm.lab.kg.controller;

import cn.hutool.core.convert.Convert;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.irm.lab.common.annotation.MyLog;
import org.irm.lab.common.api.R;
import org.irm.lab.common.constant.LogConstant;
import org.irm.lab.common.support.MyPage;
import org.irm.lab.common.utils.Func;
import org.irm.lab.common.vo.ListConstantVO;
import org.irm.lab.common.wrapper.ListConstantWrapper;
import org.irm.lab.kg.constant.PropertyDataTypeConstant;
import org.irm.lab.kg.entity.KnowledgeProperty;
import org.irm.lab.kg.feign.KnowledgePropertyFeign;
import org.irm.lab.kg.service.IKnowledgePropertyService;
import org.irm.lab.kg.vo.KnowledgePropertyVO;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;

@RestController
@RequestMapping("/knowledge-property")
@Api(value = "知识属性")
public class KnowledgePropertyController implements KnowledgePropertyFeign {

    @Resource
    private IKnowledgePropertyService knowledgePropertyService;

    @ApiOperation(value = "属性分页")
    @GetMapping("/page")
    public R<MyPage<KnowledgeProperty>> page(@RequestParam(required = false) Map<String, Object> pageMap) {
        // 获取分页条件
        Integer page = Convert.toInt(pageMap.getOrDefault("page", 1));
        Integer size = Convert.toInt(pageMap.getOrDefault("size", 20));
        // 分页查询
        return R.data(knowledgePropertyService.page(pageMap, page, size));
    }

    @ApiOperation(value = "属性列表")
    @GetMapping("/list")
    public R<List<KnowledgeProperty>> list() {
        return R.data(knowledgePropertyService.list());
    }

    @ApiOperation(value = "属性列表")
    @GetMapping("/info-vo")
    public R<KnowledgePropertyVO> listVO(@RequestParam String id) {
        return R.data(knowledgePropertyService.infoVO(id));
    }

    @MyLog(menu = LogConstant.MENU_VIEW, dataType = LogConstant.DATA_PROPERTY, operation = LogConstant.OPERATION_ADD)
    @ApiOperation(value = "属性增改")
    @PostMapping("/save")
    public R<KnowledgeProperty> save(@Validated @RequestBody KnowledgeProperty knowledgeProperty) {
        return R.data(knowledgePropertyService.save(knowledgeProperty));
    }

    @MyLog(menu = LogConstant.MENU_VIEW, dataType = LogConstant.DATA_PROPERTY, operation = LogConstant.OPERATION_REMOVE)
    @ApiOperation(value = "属性删除")
    @PostMapping(value = "/remove")
    public R<Void> remove(@RequestBody String ids) {
        knowledgePropertyService.remove(Func.objToStrList(ids));
        return R.success();
    }

    @ApiOperation(value = "数据类型")
    @GetMapping("/data-type")
    public R<List<ListConstantVO>> allDataType() {
        return R.data(ListConstantWrapper.listVO(PropertyDataTypeConstant.class));
    }

    /**
     * 根据Id列表获取属性列表
     *
     * @param ids id列表
     * @return {@link KnowledgeProperty}
     */
    @ApiOperation(value = "根据Id列表获取属性列表")
    @GetMapping("/find-by-ids")
    public R<List<KnowledgeProperty>> findByIds(@RequestParam List<String> ids) {
        return R.data(knowledgePropertyService.listByIds(ids));
    }

    /**
     * 根据Id查询
     *
     * @param id 属性Id
     * @return {@link KnowledgeProperty}
     */
    @ApiOperation(value = "单个属性")
    @GetMapping("/info")
    public R<KnowledgeProperty> info(@RequestParam String id) {
        return R.data(knowledgePropertyService.info(id));
    }

    /**
     * 根据标识查询属性
     * @param identifier 概念标识
     * @return
     */
    @GetMapping("/find-by-concept-identifier")
    public R<KnowledgeProperty> findByIdentifier(@RequestParam String identifier) {
        return R.data(knowledgePropertyService.findByIdentifier(identifier));
    }
}
