package org.irm.lab.kg.rabbitmq.consumer;

import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.nio.file.Files;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

import org.apache.tika.exception.TikaException;
import org.apache.tika.metadata.Metadata;
import org.apache.tika.parser.AutoDetectParser;
import org.apache.tika.parser.ParseContext;
import org.apache.tika.parser.Parser;
import org.apache.tika.sax.BodyContentHandler;
import org.dromara.streamquery.stream.core.stream.Steam;
import org.irm.lab.common.api.R;
import org.irm.lab.common.constant.ExceptionMessageConst;
import org.irm.lab.common.enums.AlgorithmType;
import org.irm.lab.common.exception.ServiceException;
import org.irm.lab.common.provider.MinioLinkProvider;
import org.irm.lab.common.utils.FileUtil;
import org.irm.lab.common.utils.NetWorkFileUtil;
import org.irm.lab.common.utils.ThreadLocalUtil;
import org.irm.lab.front.dto.AIMiddle.OcrInferDTO;
import org.irm.lab.front.dto.AIMiddle.OcrInferResultDTO;
import org.irm.lab.front.dto.AIMiddle.OcrItemDTO;
import org.irm.lab.front.dto.AIMiddle.OcrLocationDTO;
import org.irm.lab.front.feign.AIFeign;
import org.irm.lab.front.feign.DocumentElasticSearchFeign;
import org.irm.lab.front.model.Document;
import org.irm.lab.kg.algorithm.AlgorithmProcessFactory;
import org.irm.lab.kg.algorithm.DocumentImage;
import org.irm.lab.kg.algorithm.DocumentUnit;
import org.irm.lab.kg.algorithm.PDFPreciseProcessor;
import org.irm.lab.kg.entity.annex.AnnexDocumentUnit;
import org.irm.lab.kg.message.OCRMessage;
import org.irm.lab.kg.rabbitmq.message.DocumentParsingMessage;
import org.irm.lab.kg.rabbitmq.message.IeConMessage;

import org.irm.lab.kg.rabbitmq.producer.EsSyncProducer;
import org.irm.lab.kg.repository.DocumentUnitRepository;
import org.irm.lab.kg.service.annex.impl.AnnexDocumentParsingServiceImpl;
import org.irm.lab.kg.service.forest.AIMiddlePlatformService;
import org.irm.lab.repository.constant.DocumentResolvedStatus;
import org.irm.lab.repository.entity.Resource;
import org.irm.lab.repository.entity.ResourceAnnex;
import org.irm.lab.repository.feign.ResourceAnnexFeign;
import org.irm.lab.repository.feign.ResourceFeign;
import org.irm.lab.repository.repository.ResourceRepository;
import org.springframework.amqp.core.Message;
import org.springframework.amqp.rabbit.annotation.Exchange;
import org.springframework.amqp.rabbit.annotation.Queue;
import org.springframework.amqp.rabbit.annotation.QueueBinding;
import org.springframework.amqp.rabbit.annotation.RabbitHandler;
import org.springframework.amqp.rabbit.annotation.RabbitListener;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.mock.web.MockMultipartFile;
import org.springframework.stereotype.Component;
import org.springframework.web.multipart.MultipartFile;
import org.xml.sax.SAXException;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.mongodb.client.model.Filters;
import com.rabbitmq.client.Channel;

import cn.hutool.core.convert.Convert;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

/**
     
 * @date 2024/2/27 <br/>
 */

@Slf4j
@Component
@RequiredArgsConstructor
public class OCRConsumer {

    private final PDFPreciseProcessor pdfPreciseProcessor;
    private final ResourceFeign resourceFeign;
    private final MinioLinkProvider minioLinkProvider;
    private final AIFeign aiFeign;
    private final DocumentElasticSearchFeign documentElasticSearchFeign;
    private final DocumentUnitRepository documentUnitRepository;
    private final ResourceAnnexFeign resourceAnnexFeign;
    private final AlgorithmProcessFactory algorithmProcessFactory;
    private final EsSyncProducer esSyncProducer;
    private final AnnexDocumentParsingServiceImpl annexDocumentParsingService;
    @javax.annotation.Resource
    private AIMiddlePlatformService aiMiddlePlatformService;
    private final ResourceRepository resourceRepository;

    @Value("${fileTypes.allowed}")
    private String allowed;

    @RabbitListener(bindings = @QueueBinding(
            value = @Queue(OCRMessage.QUEUE + "${spring.application.name}"),
            exchange = @Exchange(name = OCRMessage.EXCHANGE),
            key = OCRMessage.ROUTING_KEY + "${spring.application.name}"
    ), concurrency = "${ocr-archive}")
    @RabbitHandler
    public void onMessage(OCRMessage message, Channel channel, Message messageStatus) throws IOException {
        // 获取消息状态信息
        long deliveryTag = messageStatus.getMessageProperties().getDeliveryTag();
        log.info("[onMessage][线程编号:{} 消息内容：{}]", Thread.currentThread().getId(), message);
        final Resource resource = message.getResource();
        ThreadLocalUtil.set("user", message.getUser());

        try {
            final String[] split = allowed.split(",");

            final Set<String> set = Steam.of(split).toSet();
            if (FileUtil.isPDF(resource.getMediaType(),set)) {
                log.info("【{}】 非文书类资源，无需解析 ", resource.getName());
                resource.setResolveStatus(DocumentResolvedStatus.RESOLVED);
                return;
            }
            // 设置用户信息，防止租户问题
            // 发送消息开启文档解析
            final DocumentImage documentImage = message.getDocumentImage();
            String attachName = documentImage.getAttachName();
            String link = minioLinkProvider.getMinioLinkIntranet(attachName);
            log.info("[onMessage][文件下载链接:{}]", link);

            File file = NetWorkFileUtil.urlToFile(link);

            MultipartFile multipartFile = null;
            try (FileInputStream input = new FileInputStream(file)) {
                multipartFile = new MockMultipartFile("file",
                        file.getName(),
                        Files.probeContentType(file.toPath()),
                        input);
            } catch (IOException e) {
                e.printStackTrace();
            } finally {
                if (file != null && file.exists()) {
                    boolean deleted = file.delete();
                    if (!deleted) {
                        log.info("Failed to delete temporary file: " + file.getPath());
                    } else {
                        log.info("文件已成功被删除{}", file.getPath());
                    }
                }
            }

            OcrInferDTO ocrInferDTO = new OcrInferDTO();
            ocrInferDTO.setDrawBox("1");
            ocrInferDTO.setModel("dg");
            ocrInferDTO.setTenant("100001");
            final JSONObject jsonObject;
            String ocr = null;
            try {
                ocr = aiMiddlePlatformService.ocr(multipartFile, ocrInferDTO);
                jsonObject = JSONUtil.parseObj(ocr);
                log.info("调用AI服务返回结果为{}",  ocr);
            } catch (Exception e) {

                throw new ServiceException("调用AI服务出错");
            }


            final Object data = jsonObject.get("data");
            final OcrInferResultDTO bean = JSONUtil.toBean(data.toString(), OcrInferResultDTO.class);
            final List<OcrItemDTO> result = bean.getResult();
            List<DocumentUnit> units = new ArrayList<>();
            int sort = 0;
            DocumentUnit documentUnit = new DocumentUnit();
            documentUnit.setResourceId(resource.getId());
            final StringBuffer stringBuffer = new StringBuffer();
            documentUnit.setSortInCurrentPage(1);
            documentUnit.setPage(message.getPage());
            documentUnit.setType("0");





            for (OcrItemDTO ocrResult : result) {

                stringBuffer.append(ocrResult.getWords());

            }

            if (ObjectUtil.isNotEmpty(message.getMap()) && message.getMap().containsKey("isES")){

                log.info("成功出发只更新es数据");
                final R<Document> documentById = documentElasticSearchFeign.info(resource.getId());
                final Document data1 = documentById.getData();
                data1.setContent(data1.getContent()+ stringBuffer);
                documentElasticSearchFeign.updateMeta(data1);
            }else {
                documentUnit.setContent(stringBuffer.toString());
            units.add(documentUnit);
            //final List<DocumentUnit> documentUnits = pdfPreciseProcessor.mergeDocumentUnit(units, 400);
            documentUnitRepository.saveAll(units);
            List<DocumentUnit> documentUnitList = documentUnitRepository.findByCondition(Filters.eq("resourceId", resource.getId()));
                documentUnitList = documentUnitList.stream()
                        .sorted(Comparator.comparingInt(DocumentUnit::getPage)
                                .thenComparingInt(DocumentUnit::getSortInCurrentPage))
                        .collect(Collectors.toList());
            if (ObjectUtil.isNotEmpty(documentUnitList)) {
                // 合并内容
                StringBuffer stringBuffer1 = new StringBuffer();
                documentUnitList.forEach(documentUnit1 -> stringBuffer1.append(documentUnit1.getContent()));
                esSyncProducer.sendMessage(ThreadLocalUtil.get("user"), resource, null, stringBuffer1.toString(), "主文件");
                log.info("【主文件】{} 同步到ES中", resource.getName());
            }

            if (message.getIsEnd()) {

                R<List<ResourceAnnex>> resourceAnnexListR = resourceAnnexFeign.listByResourceId(resource.getId());
                final List<ResourceAnnex> data1 = resourceAnnexListR.getData();
                if (ObjectUtil.isEmpty(data1)) {
                    log.info("该资源不存在附件，无需附件解析!");
                    resource.setResolveStatus(DocumentResolvedStatus.RESOLVED);

                    return;
                }
                // 循环解析附件
                log.info(">>>>>>>>>>开始解析附件>>>>>>>>>>");
                // 资源解析成功后，开始解析附件

                for (ResourceAnnex resourceAnnex : data1) {
                    log.info("【{}】开始解析附件", resourceAnnex.getId());
                    annexDocumentParsingService.reParsingWithOCR(resourceAnnex.getId());
                }

                resource.setResolveStatus(DocumentResolvedStatus.RESOLVED);
            } else {
                resource.setResolveStatus(DocumentResolvedStatus.RESOLVED);
            }
            }

        } catch (Exception e) {
            if (messageStatus.getMessageProperties().getRedelivered()) {
                log.error("消息已重复处理失败，拒绝再次接收.....{}",e.toString());
                resource.setTextStripperStatus(ExceptionMessageConst.OCR_GEN);
                resource.setResolveStatus(DocumentResolvedStatus.RESOLVED_FAILED);

                channel.basicReject(deliveryTag, false);
            } else {
                log.info("消息即将再次返回队列中进行处理....{}",e.toString());
                channel.basicNack(deliveryTag, false, true);
            }
        }finally {
            resource.setResolveStatus(DocumentResolvedStatus.RESOLVED);
            resourceRepository.save(resource);
            ThreadLocalUtil.remove();
        }
    }

    private static String extractText(File file) {
        try (InputStream stream = new FileInputStream(file)) {
            BodyContentHandler handler = new BodyContentHandler(-1);  // 不限制字符数
            Metadata metadata = new Metadata();
            Parser parser = new AutoDetectParser();
            parser.parse(stream, handler, metadata, new ParseContext());
            return handler.toString();
        } catch (IOException | SAXException | TikaException e) {
            e.printStackTrace();
            return "";
        }
    }

    private static List<String> splitTextIntoChunks(String text, int chunkSize) {
        List<String> chunks = new ArrayList<>();
        int start = 0;
        while (start < text.length()) {
            int end = Math.min(start + chunkSize, text.length());
            chunks.add(text.substring(start, end));
            start = end;
        }
        return chunks;
    }
}
