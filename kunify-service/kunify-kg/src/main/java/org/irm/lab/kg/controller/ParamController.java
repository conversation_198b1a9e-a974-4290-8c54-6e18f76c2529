package org.irm.lab.kg.controller;

import cn.hutool.core.util.ObjectUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.irm.lab.common.api.R;
import org.irm.lab.common.exception.ServiceException;
import org.irm.lab.common.utils.ValidPathUtil;
import org.irm.lab.kg.algorithm.PDFPreciseProcessor;
import org.irm.lab.kg.service.IParamService;
import org.irm.lab.common.entity.ApiMessage;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.io.BufferedWriter;
import java.io.File;
import java.io.FileWriter;
import java.util.Arrays;
import java.util.List;

@RestController
@RequestMapping("/param")
@Api(value = "权限获取", hidden = true)
@Slf4j
public class ParamController {

    @Resource
    private IParamService paramService;
    @Resource
    private PDFPreciseProcessor pdfPreciseProcessor;

    @ApiOperation(value = "获取当前模块的数据权限", hidden = true)
    @GetMapping("/getParam")
    public R<List<ApiMessage>> getParam() {
        return R.data(paramService.getParam());
    }


    @GetMapping("/test-download")
    public R<Void> testDownload() {
        try {

            String directoryPath = "/home/<USER>/minio/data/kunify-hn-028639/upload/2023-03-29/";
            File directory = new File(directoryPath);

            if (directory.isDirectory()) {
                File[] subDirectories = directory.listFiles(File::isDirectory);

                for (File subDirectory : subDirectories) {
                    File[] files = subDirectory.listFiles();
                    if (files != null && files.length > 0) {
                        for (File file : files) {
                            // 文件名进行验证
                            String fileName = file.getName();
                            fileName = fileName.replace(fileName.substring(fileName.lastIndexOf(".")), "");
                            // 验证文件名
                            if (!isValidFileName(fileName)) {
                                throw new IllegalArgumentException("非法的文件名: " + fileName);
                            }
                            List<String> strings;
                            try {
                                strings = pdfPreciseProcessor.generateUnitTxt(file);
                            } catch (Exception e) {
                                continue;
                            }
                            if (ObjectUtil.isEmpty(strings)) continue;
                            String filePath = "/home/<USER>/minio/data/kunify-hn-028639/upload/" + fileName + ".txt";
                            // 校验文件是否在白名单路径中
                            if(!ValidPathUtil.isValidPath(filePath)) throw new ServiceException("非法文件夹路径异常");

                            BufferedWriter writer = new BufferedWriter(new FileWriter(filePath));
                            for (String line : strings) {
                                if (line.contains("普通商密")) continue;
                                writer.write(line);
                                writer.newLine();
                            }
                            writer.flush();
                            writer.close();
                            log.info("解析成功 === {}", filePath);
                        }
                    }
                }
            }


        } catch (Exception e) {
            log.info(String.valueOf(e));
        }
        return R.success();
    }

    private static boolean isValidFileName(String fileName) {
        List<String> disallowedExtensions = Arrays.asList(".sh", ".bat", ".cmd", ".js", ".py", ".pl", ".rb", ".php", ".jsp", ".asp", ".aspx");
        String lowerCaseFileName = fileName.toLowerCase();
        for (String extension : disallowedExtensions) {
            if (lowerCaseFileName.endsWith(extension)) {
                return false;
            }
        }
        return lowerCaseFileName.matches("[a-z0-9_\\u4e00-\\u9fa5-]+\\.[a-z0-9_\\u4e00-\\u9fa5-]+");
    }
}
