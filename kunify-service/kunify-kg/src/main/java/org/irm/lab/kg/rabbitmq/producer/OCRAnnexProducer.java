package org.irm.lab.kg.rabbitmq.producer;

import lombok.RequiredArgsConstructor;
import org.irm.lab.kg.entity.annex.AnnexDocumentImage;
import org.irm.lab.kg.message.OCRAnnexMessage;
import org.irm.lab.repository.entity.ResourceAnnex;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.io.Serializable;

/**
     
 * @date 2024/2/27 <br/>
      
 */
@Component
@RequiredArgsConstructor
public class OCRAnnexProducer implements Serializable {
    private final RabbitTemplate rabbitTemplate;

    @Value("${spring.application.name}")
    private String KEY_SELF;

    /**
     * 发送消息
     */
    public void sendMessage(String user, ResourceAnnex resourceAnnex, AnnexDocumentImage annexDocumentImage, Integer page, Boolean isEnd){
        OCRAnnexMessage documentParsingMessage = new OCRAnnexMessage();
        documentParsingMessage.setUser(user);
        documentParsingMessage.setResourceAnnex(resourceAnnex);
        documentParsingMessage.setAnnexDocumentImage(annexDocumentImage);
        documentParsingMessage.setPage(page);
        documentParsingMessage.setIsEnd(isEnd);
        // 发送消息
        rabbitTemplate.convertAndSend(OCRAnnexMessage.EXCHANGE,OCRAnnexMessage.ROUTING_KEY+KEY_SELF,documentParsingMessage);
    }
}
