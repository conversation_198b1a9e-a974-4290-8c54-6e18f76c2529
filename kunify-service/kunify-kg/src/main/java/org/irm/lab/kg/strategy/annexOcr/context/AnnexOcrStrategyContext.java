package org.irm.lab.kg.strategy.annexOcr.context;

import java.io.IOException;
import java.util.Map;

import javax.annotation.Resource;

import org.irm.lab.kg.enums.OcrEnum;
import org.irm.lab.kg.strategy.annexOcr.AnnexOcrStrategy;
import org.irm.lab.kg.strategy.ocr.OcrStrategy;
import org.springframework.stereotype.Service;

import lombok.RequiredArgsConstructor;

/**
 * <AUTHOR> <br/>
 * @date 2024/5/1 <br/>
 * @Copyright 博客：https://eliauku.gitee.io/  ||  per aspera and astra <br/>
 */
@Service
@RequiredArgsConstructor
public class AnnexOcrStrategyContext {

    @Resource
    private Map<String, AnnexOcrStrategy> fileInferStrategyMap;

    public boolean processOcr(String strategy, org.irm.lab.repository.entity.ResourceAnnex message) throws IOException {

      return fileInferStrategyMap.get(OcrEnum.getByAlgorithmType(strategy).getName()).ocr(strategy, message);
    }
}
