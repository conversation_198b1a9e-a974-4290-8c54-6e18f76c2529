package org.irm.lab.kg.rabbitmq.consumer;

import cn.hutool.core.convert.Convert;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.mongodb.client.model.Filters;
import com.rabbitmq.client.Channel;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.dromara.streamquery.stream.core.stream.Steam;
import org.irm.lab.common.api.R;
import org.irm.lab.common.constant.ExceptionMessageConst;
import org.irm.lab.common.exception.ServiceException;
import org.irm.lab.common.provider.MinioLinkProvider;
import org.irm.lab.common.utils.FileUtil;
import org.irm.lab.common.utils.NetWorkFileUtil;
import org.irm.lab.common.utils.ThreadLocalUtil;
import org.irm.lab.front.dto.AIMiddle.OcrInferDTO;
import org.irm.lab.front.dto.AIMiddle.OcrInferResultDTO;
import org.irm.lab.front.dto.AIMiddle.OcrItemDTO;
import org.irm.lab.front.dto.AIMiddle.OcrLocationDTO;
import org.irm.lab.front.feign.AIFeign;
import org.irm.lab.front.feign.DocumentElasticSearchFeign;
import org.irm.lab.front.model.Document;
import org.irm.lab.kg.algorithm.PDFPreciseProcessor;
import org.irm.lab.kg.entity.annex.AnnexDocumentImage;
import org.irm.lab.kg.entity.annex.AnnexDocumentUnit;
import org.irm.lab.kg.message.OCRAnnexMessage;
import org.irm.lab.kg.rabbitmq.producer.EsSyncProducer;
import org.irm.lab.kg.repository.annex.processing.AnnexDocumentImageRepository;
import org.irm.lab.kg.repository.annex.processing.AnnexDocumentUnitRepository;
import org.irm.lab.kg.service.forest.AIMiddlePlatformService;
import org.irm.lab.repository.constant.DocumentResolvedStatus;
import org.irm.lab.repository.entity.ResourceAnnex;
import org.irm.lab.repository.feign.ResourceAnnexFeign;
import org.springframework.amqp.core.Message;
import org.springframework.amqp.rabbit.annotation.*;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.mock.web.MockMultipartFile;
import org.springframework.stereotype.Component;
import org.springframework.web.multipart.MultipartFile;

import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;
import java.nio.file.Files;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

/**
     
 * @date 2024/2/27 <br/>
      
 */

@Slf4j
@Component
@RequiredArgsConstructor
public class OCRAnnexConsumer {

    private final AIFeign aiFeign;
    private final PDFPreciseProcessor pdfPreciseProcessor;
    private final MinioLinkProvider minioLinkProvider;
    private final AnnexDocumentImageRepository annexDocumentImageRepository;
    private final AnnexDocumentUnitRepository annexDocumentUnitRepository;
    private final ResourceAnnexFeign resourceAnnexFeign;
    @javax.annotation.Resource
    private AIMiddlePlatformService aiMiddlePlatformService;
    private final EsSyncProducer esSyncProducer;

    private final DocumentElasticSearchFeign documentElasticSearchFeign;


    @Value("${fileTypes.allowed}")
    private String allowed;

    @RabbitListener(bindings = @QueueBinding(
            value = @Queue(OCRAnnexMessage.QUEUE + "${spring.application.name}"),
            exchange = @Exchange(name = OCRAnnexMessage.EXCHANGE),
            key = OCRAnnexMessage.ROUTING_KEY + "${spring.application.name}"
    ),concurrency = "${ocr-annex}")
    @RabbitHandler
    public void onMessage(OCRAnnexMessage message, Channel channel, Message messageStatus) throws IOException {
        // 获取消息状态信息
        long deliveryTag = messageStatus.getMessageProperties().getDeliveryTag();
        log.info("[onMessage][线程编号:{} 消息内容：{}]", Thread.currentThread().getId(), message);
        final ResourceAnnex resource = message.getResourceAnnex();
        try {
            final String[] split = allowed.split(",");

            final Set<String> set = Steam.of(split).toSet();
            if (FileUtil.isPDF(resource.getMediaType(),set)) {
                log.info("【{}】 非文书类资源，无需解析 ", resource.getName());
                resource.setResolveStatus(DocumentResolvedStatus.RESOLVED);
                return;
            }

            // 设置用户信息，防止租户问题
            ThreadLocalUtil.set("user", message.getUser());

            // 发送消息开启文档解析
            final AnnexDocumentImage documentImage = message.getAnnexDocumentImage();
            String attachName = documentImage.getAttachName();
            String link = minioLinkProvider.getMinioLinkIntranet(attachName);
            File file = NetWorkFileUtil.urlToFile(link);

            MultipartFile multipartFile = null;
            try (FileInputStream input = new FileInputStream(file)) {
                multipartFile = new MockMultipartFile("file",
                        file.getName(),
                        Files.probeContentType(file.toPath()),
                        input);
            } catch (IOException e) {
                e.printStackTrace();
            } finally {
                if (file != null && file.exists()) {
                    boolean deleted = file.delete();
                    if (!deleted) {
                        log.info("Failed to delete temporary file: " + file.getPath());
                    }else {
                        log.info("文件已成功被删除{}",file.getPath());
                    }
                }
            }

            OcrInferDTO ocrInferDTO = new OcrInferDTO();
            ocrInferDTO.setDrawBox("1");
            ocrInferDTO.setModel("dg");
            ocrInferDTO.setTenant("100001");
            final JSONObject jsonObject;
            String ocr = null;
            try {
                ocr = aiMiddlePlatformService.ocr(multipartFile, ocrInferDTO);
                jsonObject = JSONUtil.parseObj(ocr);
                log.info("调用AI服务返回结果为{}",  ocr);
            } catch (Exception e) {

                throw new ServiceException("调用AI服务出错");
            }
            final Object data = jsonObject.get("data");
            final OcrInferResultDTO bean = JSONUtil.toBean(data.toString(), OcrInferResultDTO.class);
            final List<OcrItemDTO> result = bean.getResult();

            List<AnnexDocumentUnit> units = new ArrayList<>();

            int sort = 0;
            for (OcrItemDTO ocrResult : result) {
                AnnexDocumentUnit documentUnit = new AnnexDocumentUnit();
                documentUnit.setAnnexId(resource.getId());
                documentUnit.setContent(ocrResult.getWords());
                documentUnit.setSortInCurrentPage(sort++);
                documentUnit.setPage(message.getPage());
                documentUnit.setType("0");
                // 设置坐标
                OcrLocationDTO location = ocrResult.getLocation();
                documentUnit.setLlx(Convert.toDouble(location.getLeft()));
                documentUnit.setLly(Convert.toDouble(location.getTop()));
                documentUnit.setHeight(Convert.toDouble(location.getHeight()));
                documentUnit.setWidth(Convert.toDouble(location.getWidth()));
                units.add(documentUnit);
            }
            annexDocumentUnitRepository.saveAll(units);
            List<AnnexDocumentUnit> annexDocumentUnitList = annexDocumentUnitRepository.findByCondition(Filters.eq("annexId", resource.getId()));
            annexDocumentUnitList = annexDocumentUnitList.stream()
                    .sorted(Comparator.comparingInt(AnnexDocumentUnit::getPage)
                            .thenComparingInt(AnnexDocumentUnit::getSortInCurrentPage))
                    .collect(Collectors.toList());
            if (ObjectUtil.isNotEmpty(annexDocumentUnitList) &&  ObjectUtil.isNotEmpty(message.getMap()) && message.getMap().containsKey("isES")) {
                log.info("成功触发只更新es数据");
                // 合并内容
                StringBuffer stringBuffer = new StringBuffer();
                annexDocumentUnitList.forEach(annexDocumentUnit -> stringBuffer.append(annexDocumentUnit.getContent()));
                final R<Document> documentById = documentElasticSearchFeign.info(resource.getId());
                final Document data1 = documentById.getData();
                data1.setContent(data1.getContent()+ stringBuffer);
                documentElasticSearchFeign.updateMeta(data1);
            }else if (ObjectUtil.isNotEmpty(annexDocumentUnitList)) {
                    // 合并内容
                    StringBuffer stringBuffer = new StringBuffer();
                    annexDocumentUnitList.forEach(annexDocumentUnit -> stringBuffer.append(annexDocumentUnit.getContent()));
                    esSyncProducer.sendMessage(ThreadLocalUtil.get("user"), null, resource, stringBuffer.toString(),"附件");
                    log.info("【附件】{} 同步到ES中", resource.getName());

            }
            if (message.getIsEnd()) {
                resource.setResolveStatus(DocumentResolvedStatus.RESOLVED);
            }else {
                resource.setResolveStatus(DocumentResolvedStatus.RESOLVED);
            }

        } catch (Exception e) {
            if (messageStatus.getMessageProperties().getRedelivered()) {
                log.error("消息已重复处理失败，拒绝再次接收.....{}",e.getMessage());
                resource.setTextStripperStatus(ExceptionMessageConst.OCR_GEN);
                resource.setResolveStatus(DocumentResolvedStatus.RESOLVED_FAILED);
                channel.basicReject(deliveryTag, false);
            } else {
                log.info("消息即将再次返回队列中进行处理....{}",e.getMessage());
                channel.basicNack(deliveryTag, false, true);
            }
        }finally {
            resourceAnnexFeign.save(resource);
            ThreadLocalUtil.remove();
        }
    }


}
