package org.irm.lab.kg.controller;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.irm.lab.common.annotation.MyLog;
import org.irm.lab.common.api.R;
import org.irm.lab.common.constant.LogConstant;
import org.irm.lab.common.utils.Func;
import org.irm.lab.kg.entity.KnowledgeProperty;
import org.irm.lab.kg.entity.KnowledgeRelation;
import org.irm.lab.kg.feign.KnowledgeRelationFeign;
import org.irm.lab.kg.service.IKnowledgeRelationService;
import org.irm.lab.kg.vo.KnowledgeRelationVO;
import org.irm.lab.kg.vo.ReferenceMapVO;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

@RestController
@RequestMapping("/knowledge-relation")
@RequiredArgsConstructor
@Api(value = "知识关系")
public class KnowledgeRelationController implements KnowledgeRelationFeign {

    private final IKnowledgeRelationService knowledgeRelationService;


    /**
     * 新增/修改关系
     *
     * @param knowledgeRelation {@link KnowledgeRelation}
     * @return {@link KnowledgeRelation}
     */
    @MyLog(menu = LogConstant.MENU_VIEW, dataType = LogConstant.DATA_RELATION, operation = LogConstant.OPERATION_ADD)
    @ApiOperation(value = "新增/修改关系")
    @PostMapping("/save")
    public R<KnowledgeRelation> save(@Validated @RequestBody KnowledgeRelation knowledgeRelation) {
        return R.data(knowledgeRelationService.save(knowledgeRelation));
    }

    /**
     * 获取指定概念作为主语的所有关联关系对象
     *
     * @param modelId 模型Id
     * @param conceptName 概念名称
     */
    @GetMapping("/relation-inverse")
    public R<List<KnowledgeRelation>> relationInverse(@RequestParam String modelId,@RequestParam String conceptName){
        return R.data(knowledgeRelationService.relationInverse(modelId,conceptName));
    }

    /**
     * 删除关系
     *
     * @param ids 关系id
     */
    @MyLog(menu = LogConstant.MENU_VIEW, dataType = LogConstant.DATA_RELATION, operation = LogConstant.OPERATION_REMOVE)
    @ApiOperation(value = "删除关系")
    @PostMapping(value = "/remove")
    public R<Void> remove(@RequestBody String ids) {
        knowledgeRelationService.remove(Func.objToStrList(ids));
        return R.success();
    }

    /**
     * 获取当前关系可用属性
     *
     * @param relationId 关系Id
     * @return 可用属性列表
     */
    @ApiOperation(value = "获取当前关系可用属性", hidden = true)
    @GetMapping("/list-available-property")
    public R<List<KnowledgeProperty>> listAvailableProperty(@RequestParam String relationId) {
        return R.data(knowledgeRelationService.listAvailableProperty(relationId));
    }

    /**
     * 引用属性
     *
     * @param id  关系Id
     * @param ids 属性Id列表
     * @return {@link KnowledgeRelation}
     */
    @ApiOperation(value = "属性绑定")
    @PostMapping(value = "/bind")
    public R<KnowledgeRelation> remove(@RequestParam String id, @RequestBody String ids) {
        return R.data(knowledgeRelationService.bind(id, Func.objToStrList(ids)));
    }

    /**
     * 取消关系属性引用
     *
     * @param id 关系Id
     * @param ids 属性Id
     */
    @ApiOperation(value = "取消关系属性引用")
    @PostMapping("/remove-relation-property")
    public R<Void> removeProperty(@RequestParam String id,@RequestBody String ids){
        knowledgeRelationService.removeRelationProperty(id,Func.objToStrList(ids));
        return R.success();
    }

    /**
     * 关系分组查询
     *
     * @return 分组查询
     */
    @ApiOperation(value = "关系分组")
    @GetMapping("/group-list")
    public R<Map<String, List<KnowledgeRelationVO>>> groupList() {
        return R.data(knowledgeRelationService.groupList());
    }

    @ApiOperation(value = "根据主语宾语查关系", hidden = true)
    @GetMapping("/relation-list-by-concept")
    public R<List<KnowledgeRelation>> relationListByConcept(@RequestParam String firstId, @RequestParam String endId) {
        return R.data(knowledgeRelationService.relationListByConcept(firstId, endId));
    }

    @ApiOperation(value = "根据关系ids获取所有谓词id", hidden = true)
    @GetMapping("/list-info")
    public R<List<KnowledgeRelation>> listInfo(@RequestParam List<String> ids) {
        return R.data(knowledgeRelationService.listInfo(ids));
    }

    @ApiOperation(value = "根据概念集合查询所有关联关系ids", hidden = true)
    @GetMapping("/list-by-concept-ids")
    public R<List<String>> relationIdsByConceptIds(@RequestParam List<String> ids) {
        return R.data(knowledgeRelationService.relationIdsByConceptIds(ids));
    }

    @ApiOperation(value = "详细查询")
    @GetMapping("/info-vo")
    public R<KnowledgeRelationVO> infoVO(@RequestParam String id) {
        return R.data(knowledgeRelationService.infoVO(id));
    }


    @ApiOperation(value = "模型关系列表", hidden = true)
    @GetMapping("/relation-by-model")
    public R<List<ReferenceMapVO>> relationListByModelId(@RequestParam String modelId) {
        return R.data(knowledgeRelationService.relationtListByModelId(modelId));
    }

    @ApiOperation(value = "模型关系列表", hidden = true)
    @GetMapping("/relation-by-model-concept")
    public R<List<KnowledgeRelation>> relationListByModelIdAndConceptOne(@RequestParam String modelId, @RequestParam String conceptId) {
        return R.data(knowledgeRelationService.relationListByModelIdAndConceptOne(modelId, conceptId));
    }

    @ApiOperation(value = "获取当前概念涉及到的所有关系", hidden = true)
    @GetMapping("/relation-by-concept")
    public R<List<KnowledgeRelation>> getRelationByConceptId(@RequestParam String conceptId) {
        return R.data(knowledgeRelationService.getRelationByConceptId(conceptId));
    }

    @ApiOperation(value = "获取当前概念作为主语的所有关系", hidden = true)
    @GetMapping("/forward-relation-by-concept")
    public R<List<KnowledgeRelation>> getForwardRelationByConceptId(@RequestParam String conceptId) {
        return R.data(knowledgeRelationService.getForwardRelationByConceptId(conceptId));
    }

    /**
     * 获取关系列表
     *
     * @return {@link KnowledgeRelation}
     */
    @ApiOperation(value = "关系列表")
    @GetMapping("/list")
    public R<List<KnowledgeRelation>> list() {
        return R.data(knowledgeRelationService.list());
    }

    /**
     * 根据Id列表获取关系列表
     *
     * @param ids id列表
     * @return {@link KnowledgeRelation}
     */
    @ApiOperation(value = "根据Id列表获取关系列表", hidden = true)
    @GetMapping("/find-by-ids")
    public R<List<KnowledgeRelation>> findByIds(@RequestParam List<String> ids) {
        return R.data(knowledgeRelationService.findByIds(ids));
    }

    /**
     * 根据id获取关系对象
     *
     * @param id 关系Id
     * @return {@link KnowledgeRelation}
     */
    @ApiOperation(value = "关系详情")
    @GetMapping("/info")
    public R<KnowledgeRelation> info(@RequestParam String id) {
        return R.data(knowledgeRelationService.info(id));
    }

}
