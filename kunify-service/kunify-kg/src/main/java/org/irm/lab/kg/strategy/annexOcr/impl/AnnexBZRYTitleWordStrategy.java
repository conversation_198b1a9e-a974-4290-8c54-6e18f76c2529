package org.irm.lab.kg.strategy.annexOcr.impl;

import java.io.InputStream;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

import org.dromara.streamquery.stream.core.stream.Steam;
import org.irm.lab.common.utils.NetWorkFileUtil;
import org.irm.lab.common.utils.ThreadLocalUtil;
import org.irm.lab.kg.entity.annex.AnnexDocumentUnit;
import org.irm.lab.kg.rabbitmq.producer.EsSyncProducer;
import org.irm.lab.kg.repository.DocumentUnitRepository;
import org.irm.lab.kg.repository.annex.processing.AnnexDocumentUnitRepository;
import org.irm.lab.kg.utils.SplitterUtil;
import org.irm.lab.repository.entity.Resource;
import org.irm.lab.repository.entity.ResourceAnnex;
import org.irm.lab.repository.repository.ResourceRepository;
import org.irm.lab.resource.entity.Attach;
import org.irm.lab.resource.repository.AttachRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.aspose.words.Document;
import com.aspose.words.LayoutCollector;
import com.aspose.words.NodeType;
import com.aspose.words.Paragraph;
import com.mongodb.client.model.Filters;

import cn.hutool.core.util.ObjectUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR> <br/>
 * @date 2024/6/18 <br/>
 * @Copyright 博客：https://eliauku.gitee.io/  ||  per aspera and astra <br/>
 */
@Service("annexBZRYTitleWordStrategy")
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
@Slf4j
public class AnnexBZRYTitleWordStrategy extends AbstractAnnexFileOcrStrategyImpl2{
    private final ResourceRepository resourceRepository;
    private final AttachRepository attachRepository;
    private final EsSyncProducer esSyncProducer;
    private final DocumentUnitRepository documentUnitRepository;
    private final AnnexDocumentUnitRepository annexDocumentUnitRepository;
    @Override
    Map<Integer, Map<Integer, String>> getOcrResult(String strategy, ResourceAnnex message) {
        Map<Integer, Map<Integer, String>> resourceText;
        final Attach attach = attachRepository.findById(message.getPrimaryFileId());

        try (InputStream inputStream = NetWorkFileUtil.urlToInputStream(attach.getLink());) {

            log.info("开始对主文件进行word抽取");

            resourceText = processWordDocument(inputStream);

        } catch (Exception e) {
            throw new RuntimeException(e);
        }

        return resourceText;
    }

    public static Map<Integer, Map<Integer, String>> processWordDocument(InputStream inputStream) throws Exception {
        Map<Integer, Map<Integer, String>> result = new HashMap<>();
        Document doc = new Document(inputStream);
        LayoutCollector layoutCollector = new LayoutCollector(doc);
        Map<Integer, StringBuilder> pagesText = new HashMap<>();

        for (Paragraph para : (Iterable<Paragraph>) doc.getChildNodes(NodeType.PARAGRAPH, true)) {
            int pageNumber = layoutCollector.getStartPageIndex(para);
            pagesText.putIfAbsent(pageNumber, new StringBuilder());
            final String text = para.getText();
            pagesText.get(pageNumber).append(SplitterUtil.secondaryProcessing(text));
        }

        Map<Integer, String> textByPages = new HashMap<>();
        pagesText.forEach((key, value) -> textByPages.put(key, value.toString()));

        // 遍历 textByPages，使用正则表达式匹配指定格式字符串之前的文本，并移动到前一页
        int totalPages = textByPages.size();
        Pattern pattern = Pattern.compile("[一二三四五六七八九十百千万亿]+、");

        for (int i = 2; i < totalPages; i++) {
            String currentPageText = textByPages.get(i);
            Matcher matcher = pattern.matcher(currentPageText);
            if (matcher.find()) {
                int index = matcher.start();
                // 找到指定格式字符串，将其之前的内容移动到前一页
                // 更新前一页的文本
                textByPages.put(i - 1, textByPages.get(i - 1) + currentPageText.substring(0, index)
                        // 更新前一页的文本
                );
                // 更新当前页的文本，删除已移动的内容
                currentPageText = currentPageText.substring(index);
                textByPages.put(i, currentPageText);
            }
            if (!pattern.matcher(textByPages.get(textByPages.size()-1)).find()) {
                final String s = textByPages.get(textByPages.size() - 1);
                textByPages.compute(textByPages.size()-2, (k, s1) -> s1 + s);
                textByPages.remove(textByPages.size() - 1);
            }
            totalPages = textByPages.size();
        }

        Pattern pattern1 = Pattern.compile("[一二三四五六七八九十百千万亿]+、");

        for (int i = 1; i < totalPages; i++) {
            String currentPageText = textByPages.get(i);
            Matcher matcher = pattern1.matcher(currentPageText);
            int previousMatchEnd = 0;
            int corpusIndex = 1;
            Map<Integer, String> currentPageCorpora = new HashMap<>();

            while (matcher.find()) {
                int matchStart = matcher.start();
                if (matchStart > previousMatchEnd) {
                    String corpus = currentPageText.substring(previousMatchEnd, matcher.end());
                    currentPageCorpora.put(corpusIndex++, corpus);
                }
                previousMatchEnd = matcher.start();
            }

            if (previousMatchEnd < currentPageText.length()) {
                String remainingText = currentPageText.substring(previousMatchEnd);
                currentPageCorpora.put(corpusIndex, remainingText);
            }

            result.put(i, currentPageCorpora);
        }

        return result;
    }


    @Override
    void saveOcrResult(String strategy, ResourceAnnex message, Map<Integer, Map<Integer, String>> ocrResult) {

        List<AnnexDocumentUnit> units = new ArrayList<>();

        Steam.of(ocrResult).forEach(data -> {
            final Map<Integer, String> value = data.getValue();
            value.forEach((key, value1) -> {
                        AnnexDocumentUnit documentUnit = new AnnexDocumentUnit();
                        documentUnit.setAnnexId(message.getId());
                        documentUnit.setContent(SplitterUtil.secondaryProcessing(value1));
                        documentUnit.setSortInCurrentPage(key);
                        documentUnit.setPage(data.getKey());
                        documentUnit.setType("0");
                        units.add(documentUnit);
                    }
            );
        });// 9900    17:01
        // 一小时1000个

        annexDocumentUnitRepository.saveAll(units);
        List<AnnexDocumentUnit> documentUnitList = annexDocumentUnitRepository.findByCondition(Filters.eq("annexId", message.getId()));
        if (ObjectUtil.isNotEmpty(documentUnitList)) {
            // 合并内容
            StringBuffer stringBuffer = new StringBuffer();
            documentUnitList.forEach(documentUnit -> stringBuffer.append(documentUnit.getContent()));
            esSyncProducer.sendMessage(ThreadLocalUtil.get("user"), null, message, stringBuffer.toString(), "副文件");
            log.info("【副文件】{} 同步到ES中", message.getName());
        }

    }
}
