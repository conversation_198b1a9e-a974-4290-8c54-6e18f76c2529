package org.irm.lab.kg.service.impl.kgprocess;

import cn.hutool.core.convert.Convert;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONObject;
import com.mongodb.client.model.Filters;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.bson.Document;
import org.bson.conversions.Bson;
import org.irm.lab.common.api.R;
import org.irm.lab.common.exception.ServiceException;
import org.irm.lab.config.entity.Label;
import org.irm.lab.config.repository.LabelRepository;
import org.irm.lab.kg.entity.processing.*;
import org.irm.lab.kg.kgprocess.strategy.ProcessingStrategySelector;
import org.irm.lab.kg.repository.processing.CacheLabelDataRepository;
import org.irm.lab.kg.repository.processing.CacheTripleLabelDataRepository;
import org.irm.lab.kg.service.IRuleParsingGraphService;
import org.irm.lab.repository.constant.DocumentResolvedStatus;
import org.irm.lab.repository.entity.Resource;
import org.irm.lab.repository.entity.ResourceAnnex;
import org.irm.lab.repository.feign.ResourceAnnexFeign;
import org.irm.lab.repository.feign.ResourceFeign;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2023/5/8 10:20
 * @description
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class RuleParsingGraphServiceImpl implements IRuleParsingGraphService {

    private final CacheLabelDataRepository cacheLabelDataRepository;
    private final CacheTripleLabelDataRepository cacheTripleLabelDataRepository;
    private final LabelRepository labelRepository;
    private final ProcessingStrategySelector processingStrategySelector;
    private final ResourceFeign resourceFeign;
    private final ResourceAnnexFeign resourceAnnexFeign;


    /**
     * 规则解析 ===> 获取标签数据树
     *
     * @param resourceId 资源Id
     * @param dataSetId  数据集Id
     * @return 标签数据
     */
    @Override
    public JSONArray relevanceShow(String resourceId, String dataSetId) {
        Map<String, List<JSONObject>> labelDatamap = entitySortByKeyValue(dataSetId);
        Bson and = Filters.and(Filters.eq("resourceId", resourceId), Filters.eq("type", "规则解析"));
        Bson and1 = Filters.and(Filters.eq("resourceId", resourceId), Filters.eq("topLabel", true));
        HashMap<String, Label> labelMap = new HashMap<>();
        // 对关系标签进行分组
        List<CacheTripleLabelData> allCacheTripleLabelDataList = cacheTripleLabelDataRepository.findByCondition(and);
        Map<String, List<CacheTripleLabelData>> collect = allCacheTripleLabelDataList.stream().collect(Collectors.groupingBy(CacheTripleLabelData::getStartLabelDataId));
        // 获取该资源规则解析产生的所有标签数据
        List<CacheLabelData> cacheLabelDataList = cacheLabelDataRepository.findByCondition(Filters.or(and, and1));
        // 对标签数据进行分组
        Map<String, List<CacheLabelData>> CacheLabelDataMap = cacheLabelDataList.stream().collect(Collectors.groupingBy(CacheLabelData::getId));
        // 遍历标签数据
        for (CacheLabelData cacheLabelData : cacheLabelDataList) {
            // 创建数组存储关系
            List<JSONObject> relations = new ArrayList<>();
            // 1、封装第一级标签的属性
            // 把标签数据的属性放在和关系平级的位置，给属性封装名称
            for (LabelPropertyVO labelProperty : cacheLabelData.getLabelProperties()) {
                if (labelProperty.getLabelPropertyId() == null) continue;
                // 获取属性标签对象
                String labelId = labelProperty.getLabelPropertyId();
                Label propertyLabel = getLabel(labelMap, labelId);
                if (propertyLabel == null)
                    throw new ServiceException("数据集标签不存在，请检查数据集，并修改后重新解析！");
                // 获取属性标签
                // 为属性设置标签名称
                labelProperty.setLabelPropertyName(propertyLabel.getName());
                // 把属性封装为指定JSONObject
                JSONObject labelDataPropertyObject = new JSONObject(labelProperty);
                // 添加层级
                labelDataPropertyObject.putOpt("level", 3);
                // 把属性放在关系前面
                if (ObjectUtil.isNotEmpty(labelDataPropertyObject)) relations.add(labelDataPropertyObject);
            }
            // 1.1、为第一级标签封装关系
            // 获取每个数据标签 作为主语的 关系三元组
            List<CacheTripleLabelData> cacheTripleLabelDataList = collect.get(cacheLabelData.getId());
            if (ObjectUtil.isNotEmpty(cacheTripleLabelDataList)) {
                for (CacheTripleLabelData cacheTripleLabelData : cacheTripleLabelDataList) {
                    // 生成关系对象
                    generateRelation(relations, cacheTripleLabelData, labelMap, CacheLabelDataMap);
                }
            }
            // 获取概念标签
            String conceptLabelId = cacheLabelData.getLabelId();
            Label conceptLabel = getLabel(labelMap, conceptLabelId);
            if (conceptLabel == null) throw new ServiceException("数据集标签不存在，请检查数据集，并修改后重新解析！");
            // 给标签数据的标签Id封装为labelName
            cacheLabelData.setLabelName(conceptLabel.getName());
            // 转换为JSONObject
            JSONObject labelDataJsonObject = new JSONObject(cacheLabelData);
            // 添加 层级
            labelDataJsonObject.putOpt("level", 2);
            // 添加关系
            labelDataJsonObject.putOpt("children", relations);
            // 2、封装第二级标签
            // 根据标签Id查询标签对象，并获取标签名称
            String labelId = cacheLabelData.getLabelId();
            Label label = getLabel(labelMap, labelId);
            if (label == null) throw new ServiceException("数据集标签不存在，请检查数据集，并修改后重新解析！");
            // 添加结果
            List<JSONObject> list = labelDatamap.getOrDefault(label.getName(), new ArrayList<>());
            list.add(labelDataJsonObject);
            list = list.stream().sorted(Comparator.comparing(json -> Convert.toStr(json.getOrDefault("content", "1")))).collect(Collectors.toList());
            labelDatamap.put(label.getName(), list);

        }
        // 封装标签数据
        JSONArray jsonArray = new JSONArray();
        Set<String> keySet = labelDatamap.keySet();
        for (String key : keySet) {
            JSONObject jsonObject = new JSONObject();
            // 获取标签ID
            String labelId = labelDatamap.get(key).get(0).getStr("labelId");
            jsonObject.putOpt("labelId", labelId);
            jsonObject.putOpt("labelName", key);
            jsonObject.putOpt("children", labelDatamap.get(key));
            jsonObject.putOpt("level", 1);
            jsonArray.add(jsonObject);
        }
        return jsonArray;

    }

    /**
     * 获取标签对象
     *
     * @param labelMap 标签缓存Map
     * @param labelId  标签Id
     * @return 标签对象
     */
    private Label getLabel(Map<String, Label> labelMap, String labelId) {
        Label label = labelMap.get(labelId);
        if (label == null) {
            try {
                label = labelRepository.findById(labelId);
            } catch (Exception e) {
                return null;
            }
            labelMap.put(labelId, label);
        }
        return label;
    }


    /**
     * 获取该资源下的所有缓存标签数据
     *
     * @param resourceId 资源Id
     * @return {@link CacheLabelData}
     */
    @Override
    public List<CacheLabelData> listCacheLabelData(String resourceId) {
        Bson and = Filters.and(Filters.eq("resourceId", resourceId), Filters.eq("type", "规则解析"));
        return cacheLabelDataRepository.findByCondition(and);
    }

    /**
     * 新增或修改缓存标签数据实例
     *
     * @param cacheLabelData {@link CacheLabelData}
     */
    @Override
    public void saveOrUpdateLabelData(CacheLabelData cacheLabelData) {
        // 新增属性 和 修改属性
        if (ObjectUtil.isNotEmpty(cacheLabelData.getLabelProperties())) {
            // 新增属性 没有 identifier
            List<LabelPropertyVO> labelProperties = cacheLabelData.getLabelProperties();
            for (LabelPropertyVO labelProperty : labelProperties) {
                // 构建属性对象
                HashMap<String, Object> filterMap = new HashMap<>();
                filterMap.put("labelPropertyId", labelProperty.getLabelPropertyId());
                filterMap.put("value", labelProperty.getValue());
                // 如果不存在identifier 就是新增属性
                if (ObjectUtil.isEmpty(labelProperty.getIdentifier())) {
                    // 设置唯一标识
                    filterMap.put("identifier", IdUtil.simpleUUID());
                } else {
                    // 修改属性，先删除原来的属性
                    cacheLabelDataRepository.pullDistinct(cacheLabelData.getId(), Map.of("labelProperties", new Document("identifier", labelProperty.getIdentifier())));
                    // 设置原来的唯一标识
                    filterMap.put("identifier", labelProperty.getIdentifier());
                }
                // 添加新的属性
                cacheLabelDataRepository.pushDistinct(cacheLabelData.getId(), Map.of("labelProperties", new Document(filterMap)));
            }
            return;
        }
        // 如果属性为空，新增或修改实例
        if (ObjectUtil.isEmpty(cacheLabelData.getLabelProperties())) {
            // 新增实例时,把属性字段置空
            cacheLabelData.setLabelProperties(null);
        }
        // 新增或修改标签数据
        cacheLabelDataRepository.save(cacheLabelData);
    }

    /**
     * 删除实例/实例的属性
     *
     * @param cacheLabelData 缓存标签数据实例
     */
    @Override
    public void removeLabelData(CacheLabelData cacheLabelData) {
        // 获取缓存标签数据ID
        String cacheLabelDataId = cacheLabelData.getId();
        CacheLabelData deleteCacheLabelData = cacheLabelDataRepository.findById(cacheLabelDataId);
        // 删除实例
        if (cacheLabelData.getLabelProperties() == null || cacheLabelData.getLabelProperties().isEmpty()) {
            if (deleteCacheLabelData.isTopLabel()) {
                throw new ServiceException("顶级公文标签无法删除!");
            }
            // 删除标签数据
            cacheLabelDataRepository.deleteByIdFake(cacheLabelDataId);
            // 删除该标签数据涉及的关系三元组
            Bson or = Filters.or(Filters.eq("startLabelDataId", cacheLabelDataId), Filters.eq("endLabelDataId", cacheLabelDataId));
            List<CacheTripleLabelData> cacheTripleLabelDataList = cacheTripleLabelDataRepository.findByCondition(or);
            // 删除多个三元组
            if (ObjectUtil.isNotEmpty(cacheTripleLabelDataList)) {
                List<String> cacheTripleLabelDataId = cacheTripleLabelDataList.stream().map(TripleLabelData::getId).collect(Collectors.toList());
                cacheTripleLabelDataRepository.deleteByIdFake(cacheTripleLabelDataId);
            }
        }
        // 删除属性
        if (ObjectUtil.isNotEmpty(cacheLabelData.getLabelProperties())) {
            for (LabelPropertyVO labelProperty : cacheLabelData.getLabelProperties()) {
                // 删除属性
                cacheLabelDataRepository.pullDistinct(cacheLabelDataId, Map.of("labelProperties", new Document("identifier", labelProperty.getIdentifier())));
            }
        }
    }

    /**
     * 新增/修改关系三元组
     *
     * @param cacheTripleLabelDataList {@link CacheTripleLabelData}
     */
    @Override
    public void saveOrUpdateTripleLabelData(List<CacheTripleLabelData> cacheTripleLabelDataList) {
        for (CacheTripleLabelData cacheTripleLabelData : cacheTripleLabelDataList) {
            // 新增或修改属性
            if (ObjectUtil.isNotEmpty(cacheTripleLabelData.getRelationProperties())) {
                for (LabelPropertyVO relationProperty : cacheTripleLabelData.getRelationProperties()) {
                    // 构建属性对象
                    Map<String, Object> map = new HashMap<>();
                    map.put("labelPropertyId", relationProperty.getLabelPropertyId());
                    map.put("value", relationProperty.getValue());
                    // 如果不存在identifier 就是新增属性
                    if (ObjectUtil.isEmpty(relationProperty.getIdentifier())) {
                        // 设置唯一标识
                        map.put("identifier", IdUtil.simpleUUID());
                    } else {
                        // 修改属性，先删除原来的属性
                        cacheTripleLabelDataRepository.pullDistinct(cacheTripleLabelData.getId(), Map.of("relationProperties", new Document("identifier", relationProperty.getIdentifier())));
                        // 设置原来的唯一标识
                        map.put("identifier", relationProperty.getIdentifier());
                    }
                    // 添加新的属性
                    cacheTripleLabelDataRepository.pushDistinct(cacheTripleLabelData.getId(), Map.of("relationProperties", new Document(map)));
                }
                return;
            }
            // 如果属性为空，就是新增或修改关系
            if (ObjectUtil.isEmpty(cacheTripleLabelData.getRelationProperties())) {
                cacheTripleLabelData.setRelationProperties(null);
            }
            // 新增或修改关系
            cacheTripleLabelDataRepository.save(cacheTripleLabelData);
        }
    }

    /**
     * 删除关系三元组
     *
     * @param cacheTripleLabelData 关系三元组
     */
    @Override
    public void removeTripleLabelData(CacheTripleLabelData cacheTripleLabelData) {
        // 获取关系三元组Id
        String tripleLabelDataId = cacheTripleLabelData.getId();
        // 删除关系
        if (cacheTripleLabelData.getRelationProperties() == null || cacheTripleLabelData.getRelationProperties().isEmpty()) {
            cacheTripleLabelDataRepository.deleteByIdFake(tripleLabelDataId);
        }
        // 删除属性
        if (ObjectUtil.isNotEmpty(cacheTripleLabelData.getRelationProperties())) {
            for (LabelPropertyVO relationProperty : cacheTripleLabelData.getRelationProperties()) {
                cacheTripleLabelDataRepository.pullDistinct(tripleLabelDataId, Map.of("relationProperties", new Document("identifier", relationProperty.getIdentifier())));
            }
        }
    }

    /**
     * 规则解析确认  ===> 规则解析结果确认
     *
     * @param resourceId 资源Id
     */
    @Override
    public void parsingConfirm(String resourceId) {
        R<Resource> resourceR = resourceFeign.info(resourceId);
        Resource resource = resourceR.getData();
        if (!resourceR.isSuccess() || ObjectUtil.isEmpty(resource)) {
            throw new ServiceException("当前资源不存在!");
        }
        // 修改资源状态
        resource.setDocRuleParsingConfirm(true);
        // 更新资源状态
        resourceR = resourceFeign.save(resource);
        // 获取更新后的资源
        resource = resourceR.getData();
        // 更新所有附件的状态
        R<List<ResourceAnnex>> resourceAnnexListR = resourceAnnexFeign.listByResourceId(resourceId);
        List<ResourceAnnex> resourceAnnexList = resourceAnnexListR.getData();
        if (resourceAnnexListR.isSuccess() && ObjectUtil.isNotEmpty(resourceAnnexList)) {
            for (ResourceAnnex resourceAnnex : resourceAnnexList) {
                resourceAnnex.setDocRuleParsingConfirm(true);
                resourceAnnexFeign.save(resourceAnnex);
            }
        }
        // 调用资源加工选择器
        processingStrategySelector.process(resource);
    }

    /**
     * 重新进行规则解析
     *
     * @param resourceId 资源Id
     */
    @Override
    public void reRuleParsing(String resourceId) {
        R<Resource> resourceR = resourceFeign.info(resourceId);
        Resource resource = resourceR.getData();
        if (!resourceR.isSuccess() || ObjectUtil.isEmpty(resource)) {
            throw new ServiceException("当前资源不存在!");
        }
        // 修改资源状态
        resource.setRuleStatus(DocumentResolvedStatus.UN_RESOLVED);
        resource.setDocRuleParsingConfirm(false);
        resource = resourceFeign.save(resource).getData();
        // 删除全部已经生成的规则解析缓存标签
        Bson and = Filters.and(Filters.eq("resourceId", resourceId), Filters.eq("type", "规则解析"));
        List<String> cacheLabelDataIdList = cacheLabelDataRepository.findByCondition(and)
                .stream()
                .map(CacheLabelData::getId)
                .collect(Collectors.toList());
        if (ObjectUtil.isNotEmpty(cacheLabelDataIdList)) {
            cacheLabelDataRepository.deleteByIdFake(cacheLabelDataIdList);
        }
        // 删除全部已经生成的规则解析缓存关系标签
        List<String> cacheTripleLabelDataIdList = cacheTripleLabelDataRepository.findByCondition(and)
                .stream()
                .map(TripleLabelData::getId)
                .collect(Collectors.toList());
        if (ObjectUtil.isNotEmpty(cacheTripleLabelDataIdList)) {
            cacheTripleLabelDataRepository.deleteByIdFake(cacheTripleLabelDataIdList);
        }
        // 调用资源加工选择器
        processingStrategySelector.process(resource);
    }


    /**
     * 关系生成
     *
     * @param relations       关系JSONObject
     * @param tripleLabelData 关系三元组
     * @param labelMap        标签Map
     */
    private void generateRelation(List<JSONObject> relations, TripleLabelData tripleLabelData, Map<String, Label> labelMap, Map<String, List<CacheLabelData>> cacheLabelDataMap) {
        // 创建关系对象
        JSONObject relationJsonObject = new JSONObject();
        // 添加关系三元组的Id
        relationJsonObject.putOpt("id", tripleLabelData.getId());
        // 如果关系有属性的话就添加
        if (ObjectUtil.isNotEmpty(tripleLabelData.getRelationProperties())) {
            JSONArray relationArray = new JSONArray();
            for (LabelPropertyVO relationProperty : tripleLabelData.getRelationProperties()) {
                if (relationProperty.getLabelPropertyId() == null) continue;
                String labelPropertyId = relationProperty.getLabelPropertyId();
                Label label = getLabel(labelMap, labelPropertyId);
                if (label == null) throw new ServiceException("数据集标签不存在，请检查数据集，并修改后重新解析！");
                JSONObject relationPropertyObject = new JSONObject(relationProperty);
                // 根据关系标签Id获取关系标签名称
                relationPropertyObject.putOpt("labelName", label.getName());
                // 层级三
                relationPropertyObject.putOpt("level", 4);
                // 添加关系的属性
                relationArray.add(relationPropertyObject);
            }
            relationJsonObject.putOpt("children", relationArray);
        }

        // 谓词标签Id
        relationJsonObject.putOpt("relationLabelId", tripleLabelData.getRelationLabelId());
        // 谓词标签名称
        String relationLabelId = tripleLabelData.getRelationLabelId();
        Label relationLabel = getLabel(labelMap, relationLabelId);
        if (relationLabel == null) throw new ServiceException("数据集标签不存在，请检查数据集，并修改后重新解析！");
        relationJsonObject.putOpt("relationLabelName", relationLabel.getName());
        // 宾语的标签Id
        relationJsonObject.putOpt("labelId", tripleLabelData.getEndLabelId());
        // 宾语的标签名称
        String endLabelId = tripleLabelData.getEndLabelId();
        Label label = getLabel(labelMap, endLabelId);
        if (label == null) throw new ServiceException("数据集标签不存在，请检查数据集，并修改后重新解析！");
        relationJsonObject.putOpt("labelName", label.getName());
        // 宾语的标签数据Id
        relationJsonObject.putOpt("labelDataId", tripleLabelData.getEndLabelDataId());
        // 宾语的标签数据名称
        String endLabelDataId = tripleLabelData.getEndLabelDataId();
        List<CacheLabelData> cacheLabelDataList = cacheLabelDataMap.get(endLabelDataId);
        CacheLabelData endCacheLabelData;
        if (ObjectUtil.isNotEmpty(cacheLabelDataList)) {
            endCacheLabelData = cacheLabelDataList.get(0);
        } else {
            endCacheLabelData = cacheLabelDataRepository.findById(endLabelDataId);
            cacheLabelDataMap.put(endLabelDataId, List.of(endCacheLabelData));
        }
        relationJsonObject.putOpt("labelDataName", endCacheLabelData.getContent());
        // 添加层级
        relationJsonObject.putOpt("level", 3);
        // 添加关系
        relations.add(relationJsonObject);
    }

    /**
     * 结果排序TreeMap
     *
     * @param dataSetId 数据集Id
     */
    private Map<String, List<JSONObject>> entitySortByKeyValue(String dataSetId) {
        Map<String, Integer> labelSort = new HashMap<>();
        List<Label> labelList = labelRepository.findByCondition(new Document("dataSetId", dataSetId));
        labelList.forEach(m -> labelSort.put(m.getName(), m.getSort()));
        return new TreeMap<>(((o1, o2) -> {
            if (!(labelSort.containsKey(o1) && labelSort.containsKey(o2))) return 1;
            if (!Objects.equals(o1, o2)) {
                Integer o1Sort = labelSort.get(o1);
                Integer o2Sort = labelSort.get(o2);
                return Objects.equals(o1Sort, o2Sort) ? 0 : (o1Sort > o2Sort ? 1 : -1);
            }
            return 0;
        }));
    }
}
