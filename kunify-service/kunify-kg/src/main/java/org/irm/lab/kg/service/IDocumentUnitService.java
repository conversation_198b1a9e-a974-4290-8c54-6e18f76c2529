package org.irm.lab.kg.service;

import org.irm.lab.common.support.MyPage;
import org.irm.lab.kg.algorithm.DocumentUnit;
import org.irm.lab.kg.algorithm.TableHeader;
import org.irm.lab.kg.dto.CorpusGenerateDTO;
import org.irm.lab.kg.vo.DocumentUnitVO;
import org.irm.lab.kg.vo.UnitMessageVO;
import org.springframework.core.io.InputStreamResource;
import org.springframework.http.ResponseEntity;

import java.io.FileNotFoundException;
import java.util.List;
import java.util.Map;

public interface IDocumentUnitService {
    MyPage<DocumentUnitVO> page(Map<String, Object> map);

    List<DocumentUnitVO> currentPageList(Map<String, Object> map);

    DocumentUnit save(DocumentUnit documentUnit);

    void remove(List<String> ids);

    void removeAndUpdateSortInCurrentPage(List<String> ids);

    String preview(String id) throws FileNotFoundException;

    String getPagePicture(String resourceId, Integer page);

    DocumentUnit saveKeys(String unitId, TableHeader tableHeader);

    DocumentUnit saveRow(String unitId, String map);

    void removeRow(String unitId, String index);

    void removeKeys(String unitId, String tableId);

    DocumentUnit info(String id);

    List<DocumentUnit> findByIds(List<String> ids);


    List<DocumentUnit> infoByResource(String resourceId);

    UnitMessageVO getResourceMessage(String id);

    /**
     * 语料生成
     *
     */
    ResponseEntity<InputStreamResource> corpusGenerate(CorpusGenerateDTO corpusGenerateDTO);

    /**
     * 资源库语料生成
     *
     */
    ResponseEntity<InputStreamResource> corpusResource(List<String> idList);

    /**
     * 工作任务语料生成
     *
     */
    void corpusWorkTask(String workTaskId, String taskId);


    /**
     * 生成指定工作任务下的所有资源的摘要语料
     *
     * @param workTaskId 工作任务ID
     */
    void summaryCorpus(String workTaskId);
}
