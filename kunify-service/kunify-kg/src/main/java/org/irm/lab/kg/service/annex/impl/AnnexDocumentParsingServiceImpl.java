package org.irm.lab.kg.service.annex.impl;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.irm.lab.common.api.R;
import org.irm.lab.common.constant.ExceptionMessageConst;
import org.irm.lab.common.enums.AlgorithmType;
import org.irm.lab.common.exception.ServiceException;
import org.irm.lab.kg.algorithm.AlgorithmProcessFactory;
import org.irm.lab.kg.entity.annex.AnnexDocumentUnit;
import org.irm.lab.kg.service.annex.IAnnexDocumentParsingService;
import org.irm.lab.kg.service.annex.IAnnexDocumentUnitService;
import org.irm.lab.kg.service.impl.ieInfer.AnnexIeInferServiceImpl;
import org.irm.lab.kg.service.impl.kgprocess.tag.KnowledgeTagAlgorithm;
import org.irm.lab.repository.constant.DocumentResolvedStatus;
import org.irm.lab.repository.constant.KnowledgeTagStatus;
import org.irm.lab.repository.entity.ResourceAnnex;
import org.irm.lab.repository.feign.ResourceAnnexFeign;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2023/5/8 15:28
 * @description 附件文档解析业务实现类
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class AnnexDocumentParsingServiceImpl implements IAnnexDocumentParsingService {

    private final IAnnexDocumentUnitService iAnnexDocumentUnitService;
    private final ResourceAnnexFeign resourceAnnexFeign;
    private final AlgorithmProcessFactory algorithmProcessFactory;
    @javax.annotation.Resource(name = "annexKnowledgeTag")
    private KnowledgeTagAlgorithm annexKnowledgeTagAlgorithm;
    private final org.springframework.context.ApplicationContext applicationContext;


    /**
     * 重新进行附件文档解析
     *
     * @param resourceAnnexId 附件Id
     */
    @Override
    public void reParsing(String resourceAnnexId) {
        R<ResourceAnnex> resourceAnnexR = resourceAnnexFeign.info(resourceAnnexId);
        ResourceAnnex resourceAnnex = resourceAnnexR.getData();
        if (!resourceAnnexR.isSuccess() || resourceAnnex == null) {
            throw new ServiceException("该附件不存在!");
        }
        // 删除该附件的所有语料单元
        List<AnnexDocumentUnit> unitList = iAnnexDocumentUnitService.findByAnnexId(resourceAnnexId);
        List<String> unitIdList = unitList.stream().map(AnnexDocumentUnit::getId).collect(Collectors.toList());
        iAnnexDocumentUnitService.remove(unitIdList);
        // 重新进行附件文档解析
        resourceAnnex.setResolveStatus(DocumentResolvedStatus.UN_RESOLVED);
        algorithmProcessFactory.algorithmApply(AlgorithmType.PDF_PRECISE_STRIPPER).annexProcess(resourceAnnex);
        if (ExceptionMessageConst.OCR_GEN_BEGIN.equals(resourceAnnex.getTextStripperStatus())) {
            resourceAnnex.setResolveStatus(DocumentResolvedStatus.RESOLVED);
            resourceAnnexFeign.save(resourceAnnex);
            throw new ServiceException("精确解析失败，请更换解析方式后重试!");
        }
    }

    /**
     * 使用OCR重新进行附件文档解析
     *
     * @param resourceAnnexId 附件Id
     */
    @Override
    public void reParsingWithOCR(String resourceAnnexId) {
        R<ResourceAnnex> resourceAnnexR = resourceAnnexFeign.info(resourceAnnexId);
        ResourceAnnex resourceAnnex = resourceAnnexR.getData();
        if (!resourceAnnexR.isSuccess() || resourceAnnex == null) {
            throw new ServiceException("该附件不存在!");
        }
        // 删除该附件的所有语料单元
        List<AnnexDocumentUnit> unitList = iAnnexDocumentUnitService.findByAnnexId(resourceAnnexId);
        List<String> unitIdList = unitList.stream().map(AnnexDocumentUnit::getId).collect(Collectors.toList());
        iAnnexDocumentUnitService.remove(unitIdList);
        // 重新进行附件文档解析
        try {
            resourceAnnex.setResolveStatus(DocumentResolvedStatus.UN_RESOLVED);
            resourceAnnex = resourceAnnexFeign.save(resourceAnnex).getData();
            algorithmProcessFactory.algorithmApply(AlgorithmType.OCR).annexProcess(resourceAnnex);
            resourceAnnex.setResolveStatus(DocumentResolvedStatus.RESOLVED);
        } catch (Exception e) {
            resourceAnnex.setResolveStatus(DocumentResolvedStatus.RESOLVED_FAILED);

            log.error(e.toString());
        } finally {
            resourceAnnexFeign.save(resourceAnnex);
        }
    }

    /**
     * 附件文档解析结果确认
     *
     * @param resourceAnnexId 附件Id
     */
    @Override
    public void confirm(String resourceAnnexId) {
        R<ResourceAnnex> resourceAnnexR = resourceAnnexFeign.info(resourceAnnexId);
        ResourceAnnex resourceAnnex = resourceAnnexR.getData();
        if (!resourceAnnexR.isSuccess() || resourceAnnex == null) {
            throw new ServiceException("该附件不存在!");
        }
        // 更新附件状态
        resourceAnnex.setDocParsingConfirm(true);
        resourceAnnex.setTagStatus(KnowledgeTagStatus.TAG_ING);
        resourceAnnexFeign.save(resourceAnnex);
        // 附件知识标准算法识别
        AnnexIeInferServiceImpl annexIeInferService = applicationContext.getBean(AnnexIeInferServiceImpl.class);
        annexIeInferService.knowledgeTagWithAlgorithm(resourceAnnex.getWorkTaskId(), resourceAnnex);

    }
}
