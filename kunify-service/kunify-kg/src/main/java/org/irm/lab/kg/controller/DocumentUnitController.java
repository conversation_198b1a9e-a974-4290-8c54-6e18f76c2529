package org.irm.lab.kg.controller;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.irm.lab.common.annotation.MyLog;
import org.irm.lab.common.api.R;
import org.irm.lab.common.constant.LogConstant;
import org.irm.lab.common.support.MyPage;
import org.irm.lab.common.utils.Func;
import org.irm.lab.common.utils.ThreadLocalUtil;
import org.irm.lab.kg.algorithm.DocumentUnit;
import org.irm.lab.kg.algorithm.TableHeader;
import org.irm.lab.kg.dto.CorpusGenerateDTO;
import org.irm.lab.kg.feign.DocumentUnitFeign;
import org.irm.lab.kg.service.IDocumentUnitService;
import org.irm.lab.kg.strategy.annexOcr.context.AnnexOcrStrategyContext;
import org.irm.lab.kg.strategy.ocr.context.OcrStrategyContext;
import org.irm.lab.kg.vo.DocumentUnitVO;
import org.irm.lab.kg.vo.UnitMessageVO;
import org.irm.lab.repository.entity.ResourceAnnex;
import org.springframework.core.io.InputStreamResource;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.io.FileNotFoundException;
import java.io.IOException;
import java.util.List;
import java.util.Map;

@RestController
@RequestMapping("/document-unit")
@Api(value = "语料单元")
public class DocumentUnitController implements DocumentUnitFeign {

    @Resource
    private IDocumentUnitService documentUnitService;
    @Resource
    private HttpServletRequest request;

    @Resource
    private OcrStrategyContext ocrStrategyContext;

    @Resource
    private AnnexOcrStrategyContext annexOcrStrategyContext;

    @ApiOperation(value = "语料单元分页")
    @GetMapping("/page")
    public R<MyPage<DocumentUnitVO>> page(@RequestParam Map<String, Object> pageMap) {
        return R.data(documentUnitService.page(pageMap));
    }

    @ApiOperation(value = "语料单元详情")
    @GetMapping("/info")
    public R<DocumentUnit> info(@RequestParam String id) {
        return R.data(documentUnitService.info(id));
    }

    @ApiOperation(value = "语料单元增改")
    @PostMapping("/save")
    public R<DocumentUnit> save(@RequestBody DocumentUnit documentUnit) {
        return R.data(documentUnitService.save(documentUnit));
    }

    @ApiOperation(value = "语料单元删除")
    @PostMapping("/remove")
    public R<Void> removeAndUpdateSortInCurrentPage(@RequestBody String ids) {
        documentUnitService.removeAndUpdateSortInCurrentPage(Func.objToStrList(ids));
        return R.success();
    }

    @ApiOperation(value = "语料单元预览")
    @GetMapping("/preview")
    public R<String> preview(@RequestParam String id) throws FileNotFoundException {
        return R.data(documentUnitService.preview(id));
    }

    @ApiOperation(value = "获取当前页图片", hidden = true)
    @GetMapping("/get-page-picture")
    public R<String> getPagePicture(@RequestParam String resourceId, @RequestParam Integer page) {
        return R.data(documentUnitService.getPagePicture(resourceId, page));
    }

    @ApiOperation(value = "表头增改", hidden = true)
    @PostMapping("/save-keys")
    public R<DocumentUnit> saveKeys(@RequestParam String unitId, @RequestBody TableHeader tableHeader) {
        return R.data(documentUnitService.saveKeys(unitId, tableHeader));
    }

    @ApiOperation(value = "表头删除", hidden = true)
    @PostMapping("/remove-keys")
    public R<DocumentUnit> removeKeys(@RequestParam String unitId, @RequestParam String tableId) {
        documentUnitService.removeKeys(unitId, tableId);
        return R.success();
    }

    @ApiOperation(value = "表格行信息增改", hidden = true)
    @PostMapping("/save-row")
    public R<DocumentUnit> saveRow(@RequestParam String unitId, @RequestBody String map) {
        return R.data(documentUnitService.saveRow(unitId, map));
    }

    @ApiOperation(value = "表格行信息删除", hidden = true)
    @PostMapping("/remove-row")
    public R<Void> removeRow(@RequestParam String unitId, @RequestParam String index) {
        documentUnitService.removeRow(unitId, index);
        return R.success();
    }


    @ApiOperation(value = "获取当前信息所在文件", hidden = true)
    @GetMapping("/get-resource-message")
    public R<UnitMessageVO> getResourceMessage(@RequestParam String id) {
        return R.data(documentUnitService.getResourceMessage(id));
    }

    /**
     * 根据资源Id获取该资源的所有语料单元
     *
     * @param resourceId 资源Id
     * @return 预料单元列表
     */
    @Override
    @GetMapping("/find-by-resourceId")
    public R<List<DocumentUnit>> findByResourceId(String resourceId) {
        return R.data(documentUnitService.infoByResource(resourceId));
    }

    @PostMapping("/summary-corpus")
    @Override
    public R<Void> splitDocumentUnit(org.irm.lab.repository.entity.Resource r, String strategy) {

        try {
            ocrStrategyContext.processOcr(strategy,r);
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
        return R.success();

    }

    @PostMapping("/summary-corpus-annex")
    @Override
    public R<Void> splitDocumentUnitAnnex(ResourceAnnex r, String strategy) {
        try {
            annexOcrStrategyContext.processOcr(strategy,r);
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
        return R.success();
    }

    /**
     * 语料生成
     *
     */
    @ApiOperation(value = "语料生成", hidden = true)
    @PostMapping("/corpus-generate")
    public ResponseEntity<InputStreamResource> corpusGenerate(@RequestBody CorpusGenerateDTO corpusGenerateDTO ) {
        return documentUnitService.corpusGenerate(corpusGenerateDTO);
    }

    /**
     * 资源库语料生成
     *
     */
    @ApiOperation(value = "语料生成",hidden = true)
    @PostMapping("/corpus-resource")
    public ResponseEntity<InputStreamResource> corpusResource(@RequestBody String ids){
        List<String> idList = Func.objToStrList(ids);
        return documentUnitService.corpusResource(idList);
    }

    /**
     * 工作任务语料生成
     *
     */
    @ApiOperation(value = "语料生成",hidden = true)
    @GetMapping("/corpus-work-task")
    public R<Void> corpusWorkTask(@RequestParam String workTaskId, @RequestParam String fileType){
        ThreadLocalUtil.set("user",request.getHeader("user"));
        documentUnitService.corpusWorkTask(workTaskId, fileType);
        return R.success();
    }

    /**
     * 生成指定工作任务下的所有资源的摘要语料
     *
     * @param workTaskId 工作任务ID
     */
    @GetMapping("/summary-corpus")
    public R<Void> summaryCorpus(String workTaskId){
        ThreadLocalUtil.set("user",request.getHeader("user"));
        documentUnitService.summaryCorpus(workTaskId);
        return R.success();
    }


}
