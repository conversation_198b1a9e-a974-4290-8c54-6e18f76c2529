package org.irm.lab.kg.service;

import org.irm.lab.common.support.MyPage;
import org.irm.lab.kg.entity.neo4j.node.NodeEntity;
import org.irm.lab.kg.vo.echarts.EchartsVO;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2023-02-09
 */

public interface INodeEntityService {
    MyPage<NodeEntity> page(Map<String, Object> queryParam, int page, int size, int depth);

    NodeEntity save(NodeEntity nodeEntity);

    NodeEntity info(String id, int depth);

    void remove(List<String> ids);

    EchartsVO echartsNodesByConcept(String conceptId, String modelId, String showCount);

    EchartsVO echartsNodesWithRelation(String nodeId, String showCount);

    NodeEntity updateName(String nodeId, String nodeName);
}
