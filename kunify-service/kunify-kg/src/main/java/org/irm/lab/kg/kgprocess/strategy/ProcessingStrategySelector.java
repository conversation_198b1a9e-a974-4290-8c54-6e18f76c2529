package org.irm.lab.kg.kgprocess.strategy;


import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.ReflectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.extra.spring.SpringUtil;
import lombok.Getter;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.irm.lab.common.api.R;
import org.irm.lab.common.utils.ThreadLocalUtil;
import org.irm.lab.kg.enums.KgProcessEnum;
import org.irm.lab.kg.kgprocess.*;
import org.irm.lab.kg.service.ILabelDataConsolidationService;
import org.irm.lab.kg.service.annex.IAnnexLabelDataConsolidationService;
import org.irm.lab.kg.service.impl.kgprocess.CommonKgProcess;
import org.irm.lab.repository.entity.Resource;
import org.irm.lab.repository.entity.ResourceAnnex;
import org.irm.lab.repository.feign.ResourceAnnexFeign;
import org.irm.lab.repository.feign.ResourceFeign;
import org.springframework.stereotype.Service;

import javax.servlet.http.HttpServletRequest;
import java.lang.reflect.Method;
import java.util.*;

/**
 * <AUTHOR>
 * @date 2023/4/25 18:32
 * @description 加工能力选择器
 */
@Setter
@Getter
@Service
@Slf4j
public class ProcessingStrategySelector extends CommonKgProcess {


    @javax.annotation.Resource
    private ILabelDataConsolidationService iLabelDataConsolidationService;
    @javax.annotation.Resource
    private IAnnexLabelDataConsolidationService iAnnexLabelDataConsolidationService;
    @javax.annotation.Resource
    private ResourceAnnexFeign resourceAnnexFeign;
    @javax.annotation.Resource
    private HttpServletRequest request;
    @javax.annotation.Resource
    private ResourceFeign resourceFeign;

    /**
     * 所有加工能力
     */
    private final Map<String, ProcessingStrategy> capabilityMap;

    public ProcessingStrategySelector() {
        // 初始化可选的加工能力
        capabilityMap = new HashMap<>();
        capabilityMap.put("resourceInstantiation", SpringUtil.getBean(ResourceInstantiationStrategy.class));
        capabilityMap.put("metadataConversion", SpringUtil.getBean(MetadataConverterStrategy.class));
        capabilityMap.put("documentParsing", SpringUtil.getBean(DocumentParsingStrategy.class));
        capabilityMap.put("knowledgeTag", SpringUtil.getBean(KnowledgeTagStrategy.class));
        capabilityMap.put("ruleParsing", SpringUtil.getBean(RuleParsingStrategy.class));
    }

    /**
     * 资源处理
     *
     * @param resource 资源
     */
    public void process(Resource resource) {
        // 初始化所有还未执行的加工能力
        LinkedList<ProcessingStrategy> unprocessedCapabilities = initUnProcessedCapabilityList(resource);
        // 判断加工能力是否已经全部执行完毕，
        if (ObjectUtil.isEmpty(unprocessedCapabilities)) {
            if (ThreadLocalUtil.get("user") == null)
                ThreadLocalUtil.set("user", request.getHeader("user"));
            log.info("==========资源加工已全部完成==========");
            // 资源加工全部完成，修改主文件和附件状态
            processEnd(resource);
            // 合并主文件缓存标签
            iLabelDataConsolidationService.reConsolidation(resource.getId());
            // 合并附件缓存标签
            R<List<ResourceAnnex>> resourceAnnexListR = resourceAnnexFeign.listByResourceId(resource.getId());
            List<ResourceAnnex> resourceAnnexList = resourceAnnexListR.getData();
            if (!resourceAnnexListR.isSuccess() || ObjectUtil.isEmpty(resourceAnnexList)) {
                log.info("该资源暂无附件，无需知识匹配!");
                return;
            }
            // 对所有附件开启知识匹配
            for (ResourceAnnex resourceAnnex : resourceAnnexList) {
                iAnnexLabelDataConsolidationService.reConsolidation(resourceAnnex.getId());
            }
        }
        // 循环执行
        while (ObjectUtil.isNotEmpty(unprocessedCapabilities)) {
            // 按顺序执行
            ProcessingStrategy strategy = unprocessedCapabilities.removeFirst();
            log.info("资源加工能力  ===>  【{}】", strategy.getName());
            KgProcessEnum currentProcess = KgProcessEnum.getByProcessName(strategy.getName());
            if (currentProcess != null) {
                // 直接修改当前加工能力的禁用字段
                String confirmField = currentProcess.getConfirmField();
                if (StrUtil.isNotBlank(confirmField)) {
                    String methodName = "set" + Character.toUpperCase(confirmField.charAt(0)) + confirmField.substring(1);
                    // 获取对应流程，是否开启的判断方法
                    Method method = ReflectUtil.getMethod(Resource.class, methodName, boolean.class);
                    try {
                        ReflectUtil.invoke(resource, method, true);
                        R<Resource> resourceR = resourceFeign.save(resource);
                        resource = resourceR.getData();
                    } catch (Exception ignored) {
                    }
                }
            }
            // 执行对应的加工能力
            strategy.process(resource);
            // 如果不是自动完成的加工能力，则执行结束后停止
            if (!KgProcessEnum.isAutoCompleted(strategy.getName())) {
                break;
            }
        }
    }


    /**
     * 获取所有未完成的加工能力
     *
     * @param resource 资源对象
     * @return 未完成的加工能力列表
     */
    private LinkedList<ProcessingStrategy> initUnProcessedCapabilityList(Resource resource) {
        LinkedList<ProcessingStrategy> unprocessedCapabilities = new LinkedList<>();
        // 获取未执行的加工能力枚举列表
        List<KgProcessEnum> unProcessedCapabilityEnumList = unProcessedCapabilityEnumList(resource);
        // 获取所有未执行的加工能力
        for (KgProcessEnum kgProcessEnum : unProcessedCapabilityEnumList) {
            unprocessedCapabilities.addLast(getCapabilityByCode(kgProcessEnum.getCode()));
        }
        return unprocessedCapabilities;
    }

    /**
     * 根据code获取指定加工能力
     *
     * @param code 加工能力code
     * @return {@link ProcessingStrategy}
     */
    private ProcessingStrategy getCapabilityByCode(String code) {
        for (Map.Entry<String, ProcessingStrategy> entry : capabilityMap.entrySet()) {
            if (entry.getKey().equals(code)) {
                return entry.getValue();
            }
        }
        throw new IllegalArgumentException("不存在指定的加工能力标识：" + code);
    }


}
