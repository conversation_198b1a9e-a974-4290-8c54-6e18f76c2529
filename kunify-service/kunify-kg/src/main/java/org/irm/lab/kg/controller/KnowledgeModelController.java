package org.irm.lab.kg.controller;

import cn.hutool.core.convert.Convert;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.irm.lab.common.annotation.MyLog;
import org.irm.lab.common.api.R;
import org.irm.lab.common.constant.LogConstant;
import org.irm.lab.common.support.MyPage;
import org.irm.lab.common.utils.Func;
import org.irm.lab.kg.entity.KnowledgeConcept;
import org.irm.lab.kg.entity.KnowledgeModel;
import org.irm.lab.kg.entity.KnowledgeProperty;
import org.irm.lab.kg.feign.KnowledgeModelFeign;
import org.irm.lab.kg.service.IKnowledgeModelService;
import org.irm.lab.kg.vo.KnowledgeModelVO;
import org.irm.lab.kg.vo.echarts.EchartsVO;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;

@Api(value = "知识模型")
@RestController
@RequestMapping("/knowledge-model")
public class KnowledgeModelController implements KnowledgeModelFeign {

    @Resource
    private IKnowledgeModelService knowledgeModelService;

    @ApiOperation(value = "模型分页")
    @GetMapping("/page")
    public R<MyPage<KnowledgeModelVO>> page(@RequestParam(required = false) Map<String, Object> pageMap) {
        // 获取分页条件
        Integer page = Convert.toInt(pageMap.getOrDefault("page", 1));
        Integer size = Convert.toInt(pageMap.getOrDefault("size", 20));
        // 分页查询
        return R.data(knowledgeModelService.page(pageMap, page, size));
    }

    @ApiOperation(value = "列表查询")
    @GetMapping("/list")
    public R<List<KnowledgeModelVO>> list() {
        return R.data(knowledgeModelService.list());
    }

    @ApiOperation(value = "获取可用模型", notes = "已部署的模型", hidden = true)
    @GetMapping("/list-enable-model")
    public R<List<KnowledgeModel>> listEnableModel() {
        return R.data(knowledgeModelService.listEnableModel());
    }

    @ApiOperation(value = "主模型查询", hidden = true)
    @GetMapping("/main-model")
    public R<KnowledgeModelVO> getMainModel() {
        return R.data(knowledgeModelService.getMainModel());
    }

    @ApiOperation(value = "模型详情")
    @GetMapping(value = "/info")
    public R<KnowledgeModel> info(@RequestParam String id) {
        return R.data(knowledgeModelService.info(id));
    }

    @MyLog(menu = LogConstant.MENU_VIEW, dataType = LogConstant.DATA_MODEL, operation = LogConstant.OPERATION_ADD)
    @ApiOperation(value = "模型增改")
    @PostMapping("/save")
    public R<KnowledgeModel> save(@Validated @RequestBody KnowledgeModel knowledgeModel) {
        return R.data(knowledgeModelService.save(knowledgeModel));
    }

    @MyLog(menu = LogConstant.MENU_VIEW, dataType = LogConstant.DATA_MODEL, operation = LogConstant.OPERATION_REMOVE)
    @ApiOperation(value = "模型删除")
    @PostMapping(value = "/remove")
    public R<Void> remove(@RequestBody String ids) {
        knowledgeModelService.remove(Func.objToStrList(ids));
        return R.success();
    }

    @MyLog(menu = LogConstant.MENU_VIEW, dataType = LogConstant.DATA_MODEL, operation = LogConstant.OPERATION_DEPLOY)
    @ApiOperation(value = "模型部署")
    @PostMapping("/deployed")
    public R<Void> deployed(@RequestParam String id) {
        knowledgeModelService.deployed(id);
        return R.success();
    }

    @ApiOperation(value = "查看模型中某一概念的属性", hidden = true)
    @GetMapping("/info-model-concept-property")
    public R<List<KnowledgeProperty>> infoModelConceptProperty(@RequestParam String modelId, @RequestParam String conceptId) {
        return R.data(knowledgeModelService.infoModelConceptProperty(modelId, conceptId));
    }

    @ApiOperation(value = "查看模型中某一关系的属性", hidden = true)
    @GetMapping("/info-model-relation-property")
    public R<List<KnowledgeProperty>> infoModelRelationProperty(@RequestParam String modelId, @RequestParam String relationId) {
        return R.data(knowledgeModelService.infoModelRelationProperty(modelId, relationId));
    }


    @ApiOperation(value = "添加配置的概念", hidden = true)
    @GetMapping("/add-model-concept")
    public R<KnowledgeModel> addModelConcept(@RequestParam String modelId, @RequestParam String conceptId) {
        return R.data(knowledgeModelService.addModelConcept(modelId, conceptId));
    }

    @ApiOperation(value = "删除配置的概念", hidden = true)
    @GetMapping("/remove-model-concept")
    public R<KnowledgeModel> removeModelConcept(@RequestParam String modelId, @RequestParam String conceptId) {
        return R.data(knowledgeModelService.removeModelConcept(modelId, conceptId));
    }

    @ApiOperation(value = "删除配置的关系", hidden = true)
    @GetMapping("/remove-model-relation")
    public R<KnowledgeModel> removeModelRelation(@RequestParam String modelId, @RequestParam String relationId) {
        return R.data(knowledgeModelService.removeModelRelation(modelId, relationId));
    }

    @ApiOperation(value = "删除配置的概念的属性", hidden = true)
    @GetMapping("/remove-model-concept-property")
    public R<KnowledgeModel> removeModelConceptProperty(@RequestParam String modelId, @RequestParam String conceptId, @RequestParam String propertyId) {
        return R.data(knowledgeModelService.removeModelConceptProperty(modelId, conceptId, propertyId));
    }

    @ApiOperation(value = "删除配置的关系的属性", hidden = true)
    @GetMapping("/remove-model-relation-property")
    public R<KnowledgeModel> removeModelRelationProperty(@RequestParam String modelId, @RequestParam String relationId, @RequestParam String propertyId) {
        return R.data(knowledgeModelService.removeModelRelationProperty(modelId, relationId, propertyId));
    }

    @ApiOperation(value = "知识模型图", hidden = true)
    @GetMapping("/info-map")
    public R<EchartsVO> infoMap(@RequestParam String modelId) {
        return R.data(knowledgeModelService.infoMap(modelId));
    }

    @ApiOperation(value = "可用概念列表", hidden = true)
    @GetMapping("/concept-available-list")
    public R<List<KnowledgeConcept>> availableConceptList(@RequestParam String modelId) {
        return R.data(knowledgeModelService.availableConceptList(modelId));
    }

}
