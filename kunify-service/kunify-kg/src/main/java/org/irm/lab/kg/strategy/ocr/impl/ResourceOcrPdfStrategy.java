package org.irm.lab.kg.strategy.ocr.impl;

import java.io.ByteArrayOutputStream;
import java.io.FileInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Locale;
import java.util.Map;

import org.dromara.streamquery.stream.core.stream.Steam;
import org.irm.lab.common.utils.NetWorkFileUtil;
import org.irm.lab.common.utils.ThreadLocalUtil;
import org.irm.lab.front.dto.AIMiddle.OcrItemDTO;
import org.irm.lab.front.dto.AIMiddle.OcrLocationDTO;
import org.irm.lab.kg.algorithm.DocumentUnit;
import org.irm.lab.kg.algorithm.PDFPreciseProcessor;
import org.irm.lab.kg.rabbitmq.producer.EsSyncProducer;
import org.irm.lab.kg.repository.DocumentImageRepository;
import org.irm.lab.kg.repository.DocumentUnitRepository;
import org.irm.lab.kg.repository.annex.processing.AnnexDocumentImageRepository;
import org.irm.lab.kg.service.IDocumentParsingService;
import org.irm.lab.kg.utils.SplitterUtil;
import org.irm.lab.repository.entity.Resource;
import org.irm.lab.repository.repository.ResourceAnnexRepository;
import org.irm.lab.repository.repository.ResourceRepository;
import org.irm.lab.resource.entity.Attach;
import org.irm.lab.resource.feign.AttachFeign;
import org.irm.lab.resource.feign.IOssEndPoint;
import org.irm.lab.resource.repository.AttachRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.aspose.pdf.TextAbsorber;
import com.mongodb.client.model.Filters;

import cn.hutool.core.convert.Convert;
import cn.hutool.core.util.ObjectUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR> <br/>
 * @date 2024/5/2 <br/>
 * @Copyright 博客：https://eliauku.gitee.io/  ||  per aspera and astra <br/>
 */
@Service("resourceOcrPdfStrategy")
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
@Slf4j
public class ResourceOcrPdfStrategy extends AbstractFileOcrStrategyImpl{

    private final ResourceRepository resourceRepository;
    private final AttachFeign attachFeign;
    private final IOssEndPoint iOssEndPoint;

    private final ResourceAnnexRepository resourceAnnexRepository;
    private final AttachRepository attachRepository;
    private final EsSyncProducer esSyncProducer;
    private final PDFPreciseProcessor pdfPreciseProcessor;
    private final DocumentUnitRepository documentUnitRepository;


    @Override
    Map<Integer, Map<Integer, String>> getOcrResult(String strategy, Resource message) {

        Map<Integer, Map<Integer, String>> resourceText = new HashMap<>(Map.of());
        final Attach attach = attachRepository.findById(message.getPdfAttachId());

        try (InputStream inputStream = NetWorkFileUtil.urlToInputStream(attach.getLink());) {

            log.info("开始对主文件进行pdf抽取");
            resourceText.put(0, processPDFDocument(inputStream, message));

        } catch (Exception e) {
            throw new RuntimeException(e);
        }

        return resourceText;

    }

    @Override
    void saveOcrResult(String strategy, Resource message, Map<Integer, Map<Integer, String>> ocrResult) {

        List<DocumentUnit> units = new ArrayList<>();
        DocumentUnit documentUnit = new DocumentUnit();
        documentUnit.setResourceId(message.getId());

        StringBuffer stringBuffer = new StringBuffer();
        ocrResult.get(0).forEach((key, value) -> {

            stringBuffer.append(SplitterUtil.removeLineBreak(value));


        });
        documentUnit.setSortInCurrentPage(1);
        documentUnit.setPage(1);
        documentUnit.setType("0");
        documentUnit.setContent(stringBuffer.toString());
        units.add(documentUnit);
        documentUnitRepository.saveAll(units);
        List<DocumentUnit> documentUnitList = documentUnitRepository.findByCondition(Filters.eq("resourceId", message.getId()));
        if (ObjectUtil.isNotEmpty(documentUnitList)) {
            // 合并内容

            esSyncProducer.sendMessage(ThreadLocalUtil.get("user"), message, null, stringBuffer.toString(), "主文件");
            log.info("【主文件】{} 同步到ES中", message.getName());
        }
        resourceRepository.save(message);

    }

    public Map<Integer, String> processPDFDocument(InputStream inputStream, Resource resource) throws Exception {

        final Locale locale = new Locale("zh", "cn");
        Locale.setDefault(locale);
        com.aspose.pdf.Document pdfDoc = new com.aspose.pdf.Document(inputStream); // 使用完全限定名

        Map<Integer, String> pagesText = new HashMap<>();

        for (int pageNum = 1; pageNum <= pdfDoc.getPages().size(); pageNum++) {
            TextAbsorber textAbsorber = new TextAbsorber();
            pdfDoc.getPages().get_Item(pageNum).accept(textAbsorber);
            String pageText = textAbsorber.getText();
            pagesText.put(pageNum, pageText);
            logger.info("当前资源名称为{},解析到当前资源的第{}页", resource.getName(),pageNum);
        }

        pdfDoc.close();
        return pagesText;
    }


}
