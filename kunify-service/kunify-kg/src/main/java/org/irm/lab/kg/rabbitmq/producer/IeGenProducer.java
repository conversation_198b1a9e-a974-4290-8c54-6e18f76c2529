package org.irm.lab.kg.rabbitmq.producer;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.irm.lab.kg.rabbitmq.message.IeGenMessage;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * 将消息发送至ai中台
 *
 * <AUTHOR>
 * @date 2023/1/17 15:08
 */
@Component
public class IeGenProducer {
    @Resource
    private RabbitTemplate rabbitTemplate;

    public void ieGen(IeGenMessage message) {
        ObjectMapper mapper = new ObjectMapper();
        byte[] bytes;
        try {
            bytes = mapper.writeValueAsBytes(message);
        } catch (JsonProcessingException e) {
            throw new RuntimeException(e);
        }
        rabbitTemplate.convertAndSend(IeGenMessage.EXCHANGE, IeGenMessage.ROUTING_KEY, bytes);
    }
}
