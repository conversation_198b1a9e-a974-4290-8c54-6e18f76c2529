package org.irm.lab.kg.service;


import org.irm.lab.repository.entity.Resource;

/**
 * <AUTHOR>
 * @date 2023/3/7 9:49
 * @description 文档解析业务接口
 */
public interface IDocumentParsingService {

    /**
     * 文档解析
     *
     * @param resourceId 资源Id
     */
    void process(String resourceId);

    /**
     * 文档解析结果确认
     *
     * @param resourceId 资源Id
     */
    void confirm(String resourceId);


    /**
     * 重新文档解析
     *
     * @param resourceId 资源Id
     */
    void reParsing(String resourceId);

    /**
     * 使用OCR重新对文档进行解析
     *
     * @param resourceId 资源Id
     */
    void reParsingWithOCR(String resourceId, String... type);

    Resource getResource(String resourceId);

}
