package org.irm.lab.kg.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.codec.Base64Encoder;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.io.FileUtil;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.alibaba.excel.EasyExcel;
import com.mongodb.client.model.Filters;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.pdfbox.pdmodel.PDDocument;
import org.bson.Document;
import org.bson.conversions.Bson;
import org.bson.types.ObjectId;
import org.irm.lab.common.api.R;
import org.irm.lab.common.exception.ServiceException;
import org.irm.lab.common.provider.MinioLinkProvider;
import org.irm.lab.common.support.Condition;
import org.irm.lab.common.support.MyPage;
import org.irm.lab.common.utils.NetWorkFileUtil;
import org.irm.lab.common.utils.ValidPathUtil;
import org.irm.lab.kg.algorithm.DocumentImage;
import org.irm.lab.kg.algorithm.DocumentUnit;
import org.irm.lab.kg.algorithm.PDFPreciseProcessor;
import org.irm.lab.kg.algorithm.TableHeader;
import org.irm.lab.kg.dto.CorpusGenerateDTO;
import org.irm.lab.kg.dto.FileCorpusDTO;
import org.irm.lab.kg.repository.DocumentImageRepository;
import org.irm.lab.kg.repository.DocumentUnitRepository;
import org.irm.lab.kg.service.IDocumentUnitService;
import org.irm.lab.kg.vo.DocumentUnitVO;
import org.irm.lab.kg.vo.UnitMessageVO;
import org.irm.lab.repository.entity.Resource;
import org.irm.lab.repository.entity.ResourceAnnex;
import org.irm.lab.repository.feign.PeripheralInterfaceFeign;
import org.irm.lab.repository.feign.ResourceAnnexFeign;
import org.irm.lab.repository.feign.ResourceFeign;
import org.irm.lab.repository.vo.MetadataVO;
import org.irm.lab.resource.feign.AttachFeign;
import org.jetbrains.annotations.Nullable;
import org.springframework.core.io.InputStreamResource;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.util.StreamUtils;

import javax.imageio.ImageIO;
import javax.servlet.http.HttpServletRequest;
import java.awt.*;
import java.awt.image.BufferedImage;
import java.io.*;
import java.nio.file.Files;
import java.util.List;
import java.util.*;
import java.util.stream.Collectors;
import java.util.zip.ZipEntry;
import java.util.zip.ZipOutputStream;

@Slf4j
@Service
@RequiredArgsConstructor
public class DocumentUnitServiceImpl implements IDocumentUnitService {

    private final DocumentUnitRepository documentUnitRepository;
    private final DocumentImageRepository documentImageRepository;
    private final AttachFeign attachFeign;
    private final ResourceFeign resourceFeign;
    private final MinioLinkProvider minioLinkProvider;
    private final PDFPreciseProcessor pdfPreciseProcessor;
    private final PeripheralInterfaceFeign peripheralInterfaceFeign;
    private final HttpServletRequest request;
    private final ResourceAnnexFeign resourceAnnexFeign;

    /**
     * 语料单元分页
     *
     * @return 根据页码获取符合当前type的DocumentUnit
     */
    @Override
    public MyPage<DocumentUnitVO> page(Map<String, Object> map) {
        // 获取分页条件
        Integer page = Convert.toInt(map.getOrDefault("page", 1));
        //当页所有语料单元
        List<DocumentUnitVO> documentUnitVOS = currentPageList(map);
        Bson bson = Filters.eq("resourceId", map.get("resourceId"));
        List<DocumentUnit> collect = documentUnitRepository.findByCondition(bson).stream().sorted(Comparator.comparing(DocumentUnit::getPage).reversed()).collect(Collectors.toList());
        Integer maxPage = 1;
        if (ObjectUtil.isNotEmpty(collect))
            maxPage = collect.get(0).getPage();
        return new MyPage<>(page, documentUnitVOS.size(), maxPage, documentUnitVOS);
    }

    /**
     * 语料单元分页
     *
     * @return 根据页码获取符合当前type的DocumentUnit
     */
    public List<DocumentUnitVO> currentPageList(Map<String, Object> map) {
        // 获取分页条件
        Integer page = Convert.toInt(map.getOrDefault("page", 1));
        Bson bson = Filters.and(Filters.eq("page", page), Filters.eq("resourceId", map.get("resourceId")));
        List<DocumentUnit> unitList = documentUnitRepository.findByConditionAndSorted(bson, Filters.eq("sortInCurrentPage", 1));
        int label = 0;
        List<DocumentUnitVO> unitVOS = new ArrayList<>();
        for (DocumentUnit unit : unitList) {
            if (NumberUtil.isNumber(unit.getType()) && !"0".equals(unit.getType()))
                label = Integer.parseInt(unit.getType());
            //重复的实体不显示
            HashSet<String> distinct = new HashSet<>();
            unit.setDocumentTextLights(unit.getDocumentTextLights().stream()
                    .filter(textLight -> distinct.add(textLight.getLabelName() + "-" + textLight.getText()))
                    .collect(Collectors.toList()));
            DocumentUnitVO documentUnitVO = BeanUtil.copyProperties(unit, DocumentUnitVO.class);
            documentUnitVO.setLabel(label);
            unitVOS.add(documentUnitVO);
        }
        return unitVOS;
    }

    /**
     * 语料单元添加/编辑
     *
     * @param documentUnit 语料单元
     * @return 语料单元
     */
    @Override
    public DocumentUnit save(DocumentUnit documentUnit) {
        if (ObjectUtil.isNotEmpty(documentUnit.getAttachId())) {
            String link = attachFeign.info(documentUnit.getAttachId()).getData().getLink();
            documentUnit.setAttachName(link);
        }
        //需要修改sort的units
        List<DocumentUnit> documentUnits = new ArrayList<>();
        //查询当页所有unit
        List<DocumentUnitVO> unitList = currentPageList(Map.of("page", documentUnit.getPage(), "resourceId", documentUnit.getResourceId()));
        //当前unit的sort
        Integer newSort = documentUnit.getSortInCurrentPage();
        if (ObjectUtil.isNotEmpty(documentUnit.getId())) {//修改
            //查询原数据
            DocumentUnit oldUnit = info(documentUnit.getId());
            //判断当前排序是否改变
            Integer oldSort = oldUnit.getSortInCurrentPage();
            if (oldSort > newSort) {
                List<DocumentUnitVO> updateUnit = unitList.stream().filter(unit -> unit.getSortInCurrentPage() >= newSort && unit.getSortInCurrentPage() < oldSort).collect(Collectors.toList());
                updateUnit.forEach(unit -> {
                    Integer sortInCurrentPage = unit.getSortInCurrentPage();
                    unit.setSortInCurrentPage(++sortInCurrentPage);
                    documentUnits.add(unit);
                });
            } else {
                List<DocumentUnitVO> updateUnit = unitList.stream().filter(unit -> unit.getSortInCurrentPage() >= oldSort && unit.getSortInCurrentPage() <= newSort).collect(Collectors.toList());
                updateUnit.forEach(unit -> {
                    Integer sortInCurrentPage = unit.getSortInCurrentPage();
                    unit.setSortInCurrentPage(--sortInCurrentPage);
                    documentUnits.add(unit);
                });
            }
        } else {//添加
            //sort大于当前添加的unit的units
            List<DocumentUnit> documentUnitList = unitList.stream().filter(unit -> unit.getSortInCurrentPage() >= newSort).collect(Collectors.toList());
            if (ObjectUtil.isNotEmpty(documentUnitList)) {
                //中间插入，需修改后面的unit
                documentUnitList.forEach(unit -> {
                    Integer sortInCurrentPage = unit.getSortInCurrentPage();
                    unit.setSortInCurrentPage(++sortInCurrentPage);
                    documentUnits.add(unit);
                });
            } else {
                Integer maxSort;
                //查出最大sort
                List<DocumentUnitVO> collect = unitList.stream().sorted(Comparator.comparing(DocumentUnit::getSortInCurrentPage).reversed())
                        .collect(Collectors.toList());
                if (collect.size() > 0) {
                    maxSort = collect.get(0).getSortInCurrentPage();
                } else {
                    maxSort = 0;
                }
                documentUnit.setSortInCurrentPage(maxSort + 1);
            }
        }
        if (ObjectUtil.isNotEmpty(documentUnits))
            documentUnitRepository.saveAll(documentUnits);
        return documentUnitRepository.findById(documentUnitRepository.save(documentUnit));
    }

    /**
     * 语料单元删除
     *
     * @param ids 需删除的identifier集合
     */
    @Override
    public void removeAndUpdateSortInCurrentPage(List<String> ids) {
        for (String id : ids) {
            DocumentUnit documentUnit = info(id);
            //根据当前需要删除的语料单元的页码和对应资源id找到当前页所有的语料单元
            List<DocumentUnitVO> allUnitList = currentPageList(Map.of("page", documentUnit.getPage(), "resourceId", documentUnit.getResourceId()));
            //拿到当前页页码大于当前删除的语调单元的页码的所有语料单元
            List<DocumentUnitVO> unitVOList = allUnitList.stream().filter(unit -> unit.getSortInCurrentPage() > documentUnit.getSortInCurrentPage())
                    .collect(Collectors.toList());
            List<DocumentUnit> documentUnits = new ArrayList<>();
            unitVOList.forEach(unitVO -> {
                DocumentUnit documentUnit1 = BeanUtil.copyProperties(unitVO, DocumentUnit.class);
                documentUnit1.setSortInCurrentPage(documentUnit1.getSortInCurrentPage() - 1);
                documentUnits.add(documentUnit1);
            });
            documentUnitRepository.saveAll(documentUnits);
            documentUnitRepository.deleteByIdFake(id);
        }

    }

    /**
     * 普通删除
     *
     * @param ids id列表
     */
    public void remove(List<String> ids) {
        for (String id : ids) {
            documentUnitRepository.deleteByIdFake(id);
        }
    }

    public String getPagePicture(String resourceId, Integer page) {
        Bson filter = Filters.and(Filters.eq("page", page), Filters.eq("resourceId", resourceId));
        DocumentImage documentImage = documentImageRepository.findByCondition(filter).get(0);
        return minioLinkProvider.getMinioLink(documentImage.getAttachName());
    }

    @Override
    public DocumentUnit saveKeys(String unitId, TableHeader tableHeader) {

        DocumentUnit unit = info(unitId);
        List<TableHeader> collect = unit.getTableHeader().stream().sorted(Comparator.comparing(TableHeader::getSort).reversed())
                .collect(Collectors.toList());
        if (ObjectUtil.isEmpty(tableHeader.getIdentifier())) {
            tableHeader.setIdentifier(IdUtil.simpleUUID());
            if (ObjectUtil.isEmpty(collect)) {
                tableHeader.setSort(collect.get(0).getSort() + 1);
                unit.setTableHeader(List.of(tableHeader));
            } else {
                tableHeader.setSort(0);
                unit.getTableHeader().add(tableHeader);
            }
            documentUnitRepository.save(unit);
        } else {
            Document query = new Document("_id", new ObjectId(unitId));
            query.put("tableHeader.identifier", tableHeader.getIdentifier());
            Document update = new Document("tableHeader.$.name", tableHeader.getName());

            update.put("tableHeader.$.sort", tableHeader.getSort());
            documentUnitRepository.updateOne(query, new Document("$set", update));
        }
        return documentUnitRepository.findById(unitId);
    }

    /**
     * 表格行信息修改
     *
     * @param unitId 语料单元id
     * @param map    操作信息
     * @return 表格信息
     */
    @Override
    public DocumentUnit saveRow(String unitId, String map) {
        DocumentUnit unit = info(unitId);
        String tableData = unit.getTableData();
        //将表格信息转换成JsonArray
        JSONArray tableDataArray = JSONUtil.parseArray(tableData);
        //将信息转换成Object
        JSONObject entries = JSONUtil.parseObj(map);
        if (ObjectUtil.isNotEmpty(tableDataArray)) {
            Integer index = Convert.toInt(entries.get("index"));
            tableDataArray.put(index, entries);
        } else {
            tableDataArray.add(entries);
        }
        unit.setTableData(tableDataArray.toString());
        return documentUnitRepository.findById(documentUnitRepository.save(unit));
    }

    /**
     * 删除表格行信息
     *
     * @param unitId 语料单元id
     * @param index  行索引
     */
    @Override
    public void removeRow(String unitId, String index) {
        DocumentUnit unit = info(unitId);
        String tableData = unit.getTableData();
        //将表格信息转换成JsonArray
        JSONArray tableDataArray = JSONUtil.parseArray(tableData);
        tableDataArray.remove(Integer.parseInt(index));
        unit.setTableData(tableDataArray.toString());
        documentUnitRepository.save(unit);
    }

    /**
     * 表头删除
     *
     * @param unitId  语料单元id
     * @param tableId 表头id
     */
    public void removeKeys(String unitId, String tableId) {
        DocumentUnit unit = info(unitId);
        List<TableHeader> tableHeaders = unit.getTableHeader();
        List<TableHeader> collect = tableHeaders.stream().filter(s ->
                !s.getIdentifier().equals(tableId)
        ).collect(Collectors.toList());
        unit.setTableHeader(collect);
        documentUnitRepository.save(unit);
    }

    /**
     * 图片预览
     *
     * @param id 当前语料单元id
     * @return 标注后的图片
     */
    @Override
    public String preview(String id) {
        DocumentUnit unit = info(id);
//        Bson filter = Filters.and(Filters.eq("page", unit.getPage()), Filters.eq("resourceId", unit.getResourceId()));
//        DocumentImage documentImage = documentImageRepository.findByCondition(filter).get(0);
//        FileOutputStream fileOutputStream = new FileOutputStream("C:\\Users\\<USER>\\Desktop\\安大\\6.jpg");

        InputStream ins = NetWorkFileUtil.urlToInputStream(getPagePicture(unit.getResourceId(), unit.getPage()));
        //图片操作
        BufferedImage image;
        try {
            image = ImageIO.read(ins);
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
        Graphics graphics = image.getGraphics();
        graphics.setColor(Color.red);
        if (unit.getLlx() != null && unit.getLly() != null && unit.getWidth() != null && unit.getHeight() != null)
            graphics.drawRect((int) Math.floor(unit.getLlx()), (int) Math.floor(unit.getLly()), (int) Math.ceil(unit.getWidth()), (int) Math.ceil(unit.getHeight()));
        ByteArrayOutputStream os = new ByteArrayOutputStream();
        try {
            ImageIO.write(image, "jpg", os);
        } catch (IOException e) {
            throw new RuntimeException(e);
        }

        return "data:image/jpeg;base64," + Base64Encoder.encode(os.toByteArray());
    }

    public DocumentUnit info(String id) {
        return documentUnitRepository.findById(id);
    }

    @Override
    public List<DocumentUnit> findByIds(List<String> ids) {
        List<DocumentUnit> documentUnits = new ArrayList<>();
        ids.forEach(id -> {
            try {
                DocumentUnit documentUnit = documentUnitRepository.findById(id);
                documentUnits.add(documentUnit);
            } catch (Exception e) {
                e.printStackTrace();
            }
        });
        return documentUnits;
    }

    public List<DocumentUnit> infoByResource(String resourceId) {
        return documentUnitRepository.findByCondition(new Document("resourceId", resourceId));
    }

    @Override
    public UnitMessageVO getResourceMessage(String id) {
        UnitMessageVO unitMessageVO = new UnitMessageVO();
        DocumentUnit unit = info(id);
        org.irm.lab.repository.entity.Resource resource = resourceFeign.info(unit.getResourceId()).getData();
        unitMessageVO.setUnitId(id);
        unitMessageVO.setResourceId(resource.getId());
        unitMessageVO.setWorkTaskId(resource.getWorkTaskId());
        unitMessageVO.setPage(unit.getPage());
        return unitMessageVO;
    }

    /**
     * 模糊查询
     *
     * @param map 查询条件
     * @return DocumentUnitList
     */
    public List<DocumentUnit> getFilter(Map<String, Object> map) {
        Bson bson = Condition.getFilter(map, DocumentUnit.class);
        return documentUnitRepository.findByCondition(bson);
    }


    /**
     * 语料生成
     */
    @Override
    public ResponseEntity<InputStreamResource> corpusGenerate(CorpusGenerateDTO corpusGenerateDTO) {
        List<FileCorpusDTO> fileCorpus = corpusGenerateDTO.getFileCorpus();
        // 如果导出多个文件，把所有文件生成临时文件，并全部压缩后返回
        List<File> tempFiles = new ArrayList<>();
        // 创建目录，存放临时文件
        File tempFileParent = new File(System.getProperty("java.io.tmpdir"), "corpus_temp_file");
        // 校验文件是否在白名单路径中
        if(!ValidPathUtil.isValidPath(tempFileParent.getAbsolutePath())) throw new ServiceException("非法文件夹路径异常");

        // 如果已经存在，则清空原本的文件
        if (tempFileParent.exists()) {
            FileUtil.clean(tempFileParent);
        }
        // 创建该文件夹
        tempFileParent.mkdirs();
        // 生成语料
        for (FileCorpusDTO corpus : fileCorpus) {
            String fileId = corpus.getFileId();
            String fileName = corpus.getFileName();
            try {
                // 通过文件Id获取该文件的网络路径
                R<String> urlR = peripheralInterfaceFeign.downloadNoWaterMark(fileId);
                String url = urlR.getData();
                if (!urlR.isSuccess() || url == null) {
                    throw new ServiceException("文件下载地址获取失败，请检查该文件完整性！");
                }
//                String url = "http://*************:9000/kunify-hn-028641/upload/2023-07-18/e32ab319-941f-45db-adfc-d23931f39f58.pdf";
                // 通过文件的网络路径，获取该图片的输入流
                InputStream inputStream = NetWorkFileUtil.urlToInputStream(url);
                // 生成临时文件
                File tempFile = File.createTempFile(fileName, ".tmp", tempFileParent);
                long maxFileSize = 1024 * 1024 * 100; // 例如，限制文件大小为100MB
                long writtenBytes = 0;
                try (OutputStream outputStream = new FileOutputStream(tempFile)) {
                    byte[] buffer = new byte[1024];
                    int bytesRead;
                    while ((bytesRead = inputStream.read(buffer)) != -1) {
                        if (writtenBytes + bytesRead > maxFileSize) {
                            throw new IOException("File size exceeds the maximum limit");
                        }
                        outputStream.write(buffer, 0, bytesRead);
                        writtenBytes += bytesRead;
                    }
                } finally {
                    inputStream.close();
                }
                // 调用 pdfPreciseProcessor.generateUnitTxt() 传入 FileInputStream
                FileInputStream fileInputStream = new FileInputStream(tempFile);
                List<String> strings = pdfPreciseProcessor.generateUnitTxt(fileInputStream);
                // 关闭临时文件的流
                fileInputStream.close();
                if (ObjectUtil.isNotEmpty(strings)) {
                    // 创建临时文件
                    File outputFile = File.createTempFile(fileName, ".txt");
                    // 写入临时文件
                    try (BufferedWriter writer = new BufferedWriter(new FileWriter(outputFile))) {
                        for (String line : strings) {
                            if (!line.contains("普通商密")) {
                                writer.write(line);
                                writer.newLine();
                            }
                        }
                    }
                    // 如果只导出一个文件，就直接返回
                    if (fileCorpus.size() == 1) {
                        // 读取临时文件内容并返回给前端
                        byte[] fileBytes = StreamUtils.copyToByteArray(new FileInputStream(outputFile));
                        // 删除临时文件
                        outputFile.delete();
                        return ResponseEntity.ok()
                                .contentType(MediaType.APPLICATION_OCTET_STREAM)
                                .header(HttpHeaders.CONTENT_DISPOSITION, "attachment; filename=\"" + fileName + ".txt\"")
                                .body(new InputStreamResource(new ByteArrayInputStream(fileBytes)));
                    }
                    // 把文件存储到集合中
                    tempFiles.add(outputFile);
                }
            } catch (Exception e) {
                log.error("【语料生成】操作失败!", e);
            }
        }
        // 准备生成压缩包
        ResponseEntity<InputStreamResource> APPLICATION_OCTET_STREAM = getCorpusZip(tempFiles);
        if (APPLICATION_OCTET_STREAM != null) return APPLICATION_OCTET_STREAM;
        return null;
    }


    /**
     * 资源库语料生成
     */
    @Override
    public ResponseEntity<InputStreamResource> corpusResource(List<String> idList) {
        // 创建目录，存放临时文件
        File tempFileParent = new File(System.getProperty("java.io.tmpdir"), "corpus_temp_file");
        // 校验文件是否在白名单路径中
        if(!ValidPathUtil.isValidPath(tempFileParent.getAbsolutePath())) throw new ServiceException("非法文件夹路径异常");

        // 如果已经存在，则清空原本的文件
        if (tempFileParent.exists()) {
            FileUtil.clean(tempFileParent);
        }
        // 创建该文件夹
        tempFileParent.mkdirs();
        // 如果导出多个文件，把所有文件生成临时文件，并全部压缩后返回
        List<File> tempFiles = new ArrayList<>();
        for (String resourceId : idList) {
            R<Resource> resourceR = resourceFeign.info(resourceId);
            Resource resource = resourceR.getData();
            if (!resourceR.isSuccess() || resource == null) {
                throw new ServiceException("语料导出失败，该资源不存在！");
            }
            // 获取pdf文件地址
            String pdfAttachName = resource.getPdfAttachName();
            if (ObjectUtil.isEmpty(pdfAttachName)) {
                throw new ServiceException("该资源不存在pdf资源，无法生成语料！");
            }
            String fileName = resource.getName();
            ResponseEntity<InputStreamResource> corpusTxt = getCorpusTxt(idList, tempFiles, tempFileParent, pdfAttachName, fileName);
            // 如果不为null则是返回单个语料
            if (corpusTxt != null) return corpusTxt;
        }
        // 返回语料生成的压缩包
        return getCorpusZip(tempFiles);
    }


    /**
     * 工作任务语料生成
     */
    @Override
    @Async
    public void corpusWorkTask(String workTaskId, String fileType) {
        R<List<Resource>> resourceListR = resourceFeign.listByCondition(Map.of("workTaskId", workTaskId));
        List<Resource> resourceList = resourceListR.getData();
        if (!resourceListR.isSuccess() || ObjectUtil.isEmpty(resourceList)) {
            throw new ServiceException("当前任务下不存在资源！");
        }
        // 创建目录，存放临时文件
        File tempFileParent = new File(System.getProperty("java.io.tmpdir"), "0_corpus_temp_file_yuliao_" + workTaskId);
        // 校验文件是否在白名单路径中
        if(!ValidPathUtil.isValidPath(tempFileParent.getAbsolutePath())) throw new ServiceException("非法文件夹路径异常");

        // 如果已经存在，则清空原本的文件
        if (tempFileParent.exists()) {
            FileUtil.clean(tempFileParent);
        }
        tempFileParent.mkdirs();
        String rootDirectory = System.getProperty("java.io.tmpdir");
        String childDirectory = "1_corpus_txt_yuliao" + workTaskId;
        // 创建目录，存放语料
        File tempFileDirectory = new File(rootDirectory, childDirectory);
        // 校验文件是否在白名单路径中
        if(!ValidPathUtil.isValidPath(tempFileParent.getAbsolutePath())) throw new ServiceException("非法文件夹路径异常");

        // 如果已经存在，则清空原本的文件
        if (tempFileDirectory.exists()) {
            FileUtil.clean(tempFileDirectory);
        }
        tempFileDirectory.mkdirs();
        // 创建Map存储已经生成过语料的文件名称
        Map<String, String> fileNameMap = new HashMap<>();
        long startTime = System.currentTimeMillis();
        // 遍历生成语料
        for (Resource resource : resourceList) {
            // 获取主文件的pdfName
            String resourcePdfAttachName = resource.getPdfAttachName();
            // 主文件语料生成
            if (ObjectUtil.isNotEmpty(resourcePdfAttachName)) {
                // 生成主文件
                try {
                    // 获取该资源的网络路径
                    String url = minioLinkProvider.getMinioLinkIntranet(resourcePdfAttachName);
                    // 通过文件的网络路径，获取该图片的输入流
                    InputStream inputStream = NetWorkFileUtil.urlToInputStream(url);
                    // 生成临时文件
                    File tempFile = File.createTempFile(resource.getName(), ".tmp", tempFileParent);
                    long maxFileSize = 1024 * 1024 * 100; // 例如，限制文件大小为100MB
                    long writtenBytes = 0;
                    try (OutputStream outputStream = new FileOutputStream(tempFile)) {
                        byte[] buffer = new byte[1024];
                        int bytesRead;
                        while ((bytesRead = inputStream.read(buffer)) != -1) {
                            if (writtenBytes + bytesRead > maxFileSize) {
                                throw new IOException("File size exceeds the maximum limit");
                            }
                            outputStream.write(buffer, 0, bytesRead);
                            writtenBytes += bytesRead;
                        }
                    } finally {
                        inputStream.close();
                    }
                    // 生成语料段落
                    FileInputStream fileInputStream = new FileInputStream(tempFile);
                    List<String> strings = pdfPreciseProcessor.generateUnitTxt(fileInputStream);
                    // 关闭临时文件的流
                    fileInputStream.close();
                    if (ObjectUtil.isNotEmpty(strings)) {
                        String fileName;
                        try {
                            fileName = resource.getName().split("\\.")[0];
                        } catch (Exception e) {
                            fileName = resource.getName();
                        }
                        if (ObjectUtil.isNotEmpty(fileNameMap.get(fileName))) {
                            // 已存在文件，不重复生成
                            continue;
                        }
                        fileNameMap.put(fileName, fileName);
                        if ("txt".equals(fileType)) {
                            createTxtFile(tempFileDirectory, strings, fileName);
                            log.info("主文件生成语料 ===> 【{}】", fileName);
                        } else if ("excel".equals(fileType)) {
                            createExcelFile(rootDirectory, childDirectory, strings, fileName, resource.getMetadata());
                            log.info("主文件生成语料 ===> 【{}】", fileName);
                        }

                    }
                } catch (Exception e) {
                    log.error("【{}】 语料生成失败!", resource.getName(), e);
                }
            }
            // 获取该资源的附件
            R<List<ResourceAnnex>> annexListR = resourceAnnexFeign.listByResourceId(resource.getId());
            List<ResourceAnnex> annexList = annexListR.getData();
            // 附件语料生成
            if (annexListR.isSuccess() && ObjectUtil.isNotEmpty(annexList)) {
                for (ResourceAnnex resourceAnnex : annexList) {
                    String annexPdfAttachName = resourceAnnex.getPdfAttachName();
                    // 该附件存在pdf资源，生成语料
                    if (ObjectUtil.isNotEmpty(annexPdfAttachName)) {
                        // 生成主文件
                        try {
                            // 获取该资源的网络路径
                            String url = minioLinkProvider.getMinioLinkIntranet(annexPdfAttachName);
                            // 通过文件的网络路径，获取该图片的输入流
                            InputStream inputStream = NetWorkFileUtil.urlToInputStream(url);
                            // 生成临时文件
                            File tempFile = File.createTempFile(resourceAnnex.getName(), ".tmp", tempFileParent);
                            long maxFileSize = 1024 * 1024 * 100; // 例如，限制文件大小为100MB
                            long writtenBytes = 0;
                            try (OutputStream outputStream = new FileOutputStream(tempFile)) {
                                byte[] buffer = new byte[1024];
                                int bytesRead;
                                while ((bytesRead = inputStream.read(buffer)) != -1) {
                                    if (writtenBytes + bytesRead > maxFileSize) {
                                        throw new IOException("File size exceeds the maximum limit");
                                    }
                                    outputStream.write(buffer, 0, bytesRead);
                                    writtenBytes += bytesRead;
                                }
                            } finally {
                                inputStream.close();
                            }
                            // 生成语料段落
                            FileInputStream fileInputStream = new FileInputStream(tempFile);
                            List<String> strings = pdfPreciseProcessor.generateUnitTxt(fileInputStream);
                            // 关闭临时文件的流
                            fileInputStream.close();
                            if (ObjectUtil.isNotEmpty(strings)) {
                                String fileName;
                                try {
                                    fileName = resourceAnnex.getName().split("\\.")[0];
                                } catch (Exception e) {
                                    fileName = resourceAnnex.getName();
                                }
                                if (ObjectUtil.isNotEmpty(fileNameMap.get(fileName))) {
                                    // 已存在文件，不重复生成
                                    continue;
                                }
                                fileNameMap.put(fileName, fileName);
                                if ("txt".equals(fileType)) {
                                    createTxtFile(tempFileDirectory, strings, resourceAnnex.getName());
                                    log.info("附文件生成语料 ===> 【{}】", fileName);
                                } else if ("excel".equals(fileType)) {
                                    createExcelFile(rootDirectory, childDirectory, strings, fileName, resource.getMetadata());
                                    log.info("附文件生成语料 ===> 【{}】", fileName);
                                }
                            }
                        } catch (Exception e) {
                            log.error("【{}】 语料生成失败!", resource.getName(), e);
                        }
                    }

                }
            }
        }
        long endTime = System.currentTimeMillis();
        log.info("本次导出语料用时 ===> {}", endTime - startTime);
    }

    private void createExcelFile(String rootPath, String childPath, List<String> strings, String fileName, List<MetadataVO> metadata) {

        File file = new File(rootPath + childPath);
        if (!file.exists()) {
            file.mkdirs();
        }
        String exportFile = rootPath + childPath + File.separator + fileName + ".xlsx";
        EasyExcel.write(exportFile).head(head(metadata)).sheet(fileName).doWrite(data(metadata, fileName, strings));
    }

    private List<List<String>> data(List<MetadataVO> metadata, String fileName, List<String> content) {
        List<String> fileType = List.of("条例", "规定", "办法", "决议", "决定", "命令", "公报", "公告", "通告", "意见", "通知", "报告", "请示", "批复", "议案", "函", "纪要");
        List<List<String>> list = new ArrayList<List<String>>();
        List<String> value = new ArrayList<String>();
        List<MetadataVO> existValue = metadata.stream().filter(metadataVO ->
                CollUtil.isNotEmpty(metadataVO.getValue())
        ).collect(Collectors.toList());
        for (MetadataVO metadataVO : existValue) {
            String metadataValue = StrUtil.join(",", metadataVO.getValue());
            value.add(metadataValue);
        }
        for (int i = 0; i < fileType.size(); i++) {
            if (fileName.contains(fileType.get(i))) {
                value.add(fileType.get(i));
                break;
            } else if (i == fileType.size() - 1) {
                value.add("无");
            }
        }
        StringBuilder stringBuilder = new StringBuilder();
        int contentSize = 0;
        for (String string : content) {
            contentSize += string.length();
            if ((stringBuilder.length() + string.length()) > 32767) { // EXCEL CELL 单元格最长容纳32767字符
                value.add(stringBuilder.toString());
                stringBuilder.setLength(0);
                stringBuilder.append(string);
            } else {
                stringBuilder.append(string);
            }
        }
        value.add(stringBuilder.toString());
        log.info("文件【{}】 ====> 共有字符【{}】个", fileName, contentSize);
        list.add(value);
        return list;
    }

    private List<List<String>> head(List<MetadataVO> metadata) {
        List<MetadataVO> existValue = metadata.stream().filter(metadataVO ->
                CollUtil.isNotEmpty(metadataVO.getValue())
        ).collect(Collectors.toList());
        List<List<String>> list = new ArrayList<List<String>>();
        for (MetadataVO metadataVO : existValue) {
            List<String> head = new ArrayList<String>();
            head.add(metadataVO.getName());
            list.add(head);
        }

        List<String> head1 = new ArrayList<String>();
        head1.add("文种");
        List<String> head2 = new ArrayList<String>();
        head2.add("内容");
        list.add(head1);
        list.add(head2);
        return list;
    }


    private void createTxtFile(File txtTempFileParent, List<String> strings, String fileName) throws IOException {
        File outputFile = File.createTempFile(fileName, ".txt", txtTempFileParent);
        // 写入临时文件
        try (BufferedWriter writer = new BufferedWriter(new FileWriter(outputFile))) {
            for (String line : strings) {
                if (!line.contains("普通商密")) {
                    writer.write(line);
                    writer.newLine();
                }
            }
        }
    }


    /**
     * 生成指定工作任务下的所有资源的摘要语料
     *
     * @param workTaskId 工作任务ID
     */
    @Override
    @Async
    public void summaryCorpus(String workTaskId) {
        R<List<Resource>> resourceListR = resourceFeign.listByCondition(Map.of("workTaskId", workTaskId));
        List<Resource> resourceList = resourceListR.getData();
        if (!resourceListR.isSuccess() || ObjectUtil.isEmpty(resourceList)) {
            throw new ServiceException("当前任务下不存在资源！");
        }
        // 创建目录，存放txt语料
        File txtTempFileParent = new File(System.getProperty("java.io.tmpdir"), "0_corpus_txt_zhaiyao" + workTaskId);
        // 校验文件是否在白名单路径中
        if(!ValidPathUtil.isValidPath(txtTempFileParent.getAbsolutePath())) throw new ServiceException("非法文件夹路径异常");

        // 如果已经存在，则清空原本的文件
        if (txtTempFileParent.exists()) {
            FileUtil.clean(txtTempFileParent);
        }
        txtTempFileParent.mkdirs();
        // 创建map存储已经创建的文件名称
        Map<String, String> fileNameMap = new HashMap<>();
        long startTime = System.currentTimeMillis();
        // 遍历生成语料
        for (Resource resource : resourceList) {
            // 获取主文件的pdfName
            String resourcePdfAttachName = resource.getPdfAttachName();
            // 主文件语料生成
            if (ObjectUtil.isNotEmpty(resourcePdfAttachName)) {
                // 生成主文件
                try {
                    // 获取该资源的网络路径
                    String url = minioLinkProvider.getMinioLinkIntranet(resourcePdfAttachName);
                    // 通过文件的网络路径，获取该图片的输入流
                    InputStream inputStream = NetWorkFileUtil.urlToInputStream(url);
                    // 生成语料段落
                    String[] strings = pdfPreciseProcessor.extractContent(PDDocument.load(inputStream));
                    inputStream.close();
                    if (ObjectUtil.isNotEmpty(strings)) {
                        String fileName;
                        try {
                            fileName = resource.getName().split("\\.")[0];
                        } catch (Exception e) {
                            fileName = resource.getName();
                        }
                        if (ObjectUtil.isNotEmpty(fileNameMap.get(fileName))) {
                            // 已存在文件，不重复生成
                            continue;
                        }
                        fileNameMap.put(fileName, fileName);
                        File outputFile = File.createTempFile(fileName, ".txt", txtTempFileParent);
                        // 写入临时文件
                        try (BufferedWriter writer = new BufferedWriter(new FileWriter(outputFile))) {
                            for (String line : strings) {
                                writer.write(line);
                            }
                        }
                        log.info("主文件生成摘要 ===> 【{}】", outputFile.getName());
                    }
                } catch (Exception e) {
                    log.error("【{}】 摘要生成失败!", resource.getName(), e);
                }
            }
            // 获取该资源的附件
            R<List<ResourceAnnex>> annexListR = resourceAnnexFeign.listByResourceId(resource.getId());
            List<ResourceAnnex> annexList = annexListR.getData();
            // 附件语料生成
            if (annexListR.isSuccess() && ObjectUtil.isNotEmpty(annexList)) {
                for (ResourceAnnex resourceAnnex : annexList) {
                    String annexPdfAttachName = resourceAnnex.getPdfAttachName();
                    // 该附件存在pdf资源，生成语料
                    if (ObjectUtil.isNotEmpty(annexPdfAttachName)) {
                        // 生成主文件
                        try {
                            // 获取该资源的网络路径
                            String url = minioLinkProvider.getMinioLinkIntranet(annexPdfAttachName);
                            // 通过文件的网络路径，获取该图片的输入流
                            InputStream inputStream = NetWorkFileUtil.urlToInputStream(url);
                            // 生成语料段落
                            String[] strings = pdfPreciseProcessor.extractContent(PDDocument.load(inputStream));
                            inputStream.close();
                            if (ObjectUtil.isNotEmpty(strings)) {
                                String fileName;
                                try {
                                    fileName = resourceAnnex.getName().split("\\.")[0];
                                } catch (Exception e) {
                                    fileName = resourceAnnex.getName();
                                }
                                if (ObjectUtil.isNotEmpty(fileNameMap.get(fileName))) {
                                    // 已存在文件，不重复生成
                                    continue;
                                }
                                fileNameMap.put(fileName, fileName);
                                // 创建临时文件
                                File outputFile = File.createTempFile(resourceAnnex.getName(), ".txt", txtTempFileParent);
                                // 写入临时文件
                                try (BufferedWriter writer = new BufferedWriter(new FileWriter(outputFile))) {
                                    for (String line : strings) {
                                        writer.write(line);
                                    }
                                }
                                log.info("生成摘要 ===> 【{}】", outputFile.getName());
                            }
                        } catch (Exception e) {
                            log.error("【{}】 摘要生成失败!", resource.getName(), e);
                        }
                    }

                }
            }
        }
        long endTime = System.currentTimeMillis();
        log.info("本次导出摘要用时 ===> {}", endTime - startTime);
    }


    /**
     * 语料txt生成
     *
     * @param idList         文件Id集合
     * @param tempFiles      生成测txt文件
     * @param tempFileParent 路径
     * @param pdfAttachName  pdf地址
     * @param fileName       文件名称（资源名称）
     * @return txt语料
     */
    @Nullable
    private ResponseEntity<InputStreamResource> getCorpusTxt(List<String> idList, List<File> tempFiles, File tempFileParent, String pdfAttachName, String fileName) {
        try {
            // 获取该资源的网络路径
            String url = minioLinkProvider.getMinioLinkIntranet(pdfAttachName);
//                String url = "http://*************:9000/kunify-hn-028641/upload/2023-07-18/e32ab319-941f-45db-adfc-d23931f39f58.pdf";
            // 通过文件的网络路径，获取该图片的输入流
            InputStream inputStream = NetWorkFileUtil.urlToInputStream(url);
            // 生成临时文件
            File tempFile = File.createTempFile(fileName, ".tmp", tempFileParent);
            long maxFileSize = 1024 * 1024 * 100; // 例如，限制文件大小为100MB
            long writtenBytes = 0;
            try (OutputStream outputStream = new FileOutputStream(tempFile)) {
                byte[] buffer = new byte[1024];
                int bytesRead;
                while ((bytesRead = inputStream.read(buffer)) != -1) {
                    if (writtenBytes + bytesRead > maxFileSize) {
                        throw new IOException("File size exceeds the maximum limit");
                    }
                    outputStream.write(buffer, 0, bytesRead);
                    writtenBytes += bytesRead;
                }
            } finally {
                inputStream.close();
            }
            // 调用 pdfPreciseProcessor.generateUnitTxt() 传入 FileInputStream
            FileInputStream fileInputStream = new FileInputStream(tempFile);
            List<String> strings = pdfPreciseProcessor.generateUnitTxt(fileInputStream);
            // 关闭临时文件的流
            fileInputStream.close();
            if (ObjectUtil.isNotEmpty(strings)) {
                // 创建临时文件
                File outputFile = File.createTempFile(fileName, ".txt");
                // 写入临时文件
                try (BufferedWriter writer = new BufferedWriter(new FileWriter(outputFile))) {
                    for (String line : strings) {
                        if (!line.contains("普通商密")) {
                            writer.write(line);
                            writer.newLine();
                        }
                    }
                }
                // 如果只导出一个文件，就直接返回
                if (idList.size() == 1) {
                    // 读取临时文件内容并返回给前端
                    byte[] fileBytes = StreamUtils.copyToByteArray(new FileInputStream(outputFile));
                    // 删除临时文件
                    outputFile.delete();
                    return ResponseEntity.ok()
                            .contentType(MediaType.APPLICATION_OCTET_STREAM)
                            .header(HttpHeaders.CONTENT_DISPOSITION, "attachment; filename=\"" + fileName + ".txt\"")
                            .body(new InputStreamResource(new ByteArrayInputStream(fileBytes)));
                }
                // 把文件存储到集合中
                tempFiles.add(outputFile);
            }
        } catch (Exception e) {
            log.error("【语料生成】操作失败!", e);
        }
        // 生成多个语料，这里返回null，多个语料添加到集合中
        return null;
    }


    /**
     * 对生成的语料txt压缩后返货
     *
     * @param tempFiles txt文件集合
     */
    @Nullable
    private static ResponseEntity<InputStreamResource> getCorpusZip(List<File> tempFiles) {
        // 准备生成压缩包
        if (!tempFiles.isEmpty()) {
            File tempFolder = new File(System.getProperty("java.io.tmpdir"), "corpus_temp_file_zip");
            // 校验文件是否在白名单路径中
            if(!ValidPathUtil.isValidPath(tempFolder.getAbsolutePath())) throw new ServiceException("非法文件夹路径异常");
            if (tempFolder.exists()) {
                FileUtil.clean(tempFolder);
            }
            tempFolder.mkdirs();
            // 将所有临时文件移动到临时文件夹
            for (File tempFile : tempFiles) {
                File newTempFile = new File(tempFolder, tempFile.getName());
                tempFile.renameTo(newTempFile);
            }
            // 创建压缩文件
            File zipFile = new File(tempFolder.getParent(), "corpus_export.zip");
            // 生成压缩包
            try (FileOutputStream zipFileStream = new FileOutputStream(zipFile);
                 ZipOutputStream zipOut = new ZipOutputStream(zipFileStream)) {
                // 将临时文件夹中的每个文件添加到压缩文件中
                for (File tempFile : tempFolder.listFiles()) {
                    ZipEntry zipEntry = new ZipEntry(tempFile.getName());
                    zipOut.putNextEntry(zipEntry);
                    try (FileInputStream tempFileStream = new FileInputStream(tempFile)) {
                        byte[] buffer = new byte[1024];
                        int bytesRead;
                        while ((bytesRead = tempFileStream.read(buffer)) != -1) {
                            zipOut.write(buffer, 0, bytesRead);
                        }
                    }
                    zipOut.closeEntry();
                }
            } catch (Exception e) {
                throw new RuntimeException(e);
            } finally {
                // 删除临时文件夹和其内容
                for (File tempFile : tempFiles) {
                    FileUtil.del(tempFile);
                }
                FileUtil.del(tempFolder);
            }
            // 将压缩文件返回给前端
            byte[] zipBytes;
            try {
                // 校验文件是否在白名单路径中
                if(!ValidPathUtil.isValidPath(zipFile.getAbsolutePath())) throw new ServiceException("非法文件夹路径异常");
                zipBytes = Files.readAllBytes(zipFile.toPath());
            } catch (IOException e) {
                throw new RuntimeException(e);
            }
            // 删除压缩包
            zipFile.delete();
            return ResponseEntity.ok()
                    .contentType(MediaType.APPLICATION_OCTET_STREAM)
                    .header(HttpHeaders.CONTENT_DISPOSITION, "attachment; filename=\"corpus_export.zip\"")
                    .body(new InputStreamResource(new ByteArrayInputStream(zipBytes)));
        }
        return null;
    }

}
