package org.irm.lab.kg.service.annex.impl;

import cn.hutool.core.util.ObjectUtil;
import com.mongodb.client.model.Filters;
import lombok.RequiredArgsConstructor;
import org.bson.conversions.Bson;
import org.irm.lab.common.support.Condition;
import org.irm.lab.kg.entity.annex.processing.AnnexCacheLabelData;
import org.irm.lab.kg.repository.annex.AnnexCacheLabelDataRepository;
import org.irm.lab.kg.service.annex.IAnnexCacheLabelDataService;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2023/4/26 18:57
 * @description 附件缓存标签业务层接口
 */
@Service
@RequiredArgsConstructor
public class AnnexCacheLabelDataServiceImpl implements IAnnexCacheLabelDataService {
    private final AnnexCacheLabelDataRepository annexCacheLabelDataRepository;

    /**
     * 新增附件缓存标签
     *
     * @param annexCacheLabelData {@link AnnexCacheLabelData}
     * @return {@link AnnexCacheLabelData}
     */
    @Override
    public AnnexCacheLabelData save(AnnexCacheLabelData annexCacheLabelData) {
        return annexCacheLabelDataRepository.findById(annexCacheLabelDataRepository.save(annexCacheLabelData));
    }

    /**
     * 根据Id获取附件缓存标签
     *
     * @param id 附件缓存标签Id
     * @return {@link AnnexCacheLabelData}
     */
    @Override
    public AnnexCacheLabelData info(String id) {
        return annexCacheLabelDataRepository.findById(id);
    }

    /**
     * 根据附件Id、标签Id、缓存标签内容查询指定附件缓存标签
     *
     * @param annexId 附件ID
     * @param labelId 标签ID
     * @param text    缓存标签内容
     * @return {@link AnnexCacheLabelData}
     */
    @Override
    public AnnexCacheLabelData findAnnexCacheLabelDataByCondition(String annexId, String labelId, String text) {
        // 构建查询条件
        Bson filters = Filters.and(
                Filters.eq("annexId", annexId)
                , Filters.eq("labelId", labelId)
                , Filters.eq("content", text));
        // 查询结果，只能有一个
        List<AnnexCacheLabelData> annexCacheLabelDataList = annexCacheLabelDataRepository.findByCondition(filters);
        if (ObjectUtil.isNotEmpty(annexCacheLabelDataList)) {
            return annexCacheLabelDataList.get(0);
        }
        return null;
    }

    /**
     * 根据附件Id、标签类型 删除标签数据缓存
     *
     * @param annexId 附件Id
     * @param type    标签类型
     */
    @Override
    public void removeByResourceIdAndType(String annexId, String type) {
        List<String> idList = annexCacheLabelDataRepository.findByCondition(
                        Filters.and(Filters.eq("annexId", annexId), Filters.eq("type", type)))
                .stream().map(AnnexCacheLabelData::getId).collect(Collectors.toList());
        annexCacheLabelDataRepository.deleteByIdFake(idList);
    }

    /**
     * 条件查询附件缓存标签
     *
     * @param filterMap 条件
     * @return {@link AnnexCacheLabelData}
     */
    @Override
    public List<AnnexCacheLabelData> findByCondition(Map<String, Object> filterMap) {
        Bson filter = Condition.getFilter(filterMap, AnnexCacheLabelData.class);
        return annexCacheLabelDataRepository.findByCondition(filter);
    }

    /**
     * 根据附件Id获取该附件的顶级缓存标签
     *
     * @param annexId 附件Id
     * @return 顶级top缓存标签
     */
    public AnnexCacheLabelData findTopLabel(String annexId) {
        Bson top = Filters.and(Filters.eq("topLabel", true), Filters.eq("annexId", annexId));
        List<AnnexCacheLabelData> byCondition = annexCacheLabelDataRepository.findByCondition(top);
        return ObjectUtil.isNotEmpty(byCondition) ? byCondition.get(0) : null;
    }
}
