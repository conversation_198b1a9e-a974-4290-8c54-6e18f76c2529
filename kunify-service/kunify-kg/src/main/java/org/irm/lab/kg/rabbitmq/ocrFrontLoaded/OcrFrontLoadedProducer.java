package org.irm.lab.kg.rabbitmq.ocrFrontLoaded;

import org.irm.lab.kg.rabbitmq.MessageHelper;
import org.irm.lab.kg.rabbitmq.ieInfer.IeInferMessage;
import org.springframework.amqp.rabbit.connection.CorrelationData;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import cn.hutool.core.lang.UUID;
import lombok.RequiredArgsConstructor;

/**
 *
 */
@Component
@RequiredArgsConstructor
public class OcrFrontLoadedProducer {

    private final RabbitTemplate rabbitTemplate;

    @Value("${spring.application.name}")
    private String APP_KEY;

    public void send(String resourceId, String type, String user) {
        final OcrFrontLoadedMessage ocrFrontLoadedMessage = new OcrFrontLoadedMessage();
        ocrFrontLoadedMessage.setResourceId(resourceId);
        ocrFrontLoadedMessage.setType(type);
        ocrFrontLoadedMessage.setUser(user);
        rabbitTemplate.convertAndSend(
                OcrFrontLoadedMessage.EXCHANGE ,
                OcrFrontLoadedMessage.ROUTING_KEY + APP_KEY ,
                ocrFrontLoadedMessage
        );
    }

}
