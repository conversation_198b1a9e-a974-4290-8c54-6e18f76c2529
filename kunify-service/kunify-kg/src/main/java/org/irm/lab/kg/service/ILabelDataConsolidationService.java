package org.irm.lab.kg.service;

import org.irm.lab.repository.entity.Resource;

import java.util.concurrent.CompletableFuture;

/**
 * <AUTHOR>
 * @date 2023/3/9 17:37
 * @description 数据合并业务接口
 */
public interface ILabelDataConsolidationService {

    /**
     * 知识匹配 ===> 缓存标签数据合并
     *
     * @param resource 资源对象
     */
    CompletableFuture<Void> consolidation(Resource resource);

    /**
     * 重新匹配
     *
     * @param resourceId 资源Id
     */
    void reConsolidation(String resourceId);
}
