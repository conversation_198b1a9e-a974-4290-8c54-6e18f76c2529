package org.irm.lab.kg.service;

import org.irm.lab.kg.entity.processing.CacheLabelData;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2023/3/1 15:03
 * @description 标签数据缓存业务接口
 */
public interface ICacheLabelDataService {
    /**
     * 根据资源Id、标签Id、高亮词内容（标签数据内容）查找指定标签数据
     *
     * @param resourceId 资源Id
     * @param labelId    标签Id
     * @param text       高亮词内容
     * @return {@link CacheLabelData}
     */
    CacheLabelData findCacheLabelDataListByCondition(String resourceId, String labelId, String text);

    /**
     * 新增一个标签数据缓存
     *
     * @param cacheLabelData 标签数据缓存对象
     * @return {@link CacheLabelData}
     */
    CacheLabelData save(CacheLabelData cacheLabelData);

    /**
     * 根据资源Id删除标签数据缓存
     *
     * @param resourceId 资源Id
     * @param type       标签类型
     */
    void removeByResourceIdAndType(String resourceId, String type);

    /**
     * 根据Id查询
     *
     * @param cacheLabelDataId id
     * @return {@link CacheLabelData}
     */
    CacheLabelData info(String cacheLabelDataId);

    /**
     * 根据资源Id获取该资源的顶级缓存标签
     *
     * @param resourceId 资源Id
     * @return 顶级top缓存标签
     */
    CacheLabelData findTopLabel(String resourceId);


    /**
     * 条件查询缓存标签数据
     *
     * @param filterMap 条件Map
     * @return {@link CacheLabelData}
     */
    List<CacheLabelData> findByCondition(Map<String, Object> filterMap);
}
