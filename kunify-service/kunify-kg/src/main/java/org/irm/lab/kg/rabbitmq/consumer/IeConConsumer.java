package org.irm.lab.kg.rabbitmq.consumer;


import cn.hutool.core.util.ObjectUtil;
import cn.hutool.json.JSONUtil;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.rabbitmq.client.Channel;
import lombok.RequiredArgsConstructor;
import lombok.extern.log4j.Log4j2;
import org.irm.lab.common.api.R;
import org.irm.lab.common.constant.AuthConstant;
import org.irm.lab.common.utils.RedisUtil;
import org.irm.lab.common.utils.ThreadLocalUtil;
import org.irm.lab.kg.kgprocess.strategy.ProcessingStrategySelector;
import org.irm.lab.kg.rabbitmq.message.IeConMessage;
import org.irm.lab.kg.rabbitmq.message.IeGenMessage;
import org.irm.lab.kg.service.impl.kgprocess.tag.KnowledgeTagAlgorithm;
import org.irm.lab.kg.vo.ie.IeInferItem;
import org.irm.lab.kg.vo.ie.IeInferRelation;
import org.irm.lab.kg.vo.ie.IeInferResultVO;
import org.irm.lab.repository.constant.KnowledgeTagStatus;
import org.irm.lab.repository.constant.RedisConstant;
import org.irm.lab.repository.entity.Resource;
import org.irm.lab.repository.entity.ResourceAnnex;
import org.irm.lab.repository.feign.ResourceAnnexFeign;
import org.irm.lab.repository.feign.ResourceFeign;
import org.springframework.amqp.core.Message;
import org.springframework.amqp.rabbit.annotation.*;
import org.springframework.stereotype.Component;

import java.io.IOException;
import java.util.List;
import java.util.Map;

/**
 * 接收来自AI中台的消息
 *
 * <AUTHOR>
 * @date 2023/1/17 15:14
 */
@Log4j2
@Component
@RequiredArgsConstructor
public class IeConConsumer {


    @javax.annotation.Resource
    private org.springframework.context.ApplicationContext applicationContext;
    private final RedisUtil redisUtil;
    private final ResourceAnnexFeign resourceAnnexFeign;
    private final ResourceFeign resourceFeign;
    private final ProcessingStrategySelector processingStrategySelector;

    @RabbitListener(bindings = @QueueBinding(
            value = @Queue(IeGenMessage.QUEUE),
            exchange = @Exchange(name = IeGenMessage.EXCHANGE),
            key = IeGenMessage.ROUTING_KEY
    ))
    @RabbitHandler
    public void onMessage(byte[] message, Channel channel, Message messageStatus) throws IOException {
        long deliveryTag = messageStatus.getMessageProperties().getDeliveryTag();
        log.info("[onMessage][线程编号:{}]", Thread.currentThread().getId());
        ObjectMapper mapper = new ObjectMapper();
        IeConMessage ieConMessage = mapper.readValue(message, IeConMessage.class);
        ThreadLocalUtil.set("user", JSONUtil.createObj()
                .putOpt(AuthConstant.TENANT_ID, ieConMessage.getTenantId())
                .putOpt(AuthConstant.USER_ID, ieConMessage.getUserId()).toString());
        Map<String, IeInferResultVO> result = ieConMessage.getResult();

        // 主文件标注
        String resourceId = ieConMessage.getResourceId();
        if (ObjectUtil.isNotEmpty(resourceId)) {
            // 识别失败
            try {
                if (!ieConMessage.getStatus()) {
                    applicationContext.getBean("resourceKnowledgeTag", KnowledgeTagAlgorithm.class).recognitionStatus(resourceId, KnowledgeTagStatus.TAG_FAILED);
                    log.error("主文件算法识别失败!");
                    return;
                }
                // 处理消息
                for (Map.Entry<String, IeInferResultVO> entry : result.entrySet()) {
                    String unitId = entry.getKey();
                    List<IeInferItem> entityList = entry.getValue().getResultsPlus();
                    List<IeInferRelation> relationList = entry.getValue().getRelationPlus();
                    applicationContext.getBean("resourceKnowledgeTag", KnowledgeTagAlgorithm.class).algorithmRecognition(unitId, entityList, relationList);
                }
                log.info("<<<<<<<<<<主文件知识标注算法识别完毕<<<<<<<<<<");
                // 主文件算法识别完毕，修改资源状态 “识别成功”
                applicationContext.getBean("resourceKnowledgeTag", KnowledgeTagAlgorithm.class).recognitionStatus(resourceId, KnowledgeTagStatus.TAG);
                // 判断该文件是否存在附件
                R<List<ResourceAnnex>> resourceAnnexListR = resourceAnnexFeign.listByResourceId(resourceId);
                List<ResourceAnnex> resourceAnnexList = resourceAnnexListR.getData();
                Resource resource = resourceFeign.info(resourceId).getData();
                if (!resourceAnnexListR.isSuccess() || ObjectUtil.isEmpty(resourceAnnexList)) {
                    // 主文件识别完成后，判断是否继续向下执行
                    Object value = redisUtil.get(RedisConstant.AUTO_PASS_RESOURCE + resource.getId());
                    if (value != null) {
                        resource.setKnowledgeTagConfirm(true);
                        resource = resourceFeign.save(resource).getData();
                        processingStrategySelector.process(resource);
                    }
                }
                ThreadLocalUtil.remove();
            } catch (Exception e) {
                if (messageStatus.getMessageProperties().getRedelivered()) {
                    // 算法识别完毕，修改资源状态 “识别失败”
                    applicationContext.getBean("resourceKnowledgeTag", KnowledgeTagAlgorithm.class).recognitionStatus(resourceId, KnowledgeTagStatus.TAG_FAILED);
                    // 打印错误信息
                    log.error(e.getMessage());
                    log.error("消息已重复处理失败，拒绝再次接收.....");
                    channel.basicReject(deliveryTag, false);
                } else {
                    log.info("消息即将再次返回队列中进行处理....");
                    channel.basicNack(deliveryTag, false, true);
                }
            }
        }

        // 附件标注
        String annexId = ieConMessage.getAnnexId();
        if (ObjectUtil.isNotEmpty(annexId)) {
            // 识别失败
            if (!ieConMessage.getStatus()) {
                applicationContext.getBean("annexKnowledgeTag", KnowledgeTagAlgorithm.class).recognitionStatus(annexId, KnowledgeTagStatus.TAG_FAILED);
                log.error("附件算法识别失败!");
                return;
            }
            try {
                // 处理消息
                for (Map.Entry<String, IeInferResultVO> entry : result.entrySet()) {
                    String unitId = entry.getKey();
                    List<IeInferItem> entityList = entry.getValue().getResultsPlus();
                    List<IeInferRelation> relationList = entry.getValue().getRelationPlus();
                    applicationContext.getBean("annexKnowledgeTag", KnowledgeTagAlgorithm.class).algorithmRecognition(unitId, entityList, relationList);
                }
                log.info("<<<<<<<<<<附件知识标注算法识别完毕<<<<<<<<<<");
                // 算法识别完毕，修改资源状态 “识别成功”
                applicationContext.getBean("annexKnowledgeTag", KnowledgeTagAlgorithm.class).recognitionStatus(annexId, KnowledgeTagStatus.TAG);
                // 判断是否为最后一个附件，如果是，则判断是否自动执行
                if (ieConMessage.getIsEnd()) {
                    ResourceAnnex resourceAnnex = resourceAnnexFeign.info(annexId).getData();
                    Resource resource = resourceFeign.info(resourceAnnex.getResourceId()).getData();
                    Object value = redisUtil.get(RedisConstant.AUTO_PASS_RESOURCE + resource.getId());
                    if (value != null) {
                        resource.setKnowledgeTagConfirm(true);
                        resource = resourceFeign.save(resource).getData();
                        processingStrategySelector.process(resource);
                    }
                }
                ThreadLocalUtil.remove();
            } catch (Exception e) {
                if (messageStatus.getMessageProperties().getRedelivered()) {
                    // 算法识别完毕，修改资源状态 “识别失败”
                    applicationContext.getBean("annexKnowledgeTag", KnowledgeTagAlgorithm.class).recognitionStatus(annexId, KnowledgeTagStatus.TAG_FAILED);
                    // 打印错误信息
                    log.error(e.getMessage());
                    log.error("消息已重复处理失败，拒绝再次接收.....");
                    channel.basicReject(deliveryTag, false);
                } else {
                    log.info("消息即将再次返回队列中进行处理....");
                    channel.basicNack(deliveryTag, false, true);
                }
            }
        }


    }
}
