package org.irm.lab.kg.service.impl;

import cn.hutool.core.util.ObjectUtil;
import com.mongodb.client.model.Filters;
import lombok.RequiredArgsConstructor;
import org.irm.lab.common.entity.AbstractBaseEntity;
import org.irm.lab.kg.entity.processing.CacheTripleLabelData;
import org.irm.lab.kg.repository.processing.CacheTripleLabelDataRepository;
import org.irm.lab.kg.service.ICacheTripleLabelDataService;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2023/3/1 15:08
 * @description 标签关系缓存业务实现类
 */
@Service
@RequiredArgsConstructor
public class CacheTripleLabelDataServiceImpl implements ICacheTripleLabelDataService {

    private final CacheTripleLabelDataRepository cacheTripleLabelDataRepository;

    /**
     * 新增缓存标签关系
     *
     * @param cacheTripleLabelData {@link CacheTripleLabelData}
     * @return {@link CacheTripleLabelData}
     */
    @Override
    public CacheTripleLabelData save(CacheTripleLabelData cacheTripleLabelData) {
        return cacheTripleLabelDataRepository.findById(cacheTripleLabelDataRepository.save(cacheTripleLabelData)) ;
    }

    /**
     * 批量新增标签数据关系缓存
     *
     * @param cacheTripleLabelDataList {@link CacheTripleLabelData}
     */
    @Override
    public void saveAll(List<CacheTripleLabelData> cacheTripleLabelDataList) {
        cacheTripleLabelDataRepository.saveAll(cacheTripleLabelDataList);
    }


    /**
     * 根据标签数据缓存Id查询该数据的所有关系
     *
     * @param cacheLabelDataId 标签数据缓存I
     * @return {@link CacheTripleLabelData}
     */
    @Override
    public List<CacheTripleLabelData> listByCacheLabelDataId(String cacheLabelDataId) {
        return cacheTripleLabelDataRepository.findByCondition(Filters.eq("startLabelDataId", cacheLabelDataId));
    }

    /**
     * 删除标签数据缓存的所有关系（包含正向和逆向）
     *
     * @param cacheLabelDataId 标签数据缓存Id
     */
    @Override
    public void removeRelation(String cacheLabelDataId) {
        // 删除原本的正向关系
        List<String> idList = cacheTripleLabelDataRepository.findByCondition(Filters.eq("startLabelDataId", cacheLabelDataId)).stream().map(AbstractBaseEntity::getId).collect(Collectors.toList());
        if (ObjectUtil.isNotEmpty(idList) && !idList.isEmpty()) {
            cacheTripleLabelDataRepository.deleteByIdFake(idList);
        }

    }


    /**
     * 删除指定关系三元组
     *
     * @param id 关系三元组Id
     */
    @Override
    public void remove(String id) {
        cacheTripleLabelDataRepository.deleteByIdFake(id);
    }
}
