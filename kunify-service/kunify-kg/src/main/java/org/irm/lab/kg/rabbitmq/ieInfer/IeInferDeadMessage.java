package org.irm.lab.kg.rabbitmq.ieInfer;

import java.io.Serializable;

import lombok.Data;

@Data
public class IeInferDeadMessage implements Serializable {

    public static final String QUEUE = "QUEUE_IE_INFER";

    public static final String EXCHANGE = "EXCHANGE_IE_INFER";

    public static final String ROUTING_KEY = "ROUTING_KEY_IE_INFER";

    private Long appraisalFileId;

    private String tenantId;

    private Boolean flag;

}
