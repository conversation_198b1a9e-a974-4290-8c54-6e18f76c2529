package org.irm.lab.kg.rabbitmq.producer;

import lombok.RequiredArgsConstructor;
import org.irm.lab.kg.rabbitmq.message.DocumentParsingMessage;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @date 2023/3/8 16:23
 * @description 文档解析消费者
 */
@Component
@RequiredArgsConstructor
public class DocumentParsingProducer {
    private final RabbitTemplate rabbitTemplate;

    @Value("${spring.application.name}")
    private String KEY_SELF;

    /**
     * 发送消息
     */
    public void sendMessage(String user,String resourceId){
        DocumentParsingMessage documentParsingMessage = new DocumentParsingMessage();
        documentParsingMessage.setUser(user);
        documentParsingMessage.setResourceId(resourceId);
        // 发送消息
        rabbitTemplate.convertAndSend(DocumentParsingMessage.EXCHANGE,DocumentParsingMessage.ROUTING_KEY+KEY_SELF,documentParsingMessage);
    }
}
