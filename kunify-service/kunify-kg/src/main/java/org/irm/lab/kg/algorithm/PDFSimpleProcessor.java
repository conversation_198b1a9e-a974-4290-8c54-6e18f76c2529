package org.irm.lab.kg.algorithm;

import lombok.RequiredArgsConstructor;
import org.apache.pdfbox.pdmodel.PDDocument;
import org.irm.lab.common.constant.ExceptionMessageConst;
import org.irm.lab.common.enums.AlgorithmType;
import org.irm.lab.common.exception.ServiceException;
import org.irm.lab.common.pojo.BaseStatus;
import org.irm.lab.common.provider.MinioLinkProvider;
import org.irm.lab.common.utils.NetWorkFileUtil;
import org.irm.lab.kg.repository.DocumentSummaryRepository;
import org.irm.lab.kg.repository.DocumentUnitRepository;
import org.irm.lab.repository.entity.Resource;
import org.irm.lab.repository.feign.ResourceFeign;
import org.springframework.stereotype.Service;

import java.io.*;
import java.util.List;

/**
 * pdf解析器
 *
 * <AUTHOR>
 * @date 2023/1/30 16:37
 */
@Service
@RequiredArgsConstructor
public class PDFSimpleProcessor extends PDFProcess {

    private final ResourceFeign resourceFeign;
    private final DocumentUnitRepository documentUnitRepository;
    private final DocumentSummaryRepository documentSummaryRepository;
    private final MinioLinkProvider minioLinkProvider;

    @Override
    public AlgorithmType getCode() {
        return AlgorithmType.PDF_SIMPLE_STRIPPER;
    }

    @Override
    public void process(Resource resource) {
        //获取资源文件路径
        String pdfLink = minioLinkProvider.getMinioLinkIntranet(resource.getPdfAttachName());
        if (pdfLink == null) return;
        try (InputStream inputStream = NetWorkFileUtil.urlToInputStream(pdfLink);
             PDDocument helloDocument = PDDocument.load(inputStream)) {
            resource.setTextStripperStatus(BaseStatus.OK);
            GetCharLocationAndSize textStripper = new GetCharLocationAndSize();
            textStripper.setSortByPosition(true);
            textStripper.setStartPage(0);
            resource.setPages(helloDocument.getNumberOfPages());
            textStripper.setEndPage(helloDocument.getNumberOfPages());
            Writer dummy = new OutputStreamWriter(new ByteArrayOutputStream());
            textStripper.writeText(helloDocument, dummy);
            List<DocumentUnit> line = textStripper.getLine();
            documentUnitRepository.saveAll(line);
            List<DocumentSummary> summaries = mergeParagraph(line, 300);
            documentSummaryRepository.saveAll(summaries);
        } catch (IOException e) {
            resource.setTextStripperStatus(ExceptionMessageConst.PDF_STRIPPER);
            throw new ServiceException(ExceptionMessageConst.PDF_STRIPPER + "【" + e.getMessage() + "】");
        } finally {
            resourceFeign.save(resource);
        }
    }
}
