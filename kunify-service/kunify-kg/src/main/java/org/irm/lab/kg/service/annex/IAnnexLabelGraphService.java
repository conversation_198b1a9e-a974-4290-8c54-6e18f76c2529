package org.irm.lab.kg.service.annex;

import cn.hutool.json.JSONArray;
import org.irm.lab.kg.entity.annex.processing.AnnexLabelData;
import org.irm.lab.kg.entity.annex.processing.AnnexTripleLabelData;
import org.irm.lab.repository.entity.ResourceAnnex;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/5/10 17:46
 * @description 附件标签树结构业务接口
 */
public interface IAnnexLabelGraphService {

    /**
     * 知识匹配 ===> 获取标签数据树
     *
     * @param annexId   附件Id
     * @param dataSetId 数据集Id
     * @return 标签数据
     */
    JSONArray relevanceShow(String annexId, String dataSetId);

    /**
     * 获取该资源下的所有标签数据
     *
     * @param annexId 资源Id
     * @return {@link AnnexLabelData}
     */
    List<AnnexLabelData> listLabelData(String annexId);


    /**
     * 新增或修改附件标签数据对象
     *
     * @param annexLabelData {@link AnnexLabelData}
     */
    void saveOrUpdateLabelData(AnnexLabelData annexLabelData);

    /**
     * 删除实例/实例的属性
     *
     * @param annexLabelData 附件标签数据
     */
    void removeLabelData(AnnexLabelData annexLabelData);

    /**
     * 新增/修改附件关系三元组
     *
     * @param annexTripleLabelDataList {@link AnnexTripleLabelData}
     */
    void saveOrUpdateTripleLabelData(List<AnnexTripleLabelData> annexTripleLabelDataList);

    /**
     * 删除附件关系三元组/属性
     *
     * @param annexTripleLabelData 附件关系三元组
     */
    void removeTripleLabelData(AnnexTripleLabelData annexTripleLabelData);

    /**
     * 匹配确认
     *
     * @param resourceAnnex 附件
     * @param dataSetId     数据集Id
     * @param modelId       模型Id
     * @param autoAlignment 自动实体对齐
     */
    void matchConfirm(ResourceAnnex resourceAnnex, String dataSetId, String modelId, Boolean autoAlignment);
}
