package org.irm.lab.kg.service.annex.impl;

import com.mongodb.client.model.Filters;
import lombok.RequiredArgsConstructor;
import org.irm.lab.kg.entity.annex.processing.AnnexCacheTripleLabelData;
import org.irm.lab.kg.entity.processing.CacheTripleLabelData;
import org.irm.lab.kg.repository.annex.AnnexCacheTripleLabelDataRepository;
import org.irm.lab.kg.service.annex.IAnnexCacheTripleLabelDataService;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/5/6 18:46
 * @description 附件缓存关系标签
 */
@Service
@RequiredArgsConstructor
public class AnnexCacheTripleLabelDataServiceImpl implements IAnnexCacheTripleLabelDataService {
    private final AnnexCacheTripleLabelDataRepository annexCacheTripleLabelDataRepository;


    /**
     * 新增关系缓存标签
     *
     * @param annexCacheTripleLabelData {@link AnnexCacheTripleLabelData}
     */
    @Override
    public AnnexCacheTripleLabelData save(AnnexCacheTripleLabelData annexCacheTripleLabelData) {
        return annexCacheTripleLabelDataRepository.findById(annexCacheTripleLabelDataRepository.save(annexCacheTripleLabelData));
    }

    /**
     * 删除关系缓存标签
     *
     * @param id id
     */
    @Override
    public void remove(String id) {
        annexCacheTripleLabelDataRepository.deleteByIdFake(id);
    }


    /**
     * 根据标签数据缓存Id查询该数据的所有关系
     *
     * @param cacheLabelDataId 标签数据缓存I
     * @return {@link CacheTripleLabelData}
     */
    @Override
    public List<AnnexCacheTripleLabelData> listByCacheLabelDataId(String cacheLabelDataId) {
        return annexCacheTripleLabelDataRepository.findByCondition(Filters.eq("startLabelDataId", cacheLabelDataId));
    }
}
