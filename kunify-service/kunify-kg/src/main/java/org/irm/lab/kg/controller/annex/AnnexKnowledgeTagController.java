package org.irm.lab.kg.controller.annex;

import cn.hutool.core.util.ObjectUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.irm.lab.common.annotation.MyLog;
import org.irm.lab.common.api.R;
import org.irm.lab.common.constant.LogConstant;
import org.irm.lab.common.utils.Func;
import org.irm.lab.config.entity.Label;
import org.irm.lab.kg.algorithm.DocumentTextLight;
import org.irm.lab.kg.algorithm.DocumentUnit;
import org.irm.lab.kg.dto.AnnexCacheLabelDataDTO;
import org.irm.lab.kg.dto.CacheLabelDataDTO;
import org.irm.lab.kg.entity.annex.AnnexDocumentUnit;
import org.irm.lab.kg.entity.annex.processing.AnnexCacheTripleLabelData;
import org.irm.lab.kg.entity.processing.CacheTripleLabelData;
import org.irm.lab.kg.entity.processing.LabelPropertyVO;
import org.irm.lab.kg.service.annex.IAnnexKnowledgeTagService;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/5/6 15:03
 * @description 附件知识标注控制器
 */
@Api(value = "附件知识标注控制器")
@RestController
@RequestMapping("/annex-knowledge-tag")
@RequiredArgsConstructor
public class AnnexKnowledgeTagController {
    private final IAnnexKnowledgeTagService iAnnexKnowledgeTagService;

    /**
     * 新增高亮词
     *
     * @param documentUnitId    段落Id
     * @param documentTextLight {@link DocumentTextLight}
     * @return {@link DocumentUnit}
     */
    @ApiOperation(value = "新增高亮词")
    @PostMapping("/save")
    public R<AnnexDocumentUnit> save(@RequestParam("documentUnitId") String documentUnitId, @RequestBody DocumentTextLight documentTextLight) {
        if (ObjectUtil.isEmpty(documentTextLight.getStart()) || ObjectUtil.isEmpty(documentTextLight.getEnd()))
            return R.failed("下标错误!");
        return R.data(iAnnexKnowledgeTagService.save(documentUnitId, documentTextLight));
    }

    /**
     * 更新高亮词
     *
     * @param documentUnitId    段落Id
     * @param documentTextLight {@link DocumentTextLight}
     * @return {@link DocumentUnit}
     */
    @ApiOperation(value = "更新高亮词")
    @PostMapping("/update")
    public R<AnnexDocumentUnit> update(@RequestParam("documentUnitId") String documentUnitId, @RequestBody DocumentTextLight documentTextLight) {
        if (ObjectUtil.isEmpty(documentTextLight.getStart()) || ObjectUtil.isEmpty(documentTextLight.getEnd()))
            return R.failed("下标错误!");
        return R.data(iAnnexKnowledgeTagService.update(documentUnitId, documentTextLight));
    }

    /**
     * 清除指定附件下的所有高亮词
     *
     * @param annexId 资源Id
     */
    @ApiOperation(value = "清除指定资源下的所有高亮词")
    @PostMapping("/clear-all")
    public R<String> clearAll(@RequestParam("annexId") String annexId) {
        iAnnexKnowledgeTagService.clearAll(annexId);
        return R.success();
    }

    /**
     * 清除当前页面的所有高亮词
     *
     * @param ids 语料单元Id列表
     */
    @ApiOperation(value = "清除当前页面的所有高亮词")
    @PostMapping("/clear-current-page")
    public R<String> clearCurrentPage(@RequestBody String ids) {
        List<String> idList = Func.objToStrList(ids);
        iAnnexKnowledgeTagService.removeByIds(idList);
        return R.success();
    }

    /**
     * 删除指定的高亮词
     *
     * @param identifier 高亮词唯一标识
     */
    @ApiOperation(value = "删除指定的高亮词")
    @PostMapping("/clear-by-identify")
    public R<String> clearByIdentify(@RequestParam("documentUnitId") String documentUnitId, @RequestParam("identifier") String identifier) {
        iAnnexKnowledgeTagService.clearByIdentify(documentUnitId, identifier);
        return R.success();
    }

    /**
     * 根据工作任务Id和类型 获取指定类型的标签
     *
     * @param workTaskId 工作任务Id
     * @param type       标签类型 概念、关系、属性
     * @return {@link Label}
     */
    @ApiOperation(value = "根据工作任务Id和类型 获取指定类型的标签", hidden = true)
    @GetMapping("/list-label")
    public R<List<Label>> listLabel(@RequestParam("workTaskId") String workTaskId, @RequestParam("type") String type) {
        return R.data(iAnnexKnowledgeTagService.listLabelOrPropertyOrPredicate(workTaskId, type));
    }

    /**
     * 根据附件Id获取实例（高亮词）对象
     *
     * @param annexId 附件Id
     * @return {@link DocumentTextLight}
     */
    @ApiOperation(value = "根据资源Id获取实例（高亮词）对象", hidden = true)
    @GetMapping("/list-entity")
    public R<List<DocumentTextLight>> listEntity(@RequestParam("annexId") String annexId) {
        return R.data(iAnnexKnowledgeTagService.listEntity(annexId));
    }


    /**
     * 为实例 新增/修改属性
     *
     * @param cacheLabelDataId 缓存标签Id
     * @param labelPropertyVO  {@link LabelPropertyVO}
     */
    @PostMapping("/save-property")
    public R<Void> saveOrUpdateProperty(@RequestParam("id") String cacheLabelDataId, @RequestBody LabelPropertyVO labelPropertyVO) {
        iAnnexKnowledgeTagService.saveOrUpdateProperty(cacheLabelDataId,labelPropertyVO);
        return R.success();
    }

    /**
     * 删除指定的属性
     *
     * @param cacheLabelDataId 缓存标签Id
     * @param identifier 属性唯一标识
     */
    @PostMapping("/remove-property")
    public R<Void> removeProperty(@RequestParam("id") String cacheLabelDataId,@RequestParam("identifier") String identifier){
        iAnnexKnowledgeTagService.removeProperty(cacheLabelDataId,identifier);
        return R.success();
    }

    /**
     * 为实例 新增/修改关系和属性
     *
     * @param annexCacheTripleLabelData {@link CacheTripleLabelData}
     */
    @PostMapping("/save-relation")
    public R<Void> saveOrUpdateRelation(@RequestBody AnnexCacheTripleLabelData annexCacheTripleLabelData){
        iAnnexKnowledgeTagService.saveOrUpdateRelation(annexCacheTripleLabelData);
        return R.success();
    }

    /**
     * 删除指定关系
     *
     * @param annexCacheTripleLabelDataId 关系标签Id
     */
    @PostMapping("/remove-relation")
    public R<Void> removeRelation(@RequestParam("id") String annexCacheTripleLabelDataId){
        iAnnexKnowledgeTagService.removeRelation(annexCacheTripleLabelDataId);
        return R.success();
    }


    /**
     * 根据标签数据缓存Id，获取该高亮词的属性和关系详情
     *
     * @param cacheLabelDataId 标签数据缓存Id
     * @return {@link CacheLabelDataDTO}
     */
    @ApiOperation(value = "根据标签数据缓存Id，获取该高亮词的属性和关系详情", hidden = true)
    @GetMapping("/info-cache-label")
    public R<AnnexCacheLabelDataDTO> infoCacheLabelData(@RequestParam String cacheLabelDataId) {
        return R.data(iAnnexKnowledgeTagService.infoCacheLabelData(cacheLabelDataId));
    }

    /**
     * 附件知识标注  重新调用算法识别
     *
     * @param annexId 附件Id
     */
    @MyLog(menu = LogConstant.MENU_KNOWLEDGE, dataType = LogConstant.DATA_ANNEX, operation = LogConstant.OPERATION_REANALYSIS)
    @ApiOperation(value = "知识标注重新调用算法识别", hidden = true)
    @PostMapping("/re-knowledge-tag")
    public R<String> reKnowledgeTag(@RequestParam("annexId") String annexId) {
        iAnnexKnowledgeTagService.reKnowledgeTag(annexId);
        return R.success();
    }


    /**
     * 附件知识标注确认
     *
     * @param annexId 附件Id
     */
    @MyLog(menu = LogConstant.MENU_KNOWLEDGE, dataType = LogConstant.DATA_ANNEX, operation = LogConstant.OPERATION_MARK_CONFIRMATION)
    @ApiOperation(value = "知识标注确认")
    @GetMapping("/confirm")
    public R<String> confirm(@RequestParam String annexId) {
        iAnnexKnowledgeTagService.confirm(annexId);
        return R.success();
    }
}
