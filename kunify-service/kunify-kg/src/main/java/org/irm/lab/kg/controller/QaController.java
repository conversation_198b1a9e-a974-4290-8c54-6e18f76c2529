package org.irm.lab.kg.controller;

import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import lombok.extern.log4j.Log4j2;
import org.irm.ai.api.doc_qa.DocQaApi;
import org.irm.ai.api.model.QaModelApi;
import org.irm.lab.common.api.R;
import org.irm.lab.common.constant.AuthConstant;
import org.irm.lab.common.utils.ThreadLocalUtil;
import org.irm.lab.kg.algorithm.DocumentUnit;
import org.irm.lab.kg.config.AiSdkLink;
import org.irm.lab.kg.service.IDocumentUnitService;
import org.irm.lab.repository.feign.ResourceFeign;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collector;
import java.util.stream.Collectors;

/**
 * 自动问答
 *
 * <AUTHOR>
 * @date 2023/3/13 15:05
 */
@Log4j2
@RestController
@RequestMapping("/qa")
public class QaController {
    @Resource
    private ResourceFeign resourceFeign;
    @Resource
    private IDocumentUnitService documentUnitService;
    private final Set<String> filterWord = Set.of("★");

    @GetMapping("/sync")
    public R<JSONArray> resourceInstance(@RequestParam String TENANT_ID) {
        JSONArray result = JSONUtil.createArray();
        JSONObject obj = JSONUtil.createObj();
        obj.putOpt(AuthConstant.TENANT_ID, TENANT_ID);
        ThreadLocalUtil.set("user", obj.toString());
        R<List<org.irm.lab.repository.entity.Resource>> listR = resourceFeign.listByCondition(new HashMap<>());
        if (!(listR.isSuccess() && listR.getData() != null)) return R.data(result);
        List<org.irm.lab.repository.entity.Resource> resources = listR.getData();
        for (org.irm.lab.repository.entity.Resource resource : resources) {
            log.info("同步文档 id：【{}】，name：【{}】",resource.getId(),resource.getName());
            List<DocumentUnit> documentUnits = documentUnitService.infoByResource(resource.getId());

            Map<Integer, List<DocumentUnit>> unitGroupByPage = documentUnits.stream().filter(m -> "0".equals(m.getType())).collect(Collectors.groupingBy(DocumentUnit::getPage));

            for (Map.Entry<Integer, List<DocumentUnit>> integerListEntry : unitGroupByPage.entrySet()) {
                JSONObject currentContent = JSONUtil.createObj();
                List<DocumentUnit> value = integerListEntry.getValue();
                StringBuilder stringBuilder = new StringBuilder();
                value.stream().filter(s->!filterWord.contains(s.getContent()))
                        .sorted(Comparator.comparing(DocumentUnit::getSortInCurrentPage))
                        .forEach(m->stringBuilder.append(m.getContent()).append("\n"));
                currentContent.putOpt("page",integerListEntry.getKey())
                        .putOpt("document_uuid",resource.getId())
                        .putOpt("document_name",resource.getName())
                        .putOpt("text",stringBuilder.toString());
                result.add(currentContent);
            }
        }
        ThreadLocalUtil.remove();
        return R.data(result);
    }

    @GetMapping("/infer")
    public R<JSONArray> infer(@RequestParam String question){
        QaModelApi qaModelApi = QaModelApi.build(AiSdkLink.sdkApikey, AiSdkLink.sdkSecretKey, AiSdkLink.qaHost);
        JSONObject infer = qaModelApi.infer(question, AiSdkLink.qaDataSet);
        if (infer ==null || !infer.containsKey("data")) return R.failed();
        JSONArray jsonArray = infer.getJSONObject("data").getJSONArray("result");
        return R.data(jsonArray);
    }
}
