package org.irm.lab.kg.service;


import org.irm.lab.common.support.MyPage;
import org.irm.lab.kg.entity.KnowledgeConcept;
import org.irm.lab.kg.entity.KnowledgeModel;
import org.irm.lab.kg.entity.KnowledgeProperty;
import org.irm.lab.kg.vo.KnowledgeModelVO;
import org.irm.lab.kg.vo.echarts.EchartsVO;

import java.util.List;
import java.util.Map;
import java.util.Set;

public interface IKnowledgeModelService {
    MyPage<KnowledgeModelVO> page(Map<String, Object> pageMap, int page, int size);

    KnowledgeModelVO getMainModel();

    List<KnowledgeModelVO> list();

    KnowledgeModel save(KnowledgeModel knowledgeModel);

    void remove(List<String> ids);

    void deployed(String id);

    KnowledgeModel info(String id);


    KnowledgeModel addModelConcept(String modelId, String conceptId);

    KnowledgeModel removeModelConcept(String modelId, String conceptId);

    KnowledgeModel removeModelRelation(String modelId, String relationId);

    KnowledgeModel removeModelConceptProperty(String modelId, String conceptId, String propertyId);

    KnowledgeModel removeModelRelationProperty(String modelId, String relationId, String propertyId);

    EchartsVO infoMap(String modelId);

    List<KnowledgeModel> list(Map<String, Object> queryParam);


    List<KnowledgeConcept> availableConceptList(String modelId);

    List<KnowledgeProperty> infoModelConceptProperty(String modelId, String conceptId);

    List<KnowledgeProperty> infoModelRelationProperty(String modelId, String relationId);

    Set<String> getAlreadyBindPropertyByConcept(String conceptId);

    Set<String> getAlreadyBindPropertyByRelation(String relationId);

    /**
     * 获取可用的模型（已部署）
     *
     * @return {@link KnowledgeModel}
     */
    List<KnowledgeModel> listEnableModel();

//    Boolean hasConcept(String conceptId);
//
//    Boolean hasRelation(String relationId);

}
