package org.irm.lab.kg.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.ObjectUtil;
import com.mongodb.client.model.Filters;
import lombok.SneakyThrows;
import org.bson.conversions.Bson;
import org.irm.lab.common.constant.AbstractBaseEntityFieldConstant;
import org.irm.lab.common.constant.ExceptionMessageConst;
import org.irm.lab.common.constant.FiledNameConst;
import org.irm.lab.common.entity.AbstractBaseEntity;
import org.irm.lab.common.exception.ServiceException;
import org.irm.lab.common.support.Condition;
import org.irm.lab.common.support.MyPage;
import org.irm.lab.common.utils.JudgmentDuplication;
import org.irm.lab.kg.entity.KnowledgeConcept;
import org.irm.lab.kg.entity.KnowledgeModel;
import org.irm.lab.kg.entity.KnowledgeProperty;
import org.irm.lab.kg.entity.KnowledgeRelation;
import org.irm.lab.kg.repository.KnowledgeConceptRepository;
import org.irm.lab.kg.repository.KnowledgeModelRepository;
import org.irm.lab.kg.repository.KnowledgePropertyRepository;
import org.irm.lab.kg.repository.KnowledgeRelationRepository;
import org.irm.lab.kg.service.IKnowledgePropertyService;
import org.irm.lab.kg.vo.KnowledgePropertyVO;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

@Service
public class KnowledgePropertyServiceImpl implements IKnowledgePropertyService {

    @Resource
    private KnowledgePropertyRepository knowledgePropertyRepository;
    @Resource
    private KnowledgeRelationRepository knowledgeRelationRepository;
    @Resource
    private KnowledgeConceptRepository knowledgeConceptRepository;
    @Resource
    private KnowledgeModelRepository knowledgeModelRepository;

    /**
     * 属性分页查询
     *
     * @param pageMap 查询条件
     * @param page    当前页码
     * @param size    每页数据条数
     * @return {@link MyPage < KnowledgeProperty >}
     */
    @Override
    public MyPage<KnowledgeProperty> page(Map<String, Object> pageMap, int page, int size) {
        Bson filter = Condition.getFilter(pageMap, KnowledgeProperty.class);
        return knowledgePropertyRepository.findPageByConditionAndSorted(
                filter, Filters.eq(AbstractBaseEntityFieldConstant.CREATE_TIME, -1), page, size);
    }

    /**
     * @return 查询所有属性
     */
    @Override
    public List<KnowledgeProperty> list() {
        return knowledgePropertyRepository.findAll()
                .stream().sorted(Comparator.comparing(AbstractBaseEntity::getCreateTime).reversed())
                .collect(Collectors.toList());
    }

    /**
     * 属性详情
     *
     * @param id 属性id
     * @return KnowledgePropertyVO
     */
    @Override
    public KnowledgePropertyVO infoVO(String id) {
        KnowledgeProperty property = info(id);
        KnowledgePropertyVO knowledgePropertyVO = BeanUtil.copyProperties(property, KnowledgePropertyVO.class);
        Bson bson = Filters.eq("propertyIds", id);
        //查找当前属性id是否被引用过
        List<KnowledgeModel> modelList = new ArrayList<>();
        List<KnowledgeRelation> relations = knowledgeRelationRepository.findByCondition(bson);
        List<KnowledgeConcept> concepts = knowledgeConceptRepository.findByCondition(bson);
        if (ObjectUtil.isNotEmpty(relations)) {
            knowledgePropertyVO.setRelationList(relations);
            Set<String> relationIds = relations.stream().map(AbstractBaseEntity::getId).collect(Collectors.toSet());
            Bson relationBson = Filters.elemMatch("relations", Filters.in("_id", relationIds));
            List<KnowledgeModel> models = knowledgeModelRepository.findByCondition(relationBson);
            modelList.addAll(models);
        }
        if (ObjectUtil.isNotEmpty(concepts)) {
            knowledgePropertyVO.setConceptList(concepts);
            Set<String> conceptIds = concepts.stream().map(AbstractBaseEntity::getId).collect(Collectors.toSet());
            Bson conceptBson = Filters.elemMatch("concepts", Filters.in("_id", conceptIds));
            modelList = knowledgeModelRepository.findByCondition(conceptBson);
        }
        if (ObjectUtil.isNotEmpty(modelList)) {
            knowledgePropertyVO.setModelList(modelList);
            knowledgePropertyVO.setQuote(true);
        }
        return knowledgePropertyVO;
    }

    /**
     * @return 查询所有属性
     */
    @Override
    public List<KnowledgeProperty> listByIds(List<String> ids) {
        return knowledgePropertyRepository.findById(ids);
    }

    @Override
    public List<KnowledgeProperty> findByBson(Bson bson) {
        return knowledgePropertyRepository.findByCondition(bson);
    }

    /**
     * 属性增改
     *
     * @param knowledgeProperty 属性实体
     * @return 操作完后的实体信息
     */
    @Override
    @SneakyThrows
    public KnowledgeProperty save(KnowledgeProperty knowledgeProperty) {
        HashMap<String, Object> modelMap = new HashMap<>();
        modelMap.put(FiledNameConst.IDENTIFIER, knowledgeProperty.getIdentifier());
        List<KnowledgeProperty> models = knowledgePropertyRepository.findByCondition(Condition.getFilter(modelMap, KnowledgeProperty.class));
        // 字段重复性校验
        JudgmentDuplication.judgment(knowledgeProperty, models, Set.of(FiledNameConst.IDENTIFIER));
        return knowledgePropertyRepository.findById(knowledgePropertyRepository.save(knowledgeProperty));
    }

    /**
     * 删除多个属性
     *
     * @param ids 属性Id集合
     */
    @Override
    public void remove(List<String> ids) {
        for (String id : ids) {
            //若当前属性已被引用则不可删除
            Bson bson = Filters.eq("propertyIds", id);
            //查找当前属性id是否被引用过
            List<KnowledgeRelation> relations = knowledgeRelationRepository.findByCondition(bson);
            List<KnowledgeConcept> concepts = knowledgeConceptRepository.findByCondition(bson);
            //若当前属性已被引用则不可删除
            if (ObjectUtil.isNotEmpty(relations) || ObjectUtil.isNotEmpty(concepts))
                throw new ServiceException(ExceptionMessageConst.PROPERTY_ALREADY_BIND);
            knowledgePropertyRepository.deleteByIdFake(ids);
        }
    }

    /**
     * @param id 属性id
     * @return 当前属性
     */
    public KnowledgeProperty info(String id) {
        return knowledgePropertyRepository.findById(id);
    }

    @Override
    public KnowledgeProperty findByIdentifier(String identifier) {
        List<KnowledgeProperty> propertiesList = knowledgePropertyRepository.findByCondition(Filters.eq("identifier", identifier));
        return ObjectUtil.isNotEmpty(propertiesList) ? propertiesList.get(0) : null;
    }
}
