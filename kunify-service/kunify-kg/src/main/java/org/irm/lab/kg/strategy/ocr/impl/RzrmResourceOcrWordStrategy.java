package org.irm.lab.kg.strategy.ocr.impl;

import java.io.IOException;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.stream.Collectors;

import org.dromara.streamquery.stream.core.stream.Steam;
import org.irm.lab.common.utils.SpringUtil;
import org.irm.lab.common.utils.ThreadLocalUtil;
import org.irm.lab.kg.algorithm.DocumentUnit;
import org.irm.lab.kg.rabbitmq.producer.EsSyncProducer;
import org.irm.lab.kg.repository.DocumentUnitRepository;
import org.irm.lab.kg.service.IParagraphService;
import org.irm.lab.kg.strategy.splitter.CommandExecutor;
import org.irm.lab.kg.strategy.splitter.vo.SplitterVO;
import org.irm.lab.kg.utils.SplitterUtil;
import org.irm.lab.repository.entity.Resource;
import org.irm.lab.repository.repository.ResourceRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.mongodb.client.model.Filters;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR> <br/>
 * @date 2024/6/21 <br/>
 * @Copyright  博客：https://eliauku.gitee.io/  ||  per aspera and astra <br/>
 */
@Service("rzrmResourceOcrWordStrategy")
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
@Slf4j
public class RzrmResourceOcrWordStrategy extends AbstractFileOcrStrategyImpl{

    private final ResourceRepository resourceRepository;
    private final EsSyncProducer esSyncProducer;
    private final DocumentUnitRepository documentUnitRepository;
    private final IParagraphService paragraphService;
    private final CommandExecutor commandExecutor;

    @Override
    Map<Integer, Map<Integer, String>> getOcrResult(String strategy, Resource message) {

        String s;
        try {
            s = "";

            logger.info("开始下载文件：{}", message.getName());
            final String tempPath = paragraphService.cacheFile2TempPath(message.getId());

            if (StrUtil.isEmpty(tempPath)) {
                throw new RuntimeException("文件落地失败");
            }

            logger.info("开始解析文件：{}", tempPath + ".pdf");
            s = commandExecutor.ofd2json(tempPath + ".pdf", tempPath + ".json");
            if (ObjectUtil.isEmpty(s)) {
                throw new RuntimeException("解析失败");
            }
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
        return splitContent(s);
    }

    @Override
    void saveOcrResult(String strategy, Resource message, Map<Integer, Map<Integer, String>> ocrResult) {
        List<DocumentUnit> units = new ArrayList<>();
        DocumentUnit documentUnit = new DocumentUnit();

        StringBuffer stringBuffer = new StringBuffer();
        documentUnit.setResourceId(message.getId());

        documentUnit.setSortInCurrentPage(1);
        documentUnit.setPage(1);
        documentUnit.setType("0");
        units.add(documentUnit);
        Steam.of(ocrResult).forEach(data -> {
            final Map<Integer, String> value = data.getValue();
            value.forEach((key, value1) -> {
                stringBuffer.append(SplitterUtil.secondaryProcessing(value1));

                    }
            );
        });// 9900    17:01
        // 一小时1000个
        documentUnit.setContent(stringBuffer.toString());
        documentUnitRepository.saveAll(units);
        List<DocumentUnit> documentUnitList = documentUnitRepository.findByCondition(Filters.eq("resourceId", message.getId()));
        if (ObjectUtil.isNotEmpty(documentUnitList)) {
            // 合并内容
            esSyncProducer.sendMessage(ThreadLocalUtil.get("user"), message, null, stringBuffer.toString(), "主文件");
            log.info("【主文件】{} 同步到ES中", message.getName());
        }
        resourceRepository.save(message);
    }

    public ConcurrentHashMap<Integer, Map<Integer, String>> splitContent(String json) {

        final SplitterVO bean = JSONUtil.toBean(json, SplitterVO.class);

        ConcurrentHashMap<Integer, Map<Integer, String>> finalMap = new ConcurrentHashMap<>();
        final HashSet<Integer> pageSet = new HashSet<>();

        final HashSet<Integer> indexSet = new HashSet<>();
        Map<Integer, Integer> indexToPageMap = new HashMap<>();
        final List<SplitterVO.ContentItem> content = bean.getContent();
        for (SplitterVO.ContentItem splitterVO : content) {
            if (indexToPageMap.containsKey(splitterVO.getIndex())) {
                continue;
            }
            final int index = splitterVO.getIndex();
            final int pageIndex = splitterVO.getPageIndex();
            indexSet.add(index);
            pageSet.add(pageIndex);
            indexToPageMap.put(index, pageIndex);
        }
        Map<Integer, String> groupedContent = content.stream()
                .collect(Collectors.groupingBy(
                        SplitterVO.ContentItem::getIndex,
                        Collectors.mapping(
                                SplitterVO.ContentItem::getText,
                                Collectors.joining("")
                        )
                ));
        groupedContent.forEach((key,value) -> {
            finalMap.computeIfAbsent(indexToPageMap.get(key), k -> new HashMap<>()).put(key, value);

        });


        return finalMap;

    }
}
