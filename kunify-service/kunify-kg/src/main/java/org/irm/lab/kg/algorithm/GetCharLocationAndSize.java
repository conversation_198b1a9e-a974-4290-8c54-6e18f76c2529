package org.irm.lab.kg.algorithm;

import org.apache.pdfbox.pdmodel.PDDocument;
import org.apache.pdfbox.text.PDFTextStripper;
import org.apache.pdfbox.text.TextPosition;

import java.io.*;
import java.util.ArrayList;
import java.util.List;

/**
 * 自定义pdf文本解析器，重写了文本输出，可获取文本的坐标
 * <AUTHOR>
 * @date 2023/1/29 11:33
 */
public class GetCharLocationAndSize extends PDFTextStripper {

    private static final List<DocumentUnit> lines = new ArrayList<>();

    private static Double currentY = 0.0;

    private static Integer currentPage = 0;
    private static Integer sortInCurrentPage = 0;

    public GetCharLocationAndSize() throws IOException {
    }
    /**
     * @throws IOException If there is an error parsing the document.
     */
    public static void main( String[] args ) throws IOException {
        String fileName = "F:\\demoQ\\word\\华能\\华能材料\\数据\\会议纪要\\文件\\63b53d2caf93b\\A0001-WS2020-Y-BGS-0009_中国华能集团有限公司2020年第9次党组会\\1_中国华能集团有限公司2020年党组会会议纪要第9期.pdf";
        try (PDDocument document = PDDocument.load(new File(fileName))) {
            PDFTextStripper stripper = new GetCharLocationAndSize();
            stripper.setSortByPosition(true);
            stripper.setStartPage(0);
            stripper.setEndPage(document.getNumberOfPages());

            Writer dummy = new OutputStreamWriter(new ByteArrayOutputStream());
            stripper.writeText(document, dummy);
        }
    }

    /**
     * Override the default functionality of PDFTextStripper.writeString()
     */
    @Override
    protected void writeString(String string, List<TextPosition> textPositions) {
        if (currentPage != getCurrentPageNo()){
            currentPage = getCurrentPageNo();
            //新的一页，重新计数
            sortInCurrentPage = 0;
        }else{
            //相同页，序号+1
            ++sortInCurrentPage;
        }
        TextPosition textPosition = textPositions.get(0);
        if (textPosition.getYDirAdj() == currentY){
            DocumentUnit line = lines.get(lines.size() - 1);
            line.setContent(line.getContent()+string);
            TextPosition textPosition1 = textPositions.get(textPositions.size() - 1);
            double r = computeWidth(textPosition1,line);
            line.setWidth(line.getWidth() + r);
            //同一行，序号不加
            --sortInCurrentPage;
        }else {
            DocumentUnit line = new DocumentUnit();
            line.setContent(string);
            line.setLlx((double) textPosition.getXDirAdj());
            line.setLly((double) textPosition.getYDirAdj());
            line.setHeight((double) textPosition.getHeightDir());
            TextPosition textPosition1 = textPositions.get(textPositions.size() - 1);
            float r = computeWidth(textPosition1,line);
            line.setWidth(line.getWidth() + r);
            line.setPage(getCurrentPageNo());
            line.setSortInCurrentPage(sortInCurrentPage);
            lines.add(line);
            currentY = line.getLly();
        }
//        for (TextPosition text : textPositions) {
//            System.out.println(text.getUnicode()+ " [(X=" + text.getX() + ",Y=" +
//                    text.getY() + ") height=" + text.getHeight() + " width=" +
//                    text.getWidth() + "]");
//            }

    }

    public float computeWidth(TextPosition lastOftextPosition,DocumentUnit line){
        //最后一个字符的rrx坐标，用于计算长度
        float rrx = lastOftextPosition.getXDirAdj() + lastOftextPosition.getWidthDirAdj();
        float llx = (float) (line.getLlx()+line.getWidth());
        return rrx-llx;
    }

    public List<DocumentUnit> getLine(){
        return lines;
    }
}
