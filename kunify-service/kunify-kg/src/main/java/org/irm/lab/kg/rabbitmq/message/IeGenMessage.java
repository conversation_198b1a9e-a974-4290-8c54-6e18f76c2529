package org.irm.lab.kg.rabbitmq.message;

import lombok.Data;

import java.io.Serializable;
import java.util.Map;

/**
 * 向ai中台发送消息
 *
 * <AUTHOR>
 * @date 2023/1/17 14:56
 */
@Data
public class IeGenMessage implements Serializable {
    public static final String QUEUE = "KUNIFY4_QUEUE_IE_GEN";

    public static final String EXCHANGE = "KUNIFY4_EXCHANGE_IE_GEN";

    public static final String ROUTING_KEY = "KUNIFY4_ROUTING_IE_GEN";

    //key 语料单元id  value：语料单元内容
    private Map<String, String> content;
    private String schema;
    private String tenantId;
    private String userId;
    private String resourceId;
    private String annexId;
    private Boolean isEnd;
}
