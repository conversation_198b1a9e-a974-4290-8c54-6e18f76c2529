package org.irm.lab.kg.kgprocess;

import cn.hutool.core.util.ObjectUtil;
import lombok.extern.slf4j.Slf4j;
import org.irm.lab.common.api.R;
import org.irm.lab.kg.enums.KgProcessEnum;
import org.irm.lab.kg.kgprocess.strategy.ProcessingStrategy;
import org.irm.lab.kg.rabbitmq.annexIeInfer.AnnexIeInferProducer;
import org.irm.lab.kg.service.impl.ieInfer.AnnexIeInferServiceImpl;
import org.irm.lab.kg.service.impl.kgprocess.tag.KnowledgeTagAlgorithm;
import org.irm.lab.repository.constant.KnowledgeTagStatus;
import org.irm.lab.repository.entity.Resource;
import org.irm.lab.repository.entity.ResourceAnnex;
import org.irm.lab.repository.feign.ResourceAnnexFeign;
import org.irm.lab.repository.feign.ResourceFeign;
import org.springframework.stereotype.Component;

import java.util.List;


/**
 * <AUTHOR>
 * @date 2023/4/25 18:30
 * @description 知识标注
 */
@Slf4j
@Component
public class KnowledgeTagStrategy implements ProcessingStrategy {

    private final static String CODE = KgProcessEnum.KNOWLEDGE_TAG.getCode();
    private final static String NAME = KgProcessEnum.KNOWLEDGE_TAG.getName();

    @javax.annotation.Resource
    private org.springframework.context.ApplicationContext applicationContext;
    @javax.annotation.Resource
    private ResourceFeign resourceFeign;
    @javax.annotation.Resource
    private ResourceAnnexFeign resourceAnnexFeign;
    @javax.annotation.Resource
    private AnnexIeInferProducer annexIeInferProducer;

    @Override
    public void process(Resource resource) {
        String workTaskId = resource.getWorkTaskId();
        // 更新主资源状态
        resource.setTagStatus(KnowledgeTagStatus.TAG_ING);
        resourceFeign.save(resource);
        // 主资源知识标注算法识别
        applicationContext.getBean("resourceKnowledgeTag", KnowledgeTagAlgorithm.class).knowledgeTagWithAlgorithm(workTaskId, resource);
        // todo 临时修改为识别成功
        //resource.setTagStatus(KnowledgeTagStatus.TAG);
        //resourceFeign.save(resource);

        // 附件知识标注算法识别
        // 获取资源的全部附件
        R<List<ResourceAnnex>> resourceAnnexListR = resourceAnnexFeign.listByResourceId(resource.getId());
        List<ResourceAnnex> resourceAnnexList = resourceAnnexListR.getData();
        if (!resourceAnnexListR.isSuccess() || ObjectUtil.isEmpty(resourceAnnexList)) {
            log.info("{}  不存在附件，无需算法识别", resource.getName());
            return;
        }
        // 循环对附件进行算法识别
        for (int i = 0; i < resourceAnnexList.size(); i++) {
            ResourceAnnex resourceAnnex = resourceAnnexList.get(i);
            // 如果附件已经识别完成，则会重新识别附件
            /*if (KnowledgeTagStatus.TAG.equals(resourceAnnex.getTagStatus())){
                log.info("附件：{} 已识别，无需重复识别!",resourceAnnex.getName());
                continue;
            }*/
            // 更新附件状态
            resourceAnnex.setDocParsingConfirm(true);
            resourceAnnex.setTagStatus(KnowledgeTagStatus.TAG_ING);
            resourceAnnexFeign.save(resourceAnnex);
            // 如果是最后一个附件
            applicationContext.getBean(AnnexIeInferServiceImpl.class).knowledgeTagWithAlgorithm(workTaskId, resourceAnnex);
            // todo 临时修改为识别成功
            resourceAnnex.setTagStatus(KnowledgeTagStatus.TAG);
            resourceAnnexFeign.save(resourceAnnex);
        }

    }

    @Override
    public String getCode() {
        return CODE;
    }

    @Override
    public String getName() {
        return NAME;
    }
}
