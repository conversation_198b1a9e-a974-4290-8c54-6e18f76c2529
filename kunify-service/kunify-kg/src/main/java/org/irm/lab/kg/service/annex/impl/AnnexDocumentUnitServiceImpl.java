package org.irm.lab.kg.service.annex.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.codec.Base64Encoder;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.mongodb.client.model.Filters;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.bson.Document;
import org.bson.conversions.Bson;
import org.bson.types.ObjectId;
import org.irm.lab.common.support.Condition;
import org.irm.lab.common.support.MyPage;
import org.irm.lab.common.utils.NetWorkFileUtil;
import org.irm.lab.kg.algorithm.DocumentUnit;
import org.irm.lab.kg.algorithm.TableHeader;
import org.irm.lab.kg.entity.annex.AnnexDocumentUnit;
import org.irm.lab.kg.repository.annex.processing.AnnexDocumentUnitRepository;
import org.irm.lab.kg.service.annex.IAnnexDocumentImageService;
import org.irm.lab.kg.service.annex.IAnnexDocumentUnitService;
import org.irm.lab.kg.vo.annex.AnnexDocumentUnitVO;
import org.irm.lab.resource.feign.AttachFeign;
import org.springframework.stereotype.Service;

import javax.imageio.ImageIO;
import java.awt.*;
import java.awt.image.BufferedImage;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Slf4j
@Service
@RequiredArgsConstructor
public class AnnexDocumentUnitServiceImpl implements IAnnexDocumentUnitService {

    private final AnnexDocumentUnitRepository annexDocumentUnitRepository;
    private final IAnnexDocumentImageService iAnnexDocumentImageService;
    private final AttachFeign attachFeign;


    /**
     * 语料单元分页
     *
     * @return 根据页码获取符合当前type的DocumentUnit
     */
    @Override
    public MyPage<AnnexDocumentUnitVO> page(Map<String, Object> map) {
        // 获取分页条件
        Integer page = Convert.toInt(map.getOrDefault("page", 1));
        //当页所有语料单元
        List<AnnexDocumentUnitVO> annexDocumentUnitVOS = currentPageList(map);
        List<DocumentUnit> collect = annexDocumentUnitRepository.findByCondition(Filters.eq("annexId", map.get("annexId")))
                .stream()
                .sorted(Comparator.comparing(DocumentUnit::getPage)
                        .reversed())
                .collect(Collectors.toList());
        Integer maxPage = 1;
        if (ObjectUtil.isNotEmpty(collect))
            maxPage = collect.get(0).getPage();
        return new MyPage<>(page, annexDocumentUnitVOS.size(), maxPage, annexDocumentUnitVOS);
    }


    /**
     * 语料单元添加/编辑
     *
     * @param annexDocumentUnit 附件语料单元
     * @return {@link  AnnexDocumentUnit}
     */
    @Override
    public AnnexDocumentUnit save(AnnexDocumentUnit annexDocumentUnit) {
        if (ObjectUtil.isNotEmpty(annexDocumentUnit.getAttachId())) {
            String link = attachFeign.info(annexDocumentUnit.getAttachId()).getData().getLink();
            annexDocumentUnit.setAttachName(link);
        }
        //需要修改sort的units
        List<AnnexDocumentUnit> annexDocumentUnits = new ArrayList<>();
        //查询当页所有unit
        List<AnnexDocumentUnitVO> annexUnitList = currentPageList(Map.of("page", annexDocumentUnit.getPage(), "annexId", annexDocumentUnit.getAnnexId()));
        //当前unit的sort
        Integer newSort = annexDocumentUnit.getSortInCurrentPage();
        if (ObjectUtil.isNotEmpty(annexDocumentUnit.getId())) {//修改
            //查询原数据
            AnnexDocumentUnit oldUnit = info(annexDocumentUnit.getId());
            //判断当前排序是否改变
            Integer oldSort = oldUnit.getSortInCurrentPage();
            if (oldSort > newSort) {
                List<AnnexDocumentUnitVO> updateUnit = annexUnitList
                        .stream()
                        .filter(unit -> unit.getSortInCurrentPage() >= newSort && unit.getSortInCurrentPage() < oldSort)
                        .collect(Collectors.toList());
                updateUnit.forEach(unit -> {
                    Integer sortInCurrentPage = unit.getSortInCurrentPage();
                    unit.setSortInCurrentPage(++sortInCurrentPage);
                    annexDocumentUnits.add(unit);
                });
            } else {
                List<AnnexDocumentUnitVO> updateUnit = annexUnitList
                        .stream()
                        .filter(unit -> unit.getSortInCurrentPage() >= oldSort && unit.getSortInCurrentPage() <= newSort)
                        .collect(Collectors.toList());
                updateUnit.forEach(unit -> {
                    Integer sortInCurrentPage = unit.getSortInCurrentPage();
                    unit.setSortInCurrentPage(--sortInCurrentPage);
                    annexDocumentUnits.add(unit);
                });
            }
        } else {//添加
            //sort大于当前添加的unit的units
            List<AnnexDocumentUnit> annexDocumentUnitList = annexUnitList
                    .stream()
                    .filter(unit -> unit.getSortInCurrentPage() >= newSort)
                    .collect(Collectors.toList());
            if (ObjectUtil.isNotEmpty(annexDocumentUnitList)) {
                //中间插入，需修改后面的unit
                annexDocumentUnitList.forEach(unit -> {
                    Integer sortInCurrentPage = unit.getSortInCurrentPage();
                    unit.setSortInCurrentPage(++sortInCurrentPage);
                    annexDocumentUnits.add(unit);
                });
            } else {
                Integer maxSort;
                //查出最大sort
                List<AnnexDocumentUnitVO> collect = annexUnitList
                        .stream()
                        .sorted(Comparator.comparing(DocumentUnit::getSortInCurrentPage)
                                .reversed())
                        .collect(Collectors.toList());
                if (collect.size() > 0) {
                    maxSort = collect.get(0).getSortInCurrentPage();
                } else {
                    maxSort = 0;
                }
                annexDocumentUnit.setSortInCurrentPage(maxSort + 1);
            }
        }
        if (ObjectUtil.isNotEmpty(annexDocumentUnits))
            annexDocumentUnitRepository.saveAll(annexDocumentUnits);
        return annexDocumentUnitRepository.findById(annexDocumentUnitRepository.save(annexDocumentUnit));
    }

    /**
     * 语料单元删除
     *
     * @param ids 需删除的语料单元Id列表
     */
    @Override
    public void removeAndUpdateSortInCurrentPage(List<String> ids) {
        for (String id : ids) {
            AnnexDocumentUnit annexDocumentUnit = info(id);
            //根据当前需要删除的语料单元的页码和对应资源id找到当前页所有的语料单元
            List<AnnexDocumentUnitVO> allUnitList = currentPageList(Map.of("page", annexDocumentUnit.getPage(), "annexId", annexDocumentUnit.getAnnexId()));
            //拿到当前页页码大于当前删除的语调单元的页码的所有语料单元
            List<AnnexDocumentUnitVO> unitVOList = allUnitList
                    .stream()
                    .filter(unit -> unit.getSortInCurrentPage() > annexDocumentUnit.getSortInCurrentPage())
                    .collect(Collectors.toList());
            List<AnnexDocumentUnit> annexDocumentUnits = new ArrayList<>();
            unitVOList.forEach(unitVO -> {
                AnnexDocumentUnit documentUnit1 = BeanUtil.copyProperties(unitVO, AnnexDocumentUnit.class);
                documentUnit1.setSortInCurrentPage(documentUnit1.getSortInCurrentPage() - 1);
                annexDocumentUnits.add(documentUnit1);
            });
            annexDocumentUnitRepository.saveAll(annexDocumentUnits);
            annexDocumentUnitRepository.deleteByIdFake(id);
        }

    }

    /**
     * 根据预料单元Id删除
     *
     * @param ids 语料单元ID
     */
    @Override
    public void remove(List<String> ids) {
        for (String id : ids) {
            annexDocumentUnitRepository.deleteByIdFake(id);
        }
    }


    /**
     * 修改表头
     *
     * @param unitId      预料单元Id
     * @param tableHeader 表头
     * @return {@link AnnexDocumentUnit}
     */
    @Override
    public AnnexDocumentUnit saveKeys(String unitId, TableHeader tableHeader) {
        AnnexDocumentUnit unit = info(unitId);
        List<TableHeader> collect = unit
                .getTableHeader()
                .stream()
                .sorted(Comparator.comparing(TableHeader::getSort).reversed())
                .collect(Collectors.toList());
        if (ObjectUtil.isEmpty(tableHeader.getIdentifier())) {
            tableHeader.setIdentifier(IdUtil.simpleUUID());
            if (ObjectUtil.isEmpty(collect)) {
                tableHeader.setSort(collect.get(0).getSort() + 1);
                unit.setTableHeader(List.of(tableHeader));
            } else {
                tableHeader.setSort(0);
                unit.getTableHeader().add(tableHeader);
            }
            annexDocumentUnitRepository.save(unit);
        } else {
            Document query = new Document("_id", new ObjectId(unitId));
            query.put("tableHeader.identifier", tableHeader.getIdentifier());
            Document update = new Document("tableHeader.$.name", tableHeader.getName());

            update.put("tableHeader.$.sort", tableHeader.getSort());
            annexDocumentUnitRepository.updateOne(query, new Document("$set", update));
        }
        return annexDocumentUnitRepository.findById(unitId);
    }

    /**
     * 表头删除
     *
     * @param unitId  语料单元id
     * @param tableId 表头id
     */
    public void removeKeys(String unitId, String tableId) {
        AnnexDocumentUnit unit = info(unitId);
        List<TableHeader> tableHeaders = unit.getTableHeader();
        List<TableHeader> collect = tableHeaders.stream().filter(s ->
                !s.getIdentifier().equals(tableId)
        ).collect(Collectors.toList());
        unit.setTableHeader(collect);
        annexDocumentUnitRepository.save(unit);
    }

    /**
     * 表格行信息修改
     *
     * @param unitId 语料单元id
     * @param map    操作信息
     * @return 表格信息
     */
    @Override
    public AnnexDocumentUnit saveRow(String unitId, String map) {
        AnnexDocumentUnit unit = info(unitId);
        String tableData = unit.getTableData();
        //将表格信息转换成JsonArray
        JSONArray tableDataArray = JSONUtil.parseArray(tableData);
        //将信息转换成Object
        JSONObject entries = JSONUtil.parseObj(map);
        if (ObjectUtil.isNotEmpty(tableDataArray)) {
            Integer index = Convert.toInt(entries.get("index"));
            tableDataArray.put(index, entries);
        } else {
            tableDataArray.add(entries);
        }
        unit.setTableData(tableDataArray.toString());
        return annexDocumentUnitRepository.findById(annexDocumentUnitRepository.save(unit));
    }

    /**
     * 删除表格行信息
     *
     * @param unitId 语料单元id
     * @param index  行索引
     */
    @Override
    public void removeRow(String unitId, String index) {
        AnnexDocumentUnit unit = info(unitId);
        String tableData = unit.getTableData();
        //将表格信息转换成JsonArray
        JSONArray tableDataArray = JSONUtil.parseArray(tableData);
        tableDataArray.remove(Integer.parseInt(index));
        unit.setTableData(tableDataArray.toString());
        annexDocumentUnitRepository.save(unit);
    }


    /**
     * 图片预览
     *
     * @param id 当前语料单元id
     * @return 标注后的图片
     */
    @Override
    public String preview(String id) {
        AnnexDocumentUnit unit = info(id);
        // 获取图片预览地址
        String pagePicture = iAnnexDocumentImageService.getPagePicture(unit.getAnnexId(), unit.getPage());
        // 根据图片预览地址读取匹配输入流
        InputStream ins = NetWorkFileUtil.urlToInputStream(pagePicture);
        //图片操作
        BufferedImage image;
        try {
            image = ImageIO.read(ins);
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
        Graphics graphics = image.getGraphics();
        graphics.setColor(Color.red);
        if (unit.getLlx() != null && unit.getLly() != null && unit.getWidth() != null && unit.getHeight() != null)
            graphics.drawRect((int) Math.floor(unit.getLlx()), (int) Math.floor(unit.getLly()), (int) Math.ceil(unit.getWidth()), (int) Math.ceil(unit.getHeight()));
        ByteArrayOutputStream os = new ByteArrayOutputStream();
        try {
            ImageIO.write(image, "jpg", os);
        } catch (IOException e) {
            throw new RuntimeException(e);
        }

        return "data:image/jpeg;base64," + Base64Encoder.encode(os.toByteArray());
    }


    /**
     * 根据Id查询附件预料单元
     *
     * @param id 附件预料单元Id
     * @return {@link AnnexDocumentUnit}
     */
    public AnnexDocumentUnit info(String id) {
        return annexDocumentUnitRepository.findById(id);
    }

    /**
     * 根据附件Id查询附件的所有语料单元
     *
     * @param annexId 附件Id
     * @return {@link AnnexDocumentUnit}
     */
    @Override
    public List<AnnexDocumentUnit> findByAnnexId(String annexId) {
        return annexDocumentUnitRepository.findByCondition(new Document("annexId", annexId));
    }


    /**
     * 模糊查询
     *
     * @param map 查询条件
     * @return DocumentUnitList
     */
    public List<AnnexDocumentUnit> getFilter(Map<String, Object> map) {
        Bson bson = Condition.getFilter(map, AnnexDocumentUnit.class);
        return annexDocumentUnitRepository.findByCondition(bson);
    }

    /**
     * 语料单元分页
     *
     * @return 根据页码获取符合当前type的DocumentUnit
     */
    public List<AnnexDocumentUnitVO> currentPageList(Map<String, Object> map) {
        // 获取分页条件
        Bson bson = Filters.and(Filters.eq("page", Convert.toInt(map.getOrDefault("page", 1))), Filters.eq("annexId", map.get("annexId")));
        List<AnnexDocumentUnit> unitList = annexDocumentUnitRepository.findByConditionAndSorted(bson, Filters.eq("sortInCurrentPage", 1));
        int label = 0;
        List<AnnexDocumentUnitVO> unitVOS = new ArrayList<>();
        for (AnnexDocumentUnit unit : unitList) {
            if (NumberUtil.isNumber(unit.getType()) && !"0".equals(unit.getType()))
                label = Integer.parseInt(unit.getType());
            AnnexDocumentUnitVO AnnexDocumentUnitVO = BeanUtil.copyProperties(unit, AnnexDocumentUnitVO.class);
            AnnexDocumentUnitVO.setLabel(label);
            unitVOS.add(AnnexDocumentUnitVO);
        }
        return unitVOS;
    }



}
