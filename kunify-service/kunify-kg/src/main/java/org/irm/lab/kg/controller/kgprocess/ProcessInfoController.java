package org.irm.lab.kg.controller.kgprocess;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.ReflectUtil;
import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.irm.lab.common.api.R;
import org.irm.lab.common.exception.ServiceException;
import org.irm.lab.config.entity.Process;
import org.irm.lab.config.entity.ProcessNode;
import org.irm.lab.config.entity.WorkTask;
import org.irm.lab.config.feign.WorkTaskFeign;
import org.irm.lab.kg.enums.KgProcessEnum;
import org.irm.lab.kg.service.IParagraphService;
import org.irm.lab.kg.service.IProcessInfoService;
import org.irm.lab.kg.vo.ResourceProcessInfoVo;
import org.irm.lab.repository.entity.BaseResource;
import org.irm.lab.repository.entity.Resource;
import org.irm.lab.repository.entity.ResourceAnnex;
import org.irm.lab.repository.feign.ResourceAnnexFeign;
import org.irm.lab.repository.feign.ResourceFeign;
import org.springframework.web.bind.annotation.*;

import java.lang.reflect.Method;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2023/3/9 16:36
 * @description 资源加工信息控制器
 */
@RestController
@RequestMapping("/process-info")
@Api(value = "资源加工信息控制器", hidden = true)
@RequiredArgsConstructor
public class ProcessInfoController {

    private final IProcessInfoService iProcessInfoService;
    private final WorkTaskFeign workTaskFeign;
    private final ResourceFeign resourceFeign;
    private final ResourceAnnexFeign resourceAnnexFeign;
    private final IParagraphService paragraphService;


    /**
     * 根据工作任务Id和资源Id获取详情信息
     *
     * @param workTaskId 工作任务Id
     * @param resourceId 资源Id
     * @return {@link ResourceProcessInfoVo}
     */
    @ApiOperation(value = "根据工作任务Id和资源Id获取详情", hidden = true)
    @GetMapping("/info")
    public R<ResourceProcessInfoVo> processInfo(String workTaskId, String resourceId) {
        return R.data(iProcessInfoService.processInfo(workTaskId, resourceId));
    }

    /**
     * 一键通过资源加工
     *
     * @param resourceId 资源Id
     */
    @ApiOperation(value = "一键通过资源加工", notes = "解析确认->知识标注算法识别->标注确认")
    @PostMapping("/pass-resource")
    public R<String> passResource(@RequestParam  String resourceId) {
//        iProcessInfoService.passResource(resourceId);
        String s = paragraphService.readFileByCacheFile("/Users/<USER>/Desktop/abc.json");
        System.out.println("s = " + s);
        return R.success();
    }

    /**
     * 批量通过资源加工
     */
    @ApiOperation(value = "批量通过资源加工", notes = "解析确认->知识标注算法识别->标注确认")
    @PostMapping("/pass-resource-multi")
    public R<String> passResourceMulti(@RequestBody Map<String, Object> params) {
        List<String> resourceIds = JSONUtil.toList(JSONUtil.parseArray(params.get("resourceIds")), String.class);
        iProcessInfoService.passResourceMulti(resourceIds);
        return R.success();
    }


    /**
     * 资源加工全部文件一键通过
     *
     * @param workTaskId 工作任务Id
     */
    @ApiOperation(value = "资源加工全部文件，一键通过")
    @GetMapping("/pass-resource-all")
    private R<Void> passAllResource(@RequestParam String workTaskId) {
        iProcessInfoService.passAllResource(workTaskId);
        return R.success();
    }

    /**
     * 资源加工能力和顺序
     *
     * @param resourceId 资源Id
     */
    @GetMapping("/process-sort")
    public R<JSONArray> processSort(@RequestParam("resourceId") String resourceId) {
        R<Resource> resourceR = resourceFeign.info(resourceId);
        Resource resource = resourceR.getData();
        if (!resourceR.isSuccess() || resource == null) {
            throw new ServiceException("当前资源不存在!");
        }
        // 工作任务Id
        String workTaskId = resource.getWorkTaskId();
        Map<String, Integer> nameSort = KgProcessEnum.getNameSort();
        JSONArray jsonArray = new JSONArray();
        for (Map.Entry<String, Integer> stringIntegerEntry : nameSort.entrySet()) {
            if (stringIntegerEntry.getKey().equals(KgProcessEnum.RESOURCE_INSTANTIATION.getName())) continue;
            if (stringIntegerEntry.getKey().equals(KgProcessEnum.METADATA_CONVERSION.getName())) continue;
            JSONObject entries = new JSONObject();
            // 能力名称
            String stageName = stringIntegerEntry.getKey();
            entries.putOpt("name", stageName);
            // 排序
            Integer sort = stringIntegerEntry.getValue();
            entries.putOpt("flag", sort);
            // 是否开启该能力
            boolean enable = enable(workTaskId, stageName);
            entries.putOpt("enabled", enable);
            // 标签是否禁用
            boolean disable = disable(resource, stageName);
            entries.putOpt("disable", disable);
            jsonArray.add(entries);
        }
        return R.data(jsonArray);
    }

    /**
     * 资源加工能力和顺序
     *
     * @param annexId 附件Id
     */
    @GetMapping("/annex-process-sort")
    public R<JSONArray> annexProcessSort(@RequestParam("annexId") String annexId) {
        R<ResourceAnnex> resourceAnnexR = resourceAnnexFeign.info(annexId);
        ResourceAnnex resourceAnnex = resourceAnnexR.getData();
        if (!resourceAnnexR.isSuccess() || resourceAnnex == null) {
            throw new ServiceException("当前附件不存在!");
        }
        String workTaskId = resourceAnnex.getWorkTaskId();
        Map<String, Integer> nameSort = KgProcessEnum.getNameSort();
        JSONArray jsonArray = new JSONArray();
        for (Map.Entry<String, Integer> stringIntegerEntry : nameSort.entrySet()) {
            if (stringIntegerEntry.getKey().equals(KgProcessEnum.RESOURCE_INSTANTIATION.getName())) continue;
            if (stringIntegerEntry.getKey().equals(KgProcessEnum.METADATA_CONVERSION.getName())) continue;
            JSONObject entries = new JSONObject();
            // 能力名称
            String stageName = stringIntegerEntry.getKey();
            entries.putOpt("name", stageName);
            // 排序
            Integer sort = stringIntegerEntry.getValue();
            entries.putOpt("flag", sort);
            // 是否开启该能力
            boolean enable = enable(workTaskId, stageName);
            entries.putOpt("enabled", enable);
            // 标签是否禁用
            boolean disable = disable(resourceAnnex, stageName);
            entries.putOpt("disable", disable);
            jsonArray.add(entries);
        }
        return R.data(jsonArray);
    }

    /**
     * 判断当前工作任务是否开启指定加工能力
     *
     * @param workTaskId 工作任务Id
     * @param stageName  加工能力名称
     * @return 该工作任务是否开启了此加工能力
     */
    private boolean enable(String workTaskId, String stageName) {
        try {
            // 获取指定加工能力的枚举对象
            KgProcessEnum kgProcessEnum = KgProcessEnum.getByProcessName(stageName);
            // 判断是否开启次加工能力的字段名称
            String enableField = Objects.requireNonNull(kgProcessEnum).getEnableField();
            // 获取工作任务对象
            R<WorkTask> workTaskR = workTaskFeign.info(workTaskId);
            WorkTask workTask = workTaskR.getData();
            if (!workTaskR.isSuccess() || workTask == null) {
                throw new ServiceException("当前工作任务不存在!");
            }
            Process process = workTask.getProcess();
            // 知识库流程
            List<ProcessNode> kgProcessNodes = process.getKgProcessNodes();
            // key: stageName value:ProcessNode
            Map<String, List<ProcessNode>> processNodeMap = kgProcessNodes.stream().collect(Collectors.groupingBy(ProcessNode::getStageName));
            // 获取资源加工环节的流程对象
            List<ProcessNode> processNodes = processNodeMap.get("资源加工环节");
            ProcessNode processNode = processNodes.get(0);
            // 拼接方法名
            String methodName = "get" + Character.toUpperCase(enableField.charAt(0)) + enableField.substring(1);
            // 获取对应流程，是否开启的判断方法
            Method method = ReflectUtil.getMethod(ProcessNode.class, methodName);
            // 获取结果
            return (Boolean) method.invoke(processNode);
        } catch (Exception e) {
            return false;
        }
    }

    /**
     * 判断当前资源的所有加工能力的标签禁用状态
     *
     * @param baseResource 资源状态
     * @param stageName    加工能力名称
     * @return 禁用状态
     */
    private boolean disable(BaseResource baseResource, String stageName) {
        try {
            KgProcessEnum kgProcessEnum = KgProcessEnum.getByProcessName(stageName);
            // 获取当前资源标签禁用判断字段
            String enableField = Objects.requireNonNull(kgProcessEnum).getConfirmField();
            // 如果没有禁用依据字段，证明直接展示，无需禁用
            if (ObjectUtil.isEmpty(enableField)) {
                return false;
            }
            String methodName = "is" + Character.toUpperCase(enableField.charAt(0)) + enableField.substring(1);
            // 获取对应流程，是否开启的判断方法
            Method method = ReflectUtil.getMethod(BaseResource.class, methodName);
            // 获取结果
            return !(Boolean) method.invoke(baseResource);
        } catch (Exception e) {
            return true;
        }
    }
}
