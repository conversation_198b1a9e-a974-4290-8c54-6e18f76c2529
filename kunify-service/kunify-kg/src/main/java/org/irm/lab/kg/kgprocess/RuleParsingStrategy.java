package org.irm.lab.kg.kgprocess;

import org.irm.lab.common.api.R;
import org.irm.lab.common.enums.AlgorithmType;
import org.irm.lab.common.exception.ServiceException;
import org.irm.lab.common.utils.RedisUtil;
import org.irm.lab.config.entity.Process;
import org.irm.lab.config.entity.ProcessNode;
import org.irm.lab.config.entity.WorkTask;
import org.irm.lab.config.enums.ProcessNodeEnum;
import org.irm.lab.config.feign.WorkTaskFeign;
import org.irm.lab.kg.algorithm.AlgorithmProcessFactory;
import org.irm.lab.kg.enums.KgProcessEnum;
import org.irm.lab.kg.kgprocess.strategy.ProcessingStrategy;
import org.irm.lab.kg.kgprocess.strategy.ProcessingStrategySelector;
import org.irm.lab.repository.constant.RedisConstant;
import org.irm.lab.repository.entity.Resource;
import org.irm.lab.repository.feign.ResourceFeign;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/4/25 18:31
 * @description 规则解析
 */
@Component
public class RuleParsingStrategy implements ProcessingStrategy {
    private final static String CODE = KgProcessEnum.RULE_PARSING.getCode();
    private final static String NAME = KgProcessEnum.RULE_PARSING.getName();

    @javax.annotation.Resource
    private AlgorithmProcessFactory algorithmProcessFactory;
    @javax.annotation.Resource
    private WorkTaskFeign workTaskFeign;
    @javax.annotation.Resource
    private RedisUtil redisUtil;
    @javax.annotation.Resource
    private ResourceFeign resourceFeign;
    @Lazy
    @javax.annotation.Resource
    private ProcessingStrategySelector processingStrategySelector;

    @Override
    public void process(Resource resource) {
        R<WorkTask> workTaskR = workTaskFeign.info(resource.getWorkTaskId());
        WorkTask workTask = workTaskR.getData();
        if (!workTaskR.isSuccess() || workTask == null) {
            throw new ServiceException("当前工作任务不存在!");
        }
        Process process = workTask.getProcess();
        List<ProcessNode> processNodeList = process.getKgProcessNodes();
        for (ProcessNode processNode : processNodeList) {
            // 资源加工环节
            if (ProcessNodeEnum.RESOURCE_PROCESS.getNodeName().equals(processNode.getStageName())) {
                // 获取规则解析器
                List<String> docRuleParsingNameList = processNode.getDocRuleParsing();
                for (String name : docRuleParsingNameList) {
                    // 调用规则解析
                    algorithmProcessFactory.algorithmApply(AlgorithmType.name(name)).process(resource);
                }
            }
        }
        // 规则解析后判断是否继续执行
        Object value = redisUtil.get(RedisConstant.AUTO_PASS_RESOURCE + resource.getId());
        if (value != null) {
            resource.setDocRuleParsingConfirm(true);
            resource = resourceFeign.save(resource).getData();
            processingStrategySelector.process(resource);
        }

    }

    @Override
    public String getCode() {
        return CODE;
    }

    @Override
    public String getName() {
        return NAME;
    }
}
