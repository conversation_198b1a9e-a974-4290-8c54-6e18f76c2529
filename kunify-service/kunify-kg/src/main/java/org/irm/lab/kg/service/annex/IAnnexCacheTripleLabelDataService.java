package org.irm.lab.kg.service.annex;

import org.irm.lab.kg.entity.annex.processing.AnnexCacheTripleLabelData;
import org.irm.lab.kg.entity.processing.CacheTripleLabelData;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/5/6 18:45
 * @description 附件缓存关系标签
 */
public interface IAnnexCacheTripleLabelDataService {

    /**
     * 新增关系缓存标签
     *
     * @param annexCacheTripleLabelData {@link AnnexCacheTripleLabelData}
     */
    AnnexCacheTripleLabelData save(AnnexCacheTripleLabelData annexCacheTripleLabelData);

    /**
     * 删除关系缓存标签
     *
     * @param id id
     */
    void remove(String id);

    /**
     * 根据标签数据缓存Id查询该数据作为主语的所有关系
     *
     * @param cacheLabelDataId 标签数据缓存I
     * @return {@link CacheTripleLabelData}
     */
    List<AnnexCacheTripleLabelData> listByCacheLabelDataId(String cacheLabelDataId);
}
