package org.irm.lab.kg.service;

import org.irm.lab.config.entity.Label;
import org.irm.lab.kg.algorithm.DocumentTextLight;
import org.irm.lab.kg.algorithm.DocumentUnit;
import org.irm.lab.kg.dto.CacheLabelDataDTO;
import org.irm.lab.kg.entity.processing.CacheTripleLabelData;
import org.irm.lab.kg.entity.processing.LabelPropertyVO;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/2/23 11:57
 * @description 知识标注业务层接口
 */
public interface IKnowledgeTagService {
    /**
     * 新增高亮词
     *
     * @param documentUnitId    段落Id
     * @param documentTextLight {@link DocumentTextLight}
     * @return {@link DocumentUnit}
     */
    DocumentUnit save(String documentUnitId, DocumentTextLight documentTextLight);

    /**
     * 更新高亮词数据
     *
     * @param documentUnitId    段落Id
     * @param documentTextLight {@link DocumentTextLight}
     * @return {@link DocumentUnit}
     */
    DocumentUnit update(String documentUnitId, DocumentTextLight documentTextLight);

    /**
     * 清除指定资源下的所有高亮词
     *
     * @param resourceId 资源Id
     */
    void clearAll(String resourceId);

    /**
     * 清除指定的高亮词
     *
     * @param idList     高亮词列表
     */
    void removeByIds(List<String> idList);

    /**
     * 根据唯一标识删除高亮词
     *
     * @param documentUnitId 预料单元Id
     * @param identify       高亮词唯一标识
     */
    void clearByIdentify(String documentUnitId, String identify);

    /**
     * 根据工作任务Id获取数据集标签列表
     *
     * @param workTaskId 工作任务Id
     * @return {@link Label}
     */
    List<Label> listLabelOrPropertyOrPredicate(String workTaskId,String type);


    /**
     * 根据资源Id获取实例（高亮词）对象
     *
     * @param resourceId 资源Id
     * @return {@link DocumentTextLight}
     */
    List<DocumentTextLight> listEntity(String resourceId);


    /**
     * 为实例 新增/修改属性
     *
     * @param cacheLabelDataId 缓存标签Id
     * @param labelPropertyVO  {@link LabelPropertyVO}
     */
    void saveOrUpdateProperty(String cacheLabelDataId,LabelPropertyVO labelPropertyVO);


    /**
     * 删除指定的属性
     *
     * @param cacheLabelDataId 缓存标签Id
     * @param identifier 属性唯一标识
     */
    void removeProperty(String cacheLabelDataId, String identifier);


    /**
     * 为实例 新增/修改关系和属性
     *
     * @param cacheTripleLabelData {@link CacheTripleLabelData}
     */
    void saveOrUpdateRelation(CacheTripleLabelData cacheTripleLabelData);

    /**
     * 删除指定关系
     *
     * @param cacheTripleLabelDataId 关系标签Id
     */
    void removeRelation(String cacheTripleLabelDataId);

    /**
     * 根据标签数据缓存Id，获取该高亮词的属性和关系详情
     *
     * @param cacheLabelDataId 标签数据缓存Id
     * @return {@link CacheLabelDataDTO}
     */
     CacheLabelDataDTO infoCacheLabelData(String cacheLabelDataId);

    /**
     * 知识标注  重新调用算法识别
     *
     * @param resourceId 资源Id
     */
    void reKnowledgeTag(String resourceId);

    /**
     * 知识标注确认
     *
     * @param resourceId 资源Id
     */
    void confirm(String resourceId);



}
