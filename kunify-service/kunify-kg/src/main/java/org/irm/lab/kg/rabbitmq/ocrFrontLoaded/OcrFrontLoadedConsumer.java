package org.irm.lab.kg.rabbitmq.ocrFrontLoaded;

import java.io.ByteArrayOutputStream;
import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.nio.file.Files;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Set;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.TimeoutException;
import java.util.stream.Collectors;

import org.dromara.streamquery.stream.core.stream.Steam;
import org.irm.lab.common.api.R;
import org.irm.lab.common.constant.AuthConstant;
import org.irm.lab.common.constant.ExceptionMessageConst;
import org.irm.lab.common.provider.MinioLinkProvider;
import org.irm.lab.common.utils.FileUtil;
import org.irm.lab.common.utils.NetWorkFileUtil;
import org.irm.lab.common.utils.ResourceType;
import org.irm.lab.common.utils.ThreadLocalUtil;
import org.irm.lab.front.dto.AIMiddle.OcrInferDTO;
import org.irm.lab.front.dto.AIMiddle.OcrInferResultDTO;
import org.irm.lab.front.dto.AIMiddle.OcrItemDTO;
import org.irm.lab.front.dto.AIMiddle.OcrLocationDTO;
import org.irm.lab.kg.algorithm.DocumentImage;
import org.irm.lab.kg.entity.annex.AnnexDocumentImage;
import org.irm.lab.kg.entity.annex.AnnexDocumentUnit;
import org.irm.lab.kg.rabbitmq.message.OCRAnnexMessage;
import org.irm.lab.kg.repository.DocumentImageRepository;
import org.irm.lab.kg.repository.annex.processing.AnnexDocumentImageRepository;
import org.irm.lab.kg.service.IDocumentParsingService;
import org.irm.lab.repository.constant.DocumentResolvedStatus;
import org.irm.lab.repository.entity.Resource;
import org.irm.lab.repository.entity.ResourceAnnex;
import org.irm.lab.repository.repository.ResourceAnnexRepository;
import org.irm.lab.repository.repository.ResourceRepository;
import org.irm.lab.resource.entity.Attach;
import org.irm.lab.resource.feign.AttachFeign;
import org.irm.lab.resource.feign.IOssEndPoint;
import org.irm.lab.resource.repository.AttachRepository;
import org.springframework.amqp.core.Message;
import org.springframework.amqp.rabbit.annotation.Exchange;
import org.springframework.amqp.rabbit.annotation.Queue;
import org.springframework.amqp.rabbit.annotation.QueueBinding;
import org.springframework.amqp.rabbit.annotation.RabbitHandler;
import org.springframework.amqp.rabbit.annotation.RabbitListener;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.mock.web.MockMultipartFile;
import org.springframework.stereotype.Component;
import org.springframework.web.multipart.MultipartFile;

import com.aspose.words.Document;
import com.aspose.words.FontSettings;
import com.aspose.words.SaveFormat;
import com.mongodb.client.model.Filters;
import com.rabbitmq.client.Channel;
import com.suwell.ofd.custom.agent.AtomAgent;
import com.suwell.ofd.custom.wrapper.Const;
import com.suwell.ofd.custom.wrapper.Packet;
import com.suwell.ofd.custom.wrapper.model.Common;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.json.JSONUtil;
import cn.hutool.system.OsInfo;
import cn.hutool.system.SystemUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

/**
 * 在重新解析文档的时候先删除掉源文件类型为word的转成pdf的原件,重新转换，然后再进行ocr解析
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class OcrFrontLoadedConsumer {

    private final ResourceRepository resourceRepository;
    private final AttachFeign attachFeign;
    private final IOssEndPoint iOssEndPoint;
    private final AttachRepository attachRepository;
    private final ResourceAnnexRepository resourceAnnexRepository;
    private final IDocumentParsingService idocumentParsingService;
    private final DocumentImageRepository documentImageRepository;
    private final AnnexDocumentImageRepository annexDocumentImageRepository;

    @Value("${convert.url}")
    private String convertUrl;

    @Value("${convert.port}")
    private String convertPort;

    @Value("${fileTypes.allowed}")
    private String allowed;

    @RabbitListener(bindings = @QueueBinding(
            value = @Queue(OcrFrontLoadedMessage.QUEUE + "${spring.application.name}"),
            exchange = @Exchange(name = OcrFrontLoadedMessage.EXCHANGE),
            key = OcrFrontLoadedMessage.ROUTING_KEY + "${spring.application.name}"
    ), concurrency = "3-4")
    @RabbitHandler
    public void onMessage(OcrFrontLoadedMessage message, Channel channel, Message messageStatus) throws IOException {
        // 获取消息状态信息
        long deliveryTag = messageStatus.getMessageProperties().getDeliveryTag();
        log.info("[onMessage][线程编号:{} 消息内容：{}]", Thread.currentThread().getId(), message);

        ThreadLocalUtil.set("user", message.getUser());
        final String id = message.getResourceId();
        final Resource resource = resourceRepository.findById(id);

        final String[] split = allowed.split(",");

        final Set<String> set = Steam.of(split).toSet();

        // 判断主文件是否是
        if (FileUtil.isPDF(resource.getMediaType(),set)) {
            log.info("【{}】 非文书类资源，无需解析 ", resource.getName());
            resource.setResolveStatus(DocumentResolvedStatus.RESOLVED);
            return;
        }


            ThreadLocalUtil.set("user", message.getUser());



        try {
            // 原有的处理逻辑
            final List<ResourceAnnex> resourceAnnexList = resourceAnnexRepository.findByCondition(Filters.eq("resourceId", id));


            // 当前是所有的文件
            // 判断主文件和附件里边是否存在word，一旦存在的话就对其中的word进行处理

            if (ResourceType.PUBLIC.equals(ResourceType.isPDF(resource.getMediaType()))) {
                // 主文件处理 主文件的是word  先进行处理再发送ocr消息
                log.info("----主文件----[onMessage][线程编号:{} resourceId：{} 开始进行前置处理]", Thread.currentThread().getId(), id);

                log.info("----主文件----[onMessage][线程编号:{} resourceId：{} 开始清空数据]", Thread.currentThread().getId(), id);


                try {
                    final String pdfAttachId = resource.getPdfAttachId();
                    attachRepository.deleteByIdFake(Collections.singletonList(pdfAttachId));
                    iOssEndPoint.removeFile(resource.getPdfAttachName());
                } catch (Exception e) {
                    log.error("----主文件----[onMessage][线程编号:{} resourceId：{} 删除pdf文件失败]", Thread.currentThread().getId(), id);
                }
                iOssEndPoint.removeFile(resource.getPdfAttachName());

                // 删除文件所有的图片
                List<DocumentImage> documentImages = documentImageRepository.findByConditionAndSorted(Filters.eq("resourceId", resource.getId()),
                        Filters.eq("page", 1));
                final Long l = documentImageRepository.deleteManyByIds(documentImages.stream().map(DocumentImage::getId).collect(Collectors.toList()));

                log.info("----主文件----[onMessage][线程编号:{} resourceId：{} 成功删除图片数据 {}条-----主文件]", Thread.currentThread().getId(), id, l);

                log.info("----主文件----[onMessage][线程编号:{} resourceId：{} 成功清空数据]", Thread.currentThread().getId(), id);
                log.info("----主文件----[onMessage][线程编号:{} resourceId：{} 开始进行「{}」转pdf]", Thread.currentThread().getId(), id,resource.getMediaType());
                final Attach attach = attachRepository.findById(resource.getPrimaryFileId());
                log.info("从数据库拿到数据为{}", attach);
                //final String minioLinkIntranet = minioLinkProvider.getMinioLinkIntranet(resource.getPdfAttachName());
                //final String link = minioLinkIntranet.getLink();
                allToPdf(resource, attach.getFileOriginalName(), attach.getLink());
                log.info("----主文件----[onMessage][线程编号:{} resourceId：{} 成功进行「{}」转pdf]", Thread.currentThread().getId(), id,resource.getMediaType());
            }

            resourceAnnexList.forEach(resourceAnnex -> {

                if (FileUtil.isPDF(resourceAnnex.getMediaType(),set)) {
                    log.info("【{}】 非文书类资源，无需解析 ", resourceAnnex.getName());
                    resourceAnnex.setResolveStatus(DocumentResolvedStatus.RESOLVED);
                    resourceAnnexRepository.save(resourceAnnex);
                    return;
                }


                if (ResourceType.PUBLIC.equals(ResourceType.isPDF(resourceAnnex.getMediaType()))) {
                    log.info("----附件----[onMessage][线程编号:{} resourceId：{} 开始进行前置处理]", Thread.currentThread().getId(), resourceAnnex.getId());
                    log.info("----附件----[onMessage][线程编号:{} resourceId：{} 开始清空数据]", Thread.currentThread().getId(), resourceAnnex.getId());
                    final String pdfAttachId = resourceAnnex.getPdfAttachId();
                    attachRepository.deleteByIdFake(Collections.singletonList(pdfAttachId));
                    iOssEndPoint.removeFile(resourceAnnex.getPdfAttachName());
                    // 删除文件所有的图片

                    List<AnnexDocumentImage> annexDocumentImages = annexDocumentImageRepository.findByConditionAndSorted(Filters.eq("annexId", resourceAnnex.getId()),
                            Filters.eq("page", 1));
                    log.info("一个有{}个附件", annexDocumentImages.size());
                    log.info("----附件----[onMessage][线程编号:{} resourceId：{} 开始进行删除图片数据]", Thread.currentThread().getId(), resourceAnnex.getId());

                    if (ObjectUtil.isNotEmpty(annexDocumentImages)) {
                        int i = 0;
                        annexDocumentImages.forEach(data -> {
                            log.info("----附件----[onMessage][线程编号:{} resourceId：{} 开始删除第{}个附件]", Thread.currentThread().getId(), resourceAnnex.getId(), i);
                            annexDocumentImageRepository.deleteById(data.getId());

                        });
                    }


                    log.info("----附件----[onMessage][线程编号:{} resourceId：{} 成功清空数据]", Thread.currentThread().getId(), resourceAnnex.getId());
                    log.info("----附件----[onMessage][线程编号:{} resourceId：{} 开始进行「{}」转pdf]", Thread.currentThread().getId(), resourceAnnex.getId(),resourceAnnex.getMediaType());
                    final Attach attach = attachRepository.findById(resourceAnnex.getPrimaryFileId());
                    if (ObjectUtil.isEmpty(attach)) {
                        log.info("----附件----[onMessage][线程编号:{} resourceId：{} 附件不存在]", Thread.currentThread().getId(), resourceAnnex.getId());
                        return;
                    }
                    log.info("从数据库拿到数据为{}", attach);
                    annexConvertToPdf(resourceAnnex, attach);
                    log.info("----附件----[onMessage][线程编号:{} resourceId：{} 成功进行word转pdf]", Thread.currentThread().getId(), id);
                }
            });

            if ("ocr".equals(message.getType())) {
                idocumentParsingService.reParsingWithOCR(id);
            } else {
                idocumentParsingService.reParsing(id);
            }
        } catch (Exception e) {
            if (messageStatus.getMessageProperties().getRedelivered()) {
                log.error("消息已重复处理失败，拒绝再次接收.....{}", e.getMessage());
                resource.setResolveStatus(DocumentResolvedStatus.RESOLVED_FAILED);

                //todo 设置异常状态
                channel.basicReject(deliveryTag, false);
            } else {
                resource.setResolveStatus(DocumentResolvedStatus.RESOLVED_FAILED);
                log.info("消息即将再次返回队列中进行处理....{}", e.getMessage());
                channel.basicNack(deliveryTag, false, true);
                log.error("[onMessage][线程编号:{} resourceId：{} 发生异常]", Thread.currentThread().getId(), id, e);
            }
        } finally {
            resourceRepository.save(resource);
            ThreadLocalUtil.remove();
        }
    }

    public void wordToPdf(Resource resource, String name, String link) {
        try (InputStream inputStream = NetWorkFileUtil.urlToInputStream(link);
             ByteArrayOutputStream outputStream = new ByteArrayOutputStream()
        ) {
            Document document = new Document(inputStream);
            // 对于linux系统读取指定的文字库
            OsInfo osInfo = SystemUtil.getOsInfo();
            if (osInfo.isLinux()) {
                FontSettings.getDefaultInstance().setFontsFolder("/usr/share/fonts/chinese", true);
            }
            log.info("开始转换pdf{}", name);
            // 转换为pdf文件
            document.save(outputStream, SaveFormat.PDF);
            log.info("转换pdf完成{}", name);
            // 获取文件名称
            String pdfName = name.substring(0, name.lastIndexOf(".")) + ".pdf";
            log.info("得到的文件名为{}", pdfName);
            MockMultipartFile mockMultipartFile = new MockMultipartFile(pdfName, pdfName, "application/pdf", outputStream.toByteArray());
            // 上传文件，并获取文件对象
            Attach attach = uploadSinglePdf(mockMultipartFile);
            log.info("得到的文件为{}", attach.toString());
            resource.setPdfAttachId(attach.getId());
            // 设置文件pdf文件名
            resource.setPdfAttachName(attach.getOssObjectName());
            // 更新资源数据
            String resourceId = resourceRepository.save(resource);
            log.info("主文件[wordToPdf][线程编号:{} resourceId：{} 成功转换为PDF]", Thread.currentThread().getId(), resourceId);

        } catch (Exception e) {
            throw new RuntimeException(e.toString());
        }
    }
    public void allToPdf(Resource resource, String name, String link) {
        Packet packet = null;
        AtomAgent ha = null;
        try (InputStream inputStream = NetWorkFileUtil.urlToInputStream(link);
             ByteArrayOutputStream outputStream = new ByteArrayOutputStream()) {

            log.info("Starting conversion to PDF: {}", name);

            // Create WPS packet
            packet = new Packet(Const.PackType.COMMON, Const.Target.PDF);
            packet.file(new Common("file", resource.getMediaType(), inputStream));
            ha = new AtomAgent(convertUrl + ":" + convertPort);
            // Convert to PDF
            log.info("当前调用的地址为「{}」",convertUrl + ":" + convertPort);
            ha.convert(packet, outputStream);

            log.info("Finished converting to PDF: {}", name);

            // Get PDF file name
            String pdfName = name.substring(0, name.lastIndexOf(".")) + ".pdf";
            log.info("Generated PDF file name: {}", pdfName);

            // Create MockMultipartFile
            MockMultipartFile mockMultipartFile = new MockMultipartFile(pdfName, pdfName, "application/pdf", outputStream.toByteArray());

            // Upload file and get file object
            Attach attach = uploadSinglePdf(mockMultipartFile);
            log.info("Uploaded file: {}", attach.toString());

            // Update resource object
            resource.setPdfAttachId(attach.getId());
            resource.setPdfAttachName(attach.getOssObjectName());

            // Save updated resource
            String resourceId = resourceRepository.save(resource);
            log.info("Main file [wpsToPdf][Thread ID: {} resourceId: {} successfully converted to PDF]", Thread.currentThread().getId(), resourceId);

        } catch (Exception e) {
            log.error("Error converting WPS to PDF: {}", e.getMessage());
            throw new RuntimeException("Failed to convert WPS to PDF: " + e.toString());
        } finally {
            try {
                if (packet != null) {
                    packet.close();
                }
                if (ha != null) {
                    ha.close();
                }
            } catch (IOException e) {
                log.error("Error closing resources: {}", e.getMessage());
            }
        }
    }

    public Attach uploadSinglePdf(MultipartFile multipartFile) {
        // 上传文件到Minio中
        R<Attach> r = iOssEndPoint.putPdf(multipartFile);
        if (!r.isSuccess()) {
            return null;
        }
        return r.getData();
    }

    public void annexWordToPdf(ResourceAnnex resourceAnnex, Attach attach) {
        try (InputStream inputStream = NetWorkFileUtil.urlToInputStream(attach.getLink());
             ByteArrayOutputStream outputStream = new ByteArrayOutputStream()
        ) {
            Document document = new Document(inputStream);

            OsInfo osInfo = SystemUtil.getOsInfo();
            if (osInfo.isLinux()) {
                FontSettings.getDefaultInstance().setFontsFolder("/usr/share/fonts/chinese", true);
            }
            // 转换为pdf文件
            document.save(outputStream, SaveFormat.PDF);
            // 获取文件名称
            String pdfName = attach.getFileOriginalName().substring(0, attach.getFileOriginalName().lastIndexOf(".")) + ".pdf";
            MockMultipartFile mockMultipartFile = new MockMultipartFile(pdfName, pdfName, "application/pdf", outputStream.toByteArray());
            // 上传文件，并获取文件对象
            Attach newAttach = uploadSinglePdf(mockMultipartFile);
            // 设置wordAttachId和wordAttachName
            resourceAnnex.setWordAttachId(attach.getId());
            resourceAnnex.setWordAttachName(attach.getOssObjectName());
            // 设置转换为pdf后的id和name
            resourceAnnex.setPdfAttachId(newAttach.getId());
            resourceAnnex.setPdfAttachName(newAttach.getOssObjectName());
            // 保存附件
            resourceAnnexRepository.save(resourceAnnex);
        } catch (Exception e) {

            throw new RuntimeException(e);
        }
    }

    public void annexConvertToPdf(ResourceAnnex resourceAnnex, Attach attach) {
        Packet packet = null;
        AtomAgent ha = null;
        try (InputStream inputStream = NetWorkFileUtil.urlToInputStream(attach.getLink());
             ByteArrayOutputStream outputStream = new ByteArrayOutputStream()) {

            log.info("Starting conversion to PDF for file: {}", attach.getFileOriginalName());
            // Create WPS packet
            packet = new Packet(Const.PackType.COMMON, Const.Target.PDF);
            packet.file(new Common("file", resourceAnnex.getMediaType(), inputStream));
            ha = new AtomAgent(convertUrl + ":" + convertPort);
            // Convert to PDF
            log.info("当前调用的地址为「{}」",convertUrl + ":" + convertPort);

            // Convert to PDF
            ha.convert(packet, outputStream);

            log.info("Finished converting to PDF: {}", attach.getFileOriginalName());

            // Get PDF file name
            String pdfName = attach.getFileOriginalName().substring(0, attach.getFileOriginalName().lastIndexOf(".")) + ".pdf";
            log.info("Generated PDF file name: {}", pdfName);

            // Create MockMultipartFile
            MockMultipartFile mockMultipartFile = new MockMultipartFile(pdfName, pdfName, "application/pdf", outputStream.toByteArray());

            // Upload file and get file object
            Attach newAttach = uploadSinglePdf(mockMultipartFile);
            log.info("Uploaded PDF file: {}", newAttach.toString());

            // Set converted PDF attachId and attachName
            resourceAnnex.setPdfAttachId(newAttach.getId());
            resourceAnnex.setPdfAttachName(newAttach.getOssObjectName());

            // Save the resourceAnnex
            resourceAnnexRepository.save(resourceAnnex);
            log.info("Saved resourceAnnex after WPS to PDF conversion");

        } catch (Exception e) {
            log.error("Error converting WPS to PDF: {}", e.getMessage());
            throw new RuntimeException("Failed to convert WPS to PDF: " + e.toString());
        } finally {
            try {
                if (packet != null) {
                    packet.close();
                }
                if (ha != null) {
                    ha.close();
                }
            } catch (IOException e) {
                log.error("Error closing resources: {}", e.getMessage());
            }
        }
    }


}
