package org.irm.lab.kg.rabbitmq.producer;

import lombok.RequiredArgsConstructor;
import org.irm.lab.kg.rabbitmq.message.MetadataConverterMessage;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @date 2023/3/8 16:22
 * @description 元数据转化生产者
 */
@Component
@RequiredArgsConstructor
public class MetadataConverterProducer {
    private final RabbitTemplate rabbitTemplate;

    @Value("${spring.application.name}")
    private String KEY_SELF;

    /**
     * 发送消息
     */
    public void sendMessage(String user, String topCacheLabelDataId, String processId,String resourceId){
        MetadataConverterMessage metadataConverterMessage = new MetadataConverterMessage();
        metadataConverterMessage.setUser(user);
        metadataConverterMessage.setTopCacheLabelDataId(topCacheLabelDataId);
        metadataConverterMessage.setProcessId(processId);
        metadataConverterMessage.setResourceId(resourceId);
        // 发送消息
        rabbitTemplate.convertAndSend(MetadataConverterMessage.EXCHANGE,MetadataConverterMessage.ROUTING_KEY+KEY_SELF,metadataConverterMessage);
    }
}
