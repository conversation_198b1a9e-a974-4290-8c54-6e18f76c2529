package org.irm.lab.kg.rabbitmq.ocrFrontLoaded;

import java.io.Serializable;
import java.util.List;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 */
@Data
public class OcrFrontLoadedMessage implements Serializable {



    public static final String QUEUE = "QUEUE_OCR_FRONT_LOADED";
    public static final String EXCHANGE = "EXCHANGE_OCR_FRONT_LOADED";
    public static final String ROUTING_KEY = "ROUTING_KEY_OCR_FRONT_LOADED";


    /**
     * 资源id
     */
    private String resourceId;

    /**
     * 类型
     */
    private String type;

    /**
     * 当前用户信息
     */
    private String user;

}
