package org.irm.lab.kg.rabbitmq.ieInfer;

import java.io.IOException;
import java.net.URLEncoder;
import java.util.List;
import java.util.concurrent.atomic.AtomicReference;
import java.util.stream.Collectors;

import javax.annotation.Resource;

import org.irm.lab.common.utils.RedisUtil;
import org.irm.lab.common.utils.ThreadLocalUtil;
import org.irm.lab.kg.algorithm.DocumentUnit;
import org.irm.lab.kg.dto.ie.req.IeInferReqDTO;
import org.irm.lab.kg.dto.ie.resp.IeInferRespDTO;
import org.irm.lab.kg.kgprocess.strategy.ProcessingStrategySelector;
import org.irm.lab.kg.repository.DocumentUnitRepository;
import org.irm.lab.kg.service.IKgResourceService;
import org.irm.lab.kg.service.forest.AIMiddlePlatformService;
import org.irm.lab.kg.service.impl.kgprocess.tag.KnowledgeTagAlgorithm;
import org.irm.lab.kg.vo.ie.IeInferItem;
import org.irm.lab.kg.vo.ie.IeInferRelation;
import org.irm.lab.repository.constant.KnowledgeTagStatus;
import org.irm.lab.repository.feign.ResourceAnnexFeign;
import org.irm.lab.repository.feign.ResourceFeign;
import org.irm.lab.repository.repository.ResourceRepository;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.amqp.core.Message;
import org.springframework.amqp.rabbit.annotation.Exchange;
import org.springframework.amqp.rabbit.annotation.Queue;
import org.springframework.amqp.rabbit.annotation.QueueBinding;
import org.springframework.amqp.rabbit.annotation.RabbitHandler;
import org.springframework.amqp.rabbit.annotation.RabbitListener;
import org.springframework.amqp.support.AmqpHeaders;
import org.springframework.messaging.handler.annotation.Header;
import org.springframework.stereotype.Component;

import com.alibaba.fastjson.JSON;
import com.rabbitmq.client.Channel;

import cn.hutool.core.convert.Convert;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.log4j.Log4j2;

@Component
@RequiredArgsConstructor
@Log4j2
public class IeInferConsumer {

    private static Logger logger = LoggerFactory.getLogger(IeInferConsumer.class);
    @Resource
    private AIMiddlePlatformService aiMiddlePlatformService;

    @javax.annotation.Resource
    private org.springframework.context.ApplicationContext applicationContext;
    private final RedisUtil redisUtil;
    private final ResourceAnnexFeign resourceAnnexFeign;
    private final ResourceFeign resourceFeign;
    private final ProcessingStrategySelector processingStrategySelector;
    private final ResourceRepository resourceRepository;
    private final DocumentUnitRepository documentUnitRepository;
    private final IKgResourceService kgResourceService;

    @RabbitListener(bindings = @QueueBinding(
            value = @Queue(IeInferMessage.QUEUE + "${spring.application.name}"),
            exchange = @Exchange(name = IeInferMessage.EXCHANGE),
            key = IeInferMessage.ROUTING_KEY + "${spring.application.name}"
    ),concurrency = "20", containerFactory = "rabbitListenerContainerFactory")
    @RabbitHandler
    public void onMessage(IeInferMessage ieInferMessage, Channel channel,Message messageStatus, @Header(AmqpHeaders.DELIVERY_TAG) long deliveryTag) throws IOException {

        ThreadLocalUtil.set("user", ieInferMessage.getUserId());
        long timeStart = DateUtil.current();

        try {

            final IeInferReqDTO ieInferReqDTO = new IeInferReqDTO();

            log.info("转换之前的语料{}", ieInferMessage.getText());

            final org.irm.lab.repository.entity.Resource one = resourceRepository.findOne(ieInferMessage.getResourceId());

            String name = one.getName();

            // 找到最后一个点的位置
            int index = name.lastIndexOf('.');

            if (index > 0) {
                name = name.substring(0, index);
            }


            String text = ieInferMessage.getText();

            String result = name + "\n" + text;

            ieInferReqDTO.setText(URLEncoder.encode(result));

            log.info("当前的语料{}", ieInferReqDTO.getText());

            JSONArray inputArray = new JSONArray(ieInferMessage.getSchemas());
            JSONObject mergedObject = new JSONObject();

            // 遍历输入的JSON数组，合并所有对象
            for (int i = 0; i < inputArray.size(); i++) {
                JSONObject currentObject = inputArray.getJSONObject(i);
                for (String key : currentObject.keySet()) {
                    /*if (ObjectUtil.equals(currentObject.get(key), "公文")) {
                        continue;
                    }*/
                    if (ObjectUtil.equals(key, "人员")) {
                        currentObject.getJSONArray(key).remove("办理公文");
                    }
                    /*if (ObjectUtil.equals(key, "公文")) {
                        currentObject.getJSONArray(key).remove("发文");
                    }*/
                    mergedObject.put(key, currentObject.getJSONArray(key));
                }
            }

            // 创建包含合并后对象的新JSON数组
            JSONArray outputArray = new JSONArray();
            outputArray.put(mergedObject);
            final String classifyByResourceOrAnnexId = kgResourceService.getClassifyByResourceOrAnnexId(ieInferMessage.getResourceId());
            ieInferReqDTO.setSchemas(classifyByResourceOrAnnexId);
            //ieInferReqDTO.setSchemas(outputArray.toString());
            ieInferReqDTO.setType(2);
            ieInferReqDTO.setTenant("100001");

            // 调用实体抽取
            log.info("当前的schemas{}", ieInferReqDTO.getSchemas());
            log.info("当前的语料{}", ieInferReqDTO.getText());

            final String entityExtraction = aiMiddlePlatformService.inferApi(ieInferReqDTO);
            ieInferReqDTO.setType(2);

            final String resourceId = ieInferMessage.getResourceId();
            if (ObjectUtil.isNotEmpty(resourceId)){
                log.info("开始处理主文件：" + ieInferMessage);
                JSONObject resultJson = JSONUtil.parseObj(entityExtraction);
                log.info("抽取出来的数据是{}",entityExtraction);

                final String entityExtractionCode = Convert.toStr(resultJson.get("code"));

                applicationContext.getBean("resourceKnowledgeTag", KnowledgeTagAlgorithm.class).recognitionStatus(resourceId, KnowledgeTagStatus.TAG_ING);
                if (!"200".equals(entityExtractionCode)) {
                    applicationContext.getBean("resourceKnowledgeTag", KnowledgeTagAlgorithm.class).recognitionStatus(resourceId, KnowledgeTagStatus.TAG_FAILED);
                    log.error("主文件算法识别失败！");
                    throw new RuntimeException("实体抽取失败！");
                }

                final String corpusId = ieInferMessage.getCorpusId();
                final String str = Convert.toStr(resultJson.get("data"));
                final IeInferRespDTO ieInferRespDTO = JSON.parseObject(str, IeInferRespDTO.class);


                final List<IeInferRelation> relations = ieInferRespDTO.getRelations();

                final List<IeInferItem> results = ieInferRespDTO.getResults();
                AtomicReference<String> uuid = new AtomicReference<>("");
                final List<IeInferItem> inferItems = results.stream().filter(data -> {
                    log.info("当前schema为{}",data.getSchema());
                    if (ObjectUtil.equals(data.getSchema(), "公文") || ObjectUtil.equals(data.getSchema(), "制度")){
                        uuid.set(data.getUuid());
                        return false;
                    }
                    return true;
                }).collect(Collectors.toList());
                inferItems.forEach(data -> {

                    if (data.getType().equals("属性")) {
                        data.setType("实体"); data.setSchema(null);
                    }
                });

                /*relations.forEach(data -> {
                    if (ObjectUtil.equals(uuid.get(),data.getStart())){
                        data.setStart("0");
                    }
                });*/
                DocumentUnit documentUnit = documentUnitRepository.findById(corpusId);
                String json = JSONUtil.toJsonStr(ieInferRespDTO.getIePredicateResults());

                //保存中台处理之后的数据

                documentUnit.setAiContent(ieInferRespDTO.getContent());
                documentUnit.setIeInferResult(json);
                documentUnit.setAiInferResult(str);
                documentUnitRepository.save(documentUnit);
                applicationContext.getBean("resourceKnowledgeTag", KnowledgeTagAlgorithm.class).algorithmRecognition(corpusId,inferItems, relations);
                log.info("<<<<<<<<<<<主文件算法识别完毕<<<<<<<<<<<<<<");

                applicationContext.getBean("resourceKnowledgeTag", KnowledgeTagAlgorithm.class).recognitionStatus(resourceId, KnowledgeTagStatus.TAG);

            }


        }catch (Exception e) {

            if (messageStatus.getMessageProperties().getRedelivered()) {
                // 算法识别完毕，修改资源状态 “识别失败”
                if (ieInferMessage.getResourceId() != null) {
                    applicationContext.getBean("resourceKnowledgeTag", KnowledgeTagAlgorithm.class).recognitionStatus(ieInferMessage.getResourceId(), KnowledgeTagStatus.TAG_FAILED);
                }else if (ieInferMessage.getAnnexId() != null) {
                    applicationContext.getBean("annexKnowledgeTag", KnowledgeTagAlgorithm.class).recognitionStatus(ieInferMessage.getAnnexId(), KnowledgeTagStatus.TAG_FAILED);
                }

                // 打印错误信息
                log.error("出现的问题是{}",e.toString());
                log.error("消息已重复处理失败，拒绝再次接收.....");
                if (channel.isOpen()) {
                    channel.basicReject(deliveryTag, false);
                }
            } else {
                log.error("[onMessage][线程编号:{} resourceId：{} 发生异常]", Thread.currentThread().getId(), ieInferMessage.getResourceId(), e);
                if (channel.isOpen()) {
                    channel.basicNack(deliveryTag, false, true);
                }
                log.info("消息即将再次返回队列中进行处理....");

                log.error(e.toString());
            }
        }
        finally {
            ThreadLocalUtil.remove();
            logger.info("完成识别，总耗时：{}s--[线程编号:{}]", (DateUtil.current() - timeStart) / 1000, Thread.currentThread().getId());
        }

    }

}
