package org.irm.lab.kg.service;

import org.irm.lab.kg.entity.processing.CacheTripleLabelData;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/3/1 15:08
 * @description 标签关系缓存业务接口
 */
public interface ICacheTripleLabelDataService {

    /**
     * 新增缓存标签关系
     *
     * @param cacheTripleLabelData {@link CacheTripleLabelData}
     * @return {@link CacheTripleLabelData}
     */
    CacheTripleLabelData save(CacheTripleLabelData cacheTripleLabelData);

    /**
     * 批量新增标签数据关系缓存
     *
     * @param cacheTripleLabelDataList {@link CacheTripleLabelData}
     */
    void saveAll(List<CacheTripleLabelData> cacheTripleLabelDataList);

    /**
     * 根据标签数据缓存Id查询该数据的所有关系
     *
     * @param cacheLabelDataId 标签数据缓存I
     * @return {@link CacheTripleLabelData}
     */
    List<CacheTripleLabelData> listByCacheLabelDataId(String cacheLabelDataId);

    /**
     * 删除标签数据缓存的所有关系（包含正向和逆向）
     *
     * @param cacheLabelDataId 标签数据缓存Id
     */
    void removeRelation(String cacheLabelDataId);

    /**
     * 删除指定关系三元组
     *
     * @param id 关系三元组Id
     */
    void remove(String id);
}
