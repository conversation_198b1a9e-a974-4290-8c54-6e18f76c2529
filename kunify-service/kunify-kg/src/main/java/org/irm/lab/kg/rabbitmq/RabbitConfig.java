package org.irm.lab.kg.rabbitmq;


import org.irm.lab.kg.message.EsSyncMessage;
import org.irm.lab.kg.message.OCRAnnexMessage;
import org.irm.lab.kg.message.OCRMessage;
import org.irm.lab.kg.rabbitmq.annexIeInfer.AnnexIeInferMessage;
import org.irm.lab.kg.rabbitmq.ieInfer.IeInferMessage;
import org.irm.lab.kg.rabbitmq.message.DocumentParsingMessage;
import org.irm.lab.kg.rabbitmq.message.IeGenMessage;
import org.irm.lab.kg.rabbitmq.message.MetadataConverterMessage;
import org.irm.lab.kg.rabbitmq.ocrFrontLoaded.OcrFrontLoadedMessage;
import org.springframework.amqp.core.Binding;
import org.springframework.amqp.core.BindingBuilder;
import org.springframework.amqp.core.DirectExchange;
import org.springframework.amqp.core.Queue;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

@Configuration
public class RabbitConfig {


    public static class DirectExchangeFeatureGenConfiguration {

        // 创建 Queue
        @Bean
        public Queue featureGenQueue() {
            return new Queue(IeGenMessage.QUEUE, // Queue 名字
                    true, // durable: 是否持久化
                    false, // exclusive: 是否排它
                    false); // autoDelete: 是否自动删除
        }

        // 创建 Direct Exchange
        @Bean
        public DirectExchange featureGenExchangeMinio() {
            return new DirectExchange(IeGenMessage.EXCHANGE,
                    true,  // durable: 是否持久化
                    false);  // exclusive: 是否排它
        }

        // 创建 Binding
        // Exchange：Demo01Message.EXCHANGE
        // Routing key：Demo01Message.ROUTING_KEY
        // Queue：Demo01Message.QUEUE
        @Bean
        public Binding featureGenBinding() {
            return BindingBuilder.bind(featureGenQueue()).to(featureGenExchangeMinio()).with(IeGenMessage.ROUTING_KEY);
        }
    }

    // 元数据转化器配置
    public static class MetadataConverterConfiguration{
        /**
         * 获取RoutingKey尾缀
         */
        @Value("${spring.application.name}")
        private String KEY_SELF;

        /**
         * 创建 Queue
         */
        @Bean
        public Queue metadataConverterQueue() {
            return new Queue(MetadataConverterMessage.QUEUE+KEY_SELF, // Queue 名字
                    true, // durable: 是否持久化
                    false, // exclusive: 是否排它
                    false); // autoDelete: 是否自动删除
        }

        /**
         * 创建 Direct Exchange
         */
        @Bean
        public DirectExchange metadataConverterExchange() {
            return new DirectExchange(MetadataConverterMessage.EXCHANGE,
                    true,  // durable: 是否持久化
                    false);  // exclusive: 是否排它
        }

        /**
         * 创建 Binding
         * Exchange：Demo01Message.EXCHANGE
         * Routing key：Demo01Message.ROUTING_KEY
         * Queue：Demo01Message.QUEUE
         */
        @Bean
        public Binding metadataConverterBinding() {
            return BindingBuilder.bind(metadataConverterQueue()).to(metadataConverterExchange()).with(MetadataConverterMessage.ROUTING_KEY+KEY_SELF);
        }

    }

    // 文档解析配置
    public static class DocumentParsingConfiguration{
        /**
         * 获取RoutingKey尾缀
         */
        @Value("${spring.application.name}")
        private String KEY_SELF;

        /**
         * 创建 Queue
         */
        @Bean
        public Queue documentParsingQueue() {
            return new Queue(DocumentParsingMessage.QUEUE+KEY_SELF, // Queue 名字
                    true, // durable: 是否持久化
                    false, // exclusive: 是否排它
                    false); // autoDelete: 是否自动删除
        }

        /**
         * 创建 Direct Exchange
         */
        @Bean
        public DirectExchange documentParsingExchange() {
            return new DirectExchange(DocumentParsingMessage.EXCHANGE,
                    true,  // durable: 是否持久化
                    false);  // exclusive: 是否排它
        }

        /**
         * 创建 Binding
         * Exchange：Demo01Message.EXCHANGE
         * Routing key：Demo01Message.ROUTING_KEY
         * Queue：Demo01Message.QUEUE
         */
        @Bean
        public Binding documentParsingBinding() {
            return BindingBuilder.bind(documentParsingQueue()).to(documentParsingExchange()).with(DocumentParsingMessage.ROUTING_KEY+KEY_SELF);
        }
    }

    // Es同步配置
    public static class EsSyncConfiguration{

        /**
         * 创建 Queue
         */
        @Bean
        public Queue esSyncQueue() {
            return new Queue(EsSyncMessage.QUEUE, // Queue 名字
                    true, // durable: 是否持久化
                    false, // exclusive: 是否排它
                    false); // autoDelete: 是否自动删除
        }

        /**
         * 创建 Direct Exchange
         */
        @Bean
        public DirectExchange esSyncExchange() {
            return new DirectExchange(EsSyncMessage.EXCHANGE,
                    true,  // durable: 是否持久化
                    false);  // exclusive: 是否排它
        }


        /**
         * 创建 Binding
         * Exchange：Demo01Message.EXCHANGE
         * Routing key：Demo01Message.ROUTING_KEY
         * Queue：Demo01Message.QUEUE
         */
        @Bean
        public Binding esSyncBinding() {
            return BindingBuilder.bind(esSyncQueue()).to(esSyncExchange()).with(EsSyncMessage.ROUTING_KEY);
        }


        public  static class fileInferMessage{

            /**
             * 获取RoutingKey尾缀
             */
            @Value("${spring.application.name}")
            private String KEY_SELF;

            @Bean
            public Queue fileInferDeadQueue() {
                return new Queue(IeInferMessage.QUEUE+KEY_SELF , // Queue 名字
                        true, // durable: 是否持久化
                        false, // exclusive: 是否排它
                        false); // autoDelete: 是否自动删除
            }

            //创建fileInferDead交换机
            @Bean
            public DirectExchange fileInferDeadExchange() {
                return new DirectExchange(IeInferMessage.EXCHANGE ,
                        true,  // durable: 是否持久化
                        false);  // exclusive: 是否排它
            }

            // 创建 fileInferDeadBinding
            // Exchange：Demo01Message.EXCHANGE
            // Routing key：Demo01Message.ROUTING_KEY
            // Queue：Demo01Message.QUEUE
            @Bean
            public Binding fileInferDeadBinding() {
                return BindingBuilder.bind(fileInferDeadQueue()).to(fileInferDeadExchange()).with(IeInferMessage.ROUTING_KEY+KEY_SELF );
            }
        }


        public static class OCRConfiguration{
            /**
             * 获取RoutingKey尾缀
             */
            @Value("${spring.application.name}")
            private String KEY_SELF;

            /**
             * 创建 Queue
             */
            @Bean
            public Queue OCRQueue() {
                return new Queue(OCRMessage.QUEUE+KEY_SELF, // Queue 名字
                        true, // durable: 是否持久化
                        false, // exclusive: 是否排它
                        false); // autoDelete: 是否自动删除
            }

            /**
             * 创建 Direct Exchange
             */
            @Bean
            public DirectExchange OCRExchange() {
                return new DirectExchange(OCRMessage.EXCHANGE,
                        true,  // durable: 是否持久化
                        false);  // exclusive: 是否排它
            }

            /**
             * 创建 Binding
             * Exchange：Demo01Message.EXCHANGE
             * Routing key：Demo01Message.ROUTING_KEY
             * Queue：Demo01Message.QUEUE
             */
            @Bean
            public Binding OCRBinding() {
                return BindingBuilder.bind(OCRQueue()).to(OCRExchange()).with(OCRMessage.ROUTING_KEY+KEY_SELF);
            }
        }


        public static class OCRAnnexConfiguration{
            /**
             * 获取RoutingKey尾缀
             */
            @Value("${spring.application.name}")
            private String KEY_SELF;

            /**
             * 创建 Queue
             */
            @Bean
            public Queue OCRQueue() {
                return new Queue(OCRAnnexMessage.QUEUE+KEY_SELF, // Queue 名字
                        true, // durable: 是否持久化
                        false, // exclusive: 是否排它
                        false); // autoDelete: 是否自动删除
            }

            /**
             * 创建 Direct Exchange
             */
            @Bean
            public DirectExchange OCRExchange() {
                return new DirectExchange(OCRAnnexMessage.EXCHANGE,
                        true,  // durable: 是否持久化
                        false);  // exclusive: 是否排它
            }

            /**
             * 创建 Binding
             * Exchange：Demo01Message.EXCHANGE
             * Routing key：Demo01Message.ROUTING_KEY
             * Queue：Demo01Message.QUEUE
             */
            @Bean
            public Binding OCRBinding() {
                return BindingBuilder.bind(OCRQueue()).to(OCRExchange()).with(OCRAnnexMessage.ROUTING_KEY+KEY_SELF);
            }
        }

        /**
         * 预处理前置重新加载配置
         */
        public static class OCRFrontLoadedConfiguration{
            /**
             * 获取RoutingKey尾缀
             */
            @Value("${spring.application.name}")
            private String KEY_SELF;

            /**
             * 创建 Queue
             */
            @Bean
            public Queue OCRFrontLoadedQueue() {
                return new Queue(OcrFrontLoadedMessage.QUEUE+KEY_SELF, // Queue 名字
                        true, // durable: 是否持久化
                        false, // exclusive: 是否排它
                        false); // autoDelete: 是否自动删除
            }

            /**
             * 创建 Direct Exchange
             */
            @Bean
            public DirectExchange OCRFrontLoadedExchange() {
                return new DirectExchange(OcrFrontLoadedMessage.EXCHANGE,
                        true,  // durable: 是否持久化
                        false);  // exclusive: 是否排它
            }

            /**
             * 创建 Binding
             * Exchange：Demo01Message.EXCHANGE
             * Routing key：Demo01Message.ROUTING_KEY
             * Queue：Demo01Message.QUEUE
             */
            @Bean
            public Binding OCRFrontLoadedBinding() {
                return BindingBuilder.bind(OCRFrontLoadedQueue()).to(OCRFrontLoadedExchange()).with(OcrFrontLoadedMessage.ROUTING_KEY+KEY_SELF);
            }
        }


        /**
         * 附件关系抽取
         */
        public static class AnnexIeInferConfiguration{
            /**
             * 获取RoutingKey尾缀
             */
            @Value("${spring.application.name}")
            private String KEY_SELF;

            /**
             * 创建 Queue
             */
            @Bean
            public Queue AnnexIeInferQueue() {
                return new Queue(AnnexIeInferMessage.QUEUE+KEY_SELF, // Queue 名字
                        true, // durable: 是否持久化
                        false, // exclusive: 是否排它
                        false); // autoDelete: 是否自动删除
            }

            /**
             * 创建 Direct Exchange
             */
            @Bean
            public DirectExchange AnnexIeInferExchange() {
                return new DirectExchange(AnnexIeInferMessage.EXCHANGE,
                        true,  // durable: 是否持久化
                        false);  // exclusive: 是否排它
            }

            /**
             * 创建 Binding
             * Exchange：Demo01Message.EXCHANGE
             * Routing key：Demo01Message.ROUTING_KEY
             * Queue：Demo01Message.QUEUE
             */
            @Bean
            public Binding AnnexIeInferBinding() {
                return BindingBuilder.bind(AnnexIeInferQueue()).to(AnnexIeInferExchange()).with(AnnexIeInferMessage.ROUTING_KEY+KEY_SELF);
            }
        }


    }

}
