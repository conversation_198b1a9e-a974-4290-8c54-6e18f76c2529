package org.irm.lab.kg.algorithm.ruleMatching.huiyijiyao;

import cn.hutool.core.lang.Opt;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.log4j.Log4j2;
import org.apache.pdfbox.pdmodel.PDDocument;
import org.apache.pdfbox.text.PDFTextStripper;
import org.irm.lab.common.api.R;
import org.irm.lab.common.enums.AlgorithmType;
import org.irm.lab.kg.enums.MeetingTypeEnum;
import org.irm.lab.common.provider.MinioLinkProvider;
import org.irm.lab.common.utils.NetWorkFileUtil;
import org.irm.lab.kg.algorithm.IAlgorithmProcessor;
import org.irm.lab.kg.algorithm.ruleMatching.RuleParsingEntity;
import org.irm.lab.kg.algorithm.ruleMatching.RuleParsingRelation;
import org.irm.lab.kg.algorithm.ruleMatching.RuleParsingResult;
import org.irm.lab.kg.constant.LabelConstant;
import org.irm.lab.kg.service.impl.kgprocess.ruleMatching.DocRuleParsing;
import org.irm.lab.repository.constant.DocumentResolvedStatus;
import org.irm.lab.repository.entity.Resource;
import org.irm.lab.repository.entity.ResourceAnnex;

import org.irm.lab.repository.feign.ResourceAnnexFeign;
import org.irm.lab.repository.feign.ResourceFeign;
import org.jetbrains.annotations.NotNull;
import org.springframework.stereotype.Service;

import java.io.InputStream;
import java.util.*;
import java.util.function.Predicate;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

/**
 * 会议纪要
 *
 * <AUTHOR>
 * @date 2023/4/17 10:32
 */
@Log4j2
@Service
@RequiredArgsConstructor
public class JI_YAO_01 implements IAlgorithmProcessor {
    public static final Set<String> personTypeSet = new HashSet<>(List.of("参加：", "列席：", "出席：", "记录：", "发送：", "安IJ席："));
    public static final Map<String, String> personTypeMap =
            new HashMap<>(Map.of("参加", "参会人员", "列席", "列席人员", "记录", "记录人员"));

    public static final Map<String, String> tripleMap =
            new HashMap<>(
                    Map.of("参加", "会议", "列席", "会议", "记录", "会议", "主持", "会议", "发送", "公文", "出席", "会议")
            );
    public static final Set<String> staticEntitySet = new HashSet<>(List.of("公司领导", "总助总师", "副总师"));

    public static final List<String> compareList = new ArrayList<>(List.of("同志", "总经理", "副总经理","经理","公司"));

    private final ResourceAnnexFeign resourceAnnexFeign;
    private final ResourceFeign resourceFeign;
    private final MinioLinkProvider minioLinkProvider;
    @javax.annotation.Resource(name = "resourceDocRuleParsing")
    private DocRuleParsing docRuleParsing;

    @Override
    public AlgorithmType getCode() {
        return AlgorithmType.PDF_RULE_JI_YAO_01;
    }


    @Override
    public void process(Resource resource) {
        String processName = AlgorithmType.PDF_RULE_JI_YAO_01.getName();
        log.info(">>>>>>>>>>【主文件】【{}】【规则解析】开始>>>>>>>>>>", processName);
        try {
            resource.setRuleStatus(DocumentResolvedStatus.RESOLVING);
            resourceFeign.save(resource);

            /*// 获取规则解析结果
            JSONObject results = ruleProcess(resource);
            // 对结果转换为实体
            RuleParsingResult ruleParsingResult = JSONUtil.toBean(results, RuleParsingResult.class, true);
            // 存储处理后的实体
            List<RuleParsingEntity> entityList = new ArrayList<>();
            // 存储处理后的属性(实例的属性)
            List<RuleParsingEntity> propertyList = new ArrayList<>();
            // 存储处理后关系
            List<RuleParsingRelation> relationList = ruleParsingResult.getRelation();
            // 实例处理
            processEntity(ruleParsingResult.getResult(), entityList, propertyList);
            // 生成主文件缓存标签
            docRuleParsing.ruleParsingCacheLabelData(resource, entityList, propertyList, relationList);
            resource.setRuleStatus(DocumentResolvedStatus.RESOLVED);*/
            log.info("<<<<<<<<<<【主文件】【{}】【规则解析】结束<<<<<<<<<<", processName);
        } catch (Exception e) {
            resource.setRuleStatus(DocumentResolvedStatus.RESOLVED_FAILED);
            log.error("解析失败 {}", e.getMessage());
        } finally {
            resourceFeign.save(resource);
        }
    }

    /**
     * 对识别出的实体进行处理
     *
     * @param entityList    实体列表
     * @param newEntityList 处理后的实体列表
     * @param propertyList  处理后的属性列表
     */
    @Override
    public void processEntity(List<RuleParsingEntity> entityList, List<RuleParsingEntity> newEntityList, List<RuleParsingEntity> propertyList) {
        for (RuleParsingEntity ruleParsingEntity : entityList) {
            List<String> valueList = new ArrayList<>();
            // 不对会议议题和纪要进行处理
            if ("会议议题".equals(ruleParsingEntity.getSchema()) || "纪要".equals(ruleParsingEntity.getSchema())) {
                newEntityList.add(ruleParsingEntity);
                continue;
            }
            String[] split = ruleParsingEntity.getValue().split("、");
            for (String s : split) {
                String[] split1 = s.split("，");
                valueList.addAll(List.of(split1));
            }
            // 裁切
            List<String> newValueList = valueList.stream()
                    .map(str -> str.replace("。", "")
                            .replace("：", ""))
                    .collect(Collectors.toList());
            // 如果是包含特定实体的集合，进行特殊处理
            if (valueList.stream().anyMatch(staticEntitySet::contains)) {
                newValueList.forEach(str -> {
                    RuleParsingEntity newRuleParsingEntity = new RuleParsingEntity();
                    newRuleParsingEntity.setUuid(ruleParsingEntity.getUuid());
                    newRuleParsingEntity.setType(LabelConstant.CONCEPT);
                    newRuleParsingEntity.setValue(str);
                    // 对特定的实体进行处理
                    if (staticEntitySet.contains(str)) {
                        newRuleParsingEntity.setSchema(LabelConstant.PEOPLE);
                    } else {
                        newRuleParsingEntity.setSchema(LabelConstant.DEPARTMENT);
                        newEntityList.add(newRuleParsingEntity);
                    }

                });
            } else if (ruleParsingEntity.getType().equals("属性")) {
                // 存储属性
                propertyList.add(ruleParsingEntity);
            } else {
                newValueList.forEach(str -> {
                    // 其它实体直接存储
                    RuleParsingEntity newRuleParsingEntity = new RuleParsingEntity();
                    newRuleParsingEntity.setUuid(ruleParsingEntity.getUuid());
                    newRuleParsingEntity.setType(ruleParsingEntity.getType());
                    newRuleParsingEntity.setValue(str);
                    newRuleParsingEntity.setSchema(ruleParsingEntity.getSchema());
                    newEntityList.add(newRuleParsingEntity);
                });
            }
        }
    }


    /**
     * 规则解析
     *
     * @param resource 资源对象
     * @return 解析接轨
     */
    public JSONObject ruleProcess(Resource resource) {
        JSONObject result = JSONUtil.createObj();
        //获取资源文件路径
        String pdfLink = minioLinkProvider.getMinioLinkIntranet(resource.getPdfAttachName());
        if (pdfLink == null) return result;
        try (InputStream inputStream = NetWorkFileUtil.urlToInputStream(pdfLink);
             PDDocument helloDocument = PDDocument.load(inputStream)) {
            String[] lines = extractContent(helloDocument);
            // 抽取“年度”和“期数”
            JSONObject object = fromFrontToBack(lines);
            // 解析获取“会议”名称和会议的相关属性
            JSONObject meeting = extractMeeting(helloDocument, object,resource);
            // 抽取参加、列席、记录、发送
            JSONObject backContent = fromBackToFront(lines, meeting);
            // 抽取会议地点和会议时间（同时抽取会议议题）
            JSONObject timeAndTopic = extractTimeAndTopic(resource, helloDocument, meeting);
            // 提取纪要
            JSONObject minutes = extractMinutes(helloDocument, meeting, timeAndTopic);
            // 合并结果
            result = mergeResult(backContent, meeting, timeAndTopic, minutes);
            return result;
        } catch (Exception e) {
            e.printStackTrace();
        }
        return result;
    }

    /**
     * 抽取会议地点（选抽）、会议时间、会议议题
     */
    private JSONObject extractTimeAndTopic(Resource resource, PDDocument helloDocument, JSONObject meeting) {
        JSONObject results = initJSONObject();
        JSONArray result = results.getJSONArray("result");
        JSONArray relation = results.getJSONArray("relation");

        // 通过附件抽取
        Map<Predicate<String>, Integer> conditions = new LinkedHashMap<>();
        conditions.put(s -> s.contains("会议通知"), 1);
        conditions.put(s -> s.contains("呈批单"), 2);
        // 获取会议的 uuid
        String meetingUUID = "0";
        JSONArray meetingJSONArray = meeting.getJSONArray("result");
        // 获取会议对象
        for (JSONObject jsonObject : meetingJSONArray.jsonIter()) {
            if ("会议".equals(jsonObject.getStr("schema"))) {
                meetingUUID = jsonObject.getStr("uuid");
            }
        }
        // 先判断主文件是否存在“呈批单”附件
        R<List<ResourceAnnex>> resourceAnnexListR = resourceAnnexFeign.listByResourceId(resource.getId());
        List<ResourceAnnex> resourceAnnexList = resourceAnnexListR.getData();
        boolean flag = true;
        if (resourceAnnexListR.isSuccess() && ObjectUtil.isNotEmpty(resourceAnnexList)) {
            for (ResourceAnnex resourceAnnex : resourceAnnexList) {
                for (Map.Entry<Predicate<String>, Integer> entry : conditions.entrySet()) {
                    Predicate<String> condition = entry.getKey();
                    if (condition.test(resourceAnnex.getName())){
                        // 处理呈批单，修改状态
                        flag = false;
                        String pdfLink = minioLinkProvider.getMinioLinkIntranet(resourceAnnex.getPdfAttachName());
                        if (pdfLink == null) return meeting;
                        try (InputStream inputStream = NetWorkFileUtil.urlToInputStream(pdfLink);
                             PDDocument document = PDDocument.load(inputStream)) {
                            // 获取附件的每页
                            List<List<String>> annexPages = parseEachParagraphWithPage(document);
                            // 抽取 “会议时间”、“会议地点”
                            for (List<String> annexPage : annexPages) {
                                for (String s : annexPage) {
                                    // 会议时间
                                    if (s.startsWith("时间：")) {
                                        String[] split = s.split("时间：");
                                        String time = split[1];
                                        String uuid = IdUtil.simpleUUID();
                                        result.add(newEntity(uuid, "属性", "会议时间", time));
                                        relation.add(newRelation("属性", meetingUUID, uuid));
                                    }
                                    // 会议地点
                                    if (s.startsWith("地点")) {
                                        String[] split = s.split("地点：");
                                        String place = split[1];
                                        String uuid = IdUtil.simpleUUID();
                                        result.add(newEntity(uuid, "属性", "会议地点", place));
                                        relation.add(newRelation("属性", meetingUUID, uuid));
                                    }
                                }
                            }
                            // 通过呈批单抽取会议议题
                            List<String> pages = parseEachPage(document);
                            StringBuilder str = new StringBuilder();
                            pages.forEach(str::append);
                            // 匹配议题
                            Pattern pattern = Pattern.compile("(?<=、(审议|听取|提出|传达)).*?(?=。)");
                            Matcher matcher = pattern.matcher(str);
                            while (matcher.find()) {
                                String uuid = IdUtil.simpleUUID();
                                result.add(newEntity(uuid, "概念", "会议议题", matcher.group()));
                                relation.add(newRelation("提出议题", meetingUUID, uuid));
                                //relation.add(newRelation("提及议题", "0", uuid));
                            }
                        } catch (Exception e) {
                            return results;
                        }
                        break;
                    }
                }
            }
        }
        // 如果不存在呈批单
        if (flag) {
            // 不存在呈批单，从文本中抽取内容
            List<String> paragraphList = parseEachPage(helloDocument);
            // 获取第一页
            String first = paragraphList.get(0);
            return getMeetingDateAndCompereAndYT(first, meeting);
        }
        return results;
    }

    /**
     * 解析获取“会议”名称和会议的相关属性
     *
     * @param helloDocument 文档
     * @param resource
     * @return 会议
     */
    private JSONObject extractMeeting(PDDocument helloDocument, JSONObject property, Resource resource) {
        JSONObject entries = initJSONObject();
        JSONArray result = entries.getJSONArray("result");
        JSONArray relation = entries.getJSONArray("relation");
        // 获取每段
        List<String> paragraphList = parseEachPage(helloDocument);
        // 获取会议名称、主持人、会议期数、会议类型
        getMeetingCompereIssues(property, entries, result, relation, paragraphList,resource);
        // 返回结果
        return entries;
    }

    /**
     * 抽取纪要和其相关属性
     */
    private JSONObject extractMinutes(PDDocument document, JSONObject meeting, JSONObject timeAndTopic) {
        String start = "0";
        JSONObject meetingJSONObject = null;
        // 获取会议
        JSONArray meetingJSONArray = meeting.getJSONArray("result");
        for (JSONObject jsonObject : meetingJSONArray.jsonIter()) {
            if ("会议".equals(jsonObject.getStr("schema"))) {
                meetingJSONObject = jsonObject;
                start = jsonObject.getStr("uuid");
            }
        }
        JSONObject entries = initJSONObject();
        JSONArray result = entries.getJSONArray("result");
        JSONArray relation = entries.getJSONArray("relation");
        String text;
        try {
            PDFTextStripper textStripper = new PDFTextStripper();
            text = textStripper.getText(document);
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
        // 获取pdf页数
        int numberOfPages = document.getNumberOfPages();
        List<String> conditions = new ArrayList<>();
        for (int i = 1; i <= numberOfPages; i++) {
            if (i == numberOfPages) {
                conditions.add("-" + i + "-");
            } else {
                conditions.add("-" + i + "一");
            }
        }
        // 把整个文档合并为一段文本
        String currentParagraph = text.replaceAll("\r\n", "");// 按照换行符进行段落拆分
        // 去掉所有页码
        for (String condition : conditions) {
            currentParagraph = currentParagraph.replaceAll(condition, "");
        }
        // 对特殊字符或错误字符进行处理
        currentParagraph = processStr(currentParagraph);
        // 获取纪要的所有内容
        String[] split = currentParagraph.split("纪要如下：");
        if (split.length == 1) {
            split = currentParagraph.split("如下事项：");
        }
        if (split.length > 1) {
            currentParagraph = split[1];
        }
        currentParagraph.split("、");
        // 匹配每个标签内容 "一、"
        Pattern pattern = Pattern.compile("(一、|二、|三、|四、|五、|六、|七、|八、|九、|十、|1、|2、|3、|4、|5、|6、|7、|8、|9、|10、)(.*?)((?=一、|二、|三、|四、|五、|六、|七、|八、|九、|十、|1、|2、|3、|4、|5、|6、|7、|8、|9、|10、)|$)", Pattern.DOTALL);
        Matcher matcher = pattern.matcher(currentParagraph);
        while (matcher.find()) {
            // 获取每一段的第一句话，作为纪要名称
            String textStr = matcher.group();
            String[] split1 = textStr.split("。");
            // 纪要名称
            String name = split1[0];
            String minutesUUID = IdUtil.simpleUUID();
            result.add(newEntity(minutesUUID, "概念", "纪要", name));
            relation.add(newRelation("提及纪要", start, minutesUUID));
            //relation.add(newRelation("提及纪要", "0", minutesUUID));
            // 设置纪要的属性
            // 1、纪要内容
            String uuid1 = IdUtil.simpleUUID();
            result.add(newEntity(uuid1, "属性", "纪要内容", textStr));
            relation.add(newRelation("属性", minutesUUID, uuid1));
            // 2、会议时间
            JSONArray timeAndTopicJSONArray = timeAndTopic.getJSONArray("result");
            for (JSONObject jsonObject : timeAndTopicJSONArray.jsonIter()) {
                if ("会议时间".equals(jsonObject.getStr("schema"))) {
                    String uuid = IdUtil.simpleUUID();
                    result.add(newEntity(uuid, "属性", "会议时间", jsonObject.getStr("value")));
                    relation.add(newRelation("属性", minutesUUID, uuid));
                }
            }
            // 3、所属会议
            String uuid2 = IdUtil.simpleUUID();
            result.add(newEntity(uuid2, "属性", "所属会议", Opt.ofNullable(meetingJSONObject.getStr("value")).orElse("暂无")));
            relation.add(newRelation("属性", minutesUUID, uuid2));
            // 4、顺序
            String[] split2 = name.split("、");
            String uuid3 = IdUtil.simpleUUID();
            result.add(newEntity(uuid3, "属性", "顺序", split2[0]));
            relation.add(newRelation("属性", minutesUUID, uuid3));
        }
        return entries;
    }

    /**
     * 获取会议名称、主持人、会议期数
     *
     * @param property      抽取出的“年度”和“期数”
     * @param entries       结果集Object
     * @param result        实体
     * @param relation      关系
     * @param paragraphList 段落
     * @param resource
     */
    private void getMeetingCompereIssues(JSONObject property, JSONObject entries, JSONArray result, JSONArray relation, List<String> paragraphList, Resource resource) {
        String meetingName = null;
        String compere = null;
        // 会议类型
        JSONObject meetingType = null;
        // 获取第一段内容
        if (ObjectUtil.isNotEmpty(paragraphList)) {
            String first = paragraphList.get(0);
            // 裁切出每一句话
            String[] sentences = first.split("[，。]");
            for (String sentence : sentences) {
                String str = sentence.trim();
                // 1、解析会议名称、主持人、会议期数
                Pattern pattern = Pattern.compile("(?<name>\\S+)(" + String.join("|", compareList) + ").*召开(?<meeting>\\S+)");
                Matcher matcher = pattern.matcher(str);
                if (matcher.find()) {
                    // 主持人
                    compere = matcher.group("name");
                    String[] split = compere.split("？");
                    if (split.length>1){
                        compere=split[1];
                    }
                    // 会议名称
                    meetingName = matcher.group("meeting");
                    if (meetingName.startsWith("了4")) meetingName = meetingName.replace("了4", "");
                    JSONArray result1 = property.getJSONArray("result");
                    String year = "";
                    String issues = "";
                    for (JSONObject jsonObject : result1.jsonIter()) {
                        String schema = jsonObject.getStr("schema");
                        if ("年度".equals(schema)) {
                            year = jsonObject.getStr("value") + "年";
                        }
                        if ("期数".equals(schema)) {
                            issues = "第" + jsonObject.getStr("value") + "期";
                            jsonObject.putOpt("value",(year+issues).replaceAll(" ",""));

                        }
                    }
                    // 设置其它会议名称
                    meetingName = "中共中国华能集团有限公司" + meetingName + (StrUtil.isNotBlank(year) ? "-" + year : "") + issues;
                    // 1、党组会抽取
                    // TODO 其它会议类型
                    if (meetingName.contains(MeetingTypeEnum.DANG_ZU.getPropertyValue())) {
                        // 添加会议类型 “党组会”
                        meetingType = newEntity(IdUtil.simpleUUID(), LabelConstant.PROPERTY, "会议类型", "党组会");
                        result.add(meetingType);

                    }
                    // 2、总经理办公会
                    if (meetingName.contains("总经理")) {
                        meetingType = newEntity(IdUtil.simpleUUID(), LabelConstant.PROPERTY, "会议类型", "总经理办公会");
                        result.add(meetingType);
                    }
                    // 3、专题会议
                    if (meetingName.contains(MeetingTypeEnum.ZHUAN_TI.getPropertyValue())) {
                        meetingType = newEntity(IdUtil.simpleUUID(), LabelConstant.PROPERTY, "会议类型", "专题会议纪要");
                        result.add(meetingType);
                    }else {
                        if (paragraphList.get(0).contains("专题")) {
                            meetingType = newEntity(IdUtil.simpleUUID(), LabelConstant.PROPERTY, "会议类型", "专题会议纪要");
                            result.add(meetingType);
                        }
                    }
                    // 4、小组会议
                    if (meetingName.contains(MeetingTypeEnum.ZHUAN_TI.getPropertyValue())) {
                        meetingType = newEntity(IdUtil.simpleUUID(), LabelConstant.PROPERTY, "会议类型", "生产经营领导小组会议纪要");
                        result.add(meetingType);
                    }

                    break;
                }
            }
        }

        // 新增实体
        JSONObject meetingObject = newEntity(IdUtil.simpleUUID(), LabelConstant.CONCEPT, "会议", Opt.ofNullable(meetingName).orElse(getYearFromContent(resource.getName())));
        JSONObject compereObject = newEntity(IdUtil.simpleUUID(), LabelConstant.CONCEPT, "人员", Opt.ofNullable(compere).orElse(""));
        result.add(meetingObject);
        //result.add(compereObject);
        // 新增关系
       // relation.add(newRelation("提及会议", "0", meetingObject.getStr("uuid")));
        relation.add(newRelation("主持人", meetingObject.getStr("uuid"), compereObject.getStr("uuid")));
        // 添加会议类型
        if (meetingType != null) {
            relation.add(newRelation("属性", meetingObject.getStr("uuid"), meetingType.getStr("uuid")));
        }
        // 完善会议的属性
        String qishuUUID = null;
        JSONArray propertyJSONArray = property.getJSONArray("result");
        if (propertyJSONArray != null) {
            for (JSONObject jsonObject : propertyJSONArray.jsonIter()) {
                if ("期数".equals(jsonObject.getStr("schema"))) {
                    jsonObject.putOpt("schema", "会议期数");
                    qishuUUID = jsonObject.getStr("uuid");
                    result.add(jsonObject);
                }
            }
        }
        JSONArray propertyJSONArray1 = property.getJSONArray("relation");
        if (propertyJSONArray1 != null) {
            for (JSONObject jsonObject : propertyJSONArray1.jsonIter()) {
                if (qishuUUID != null && qishuUUID.equals(jsonObject.getStr("end"))) {
                    jsonObject.putOpt("start", meetingObject.getStr("uuid"));
                    relation.add(jsonObject);
                }
            }
        }
        entries.putOpt("result", result);
        entries.putOpt("relation", relation);
    }

    /**
     * 抽取“年度”和“期数”
     */
    public JSONObject fromFrontToBack(String[] lines) {
        int lineLength = lines.length;
        int maxRead = Math.min(lineLength, 10);
        JSONObject obj = initJSONObject();
        for (int i = 0; i < lines.length; i++) {
            if (!obj.getJSONArray("result").isEmpty() || i > maxRead) break;
            getYearFromContent(lines[i], obj);
        }
        return obj;
    }

    /**
     * 抽取参加、列席、记录、发送
     */
    public JSONObject fromBackToFront(String[] lines, JSONObject meeting) {
        JSONObject result = initJSONObject();
        JSONArray persons = result.getJSONArray("result");
        JSONArray relations = result.getJSONArray("relation");
        int lineLength = lines.length;
        int maxRead = Math.min(lineLength, 20);
        int lastRecord = 0;
        for (int i = 1; i <= maxRead; i++) {
            String line = lines[lineLength - i];
            if (getPage(line) != null) continue;
            String relation = isPersonRelation(line);
            if (relation == null) continue;
            if (personTypeSet.contains(relation)) {
                log.info("抽到关系：【{}】", relation);
                //key:公司名  value：在职人员
                Map<String, String> map = new HashMap<>();
                String companyName = " ";
                for (int t = i; t > lastRecord; t--) {
                    if (getPage(lines[lineLength - t]) != null) continue;
                    lines[lineLength - t] = lines[lineLength - t].replace(" ", "").replace(relation, "");
                    if (StrUtil.isBlank(lines[lineLength - t])) continue;
                    String s = isPersonRelation(lines[lineLength - t]);
                    if (s != null && !personTypeSet.contains(s)) {
                        companyName = s;
                        lines[lineLength - t] = lines[lineLength - t].replace(s, "");
                    }
                    if (map.containsKey(companyName)) {
                        map.put(companyName, map.get(companyName) + lines[lineLength - t]);
                    } else {
                        map.put(companyName, lines[lineLength - t]);
                    }
                }
                for (Map.Entry<String, String> stringStringEntry : map.entrySet()) {
                    String uuid = IdUtil.simpleUUID();
                    String peopleValue = stringStringEntry.getValue();
                    // 对特殊字符、错误字符进行处理
                    peopleValue = peopleValue.replaceAll("）11", "川");
                    JSONObject person = newEntity(uuid, LabelConstant.CONCEPT, LabelConstant.PEOPLE, peopleValue);
                    // 创建关系Object
                    // 判断关系的起点
                    String relationName = relation.replace("：", "");
                    String start = tripleMap.get(relationName);
                    if ("公文".equals(start)) start = "0";
                    if ("会议".equals(start)) {
                        // 获取会议
                        JSONArray meetingJSONArray = meeting.getJSONArray("result");
                        for (JSONObject jsonObject : meetingJSONArray.jsonIter()) {
                            if ("会议".equals(jsonObject.getStr("schema"))) {
                                start = jsonObject.getStr("uuid");
                            }
                        }
                    }
                    JSONObject relationObject =
                            newRelation(
                                    personTypeMap.getOrDefault(relationName
                                            , relation.replace("：", ""))
                                    , start
                                    , uuid);
                    relations.add(relationObject);
                    if (StrUtil.isNotBlank(stringStringEntry.getKey())) {
                        String uuid1 = IdUtil.simpleUUID();
                        persons.add(newEntity(uuid1, LabelConstant.CONCEPT, LabelConstant.DEPARTMENT, stringStringEntry.getKey()));
                        relations.add(newRelation("就职于", uuid, uuid1));
                    }

                }
                lastRecord = i;
            }
            //抽到参加就不再往前抽了
            if ("参加：".equals(relation)) i += 100;
        }
        return result;
    }

    /**
     * 如果资源不存在呈批单，则从主文件文中提取 会议时间、议题
     *
     * @param line    "2021年1月4日，舒印彪同志主持召开党组会议。审议了xxx；"
     * @param meeting 会议对象
     * @return 年：2021  月：1 日：4 议题：xxx
     */
    private JSONObject getMeetingDateAndCompereAndYT(String line, JSONObject meeting) {
        String start = "0";
        // 获取会议
        JSONArray meetingJSONArray = meeting.getJSONArray("result");
        for (JSONObject jsonObject : meetingJSONArray.jsonIter()) {
            if ("会议".equals(jsonObject.getStr("schema"))) {
                start = jsonObject.getStr("uuid");
            }
        }
        JSONObject result = initJSONObject();
        String regex1 = "([0-9]{4})\\s?年\\s?([0-9]{1,2})\\s?月\\s?([0-9]{1,2})\\s?日\\s?，\\s?(.+)同志主持";
        Pattern pattern1 = Pattern.compile(regex1);
        Matcher matcher = pattern1.matcher(line);
        JSONArray entities = result.getJSONArray("result");
        JSONArray relations = result.getJSONArray("relation");
        if (matcher.find()) {
            String uuid = IdUtil.simpleUUID();
            String hui_yi_shi_jian = matcher.group(1) + "年" + matcher.group(2) + "月" + matcher.group(3) + "日";
            entities.add(newEntity(uuid, "属性", "会议时间", hui_yi_shi_jian));
            relations.add(newRelation("属性", start, uuid));

        }else {
            String regex = "(\\d{4})年(\\d{1,2})月(\\d{1,2})日";
            Pattern pattern = Pattern.compile(regex);
            Matcher matcher1 = pattern.matcher(line);
            if (matcher1.find()){
                String uuid = IdUtil.simpleUUID();
                entities.add(newEntity(uuid,"属性","会议时间",matcher1.group()));
                relations.add(newRelation("属性",start,uuid));
            }
        }
        String regex2 = "(?<condition>会议传达|传达|会议审议|审议了|审议|会议提出|提出|会议听取|听取)(.*?)(?=；|。)";
        Pattern pattern2 = Pattern.compile(regex2);
        String[] split1 = line.split("纪要如下：");
        if (split1.length > 0) line = split1[0];
        String[] split = line.split("(?<=[。；])");
        for (String s : split) {
            Matcher matcher2 = pattern2.matcher(s);
            while (matcher2.find()) {
                String uuid = IdUtil.simpleUUID();
                entities.add(newEntity(uuid, LabelConstant.CONCEPT, "会议议题", matcher2.group(2)));
                String condition = matcher2.group("condition");
                if ("会议传达".equals(condition)) {
                    condition = "传达";
                } else if ("会议审议".equals(condition) || "审议了".equals(condition) || "审议".equals(condition)) {
                    condition = "审议";
                } else if ("会议提出".equals(condition)) {
                    condition = "提出";
                } else if ("会议听取".equals(condition)) {
                    condition = "听取";
                }
                relations.add(newRelation(condition, start, uuid));
            }
        }

        return result;
    }

    /**
     * 解析pdf获取每一页内容
     *
     * @param document 文档对象
     * @return 每一页的内容
     */
    public List<String> parseEachPage(PDDocument document) {
        List<String> paragraphList = new ArrayList<>();
        try {
            PDFTextStripper textStripper = new PDFTextStripper();
            // 设置按段落提取文本
            textStripper.setSortByPosition(true);
            int totalPages = document.getNumberOfPages();
            for (int pageNum = 1; pageNum <= totalPages; pageNum++) {
                textStripper.setStartPage(pageNum);
                textStripper.setEndPage(pageNum);
                String pageText = textStripper.getText(document);
                // 将文本按段落切分（一页）
                String[] paragraphs = pageText.split("\r\n");
                StringBuilder stringBuilder = new StringBuilder();
                for (String paragraph : paragraphs) {
                    stringBuilder.append(paragraph);
                }
                // 遍历每一段文本
                paragraphList.add(stringBuilder.toString().replaceAll(" ", ""));
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        return paragraphList;
    }

    /**
     * 获取每一页的所有段落
     *
     * @param document 文档对象
     * @return 每一页的内容
     */
    public List<List<String>> parseEachParagraphWithPage(PDDocument document) {
        ArrayList<List<String>> result = new ArrayList<>();
        try {
            PDFTextStripper textStripper = new PDFTextStripper();
            // 设置按段落提取文本
            textStripper.setSortByPosition(true);
            int totalPages = document.getNumberOfPages();
            for (int pageNum = 1; pageNum <= totalPages; pageNum++) {
                textStripper.setStartPage(pageNum);
                textStripper.setEndPage(pageNum);
                String pageText = textStripper.getText(document);
                // 一页的所有段落
                String[] paragraphs = pageText.split("\r\n");
                // 添加每一页的所有段落
                result.add(List.of(paragraphs));
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        return result;
    }

    /**
     * 对特殊字符或错误字符进行处理
     */
    @NotNull
    private static String processStr(String str) {
        str = str.replaceAll(" ", "")
                .replaceAll("）11", "川")
                .replaceAll("（〈", "《")
                .replaceAll("l、", "1、");
        return str;
    }

    /**
     * 获取页码
     *
     * @param line 段落内容
     * @return 页码
     */
    public static String getPage(String line) {
        String s = line.replace(" ", "");
        //匹配以”-“或”一“开头，后面是1-3位长度的数字，再后面是”-“或”一“的段落
        String regex = "(?<=^[-,—,一])[0-9]{1,3}(?=[-,—,一])";
        Pattern pattern = Pattern.compile(regex);
        Matcher matcher = pattern.matcher(s);
        if (matcher.find()) {
            return matcher.group();
        }
        return null;
    }

    public static String isPersonRelation(String line) {
        line = StrUtil.trim(line);
        if (line.contains("：")) {
            return StrUtil.removeAll(line.split("：")[0] + "：", " ");
        }
        return null;
    }

    /**
     * 获得会议纪要会议名称
     */
    public static String getYearFromContent(String line) {

        int dotIndex = line.lastIndexOf(".");
        String result;
        if (dotIndex >= 0) {
            result = line.substring(0, dotIndex);
            String patternString = "(?:正文-)?(.*?会议)";
            Pattern pattern = Pattern.compile(patternString);
            Matcher matcher = pattern.matcher(result);

            if (matcher.find()) {
                return matcher.group(1);
            } else {
                return "";
            }
        }else {
            return "";
        }


    }
}
