package org.irm.lab.kg.rabbitmq.annexIeInfer;

import java.io.IOException;
import java.net.URLEncoder;
import java.util.List;
import java.util.concurrent.atomic.AtomicReference;
import java.util.stream.Collectors;

import javax.annotation.Resource;

import org.irm.lab.common.utils.ThreadLocalUtil;
import org.irm.lab.kg.dto.ie.req.IeInferReqDTO;
import org.irm.lab.kg.dto.ie.resp.IeInferRespDTO;
import org.irm.lab.kg.entity.annex.AnnexDocumentUnit;
import org.irm.lab.kg.repository.annex.processing.AnnexDocumentUnitRepository;
import org.irm.lab.kg.service.IKgResourceService;
import org.irm.lab.kg.service.forest.AIMiddlePlatformService;
import org.irm.lab.kg.service.impl.KgResourceServiceImpl;
import org.irm.lab.kg.service.impl.kgprocess.tag.KnowledgeTagAlgorithm;
import org.irm.lab.kg.vo.ie.IeInferItem;
import org.irm.lab.kg.vo.ie.IeInferRelation;
import org.irm.lab.repository.constant.KnowledgeTagStatus;
import org.irm.lab.repository.entity.ResourceAnnex;
import org.irm.lab.repository.repository.ResourceAnnexRepository;
import org.irm.lab.repository.repository.ResourceRepository;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.amqp.core.Message;
import org.springframework.amqp.rabbit.annotation.Exchange;
import org.springframework.amqp.rabbit.annotation.Queue;
import org.springframework.amqp.rabbit.annotation.QueueBinding;
import org.springframework.amqp.rabbit.annotation.RabbitHandler;
import org.springframework.amqp.rabbit.annotation.RabbitListener;
import org.springframework.amqp.rabbit.config.SimpleRabbitListenerContainerFactory;
import org.springframework.amqp.rabbit.connection.ConnectionFactory;
import org.springframework.context.annotation.Bean;
import org.springframework.stereotype.Component;

import com.alibaba.fastjson.JSON;
import com.rabbitmq.client.Channel;

import cn.hutool.core.convert.Convert;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * 在重新解析文档的时候先删除掉源文件类型为word的转成pdf的原件,重新转换，然后再进行ocr解析

 */
@Slf4j
@Component
public class AnnexIeInferConsumer {

    @Resource
    private IKgResourceService kgResourceServiceImpl;


    private static Logger logger = LoggerFactory.getLogger(AnnexIeInferConsumer.class);
    @Resource
    private AIMiddlePlatformService aiMiddlePlatformService;

    @Resource
    private org.springframework.context.ApplicationContext applicationContext;
    @Resource
    private  ResourceRepository resourceRepository;
    @Resource
    private  ResourceAnnexRepository resourceAnnexRepository;
    @Resource
    private  AnnexDocumentUnitRepository annexDocumentUnitRepository;



    @RabbitListener(bindings = @QueueBinding(
            value = @Queue(AnnexIeInferMessage.QUEUE + "${spring.application.name}"),
            exchange = @Exchange(name = AnnexIeInferMessage.EXCHANGE),
            key = AnnexIeInferMessage.ROUTING_KEY + "${spring.application.name}"
    ),concurrency = "10", containerFactory = "rabbitListenerContainerFactory")
    @RabbitHandler
    public void onMessage(AnnexIeInferMessage message, Channel channel, Message messageStatus) throws IOException {
        // 获取消息状态信息
        long deliveryTag = messageStatus.getMessageProperties().getDeliveryTag();
        log.info("[onMessage][线程编号:{} 消息内容：{}]", Thread.currentThread().getId(), message);

        ThreadLocalUtil.set("user", message.getUser());
        long timeStart = DateUtil.current();

        // 判断主文件是否是

        try {

            final IeInferReqDTO ieInferReqDTO = new IeInferReqDTO();

            ieInferReqDTO.setAnnex("1");
            final ResourceAnnex one = resourceAnnexRepository.findOne(message.getAnnexId());

            String name = one.getName();

            // 找到最后一个点的位置
            int index = name.lastIndexOf('.');

            if (index > 0) {
                name = name.substring(0, index);
            }

            String text = message.getText();

            String result = name + "\n" + text;

            log.info("副文件------------>发送的数据为{}",result);

            ieInferReqDTO.setText(URLEncoder.encode(result));


            JSONArray inputArray = new JSONArray(message.getSchemas());
            JSONObject mergedObject = new JSONObject();

            // 遍历输入的JSON数组，合并所有对象
            for (int i = 0; i < inputArray.size(); i++) {
                JSONObject currentObject = inputArray.getJSONObject(i);
                for (String key : currentObject.keySet()) {

                    if (ObjectUtil.equals(key, "人员")) {
                        currentObject.getJSONArray(key).remove("办理公文");
                    }

                    mergedObject.put(key, currentObject.getJSONArray(key));
                }
            }

            // 创建包含合并后对象的新JSON数组
            JSONArray outputArray = new JSONArray();
            outputArray.put(mergedObject);
            final String classifyByResourceOrAnnexId = kgResourceServiceImpl.getClassifyByResourceOrAnnexId(message.getAnnexId());
            ieInferReqDTO.setSchemas(classifyByResourceOrAnnexId);
            //ieInferReqDTO.setSchemas(outputArray.toString());
            ieInferReqDTO.setType(2);
            ieInferReqDTO.setTenant("100001");

            // 调用实体抽取
            log.info("当前的schemas{}", ieInferReqDTO.getSchemas());
            log.info("当前的语料{}", ieInferReqDTO.getText());

            //
            final String entityExtractionResult = aiMiddlePlatformService.inferApi(ieInferReqDTO);

            log.info("开始处理附件：" + message);

            JSONObject resultJson = JSONUtil.parseObj(entityExtractionResult);

            final String resultCode = Convert.toStr(resultJson.get("code"));


            if (!"200".equals(resultCode) ) {
                KnowledgeTagAlgorithm annexKnowledgeTagAlgorithm = applicationContext.getBean("annexKnowledgeTag", KnowledgeTagAlgorithm.class);
                annexKnowledgeTagAlgorithm.recognitionStatus(message.getAnnexId(), KnowledgeTagStatus.TAG_FAILED);
                log.error("附件文件算法识别失败！");
                throw new RuntimeException("附件实体抽取失败！");
            }

            final String corpusId = message.getCorpusId();
            final String str = Convert.toStr(resultJson.get("data"));
            final IeInferRespDTO ieInferRespDTO = JSON.parseObject(str, IeInferRespDTO.class);

            log.info("副文件------------>抽取出来的数据是{}",ieInferRespDTO);

            final List<IeInferRelation> relations = ieInferRespDTO.getRelations();

            final List<IeInferItem> results = ieInferRespDTO.getResults();
            AtomicReference<String> uuid = new AtomicReference<>("");
            final List<IeInferItem> inferItems = results.stream().filter(data -> {
                if (ObjectUtil.equals(data.getSchema(), "公文") || ObjectUtil.equals(data.getSchema(), "制度")) {
                    uuid.set(data.getUuid());
                    return false;
                }
                return true;
            }).collect(Collectors.toList());
            inferItems.forEach(data -> {

                if (data.getType().equals("属性")) {
                    data.setType("实体"); data.setSchema(null);
                }
            });

                /*relations.forEach(data -> {
                    if (ObjectUtil.equals(uuid.get(),data.getStart())){
                        data.setStart("0");
                    }
                });*/
            KnowledgeTagAlgorithm annexKnowledgeTagAlgorithm = applicationContext.getBean("annexKnowledgeTag", KnowledgeTagAlgorithm.class);
            annexKnowledgeTagAlgorithm.algorithmRecognition(corpusId,inferItems, relations);
            AnnexDocumentUnit annexDocumentUnit = annexDocumentUnitRepository.findById(corpusId);
            String json = JSONUtil.toJsonStr(ieInferRespDTO.getIePredicateResults());
            annexDocumentUnit.setAiContent(ieInferRespDTO.getContent());
            annexDocumentUnit.setAiInferResult(str);
            annexDocumentUnit.setIeInferResult(json);
            annexDocumentUnitRepository.save(annexDocumentUnit);
            log.info("<<<<<<<<<<<附件文件算法识别完毕<<<<<<<<<<<<<<");
            annexKnowledgeTagAlgorithm.recognitionStatus(message.getAnnexId(), KnowledgeTagStatus.TAG);

        }catch (Exception e) {

            if (messageStatus.getMessageProperties().getRedelivered()) {
                KnowledgeTagAlgorithm annexKnowledgeTagAlgorithm = applicationContext.getBean("annexKnowledgeTag", KnowledgeTagAlgorithm.class);
                annexKnowledgeTagAlgorithm.recognitionStatus(message.getAnnexId(), KnowledgeTagStatus.TAG_FAILED);

                // 打印错误信息
                log.error(e.toString());
                log.error("消息已重复处理失败，拒绝再次接收.....");

                channel.basicReject(deliveryTag, false);
            } else {
                log.error("[onMessage][线程编号:{} resourceId：{} 发生异常]", Thread.currentThread().getId(), message.getAnnexId(), e);

                log.info("消息即将再次返回队列中进行处理....");
                channel.basicNack(deliveryTag, false, true);
                log.error(e.toString());
            }
        }finally {
            logger.info("完成识别，总耗时：{}s--[线程编号:{}]", (DateUtil.current() - timeStart) / 1000, Thread.currentThread().getId());
            ThreadLocalUtil.remove();
        }
    }


}
