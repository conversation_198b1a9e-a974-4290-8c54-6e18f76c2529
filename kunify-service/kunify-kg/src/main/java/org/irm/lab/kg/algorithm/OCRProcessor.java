package org.irm.lab.kg.algorithm;

import cn.easyes.common.utils.LogUtils;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.mongodb.client.model.Filters;
import lombok.RequiredArgsConstructor;
import org.irm.ai.api.ocr.OcrApi;
import org.irm.lab.common.constant.ExceptionMessageConst;
import org.irm.lab.common.enums.AlgorithmType;
import org.irm.lab.common.exception.ServiceException;
import org.irm.lab.common.provider.MinioLinkProvider;
import org.irm.lab.common.utils.NetWorkFileUtil;
import org.irm.lab.common.utils.ThreadLocalUtil;
import org.irm.lab.front.dto.AIMiddle.OcrInferResultDTO;
import org.irm.lab.front.dto.AIMiddle.OcrItemDTO;
import org.irm.lab.front.dto.AIMiddle.OcrLocationDTO;
import org.irm.lab.front.feign.AIFeign;
import org.irm.lab.kg.config.AiSdkLink;
import org.irm.lab.kg.entity.annex.AnnexDocumentImage;
import org.irm.lab.kg.entity.annex.AnnexDocumentUnit;
import org.irm.lab.kg.entity.processing.ocr.Coordinate;
import org.irm.lab.kg.entity.processing.ocr.DocumentOCRResult;
import org.irm.lab.kg.entity.processing.ocr.DocumentOCRUnit;
import org.irm.lab.kg.rabbitmq.producer.OCRAnnexProducer;
import org.irm.lab.kg.rabbitmq.producer.OCRProducer;
import org.irm.lab.kg.repository.DocumentImageRepository;
import org.irm.lab.kg.repository.DocumentUnitRepository;
import org.irm.lab.kg.repository.annex.processing.AnnexDocumentImageRepository;
import org.irm.lab.kg.repository.annex.processing.AnnexDocumentUnitRepository;
import org.irm.lab.kg.service.IOfficeConvertService;
import org.irm.lab.repository.constant.DocumentResolvedStatus;
import org.irm.lab.repository.entity.Resource;
import org.irm.lab.repository.entity.ResourceAnnex;
import org.irm.lab.repository.feign.ResourceAnnexFeign;
import org.irm.lab.repository.feign.ResourceFeign;
import org.irm.lab.repository.repository.ResourceRepository;
import org.irm.lab.user.entity.User;
import org.springframework.mock.web.MockMultipartFile;
import org.springframework.stereotype.Service;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;
import org.springframework.web.multipart.MultipartFile;

import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;
import java.nio.file.Files;
import java.util.ArrayList;
import java.util.List;

import javax.servlet.http.HttpServletRequest;

/**
 * <AUTHOR>
 * @date 2023/1/30 16:40
 */
@Service
@RequiredArgsConstructor
public class OCRProcessor implements IAlgorithmProcessor {

    private final ResourceFeign resourceFeign;
    private final ResourceAnnexFeign resourceAnnexFeign;
    private final ResourceRepository resourceRepository;
    private final DocumentImageRepository documentImageRepository;
    private final DocumentUnitRepository documentUnitRepository;
    private final AnnexDocumentImageRepository annexDocumentImageRepository;
    private final AnnexDocumentUnitRepository annexDocumentUnitRepository;
    private final MinioLinkProvider minioLinkProvider;
    private final AIFeign aiFeign;
    private final OCRProducer ocrProducer;
    private final OCRAnnexProducer  ocrAnnexProducer;
    @javax.annotation.Resource(name = "pdfToPicServiceImpl")
    private IOfficeConvertService officeConvertService;
    @javax.annotation.Resource
    private HttpServletRequest request;


    @Override
    public AlgorithmType getCode() {
        return AlgorithmType.OCR;
    }

    @Override
    public void process(Resource resource) {

        List<DocumentImage> documentImages = documentImageRepository.findByConditionAndSorted(Filters.eq("resourceId", resource.getId()),
                Filters.eq("page", 1));
        if (ObjectUtil.isEmpty(documentImages)) {
            LogUtils.info("annexDocumentImages is empty,开始生成主文件件图片");
            officeConvertService.pdfToPic(resource);
            documentImages = documentImageRepository.findByConditionAndSorted(Filters.eq("resourceId", resource.getId()),Filters.eq("page", 1));

        }else {
            LogUtils.info("附件图片已生成无需重复生成");
        }
        if (documentImages == null || documentImages.isEmpty()) return;
        resource.setTextStripperStatus(ExceptionMessageConst.OCR_GEN_SUCCESS);
        try {

            int page = 1;
            for (int i= 0; i < documentImages.size(); i++) {
                if (i == documentImages.size()-1) {

                    ocrProducer.sendMessage(ThreadLocalUtil.get("user"), resource, documentImages.get(i), page, true);
                }else {
                    ocrProducer.sendMessage(ThreadLocalUtil.get("user"), resource, documentImages.get(i), page,false);
                }

                page++;
            }

        } catch (Exception e) {
            resource.setTextStripperStatus(ExceptionMessageConst.OCR_GEN);
            resource.setResolveStatus(DocumentResolvedStatus.RESOLVED_FAILED);
            throw new ServiceException(e);
        } finally {
            resourceRepository.save(resource);
        }
    }

    @Override
    public void annexProcess(ResourceAnnex resourceAnnex) {

        List<AnnexDocumentImage> annexDocumentImages = annexDocumentImageRepository.findByConditionAndSorted(Filters.eq("annexId", resourceAnnex.getId()),
                Filters.eq("page", 1));
        if (ObjectUtil.isEmpty(annexDocumentImages)) {
            LogUtils.info("annexDocumentImages is empty,开始生成附件图片");
            officeConvertService.annexPdfToPic(resourceAnnex);
            annexDocumentImages = annexDocumentImageRepository.findByConditionAndSorted(Filters.eq("annexId", resourceAnnex.getId()),Filters.eq("page", 1));

        }else {
            LogUtils.info("附件图片已生成无需重复生成");
        }
        resourceAnnex.setTextStripperStatus(ExceptionMessageConst.OCR_GEN_SUCCESS);

            int page = 1;
            for (int i= 0; i < annexDocumentImages.size(); i++) {
                if (i == annexDocumentImages.size()-1) {
                    ocrAnnexProducer.sendMessage(ThreadLocalUtil.get("user"), resourceAnnex, annexDocumentImages.get(i), page, true);
                }else {
                    ocrAnnexProducer.sendMessage(ThreadLocalUtil.get("user"), resourceAnnex, annexDocumentImages.get(i), page,false);
                }

                page++;
            }


    }
}
