package org.irm.lab.kg.algorithm.ruleMatching.lingdaopifu;

import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.log4j.Log4j2;
import org.apache.pdfbox.pdmodel.PDDocument;
import org.irm.lab.common.enums.AlgorithmType;
import org.irm.lab.common.provider.MinioLinkProvider;
import org.irm.lab.common.utils.NetWorkFileUtil;
import org.irm.lab.kg.algorithm.DocumentUnit;
import org.irm.lab.kg.algorithm.IAlgorithmProcessor;
import org.irm.lab.kg.algorithm.ruleMatching.RuleParsingEntity;
import org.irm.lab.kg.algorithm.ruleMatching.RuleParsingRelation;
import org.irm.lab.kg.algorithm.ruleMatching.RuleParsingResult;
import org.irm.lab.kg.constant.LabelConstant;
import org.irm.lab.kg.service.impl.kgprocess.ruleMatching.DocRuleParsing;
import org.irm.lab.repository.constant.DocumentResolvedStatus;
import org.irm.lab.repository.entity.Resource;
import org.irm.lab.repository.feign.ResourceFeign;
import org.springframework.stereotype.Service;

import java.io.InputStream;
import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

@Log4j2
@Service
@RequiredArgsConstructor
public class PI_FU_01 implements IAlgorithmProcessor {

    public static final Set<String> personTypeSet1 = new HashSet<>(List.of("签发：", "签发人："));
    public static final Set<String> personTypeSet2 = new HashSet<>(List.of("联系人："));
    public static final Set<String> fileTypeSet1 = new HashSet<>(List.of("签报编号：", "编号：", "文号："));
    public static final Set<String> fileTypeSet2 = new HashSet<>(List.of("印发"));
    public static final Set<String> instruction = new HashSet<>(List.of("当否，请批示。", "妥否，请示。"));

    @Override
    public AlgorithmType getCode() {
        return AlgorithmType.PDF_RULE_PI_FU_01;
    }

    private final ResourceFeign resourceFeign;
    private final MinioLinkProvider minioLinkProvider;
    @javax.annotation.Resource(name = "resourceDocRuleParsing")
    private DocRuleParsing docRuleParsing;
    private final org.irm.lab.kg.feign.DocumentUnitFeign documentUnitFeign;

    @Override
    public void process(Resource resource) {
        if (DocumentResolvedStatus.RESOLVED.equals(resource.getRuleStatus())) return;
        String processName = AlgorithmType.PDF_RULE_PI_FU_01.getName();
        log.info(">>>>>>>>>>【主文件】【{}】【规则解析】开始>>>>>>>>>>", processName);
        try {
            /*//修改文件状态为解析中
            resource.setRuleStatus(DocumentResolvedStatus.RESOLVING);
            resourceFeign.save(resource);
            // 获取规则解析结果
            JSONObject results = ruleProcess(resource);
            // 对结果转换为实体
            RuleParsingResult ruleParsingResult = JSONUtil.toBean(results, RuleParsingResult.class, true);
            // 存储处理后关系
            List<RuleParsingRelation> relationList = ruleParsingResult.getRelation();
            // 实例处理
            List<RuleParsingEntity> resultEntity = ruleParsingResult.getResult();
            // 存储处理后的实体
            List<RuleParsingEntity> entityList = new ArrayList<>();
            // 存储处理后的属性(实例的属性)
            List<RuleParsingEntity> propertyList = new ArrayList<>();
            filterEntity(resultEntity, entityList, propertyList, relationList);
            // 生成主文件缓存标签
            docRuleParsing.ruleParsingCacheLabelData(resource, entityList, propertyList, relationList);*/
            resource.setRuleStatus(DocumentResolvedStatus.RESOLVED);
            log.info("<<<<<<<<<<【主文件】【{}】【规则解析】结束<<<<<<<<<<", processName);
        } catch (Exception e) {
            log.error("解析失败 {}", e.getMessage());
        } finally {
            resourceFeign.save(resource);
        }
    }

    public void filterEntity(List<RuleParsingEntity> resultEntity, List<RuleParsingEntity> entityList, List<RuleParsingEntity> propertyList, List<RuleParsingRelation> relationList) {
        for (RuleParsingEntity ruleParsingEntity : resultEntity) {
            if ("概念".equals(ruleParsingEntity.getType())) {
                List<RuleParsingEntity> list = entityList.stream().filter(ent ->
                        ruleParsingEntity.getSchema().equals(ent.getSchema()) && ruleParsingEntity.getValue().equals(ent.getValue()) && ruleParsingEntity.getType().equals("概念")
                ).collect(Collectors.toList());
                if (ObjectUtil.isNotEmpty(list)) {
                    RuleParsingEntity ruleParsing = list.get(0);
                    relationList.stream().filter(ruleParsingRelation -> !ruleParsingRelation.getRelation().equals("属性"))
                            .forEach(ruleParsingRelation -> {
                                if (ruleParsingEntity.getUuid().equals(ruleParsingRelation.getEnd())) {
                                    ruleParsingRelation.setEnd(ruleParsing.getUuid());
                                } else if (ruleParsingEntity.getUuid().equals(ruleParsingRelation.getStart())) {
                                    ruleParsingRelation.setStart(ruleParsing.getUuid());
                                }
                            });
                } else {
                    entityList.add(ruleParsingEntity);
                }
            } else {
                List<RuleParsingEntity> list = propertyList.stream().filter(ent ->
                        ruleParsingEntity.getSchema().equals(ent.getSchema()) && ruleParsingEntity.getValue().equals(ent.getValue()) && ruleParsingEntity.getType().equals("属性")
                ).collect(Collectors.toList());
                if (ObjectUtil.isNotEmpty(list)) {
                    RuleParsingEntity ruleParsing = list.get(0);
                    relationList.stream().filter(ruleParsingRelation -> ruleParsingRelation.getRelation().equals("属性"))
                            .forEach(ruleParsingRelation -> {
                                if (ruleParsingEntity.getUuid().equals(ruleParsingRelation.getEnd())) {
                                    ruleParsingRelation.setEnd(ruleParsing.getUuid());
                                } else if (ruleParsingEntity.getUuid().equals(ruleParsingRelation.getStart())) {
                                    ruleParsingRelation.setStart(ruleParsing.getUuid());
                                }
                            });
                } else {
                    propertyList.add(ruleParsingEntity);
                }
            }
        }
    }

    /**
     * 获取规则解析结果
     *
     * @param resource 要获取解析结果的资源
     * @return JSONObject
     */
    public JSONObject ruleProcess(Resource resource) {
        JSONObject result = JSONUtil.createObj();
        //获取资源文件路径
        String pdfLink = minioLinkProvider.getMinioLinkIntranet(resource.getPdfAttachName());
        //若资源路径为空这集返回空结果
        if (pdfLink == null) return result;
        try (InputStream inputStream = NetWorkFileUtil.urlToInputStream(pdfLink);
             PDDocument document = PDDocument.load(inputStream)) {
            //提取文本信息
            String[] text= extractContent(document);
            //如果有编辑好的文本信息则用编辑后的，没有则是用的pdf直接解析的文本信息
            String[] lines = setLines(text,resource);
            //关系抽取
            JSONObject content = getFileResult(lines);
            result = mergeResult(content);
            return result;
        } catch (Exception e) {
            e.printStackTrace();
        }
        return result;
    }

    /**
     *  如果有编辑好的文本信息则用编辑后的，没有则是用的pdf直接解析的文本信息
     *
     * @param text
     * @param resource
     * @return
     */
    private String[] setLines(String[] text, Resource resource) {
        List<DocumentUnit> units = documentUnitFeign.findByResourceId(resource.getId()).getData();
        if (!units.isEmpty()) {
            units.sort((unit1, unit2) -> {
                Integer page1 = unit1.getPage();
                Integer page2 = unit2.getPage();
                // 检查是否为null
                if (page1 == null || page2 == null) {
                    return (page1 == null) ? 1 : -1; // 如果其中一个为null，将其视为最小或最大值（这里假设非null的页面号总是大于null）
                }
                if (!page1.equals(page2)) {
                    return page1 - page2; // 如果page1不等于page2，根据page1和page2的差值进行排序
                } else {
                    Integer sortInPage1 = unit1.getSortInCurrentPage();
                    Integer sortInPage2 = unit2.getSortInCurrentPage();
                    // 检查sortInCurrentPage是否为null，如同处理page那样
                    if (sortInPage1 == null || sortInPage2 == null) {
                        return (sortInPage1 == null) ? 1 : -1;
                    }
                    return sortInPage1 - sortInPage2; // 根据sortInPage1和sortInPage2的差值进行排序
                }
            });
            return units.stream().map(DocumentUnit::getContent).toArray(String[]::new);
        }
        return text;
    }

    /**
     * 关系抽取
     *
     * @param lines 文本信息
     * @return JSONObject
     */
    public JSONObject getFileResult(String[] lines) {
        JSONObject result = initJSONObject();
        JSONArray entities = result.getJSONArray("result");
        JSONArray relations = result.getJSONArray("relation");
        //获取文件名称
        // 添加附件（实例/关系）
        setAnnexRelation(lines, entities, relations);
        //添加人员概念关系
        setPersonRelation(lines, entities, relations);
        //添加公文属性
        setDocumentProperty(lines, entities, relations);
        //获取关联文件（实例/关系）
        setDocumentRelation(lines, entities, relations);
        //获取发文机构
        setDepartmentRelation(lines, entities, relations);
        return result;
    }

    /**
     * 抽取发文单位
     *
     * @param lines     文本内容
     * @param entities  JSONArray
     * @param relations JSONArray
     */
    public void setDepartmentRelation(String[] lines, JSONArray entities, JSONArray relations) {
        int index = 0;
        for (int i = 1; i < lines.length; i++) {
            String line = lines[i];
            line = line.replaceAll(" ", "");
            if (ObjectUtil.isEmpty(line)) continue;
            if (!instruction.contains(line)) continue;
            lines[i] = "";
            index = i;
            setDepartmentRelation(index, lines, entities, relations);
            break;
        }
    }

    public void setDepartmentRelation(Integer index, String[] lines, JSONArray entities, JSONArray relations) {
        if (index.equals(lines.length)) return;
        index = index + 1;
        String line = lines[index].replaceAll(" ", "");
        if (ObjectUtil.isEmpty(line)) {
            setDepartmentRelation(index, lines, entities, relations);
        } else {
            String nextLine = lines[index];
            if (nextLine.contains("日") && nextLine.contains("月") && nextLine.contains("年") && (nextLine.indexOf("日") > nextLine.indexOf("月"))) {
                lines[index] = "";
                int endIndex = line.lastIndexOf("年");
                line = line.substring(0, endIndex - 4);
                //生成随机id
                String uuid = IdUtil.simpleUUID();
                //生成新的实例，并新增
                JSONObject personJson = newEntity(uuid, LabelConstant.CONCEPT, LabelConstant.DEPARTMENT, line);
                relations.add(newRelation("发文单位", "0", uuid));
                entities.add(personJson);
            }
        }
    }

    /**
     * 添加公文的关联文件
     *
     * @param lines     文件信息
     * @param entities  JSONArray
     * @param relations JSONArray
     */
    public void setDocumentRelation(String[] lines, JSONArray entities, JSONArray relations) {
        List<String> documentList = new ArrayList<>();
        String document = "";
        //将文件组合成一个字符串
        for (int i = 1; i < lines.length; i++) {
            String line = lines[i];
            String page = getPage(line);
            if (page != null) line = " ";
            document = document + line;
        }
        if (ObjectUtil.isEmpty(document)) return;
        setDocument(document, documentList);
        if (ObjectUtil.isEmpty(documentList)) return;
        for (String doc : documentList) {
            if (ObjectUtil.isEmpty(doc)) return;
            //生成随机id
            String uuid = IdUtil.simpleUUID();
            //生成新的实例，并新增
            JSONObject personJson = newEntity(uuid, LabelConstant.CONCEPT, LabelConstant.DOCUMENT, doc);
            relations.add(newRelation("关联文件", "0", uuid));
            entities.add(personJson);
        }
    }

    /**
     * 递归查询所有关联文件
     *
     * @param document     文件名称
     * @param documentList 文件集合
     */
    public void setDocument(String document, List<String> documentList) {
        document = document.replaceAll(" ", "");
        if (!document.contains("《") || !document.contains("》")) return;
        String s = document.substring(document.indexOf("《"), document.indexOf("》") + 1);
        documentList.add(s);
        document = document.replace(s, " ");
        setDocument(document, documentList);
    }

    public void setAnnexRelation(String[] lines, JSONArray entities, JSONArray relations) {
        List<String> annexList = new ArrayList<>();
        int index = 0;
        String line;
        for (int i = 1; i < lines.length; i++) {
            line = lines[i];
            //跳过页码
            if (!line.contains("附件：")) continue;
            line = line.replaceAll(" ", "");
            int annexIndex = line.indexOf("附件：") + 3;
            String s = line.substring(annexIndex, annexIndex + 1);
            //抽取完毕后清除已抽取的文本内容
            lines[i] = "";
            if (!NumberUtil.isNumber(s)) {
                String onlyAnnex = line.substring(annexIndex);
                annexList.add(onlyAnnex);
                break;
            } else {
                index = i;
                String sub = line.substring(annexIndex);
                annexList.add(sub);
                break;
            }
        }
        if (index != 0) {
            index = index + 1;
            getNextAnnex("", index, lines, annexList);
        }
        if (ObjectUtil.isEmpty(annexList)) return;
        for (String annex : annexList) {
            if (annex.contains("."))
                annex = annex.replaceAll(annex.substring(0, annex.indexOf(".") + 1), "");
            if (ObjectUtil.isEmpty(annex)) return;
            //生成随机id
            String uuid = IdUtil.simpleUUID();
            //生成新的实例，并新增
            JSONObject personJson = newEntity(uuid, LabelConstant.CONCEPT, LabelConstant.ANNEX_LABEL, annex);
            relations.add(newRelation("关联附件", "0", uuid));
            entities.add(personJson);
        }
    }

    /**
     * 附件抽取
     *
     * @param annex     上条附件信息
     * @param index     当前行信息索引
     * @param lines     文本信息
     * @param annexList 附件信息集合
     */
    public void getNextAnnex(String annex, Integer index, String[] lines, List<String> annexList) {
        if (index >= lines.length - 1) return;
        String line = lines[index];
        String page = getPage(line);
        if (page != null) line = " ";
        line = line.replaceAll(" ", "");
        if (line.contains(".") && NumberUtil.isNumber(line.substring(0, line.indexOf(".")))) {
            if (ObjectUtil.isNotEmpty(annex))
                annexList.add(annex);
            annex = line;
            //抽取完毕后清除已抽取的文本内容
            lines[index] = "";
        } else {
            String nextLine = lines[index + 1];
            if (nextLine.contains("日") && nextLine.contains("月") && nextLine.contains("年") && (nextLine.indexOf("日") > nextLine.indexOf("月"))) {
                annexList.add(annex);
                return;
            }
            //抽取完毕后清除已抽取的文本内容
            lines[index] = "";
            annex = annex + line;
            index = index + 1;
            getNextAnnex(annex, index, lines, annexList);
        }
        //抽取下一条附件
        index = index + 1;
        getNextAnnex(annex, index, lines, annexList);

    }

    /**
     * 添加公文和人物的关系
     *
     * @param lines     文本信息
     * @param entities  存储容器
     * @param relations 存储容器
     */
    public void setDocumentProperty(String[] lines, JSONArray entities, JSONArray relations) {
        for (int i = 1; i < lines.length; i++) {
            String line = lines[i];
            //跳过页码
            if (getPage(line) != null) continue;
            //添加公文属性
            getFileStartProperty(i, lines, fileTypeSet1, relations, entities);
            getFileEndProperty(i, lines, fileTypeSet2, relations, entities);
        }
    }

    /**
     * 添加公文和人物的关系
     *
     * @param lines     文本信息
     * @param entities  存储容器
     * @param relations 存储容器
     */
    public void setPersonRelation(String[] lines, JSONArray entities, JSONArray relations) {
        String headLines = "";
        String bottomLines = "";
        for (int i = 1; i < lines.length; i++) {
            String line = lines[i];
            if (!headLines.contains("签报")) {
                headLines = headLines + line;
                headLines = headLines.replaceAll(" ", "");
            }
            if (bottomLines.contains("部门会签") || line.contains("部门会签")) {
                bottomLines = bottomLines + line;
                bottomLines = bottomLines.replaceAll(" ", "");
            }

            //跳过页码
            if (getPage(line) != null) continue;
            //添加公文的关联人员
            setPeopleRelation(line, personTypeSet1, relations, entities, "签发人");
            setPeopleRelation(line, personTypeSet2, relations, entities, "联系人");
        }
        if (!(headLines + bottomLines).contains("部门会签")) return;
        setPeopleRelationByLine(headLines + bottomLines, entities);
    }

    public void setPeopleRelationByLine(String line, JSONArray entities) {
        line = line.replaceAll(" ", "");
        if (ObjectUtil.isEmpty(line)) return;
        if (!line.contains("。") || !line.contains("年")) return;
        String substring = line.substring(line.indexOf("。") + 1, line.indexOf("年") + 1);
        line = line.replaceAll("。" + substring, "");
        String regex1 = "(.+)\\s?([0-9]{4})\\s?年";
        Pattern pattern1 = Pattern.compile(regex1);
        Matcher matcher = pattern1.matcher(substring);
        if (matcher.find()) {
            String name = matcher.group(1);
            if (name.contains("。"))
                name = name.substring(name.lastIndexOf("。") + 1);
            if (ObjectUtil.isEmpty(name)) return;
            JSONObject personJson = newEntity(IdUtil.simpleUUID(), LabelConstant.CONCEPT, LabelConstant.PEOPLE, name);
            entities.add(personJson);
        }
        setPeopleRelationByLine(line, entities);
    }

    /**
     * 公文人物关系
     *
     * @param line      行信息
     * @param typeSet   分类集合
     * @param relations
     * @param entities
     */
    public void setPeopleRelation(String line, Set<String> typeSet, JSONArray relations, JSONArray entities, String relationName) {
        String type = isRelation(line, typeSet);
        if (type == null) return;
        //添加人员关系
        log.info("抽到关系，关系名称是：【{}】", type);
        //截取关系信息
        String subMessage = line.substring(line.indexOf(type));
        //截取关系名称
        String subKey = subMessage.substring(0, type.length() - 1);
        //截取人物信息
        String subNumber = subMessage.substring(subKey.length() + 1);
        String fileNumber = null;
        try {
            fileNumber = subNumber.contains(" ") ? subNumber.substring(0, subNumber.indexOf(" ")) : subNumber;
            if (ObjectUtil.isEmpty(fileNumber)) return;
            //生成随机id
            String numberUuid = IdUtil.simpleUUID();
            //生成新的实例，并新增
            JSONObject personJson = newEntity(numberUuid, LabelConstant.CONCEPT, LabelConstant.PEOPLE, fileNumber);
            relations.add(newRelation(relationName, "0", numberUuid));
            entities.add(personJson);
        } catch (Exception e) {
            e.printStackTrace();
        }
        //截取除签发人信息之外的其他信息（文件编号）
        if (!"签发人".equals(relationName)) return;
        String otherMessage = line.substring(0, line.indexOf(subMessage));
        if (otherMessage.contains("：")) return;
        if (ObjectUtil.isEmpty(otherMessage)) return;
        //添加公文编号
        String uuid = IdUtil.simpleUUID();
        //生成新的实例，并新增
        JSONObject json = newEntity(uuid, LabelConstant.PROPERTY, "文件编号", otherMessage);
        relations.add(newRelation(LabelConstant.PROPERTY, "0", uuid));
        entities.add(json);
    }

    /**
     * 添加公文属性
     *
     * @param typeSet   分类集合
     * @param index     行信息索引
     * @param lines     文本啊内容
     * @param relations
     * @param entities
     */
    public void getFileStartProperty(int index, String[] lines, Set<String> typeSet, JSONArray relations, JSONArray entities) {
        String line = lines[index];
        String type = isRelation(line, typeSet);
        if (type == null) return;
        //添加人员关系
        log.info("抽到关系，关系名称是：【{}】", type);
        //截取属性信息
        String subMessage = line.substring(line.indexOf(type));
        //截取属性名称
        String subKey = subMessage.substring(0, type.length() - 1);
        //截取属性信息
        String subNumber = subMessage.substring(subKey.length() + 1);
        String fileNumber = null;
        try {
            fileNumber = subNumber.substring(0, subNumber.indexOf(" "));
            if (ObjectUtil.isEmpty(fileNumber)) return;
            //生成随机id
            String numberUuid = IdUtil.simpleUUID();
            //生成新的实例，并新增
            JSONObject personJson = newEntity(numberUuid, LabelConstant.PROPERTY, "文件编号", fileNumber);
            relations.add(newRelation(LabelConstant.PROPERTY, "0", numberUuid));
            entities.add(personJson);
        } catch (Exception e) {
            e.printStackTrace();
        }
        lines[index] = lines[index].replace(" ", "").replace(subMessage, "");
    }

    /**
     * 添加公文属性
     *
     * @param index     行信息索引
     * @param lines     文本内容
     * @param typeSet   分类集合
     * @param relations JSONArray
     * @param entities  JSONArray
     */
    public void getFileEndProperty(int index, String[] lines, Set<String> typeSet, JSONArray relations, JSONArray entities) {
        String line = lines[index];
        String type = isRelation(line, typeSet);
        if (type == null) return;
        String regex1 = "([0-9]{4})\\s?年\\s?([0-9]{1,2})\\s?月\\s?([0-9]{1,2})\\s?日印发";
        Pattern pattern1 = Pattern.compile(regex1);
        Matcher matcher = pattern1.matcher(line);
        //抽取印发时间
        String yin_fa_time = "";
        if (matcher.find()) {
            String uuid = IdUtil.simpleUUID();
            yin_fa_time = matcher.group(1) + "年" + matcher.group(2) + "月" + matcher.group(3) + "日";
            entities.add(newEntity(uuid, LabelConstant.PROPERTY, "印发日期", yin_fa_time));
            relations.add(newRelation(LabelConstant.PROPERTY, "0", uuid));
        }
        lines[index] = lines[index].replace(" ", "").replace(yin_fa_time + "印发", "");
        //抽取印发单位
        if (!line.contains(" ")) return;
        String department = line.substring(0, line.indexOf(" "));
        String uuid = IdUtil.simpleUUID();
        entities.add(newEntity(uuid, LabelConstant.PROPERTY, "印发单位", department));
        relations.add(newRelation(LabelConstant.PROPERTY, "0", uuid));
        lines[index] = lines[index].replace(department, "");
    }


    /**
     * 获取页码
     *
     * @param line 段落内容
     * @return 页码
     */
    public static String getPage(String line) {
        String s = line.replace(" ", "");
        //匹配以”-“或”一“开头，后面是1-3位长度的数字，再后面是”-“或”一“的段落
        String regex = "(?<=^[-,—,一])[0-9]{1,3}(?=[-,—,一])";
        Pattern pattern = Pattern.compile(regex);
        Matcher matcher = pattern.matcher(s);
        if (matcher.find()) {
            return matcher.group();
        }
        return null;
    }

    /**
     * 关系获取
     * eg：    签发人 ： 张三
     *
     * @param line 段落内容
     * @return 人员称号
     */
    public static String isRelation(String line, Set<String> typeSet) {
        //去除字符串首尾空格
        line = StrUtil.trim(line);
        HashSet<String> set = new HashSet<>(typeSet);
        //若该行文字包含关系词汇，返回当前关系词汇
        for (String type : set) {
            if (line.contains(type)) {
                return type;
            }
        }
        return null;
    }
}
