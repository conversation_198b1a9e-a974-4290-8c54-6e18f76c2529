package org.irm.lab.kg.service.impl;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.json.JSONObject;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.irm.lab.common.api.R;
import org.irm.lab.common.exception.ServiceException;
import org.irm.lab.common.utils.RedisUtil;
import org.irm.lab.common.utils.ThreadLocalUtil;
import org.irm.lab.config.entity.MetadataSchema;
import org.irm.lab.config.entity.Process;
import org.irm.lab.config.entity.ProcessNode;
import org.irm.lab.config.entity.WorkTask;
import org.irm.lab.config.enums.ProcessNodeEnum;
import org.irm.lab.config.feign.MetadataSchemaFeign;
import org.irm.lab.config.feign.WorkTaskFeign;
import org.irm.lab.kg.kgprocess.strategy.ProcessingStrategySelector;
import org.irm.lab.kg.service.IProcessInfoService;
import org.irm.lab.kg.vo.ResourceProcessInfoVo;
import org.irm.lab.repository.constant.*;
import org.irm.lab.repository.entity.Resource;
import org.irm.lab.repository.entity.ResourceAnnex;
import org.irm.lab.repository.feign.ResourceAnnexFeign;
import org.irm.lab.repository.feign.ResourceFeign;
import org.irm.lab.user.entity.User;
import org.irm.lab.user.feign.UserFeign;
import org.springframework.context.annotation.Lazy;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import javax.servlet.http.HttpServletRequest;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2023/3/9 16:38
 * @description 加工信息业务实现类
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class ProcessInfoServiceImpl implements IProcessInfoService {


    private final WorkTaskFeign workTaskFeign;
    private final MetadataSchemaFeign metadataSchemaFeign;
    private final UserFeign userFeign;
    private final ResourceFeign resourceFeign;
    private final ResourceAnnexFeign resourceAnnexFeign;
    private final HttpServletRequest request;
    @Lazy
    private final ProcessingStrategySelector processingStrategySelector;
    private final RedisUtil redisUtil;

    /**
     * 根据工作任务Id和资源Id获取详情信息
     *
     * @param workTaskId 工作任务Id
     * @param resourceId 资源Id
     * @return {@link ResourceProcessInfoVo}
     */
    @Override
    public ResourceProcessInfoVo processInfo(String workTaskId, String resourceId) {
        ResourceProcessInfoVo resourceProcessInfoVo = new ResourceProcessInfoVo();
        // 根据工作任务Id，获取工作任务对象
        R<WorkTask> workTaskR = workTaskFeign.info(workTaskId);
        // 获取工作任务对象
        WorkTask workTask = workTaskR.getData();
        if (!workTaskR.isSuccess() || workTask == null) throw new ServiceException("不存在指定工作任务!");
        // 获取资源对象
        R<Resource> resourceR = resourceFeign.info(resourceId);
        Resource resource = resourceR.getData();
        if (!resourceR.isSuccess() || resource == null) throw new ServiceException("当前资源不存在");
        // 设置该资源的附件数据
        R<List<ResourceAnnex>> resourceAnnexListR = resourceAnnexFeign.listByResourceId(resource.getId());
        List<ResourceAnnex> resourceAnnexList = resourceAnnexListR.getData();
        if (resourceAnnexListR.isSuccess() && ObjectUtil.isNotEmpty(resourceAnnexList)) {
            resourceProcessInfoVo.setResourceAnnexList(resourceAnnexList);
        }
        // 获取流程对象
        Process process = workTask.getProcess();
        // 设置工作任务名
        resourceProcessInfoVo.setWorkTaskName(workTask.getName());
        // 获取元数据方案对象
        R<MetadataSchema> metadataSchemaR = metadataSchemaFeign.info(process.getMetadataSchemaId());
        if (!metadataSchemaR.isSuccess()) R.failed("不存在指定的元数据方案!");
        // 设置元数据方案名称
        resourceProcessInfoVo.setMetadataSchemaName(metadataSchemaR.getData().getName());
        // 设置任务负责人
        setWorkTaskUser(resourceProcessInfoVo, process);
        // 设置环节负责人 和 加工环节
        setProcessUserAndProcess(resource, resourceProcessInfoVo, process);
        // 根据资源id获取当前资源对象
        setResource(resource, resourceProcessInfoVo);
        // 设置年份
        setYear(resource, resourceProcessInfoVo);
        // 返回结果
        return resourceProcessInfoVo;
    }

    /**
     * 设置年份
     */
    private void setYear(Resource resource, ResourceProcessInfoVo resourceProcessInfoVo) {
        resource.getMetadata().stream().filter(meta -> "年度".equals(meta.getName()) && ObjectUtil.isNotEmpty(meta.getValue()))
                .findFirst().ifPresent(meta -> resourceProcessInfoVo.setYear(meta.getValue().get(0)));
    }

    /**
     * 设置资源相关数据
     */
    private void setResource(Resource resource, ResourceProcessInfoVo resourceProcessInfoVo) {
        // 资源名称
        resourceProcessInfoVo.setResourceName(resource.getName());
        // 资源类型
        resourceProcessInfoVo.setMediaType(resource.getMediaType());
        resourceProcessInfoVo.setSubmitTime(resource.getCreateTime());
        resourceProcessInfoVo.setUpdateTime(resource.getUpdateTime());
    }

    /**
     * 设置环节负责人 和 加工环节
     */
    private void setProcessUserAndProcess(Resource resource, ResourceProcessInfoVo resourceProcessInfoVo, Process process) {
        List<User> processUserList = new ArrayList<>();
        List<JSONObject> processStatus = new ArrayList<>();
        List<JSONObject> stageStatus = new ArrayList<>();
        // 设置资源加工环节
        for (ProcessNode kgProcessNode : process.getKgProcessNodes()) {
            // 资源加工环节
            if (ProcessNodeEnum.RESOURCE_PROCESS.getNodeName().equals(kgProcessNode.getStageName())) {
                // 获取环节负责人
                List<String> ownerIds = kgProcessNode.getOwnerIds();
                List<R<User>> usersList = ownerIds.stream()
                        .map(userFeign::info)
                        .collect(Collectors.toList());
                for (R<User> userR : usersList) {
                    if (userR.isSuccess()) {
                        User user = userR.getData();
                        processUserList.add(user);
                    }
                }
                // 获取加工状态
                if (kgProcessNode.getIsMetadataConversion()) {
                    JSONObject object = new JSONObject();
                    object.putOpt("name", "元数据转化");
                    object.putOpt("flag", resource.getMetadataConverter());
                    processStatus.add(object);
                }
                if (kgProcessNode.getIsDocParsing()) {
                    JSONObject object = new JSONObject();
                    object.putOpt("name", "文档解析");
                    object.putOpt("flag", resource.isDocParsingConfirm());
                    processStatus.add(object);
                }
                if (kgProcessNode.getIsKgAnnotation()) {
                    JSONObject object = new JSONObject();
                    object.putOpt("name", "知识标注");
                    object.putOpt("flag", resource.isKnowledgeTagConfirm());
                    processStatus.add(object);
                }
                if (kgProcessNode.getIsDocRuleParsing()) {
                    JSONObject object = new JSONObject();
                    object.putOpt("name", "规则解析");
                    object.putOpt("flag", resource.isKnowledgeTagConfirm());
                    processStatus.add(object);
                }
            }
        }
        JSONObject stageStatusObject1 = new JSONObject();
        stageStatusObject1.putOpt("name", "资源加工");
        stageStatusObject1.putOpt("flag", resource.isProcessed());
        stageStatus.add(stageStatusObject1);

        JSONObject stageStatusObject2 = new JSONObject();
        stageStatusObject2.putOpt("name", "知识匹配");
        stageStatusObject2.putOpt("flag", resource.isProcessed() && !resource.isKnowledgeMatchConfirm());
        stageStatus.add(stageStatusObject2);

        JSONObject stageStatusObject3 = new JSONObject();
        stageStatusObject3.putOpt("name", "知识对齐");
        stageStatusObject3.putOpt("flag", resource.isKnowledgeMatchConfirm() && KnowledgeAlignmentStatus.UN_ALIGNMENT.equals(resource.getAlignmentStatus()));
        stageStatus.add(stageStatusObject3);

        resourceProcessInfoVo.setProcessOwnUserList(processUserList);
        resourceProcessInfoVo.setProcessStatus(processStatus);
        resourceProcessInfoVo.setStageStatus(stageStatus);
    }

    /**
     * 设置任务负责人
     */
    private void setWorkTaskUser(ResourceProcessInfoVo resourceProcessInfoVo, Process process) {
        List<User> workTaskUserList = new ArrayList<>();
        List<R<User>> userRList = process.getOwnerIds().stream().map(userFeign::info).collect(Collectors.toList());
        for (R<User> userR : userRList) {
            if (userR.isSuccess()) {
                User user = userR.getData();
                workTaskUserList.add(user);
            }
        }
        resourceProcessInfoVo.setWorkTaskOwnerList(workTaskUserList);
    }

    /**
     * 一键通过资源加工
     *
     * @param resourceId 资源Id
     */
    @Override
    public void passResource(String resourceId) {
        // 1、修改文档解析确认为 true，调用process，会进行知识标标注算法识别。
        // 根据资源Id获取资源数据
        R<Resource> resourceR = resourceFeign.info(resourceId);
        Resource resource = resourceR.getData();
        if (!resourceR.isSuccess() || ObjectUtil.isEmpty(resource)) {
            throw new ServiceException("当前资源不存在!");
        }
        // 修改当前资源数据的 加工状态为 “已完成”
        resource.setProcessed(true);
        resource.setProcessStatus(ResourceProcessStatus.PROCESSED);
        resource = resourceFeign.save(resource).getData();
        passResource(resource);
    }


    /**
     * 一键通过资源加工
     */
    @Override
    public void passResourceMulti(List<String> resourceIds) {
        resourceIds.forEach(this::passResource);
    }


    /**
     * 资源加工全部文件一键通过
     *
     * @param workTaskId 工作任务Id
     */
    @Override
    public void passAllResource(String workTaskId) {
        // 获取所有文档解析状态为 “解析完成”的资源
        Map<String, Object> filterMap = Map.of("resolveStatus", "解析完成", "tagStatus", "待识别", "workTaskId", workTaskId);
        R<List<Resource>> listR = resourceFeign.listByCondition(filterMap);
        List<Resource> resourceList = listR.getData();
        if (listR.isSuccess() && ObjectUtil.isNotEmpty(resourceList)) {
            ThreadLocalUtil.set("user", request.getHeader("user"));
            passAllResource(resourceList);
        }
    }

    /**
     * 异步开启资源加工全部文件一键通过
     *
     * @param resourceList 资源列表
     */
    @Async
    protected void passAllResource(List<Resource> resourceList) {
        for (Resource resource : resourceList) {
            resource.setDocParsingConfirm(true);
            resource = resourceFeign.save(resource).getData();
            // 开启自动通过
            redisUtil.set(RedisConstant.AUTO_PASS_RESOURCE + resource.getId(), true, 3000);
            processingStrategySelector.process(resource);
        }
    }

    /**
     * 异步开启一键通过资源加工
     *
     * @param resource 资源对象
     */
    @Async
    public void passResource(Resource resource) {
        resource.setDocParsingConfirm(true);
        resource = resourceFeign.save(resource).getData();
        // 开启自动通过
        redisUtil.set(RedisConstant.AUTO_PASS_RESOURCE + resource.getId(), true, 3000);
        // 一键通过资源加工
        ThreadLocalUtil.set("user", request.getHeader("user"));
        processingStrategySelector.process(resource);
    }


}
