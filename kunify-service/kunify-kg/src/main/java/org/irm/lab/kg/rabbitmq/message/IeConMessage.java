package org.irm.lab.kg.rabbitmq.message;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.Data;
import org.irm.lab.kg.vo.ie.IeInferResultVO;

import java.io.Serializable;
import java.util.Map;

/**
 * 从ai中台接收消息
 *
 * <AUTHOR>
 * @date 2023/3/7 15:36
 */
@Data
@JsonIgnoreProperties(ignoreUnknown = true)
public class IeConMessage implements Serializable {
    public static final String QUEUE = "KUNIFY4_QUEUE_IE_CON";

    public static final String EXCHANGE = "KUNIFY4_EXCHANGE_IE_CON";

    public static final String ROUTING_KEY = "KUNIFY4_ROUTING_IE_CON";

    private Map<String, IeInferResultVO> result;

    private String tenantId;
    private String userId;
    private Boolean status;
    private String resourceId;
    private String annexId;
    private Boolean isEnd;
}
