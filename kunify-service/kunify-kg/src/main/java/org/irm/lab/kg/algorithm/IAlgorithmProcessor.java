package org.irm.lab.kg.algorithm;

import cn.hutool.core.util.IdUtil;
import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import org.apache.pdfbox.pdmodel.PDDocument;
import org.apache.pdfbox.text.PDFTextStripper;
import org.irm.lab.common.enums.AlgorithmType;
import org.irm.lab.kg.algorithm.ruleMatching.RuleParsingEntity;
import org.irm.lab.repository.entity.Resource;
import org.irm.lab.repository.entity.ResourceAnnex;

import java.io.IOException;
import java.util.List;
import java.util.regex.Matcher;
import java.util.regex.Pattern;


/**
 * <AUTHOR>
 * @date 2023/1/30 15:24
 */
public interface IAlgorithmProcessor {

    AlgorithmType getCode();

    /**
     * 主文件解析
     *
     * @param resource 资源
     */
    default void process(Resource resource) {
    }

    /**
     * 附件解析
     *
     * @param resourceAnnex 附件
     */
    default void annexProcess(ResourceAnnex resourceAnnex) {
    }

    /**
     * 实体处理，对规则解析生成的实体列表进行处理
     *
     * @param entityList    处理前的实体列表
     * @param newEntityList 处理后的实体列表
     * @param propertyList  处理后的属性列表
     */
    default void processEntity(List<RuleParsingEntity> entityList, List<RuleParsingEntity> newEntityList, List<RuleParsingEntity> propertyList){

    }


    default String[] extractContent(PDDocument pdDocument) throws IOException {
        PDFTextStripper textStripper = new PDFTextStripper();
        String text = textStripper.getText(pdDocument);
        return text.split("\\r?\\n");
    }

    default JSONObject initJSONObject() {
        JSONObject result = JSONUtil.createObj();

        JSONArray persons = JSONUtil.createArray();
        JSONArray relations = JSONUtil.createArray();
        result.putOpt("result", persons).putOpt("relation", relations);
        return result;
    }

    default JSONObject mergeResult(JSONObject... objects) {
        JSONObject result = JSONUtil.createObj();
        JSONArray entities = JSONUtil.createArray();
        JSONArray relations = JSONUtil.createArray();
        result.putOpt("result", entities).putOpt("relation", relations);
        for (JSONObject object : objects) {
            result.getJSONArray("result").addAll(object.getJSONArray("result"));
            result.getJSONArray("relation").addAll(object.getJSONArray("relation"));
        }
        return result;
    }

    default JSONObject newEntity(String uuid, String type, String schema, String value) {
        JSONObject entity = JSONUtil.createObj();
        return entity.putOpt("value", value).putOpt("type", type)
                .putOpt("schema", schema).putOpt("uuid", uuid);
    }

    default JSONObject newRelation(String relation, String start, String end) {
        JSONObject r = JSONUtil.createObj();
        return r.putOpt("relation", relation).putOpt("start", start).putOpt("end", end);
    }

    /**
     * 获取年份  从文本内容获取
     *
     * @param line   "2021 年第 122222 期"
     * @param result 年：2021   期：122222
     */
    default void getYearFromContent(String line, JSONObject result) {
        String regexYear = "\\d{4}(?=年)";
        String regexTimes = "(?<=第).*?(?=[期次])";
        commonYear(regexYear, regexTimes, result, line.replaceAll(" ",""));
    }

    default void commonYear(String regexYear, String regexTimes, JSONObject result, String str) {
        Pattern patternYear = Pattern.compile(regexYear);
        Pattern patternTimes = Pattern.compile(regexTimes);
        Matcher matcherYear = patternYear.matcher(str);
        Matcher matcherTimes = patternTimes.matcher(str);

        JSONArray entities = result.getJSONArray("result");
        JSONArray relations = result.getJSONArray("relation");
        if (matcherYear.find()) {
            String uuid = IdUtil.simpleUUID();
            entities.add(newEntity(uuid, "属性", "年度", matcherYear.group()));
            relations.add(newRelation("属性", "0", uuid));
        }
        if (matcherTimes.find()) {
            String uuid = IdUtil.simpleUUID();
            entities.add(newEntity(uuid, "属性", "期数", matcherTimes.group()));
            relations.add(newRelation("属性", "0", uuid));
        }
    }
}
