package org.irm.lab.kg.controller;

import cn.hutool.core.convert.Convert;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.irm.lab.common.annotation.MyLog;
import org.irm.lab.common.api.R;
import org.irm.lab.common.constant.LogConstant;
import org.irm.lab.common.support.MyPage;
import org.irm.lab.common.utils.Func;
import org.irm.lab.kg.dto.NodeRelationDTO;
import org.irm.lab.kg.entity.KnowledgeProperty;
import org.irm.lab.kg.entity.neo4j.node.NodeEntity;
import org.irm.lab.kg.entity.neo4j.node.NodeRelation;
import org.irm.lab.kg.feign.GraphMaintenanceFeign;
import org.irm.lab.kg.service.IGraphMaintenanceService;
import org.irm.lab.kg.vo.PropMessageVO;
import org.irm.lab.kg.vo.echarts.EchartsVO;
import org.irm.lab.kg.vo.node.NodeEntityVO;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;
import java.util.Set;

@RestController
@RequestMapping("/graph-maintenance")
@Api(value = "图谱维护")
@RequiredArgsConstructor
public class GraphMaintenanceController implements GraphMaintenanceFeign {

    private final IGraphMaintenanceService graphMaintenanceService;


    /**
     * 图谱维护可视化 ===> 获取指定概念下的所有实例
     *
     * @param map 查询条件
     * @return {@link EchartsVO}
     */
    @ApiOperation(value = "图谱-概念获取实例", hidden = true)
    @GetMapping("/echarts-node-by-concept")
    public R<EchartsVO> echartsNodesByConcept(@RequestParam Map<String, Object> map) {
        String conceptId = Convert.toStr(map.getOrDefault("conceptId", ""));
        String modelId = Convert.toStr(map.get("modelId"));
        String showCount = Convert.toStr(map.get("showCount"));
        return R.data(graphMaintenanceService.echartsNodesByConcept(conceptId, modelId, showCount));
    }

    /**
     * 新增实例
     *
     * @param nodeEntity {@link NodeEntity}
     * @return {@link NodeEntity}
     */
    @MyLog(menu = LogConstant.MENU_VIEW, dataType = LogConstant.DATA_ENTITY, operation = LogConstant.OPERATION_ADD)
    @ApiOperation("新增实例")
    @PostMapping("/node-save")
    public R<NodeEntity> saveNode(@RequestBody NodeEntity nodeEntity) {
        return R.data(graphMaintenanceService.saveNode(nodeEntity));
    }

    /**
     * 实例删除
     *
     * @param ids 实例Id
     */
    @MyLog(menu = LogConstant.MENU_VIEW, dataType = LogConstant.DATA_ENTITY, operation = LogConstant.OPERATION_REMOVE)
    @ApiOperation("实例删除")
    @PostMapping("/node-remove")
    public R<Void> nodeRemove(@RequestBody String ids) {
        graphMaintenanceService.nodeRemove(Func.objToStrList(ids));
        return R.success();
    }


    /**
     * 图谱维护可视化 ===> 获取指定节点拓展关系和节点（深度为1）
     *
     * @param map 条件map
     * @return {@link EchartsVO}
     */
    @ApiOperation(value = "图谱-实例及其关系", hidden = true)
    @GetMapping("/echarts-node-with-relation")
    public R<EchartsVO> echartsNodesWithRelation(@RequestParam Map<String, Object> map) {
        String nodeId = Convert.toStr(map.get("nodeId"));
        String modelId = Convert.toStr(map.getOrDefault("modelId", "0"));
        String showCount = Convert.toStr(map.getOrDefault("showCount", ""));
        return R.data(graphMaintenanceService.echartsNodesWithRelation(nodeId, modelId, showCount));
    }

    /**
     * 实例详情
     *
     * @param id 实例id
     * @return NodeEntityVO
     */
    @ApiOperation("实例详情")
    @GetMapping("/node-vo-info")
    public R<NodeEntityVO> nodeVOInfo(@RequestParam String id) {
        return R.data(graphMaintenanceService.nodeVOInfo(id));
    }


    /**
     * 获取该模型下的指定概念的属性列表
     *
     * @param modelId   模型Id
     * @param conceptId 概念Id
     * @return {@link KnowledgeProperty}
     */
    @GetMapping("/list-concept-property")
    public R<List<KnowledgeProperty>> listConceptProperty(@RequestParam String modelId, @RequestParam String conceptId) {
        return R.data(graphMaintenanceService.listPropertyByModelId(modelId, conceptId));
    }

    /**
     * 获取该模型下的指定关系的属性列表
     *
     * @param modelId    模型Id
     * @param relationId 关系Id
     * @return {@link KnowledgeProperty}
     */
    @GetMapping("/list-relation-property")
    public R<List<KnowledgeProperty>> listRelationProperty(@RequestParam String modelId, @RequestParam String relationId) {
        return R.data(graphMaintenanceService.listRelationProperty(modelId, relationId));
    }


    /**
     * 新增或修改实例的属性
     *
     * @param propMessageVO {@link PropMessageVO}
     */
    @MyLog(menu = LogConstant.MENU_VIEW, dataType = LogConstant.DATA_PROPERTY, operation = LogConstant.OPERATION_ADD_OR_ALTER)
    @ApiOperation("实例属性修改")
    @PostMapping("/update-node-properties")
    public R<Void> saveOrUpdateNodeProperties(@RequestBody PropMessageVO propMessageVO) {
        graphMaintenanceService.updateNodeProperties(propMessageVO);
        return R.success();
    }

    /**
     * 新增或修改关系的属性
     *
     * @param propMessageVO {@link PropMessageVO}
     */
    @MyLog(menu = LogConstant.MENU_VIEW, dataType = LogConstant.DATA_RELATION_PROPERTY, operation = LogConstant.OPERATION_ADD_OR_ALTER)
    @ApiOperation("关系属性修改")
    @PostMapping("/update-relation-properties")
    public R<Void> saveOrUpdateRelationProperties(@RequestBody PropMessageVO propMessageVO) {
        graphMaintenanceService.updateRelationProperties(propMessageVO);
        return R.success();
    }

    /**
     * 关系删除
     *
     * @param ids id
     */
    @MyLog(menu = LogConstant.MENU_VIEW, dataType = LogConstant.DATA_ENTITY_RELATION, operation = LogConstant.OPERATION_REMOVE)
    @ApiOperation("关系删除")
    @PostMapping("/relation-remove")
    public R<Void> relationRemove(@RequestBody String ids) {
        graphMaintenanceService.relationRemove(Func.objToStrList(ids));
        return R.success();
    }

    /**
     * 删除实例属性
     *
     * @param propMessageVO {@link PropMessageVO}
     */
    @MyLog(menu = LogConstant.MENU_VIEW, dataType = LogConstant.DATA_PROPERTY, operation = LogConstant.OPERATION_REMOVE)
    @ApiOperation(value = "实例属性删除")
    @PostMapping("/remove-node-property")
    public R<Void> removeNodeProperty(@RequestBody PropMessageVO propMessageVO) {
        graphMaintenanceService.removeNodeProperty(propMessageVO);
        return R.success();
    }


    /**
     * 删除关系属性
     *
     * @param propMessageVO {@link PropMessageVO}
     */
    @MyLog(menu = LogConstant.MENU_VIEW, dataType = LogConstant.DATA_RELATION_PROPERTY, operation = LogConstant.OPERATION_REMOVE)
    @ApiOperation(value = "关系属性删除")
    @PostMapping("/remove-relation-property")
    public R<Void> removeRelationProperty(@RequestBody PropMessageVO propMessageVO) {
        graphMaintenanceService.removeRelationProperty(propMessageVO);
        return R.success();
    }


    /**
     * 根据概念和关系获取可用实例
     *
     * @param conceptId 概念Id
     * @param nodeId    主语的实例Id
     * @param modelId   实例Id
     * @return {@link NodeEntity}
     */
    @ApiOperation("根据概念和关系获取可用实例")
    @GetMapping("/list-node-relation")
    public R<List<NodeEntity>> nodeList(@RequestParam String conceptId, @RequestParam String nodeId, @RequestParam String modelId) {
        return R.data(graphMaintenanceService.listNodeRelation(conceptId, nodeId, modelId));
    }

    /**
     * 根据概念和关系获取可用实例
     *
     * @param conceptId 概念Id
     * @param nodeId    主语的实例Id
     * @param modelId   实例Id
     * @return {@link NodeEntity}
     */
    @ApiOperation("根据概念和关系获取可用实例")
    @GetMapping("/page-node-relation")
    public R<MyPage<NodeEntity>> nodePage(@RequestParam String conceptId, @RequestParam String nodeId, @RequestParam String modelId, @RequestParam Integer page, @RequestParam Integer size, @RequestParam(defaultValue = "") String entityName) {
        return R.data(graphMaintenanceService.pageNodeRelation(conceptId, nodeId, modelId, page, size, entityName));
    }


    @ApiOperation("分页获取实例")
    @GetMapping("/node-page")
    public R<MyPage<NodeEntity>> page(@RequestParam Map<String, Object> queryParam) {
        // 获取分页条件
        Integer page = Convert.toInt(queryParam.getOrDefault("page", 1));
        Integer size = Convert.toInt(queryParam.getOrDefault("size", 10));
        return R.data(graphMaintenanceService.nodePage(queryParam, page, size, 0));
    }


    @ApiOperation("概念实例集合")
    @GetMapping("/node-list")
    public R<List<NodeEntity>> nodeList(@RequestParam String conceptId, @RequestParam String modelId) {
        return R.data(graphMaintenanceService.nodeList(conceptId, modelId));
    }

    @ApiOperation("模型实例集合")
    @GetMapping("/model-node-list")
    public R<List<NodeEntity>> modelNodeList(@RequestParam String modelId) {
        return R.data(graphMaintenanceService.nodeList("0", modelId));
    }

    @MyLog(menu = LogConstant.MENU_VIEW, dataType = LogConstant.DATA_RELATION, operation = LogConstant.OPERATION_ADD)
    @ApiOperation("新增关系")
    @PostMapping("/relation-save")
    public R<Void> saveRelation(@RequestBody NodeRelationDTO nodeRelationDTO) {
        graphMaintenanceService.saveRelation(nodeRelationDTO);
        return R.success();
    }

    @ApiOperation("单个实例")
    @GetMapping("/node-info")
    public R<? extends NodeEntity> nodeInfo(@RequestParam String id) {
        return R.data(graphMaintenanceService.nodeInfo(id));
    }


    @ApiOperation("资源下的所有实例")
    @PostMapping("/resource-node")
    public R<List<NodeEntity>> getNodeListByResourceId(@RequestParam String resourceId) {
        return R.data(graphMaintenanceService.getNodeListByResourceId(resourceId));
    }

    @ApiOperation("单个关系")
    @GetMapping("/relation-info")
    public R<? extends NodeRelation> relationInfo(@RequestParam String id) {
        return R.data(graphMaintenanceService.relationInfo(id, 1));
    }

    @ApiOperation("关系详情")
    @GetMapping("/relation-vo-info")
    public R<? extends NodeRelation> relationVOInfo(@RequestParam String id) {
        return R.data(graphMaintenanceService.relationVOInfo(id));
    }


    @ApiOperation(value = "模糊查询", hidden = true)
    @GetMapping("/get-filter")
    public R<List<NodeEntity>> getFilter(@RequestParam Map<String, Object> queryParam) {
        return R.data(graphMaintenanceService.getFilter(queryParam));
    }

    @ApiOperation(value = "分类知识可视化")
    @GetMapping("/document-visual")
    public R<EchartsVO> documentVisual(@RequestParam String classify, @RequestParam String showCount, @RequestParam String name) {
        return R.data(graphMaintenanceService.documentVisual(classify, showCount, name));
    }

    /**
     * 获取实例的所有指向关系
     *
     * @param id 实例id
     * @return 关系集合
     */
    @ApiOperation(value = "获取实例的所有指向关系")
    @GetMapping("/node-toEntity")
    public R<Set<NodeRelation>> getToEntity(@RequestParam String id) {
        return R.data(graphMaintenanceService.nodeInfo(id).getToEntity());
    }

    /**
     * 获取实例的所有被指向关系
     *
     * @param id 实例id
     * @return 关系集合
     */
    @ApiOperation(value = "获取实例的所有指向关系")
    @GetMapping("/node-fromEntity")
    public R<Set<NodeRelation>> getFromEntity(@RequestParam String id) {
        return R.data(graphMaintenanceService.nodeInfo(id).getFromEntity());
    }
}
