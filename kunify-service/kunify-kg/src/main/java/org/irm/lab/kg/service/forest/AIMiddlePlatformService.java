package org.irm.lab.kg.service.forest;

import org.irm.lab.front.dto.AIMiddle.OcrInferDTO;
import org.irm.lab.kg.dto.ie.req.IeInferReqDTO;
import org.springframework.web.multipart.MultipartFile;
import com.dtflys.forest.annotation.Body;
import com.dtflys.forest.annotation.BodyType;
import com.dtflys.forest.annotation.DataFile;
import com.dtflys.forest.annotation.PostRequest;



public interface AIMiddlePlatformService {

    /**
     * ocr
     */
    @PostRequest(url ="${ai.ip}" + ":" + "${ai.port}" + "/ocr/infer/py")
    @BodyType(value = "multipart")
    String ocr(@DataFile(value = "file") MultipartFile multipartFile,
               @Body OcrInferDTO ocrInferDTO);

    /**
     * 实体抽取
     */
    @PostRequest(url ="${ai.ip}" + ":" + "${ai.port}" + "/ie/infer/api3")
    String inferApi(@Body IeInferReqDTO ieInferReqDTO);

}
