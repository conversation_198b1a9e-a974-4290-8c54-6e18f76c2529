package org.irm.lab.kg.algorithm.ruleMatching.guizhangzhidu;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.log4j.Log4j2;
import org.apache.pdfbox.pdmodel.PDDocument;
import org.irm.lab.common.enums.AlgorithmType;
import org.irm.lab.common.provider.MinioLinkProvider;
import org.irm.lab.common.utils.ForestNodeMerger;
import org.irm.lab.common.utils.NetWorkFileUtil;
import org.irm.lab.kg.algorithm.IAlgorithmProcessor;
import org.irm.lab.kg.algorithm.ruleMatching.RuleParsingEntity;
import org.irm.lab.kg.algorithm.ruleMatching.RuleParsingRelation;
import org.irm.lab.kg.algorithm.ruleMatching.RuleParsingResult;
import org.irm.lab.kg.service.impl.kgprocess.ruleMatching.DocRuleParsing;
import org.irm.lab.repository.constant.DocumentResolvedStatus;
import org.irm.lab.repository.entity.Resource;
import org.irm.lab.repository.feign.ResourceAnnexFeign;
import org.irm.lab.repository.feign.ResourceFeign;
import org.jetbrains.annotations.NotNull;
import org.springframework.stereotype.Service;

import java.io.InputStream;
import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

@Log4j2
@Service
@RequiredArgsConstructor
public class ZHI_DU_01 implements IAlgorithmProcessor {

    private final ResourceFeign resourceFeign;
    private final ResourceAnnexFeign resourceAnnexFeign;
    private final MinioLinkProvider minioLinkProvider;

    private boolean isStandard = false;

    @javax.annotation.Resource(name = "resourceDocRuleParsing")
    private DocRuleParsing docRuleParsing;

    @Override
    public AlgorithmType getCode() {
        return AlgorithmType.PDF_RULE_ZHI_DU_01;
    }

    @Override
    public void process(Resource resource) {
        String processName = AlgorithmType.PDF_RULE_ZHI_DU_01.getName();
        log.info(">>>>>>>>>>【主文件】【{}】【规则解析】开始>>>>>>>>>>", processName);
        try {
            resource.setRuleStatus(DocumentResolvedStatus.RESOLVING);
            resourceFeign.save(resource);
            /*// 获取规则解析结果
            JSONObject results = ruleProcess(resource);
            // 对结果转换为实体
            RuleParsingResult ruleParsingResult = JSONUtil.toBean(results, RuleParsingResult.class, true);
            // 存储处理后的实体
            List<RuleParsingEntity> entityList = new ArrayList<>();
            // 存储处理后的属性(实例的属性)
            List<RuleParsingEntity> propertyList = new ArrayList<>();
            // 存储处理后关系
            List<RuleParsingRelation> relationList = ruleParsingResult.getRelation();
            // 实例处理
            processEntity(ruleParsingResult.getResult(), entityList, propertyList);
            // 生成主文件缓存标签
            docRuleParsing.ruleParsingCacheLabelData(resource, entityList, propertyList, relationList);*/
            resource.setRuleStatus(DocumentResolvedStatus.RESOLVED);
            log.info("<<<<<<<<<<【主文件】【{}】【规则解析】结束<<<<<<<<<<", processName);
        } catch (Exception e) {
            resource.setRuleStatus(DocumentResolvedStatus.RESOLVED_FAILED);
            log.error("解析失败 {}", e.getMessage());
        } finally {
            resourceFeign.save(resource);
        }
    }

    @Override
    public void processEntity(List<RuleParsingEntity> entityList, List<RuleParsingEntity> newEntityList, List<RuleParsingEntity> propertyList) {
        for (RuleParsingEntity ruleParsingEntity : entityList) {
            if (StrUtil.equals(ruleParsingEntity.getType(), "概念")) newEntityList.add(ruleParsingEntity);
            else propertyList.add(ruleParsingEntity);
        }
    }

    /**
     * 规则解析
     *
     * @param resource
     * @return
     */
    public JSONObject ruleProcess(Resource resource) {
        JSONObject result = JSONUtil.createObj();
        //获取资源文件路径
        String pdfLink = minioLinkProvider.getMinioLinkIntranet(resource.getPdfAttachName());
        //若资源路径为空这集返回空结果
        if (pdfLink == null) return result;
        try (InputStream inputStream = NetWorkFileUtil.urlToInputStream(pdfLink);
             PDDocument document = PDDocument.load(inputStream)) {
            //提取文本信息，按段落切分
            String[] strings = extractContent(document);
            List<String> line = text(strings);
            result = extractProvisions(line);
            return result;
        } catch (Exception e) {
            e.printStackTrace();
        }
        return result;
    }


    //去水印、去页码、拆分段落
    private List<String> text(String[] line) {
        List<Integer> integers = new ArrayList<>();
        List<String> paragraphs = new ArrayList<>();
        for (String s : line) {
            //去页码
            if (checkIfStringContainsOnlyNumbers(s)) continue;
            //去水印
            if (StrUtil.contains(s, "Admin admin")) continue;
            if (StrUtil.contains(s, "附件：") ||
                    StrUtil.equals(s, "附件") ||
                    StrUtil.contains(s, "附表1") ||
                    StrUtil.contains(s, "附件1"))
                break;
            paragraphs.add(s);
        }
        String[] paragraph = paragraphs.toArray(new String[0]);
        isStandard = false;
        for (int i = 0; i < paragraph.length; i++) {
            //拆分段落
            String startPattern = "第[一二三四五六七八九十]+章";
            Pattern pattern = Pattern.compile(startPattern);
            Matcher matcher = pattern.matcher(paragraph[i]);
            if (matcher.find()) {
                integers.add(i);
                isStandard = true;
            }
        }
        List<String> strings = new ArrayList<>();
        if (isStandard) {
            integers.add(paragraph.length);
            List<Integer> sort = CollUtil.sort(integers, Comparator.naturalOrder());
            for (int i = 0; i < sort.size(); i++) {
                if (sort.get(i) == paragraph.length) break;
                String[] subArray = Arrays.copyOfRange(paragraph, sort.get(i), sort.get(i + 1));
                String string = String.join("", subArray).replaceAll("\\s+", "").replaceAll("—\\d+—", "");
                strings.add(string);
            }
        } else {// 语料段落
            List<Integer> integerArrayList = new ArrayList<>();
            for (int i = 0; i < paragraph.length; i++) {
                //拆分段落
                String startPattern = "第[一二三四五六七八九十]+条";
                Pattern pattern = Pattern.compile(startPattern);
                Matcher matcher = pattern.matcher(paragraph[i]);
                if (matcher.find()) {
                    integerArrayList.add(i);
                }
            }
            integerArrayList.add(paragraph.length);
            List<Integer> sort = CollUtil.sort(integerArrayList, Comparator.naturalOrder());
            for (int i = 0; i < sort.size(); i++) {
                if (sort.get(i) == paragraph.length) break;
                String[] subArray = Arrays.copyOfRange(paragraph, sort.get(i), sort.get(i + 1));
                String string = String.join("", subArray).replaceAll("\\s+", "").replaceAll("—\\d+—", "");
                strings.add(string);
            }
            strings = Arrays.asList(paragraph);
        }
        return strings;
    }

    /**
     * 判断字符串中是否只包含数字
     *
     * @param input
     * @return
     */
    private boolean checkIfStringContainsOnlyNumbers(String input) {
        String regex = "^[0-9]+$";
        return Pattern.matches(regex, input);
    }


    private JSONObject extractProvisions(List<String> line) {
        JSONObject result = initJSONObject();
        JSONArray provisions = result.getJSONArray("result");
        JSONArray relations = result.getJSONArray("relation");
        if (isStandard) {
            //规范类型
            getProvisions(line, result, provisions, relations);
        } else {
            //无制度条款类型
            getTitle(line, result, provisions, relations);
        }
        return result;
    }

    /**
     * 抽取制度条款
     */
    private void getProvisions(List<String> parts, JSONObject result, JSONArray provisions, JSONArray relations) {
        for (String part : parts) {

            String endPattern = "第[一二三四五六七八九十]+条";


            //细则
            String[] split = part.split("(?<=第[一二三四五六七八九十]+条)|(?=第[一二三四五六七八九十]+条)");


            if (split.length > 1) {
                for (int i = 1; i < split.length; i++) {
                    String s = split[i];
                    String num = split[i - 1];
                    getRelation(endPattern, s, num, provisions, relations, null);

                }
            }
        }
        result.putOpt("result", provisions);
        result.putOpt("relation", relations);
    }

    private void getTitle(List<String> parts, JSONObject result, JSONArray provisions, JSONArray relations) {
        /*JSONObject provision = new JSONObject();
        for (int i = 0; i < parts.size(); i++) {
            String part = parts.get(i);
            String endPattern = "第[一二三四五六七八九十]+条";
            if (i == 0) {
                part = part.trim();
                provision = newEntity(IdUtil.simpleUUID(), "概念", "制度条款", part);
                provisions.add(provision);
                //连接主节点
                relations.add(newRelation("提及条款", "0", provision.getStr("uuid")));
            } else {
                //细则
                String[] split = part.split("(?<=第[一二三四五六七八九十]+条)|(?=第[一二三四五六七八九十]+条)");
                if (1 < split.length) {
                    for (int j = 1; j < split.length; j++) {
                        String s = split[j];
                        String num = split[j - 1];
                        getRelation(endPattern, s, num, provisions, relations, provision);
                    }
                }
            }
        }
        result.putOpt("result", provisions);
        result.putOpt("relation", relations);*/
    }

    private void getRelation(String endPattern, String s, String num, JSONArray provisions, JSONArray relations, JSONObject provision) {

        if (!Pattern.matches(endPattern, s)) {
            JSONObject entries = new JSONObject();
            //序号

            JSONObject numProperty = newEntity(IdUtil.simpleUUID(), "属性", "条款序号", num);
            provisions.add(numProperty);

            //实例名称
            // 匹配第一个中文标点符号
            Pattern punctuation = Pattern.compile("[，。：]");
            Matcher nameMatcher = punctuation.matcher(s);
            if (nameMatcher.find()) {
                int splitIndex = nameMatcher.start();
                // 分割字符串
                String part1 =  s.substring(0, splitIndex);
                entries = getEntries(provisions, relations, provision, part1);
            }
            //内容
            JSONObject contentProperty = newEntity(IdUtil.simpleUUID(), "属性", "条款内容", s);
            provisions.add(contentProperty);
            relations.add(newRelation("属性", entries.getStr("uuid"), contentProperty.getStr("uuid")));
            //序号和实例相连
            relations.add(newRelation("属性", entries.getStr("uuid"), numProperty.getStr("uuid")));
        }
    }

    @NotNull
    private JSONObject getEntries(JSONArray provisions, JSONArray relations, JSONObject provision, String split) {
        JSONObject entries;
        entries = newEntity(IdUtil.simpleUUID(), "概念", "制度条款", split);
        provisions.add(entries);
        //JSONObject relation = newRelation("提及细则", provision.getStr("uuid"), entries.getStr("uuid"));
        //relations.add(relation);
        return entries;
    }

}
