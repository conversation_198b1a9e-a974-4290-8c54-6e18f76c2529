package org.irm.lab.kg.rabbitmq.consumer;

import com.rabbitmq.client.Channel;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.irm.lab.common.utils.ThreadLocalUtil;
import org.irm.lab.kg.rabbitmq.message.MetadataConverterMessage;
import org.irm.lab.kg.service.IMetadataConverterService;
import org.springframework.amqp.core.Message;
import org.springframework.amqp.rabbit.annotation.*;
import org.springframework.stereotype.Component;

import java.io.IOException;

/**
 * <AUTHOR>
 * @date 2023/3/8 18:07
 * @description 数据转化器消费者
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class MetadataConverterConsumer {

    private final IMetadataConverterService iMetadataConverterService;

    /**
     * 消息监听
     */
    @RabbitListener(bindings = @QueueBinding(
            value = @Queue(MetadataConverterMessage.QUEUE + "${spring.application.name}"),
            exchange = @Exchange(name = MetadataConverterMessage.EXCHANGE),
            key = MetadataConverterMessage.ROUTING_KEY + "${spring.application.name}"
    ),concurrency = "3")
    @RabbitHandler
    public void onMessage(MetadataConverterMessage message, Channel channel, Message messageStatus) throws IOException {
        // 获取消息状态信息
        long deliveryTag = messageStatus.getMessageProperties().getDeliveryTag();
        log.info("[onMessage][线程编号:{} 消息内容：{}]", Thread.currentThread().getId(), message);
        try {
            // 设置用户信息，防止租户问题
            ThreadLocalUtil.set("user", message.getUser());
            // 开启元数据转化
            iMetadataConverterService.metadataConverter(message.getTopCacheLabelDataId(),message.getProcessId(),message.getResourceId());
        } catch (Exception e) {
            if (messageStatus.getMessageProperties().getRedelivered()) {
                log.error("消息已重复处理失败，拒绝再次接收.....");
                channel.basicReject(deliveryTag, false);
            } else {
                log.info("消息即将再次返回队列中进行处理....");
                channel.basicNack(deliveryTag, false, true);
            }
        }
    }


}
