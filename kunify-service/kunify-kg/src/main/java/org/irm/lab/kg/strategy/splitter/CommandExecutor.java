package org.irm.lab.kg.strategy.splitter;

import java.io.BufferedReader;
import java.io.File;
import java.io.IOException;
import java.io.InputStreamReader;
import java.nio.file.Files;
import java.nio.file.Paths;

import org.irm.lab.common.exception.ServiceException;
import org.irm.lab.common.utils.ValidPathUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR> <br/>
 * @date 2024/5/31 <br/>
 * @Copyright 博客：https://eliauku.gitee.io/  ||  per aspera and astra <br/>
 */
@Service
public class CommandExecutor {

    private static final Logger log = LoggerFactory.getLogger(CommandExecutor.class);
    @Value("${pdf2json.path.linux}")
    private  String exeJson;

    public int executeCommandInDirectory(String directoryPath, String... command) {
        // 用于存储输出
        int exitCode = 0;
        try {
            ProcessBuilder processBuilder = new ProcessBuilder(command);
            if (!isValidFilePath(command)) {
                throw new ServiceException("非法命令异常");
            }
            processBuilder.directory(new File(directoryPath));
            final Process start = processBuilder.start();


            try {
                exitCode = start.waitFor();
            } catch (InterruptedException e) {
                e.printStackTrace();
            }

            // 合并标准输出和错误输出
            processBuilder.redirectErrorStream(true);

            return exitCode;

        } catch (IOException e) {
            e.printStackTrace();
        }
        return exitCode;
    }

    public String ofd2json(String sourceFilePath, String targetFilePath) throws IOException {
        // 执行命令

        String command = exeJson + File.separator + "documentextractor";
        String sourceUrl = "--file=" + sourceFilePath;
        String targetUrl = "--saveAsJsonFile=" + targetFilePath;
        final int i = executeCommandInDirectory(exeJson + File.separator, command, sourceUrl, targetUrl);

        if (i != 0) {
            log.error("执行命令失败: {}", command);
            return null;
        }
        // 校验文件是否在白名单路径中
        if(!ValidPathUtil.isValidPath(Paths.get(targetFilePath).toAbsolutePath().toString())) throw new ServiceException("非法文件夹路径异常");
        // 从目标文件读取 JSON 数据
        String jsonData = new String(Files.readAllBytes(Paths.get(targetFilePath)));


        // 删除源文件和目标文件
        final boolean b = Files.deleteIfExists(Paths.get(sourceFilePath));
        if (b) {
            log.info("删除源文件成功: {}", sourceFilePath);
        }

        final boolean b1 = Files.deleteIfExists(Paths.get(targetFilePath));
        if (b1) {
            log.info("删除目标文件成功: {}", targetFilePath);
        }

        return jsonData;
    }
    private boolean isValidFilePath(String[] command) {
        //for (String arg : command) {
        //
        //    if (!arg.matches("[a-zA-Z0-9./\\\\-_]+")) {
        //        return false;
        //    }
        //    Path normalizedPath = Paths.get(arg).normalize();
        //    if (!ValidPathUtil.isValidPath(normalizedPath.toString())) {
        //        return false;
        //    }
        //}
        return true;
    }

}
