package org.irm.lab.kg.service;

import org.irm.lab.repository.entity.Resource;
import org.irm.lab.repository.entity.ResourceAnnex;
import org.irm.lab.resource.entity.Attach;

/**
 * <AUTHOR>
 * @date 2023/2/15 16:58
 */
public interface IOfficeConvertService {
    void pdfToPic(Resource resource);
    void wordToPdf(Resource doc, Attach file);

    void wordToPdf(Resource doc, String name, String link);

    /**
     * 资源附件图片生成
     *
     * @param resourceAnnex 资源附件对象
     */
    void annexPdfToPic(ResourceAnnex resourceAnnex);
}
