package org.irm.lab.kg.controller.annex;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.irm.lab.common.annotation.MyLog;
import org.irm.lab.common.api.R;
import org.irm.lab.common.constant.LogConstant;
import org.irm.lab.common.support.MyPage;
import org.irm.lab.common.utils.Func;
import org.irm.lab.kg.algorithm.TableHeader;
import org.irm.lab.kg.entity.annex.AnnexDocumentUnit;
import org.irm.lab.kg.service.annex.IAnnexDocumentUnitService;
import org.irm.lab.kg.vo.annex.AnnexDocumentUnitVO;
import org.springframework.web.bind.annotation.*;

import java.io.FileNotFoundException;
import java.util.List;
import java.util.Map;

@RestController
@RequestMapping("/annex-document-unit")
@RequiredArgsConstructor
@Api(value = "附件语料单元")
public class AnnexDocumentUnitController {

    private final IAnnexDocumentUnitService iAnnexDocumentUnitService;

    /**
     * 语料分页查询
     *
     * @param pageMap 分页条件
     * @return {@link AnnexDocumentUnitVO}
     */
    @ApiOperation(value = "语料单元分页")
    @GetMapping("/page")
    public R<MyPage<AnnexDocumentUnitVO>> page(@RequestParam Map<String, Object> pageMap) {
        return R.data(iAnnexDocumentUnitService.page(pageMap));
    }

    /**
     * 语料单元新增/修改
     *
     * @param annexDocumentUnit 附件预料单元对象
     * @return {@link AnnexDocumentUnit}
     */
    @ApiOperation(value = "语料单元增改")
    @PostMapping("/save")
    public R<AnnexDocumentUnit> save(@RequestBody AnnexDocumentUnit annexDocumentUnit) {
        return R.data(iAnnexDocumentUnitService.save(annexDocumentUnit));
    }

    /**
     * 删除语料单元
     *
     * @param ids 语料单元Id
     */
    @ApiOperation(value = "语料单元删除")
    @PostMapping("/remove")
    public R<Void> removeAndUpdateSortInCurrentPage(@RequestBody String ids) {
        iAnnexDocumentUnitService.removeAndUpdateSortInCurrentPage(Func.objToStrList(ids));
        return R.success();
    }

    /**
     * 表头新增/修改
     *
     * @param unitId      语料单元id
     * @param tableHeader 表头数据
     */
    @ApiOperation(value = "表头增改", hidden = true)
    @PostMapping("/save-keys")
    public R<AnnexDocumentUnit> saveKeys(@RequestParam String unitId, @RequestBody TableHeader tableHeader) {
        return R.data(iAnnexDocumentUnitService.saveKeys(unitId, tableHeader));
    }

    /**
     * 表头删除
     *
     * @param unitId  语料单元id
     * @param tableId 表头Id
     */
    @ApiOperation(value = "表头删除", hidden = true)
    @PostMapping("/remove-keys")
    public R<Void> removeKeys(@RequestParam String unitId, @RequestParam String tableId) {
        iAnnexDocumentUnitService.removeKeys(unitId, tableId);
        return R.success();
    }

    /**
     * 表格新增/修改
     *
     * @param unitId 语料单元Id
     * @param map    表格数据
     */
    @ApiOperation(value = "表格行信息增改", hidden = true)
    @PostMapping("/save-row")
    public R<AnnexDocumentUnit> saveRow(@RequestParam String unitId, @RequestBody String map) {
        return R.data(iAnnexDocumentUnitService.saveRow(unitId, map));
    }

    /**
     * 表格信息删除
     *
     * @param unitId 语料单元Id
     * @param index  指定表格信息
     */
    @ApiOperation(value = "表格行信息删除", hidden = true)
    @PostMapping("/remove-row")
    public R<Void> removeRow(@RequestParam String unitId, @RequestParam String index) {
        iAnnexDocumentUnitService.removeRow(unitId, index);
        return R.success();
    }

    /**
     * 语料单元对应的图片预览
     *
     * @param id 语料单元Id
     */
    @ApiOperation(value = "语料单元预览")
    @GetMapping("/preview")
    public R<String> preview(@RequestParam String id) throws FileNotFoundException {
        return R.data(iAnnexDocumentUnitService.preview(id));
    }


    /**
     * 根据Id获取附件预料单元对象
     *
     * @param id 语料单元Id
     * @return {@link AnnexDocumentUnit}
     */
    @ApiOperation(value = "语料单元详情")
    @GetMapping("/info")
    public R<AnnexDocumentUnit> info(@RequestParam String id) {
        return R.data(iAnnexDocumentUnitService.info(id));
    }


    /**
     * 根据附件Id查询附件的所有语料单元
     *
     * @param annexId 附件Id
     * @return {@link AnnexDocumentUnit}
     */
    @GetMapping("/find-by-annex-id")
    public R<List<AnnexDocumentUnit>> findByAnnexId(@RequestParam String annexId) {
        return R.data(iAnnexDocumentUnitService.findByAnnexId(annexId));
    }


}
