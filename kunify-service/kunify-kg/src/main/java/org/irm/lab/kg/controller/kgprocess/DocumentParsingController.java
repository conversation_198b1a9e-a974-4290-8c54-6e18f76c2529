package org.irm.lab.kg.controller.kgprocess;

import java.util.List;
import java.util.stream.Collectors;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;

import cn.hutool.core.util.ObjectUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import org.bson.BsonDocument;
import org.bson.codecs.configuration.CodecRegistry;
import org.bson.conversions.Bson;
import org.irm.lab.common.annotation.MyLog;
import org.irm.lab.common.api.R;
import org.irm.lab.common.constant.LogConstant;
import org.irm.lab.common.utils.ThreadLocalUtil;
import org.irm.lab.kg.dto.ReParsDTO;
import org.irm.lab.kg.rabbitmq.ocrFrontLoaded.OcrFrontLoadedMessage;
import org.irm.lab.kg.rabbitmq.ocrFrontLoaded.OcrFrontLoadedProducer;
import org.irm.lab.kg.rabbitmq.producer.DocumentParsingProducer;
import org.irm.lab.kg.service.IDocumentParsingService;
import org.irm.lab.repository.constant.DocumentResolvedStatus;
import org.irm.lab.repository.entity.ResourceAnnex;
import org.irm.lab.repository.repository.ResourceAnnexRepository;
import org.irm.lab.repository.repository.ResourceRepository;
import org.springframework.web.bind.annotation.*;


/**
 * <AUTHOR>
 * @date 2023/3/9 17:17
 * @description 文档解析控制器
 */
@RestController
@RequestMapping("/doc-parsing")
@Slf4j

@RequiredArgsConstructor
@Api(value = "文档解析控制器")
public class DocumentParsingController {
    private final IDocumentParsingService idocumentParsingService;
    private final ResourceRepository resourceRepository;
    private final OcrFrontLoadedProducer ocrFrontLoadedProducer;
    private final ResourceAnnexRepository resourceAnnexRepository;
    @Resource
    private HttpServletRequest request;
    /**
     * 文档解析结果确认
     *
     * @param resourceId 资源Id
     */
    @MyLog(menu = LogConstant.MENU_RESOURCE, dataType = LogConstant.DATA_FILE, operation = LogConstant.OPERATION_ANALYSIS_TRUE)
    @ApiOperation(value = "文档解析结果确认")
    @GetMapping("/confirm")
    public R<String> confirm(@RequestParam String resourceId) {
        idocumentParsingService.confirm(resourceId);
        return R.success();
    }


    /**
     * 重新文档解析
     *
     * @param resourceId 资源Id
     */
    @MyLog(menu = LogConstant.MENU_RESOURCE, dataType = LogConstant.DATA_FILE, operation = LogConstant.OPERATION_REANALYSIS)
    @ApiOperation(value = "重新解析", hidden = true)
    @PostMapping("/re-parsing")
    public R<Void> reParsing(@RequestParam String resourceId, @RequestParam String type) {
        ThreadLocalUtil.set("user", request.getHeader("user"));
        if ("ocr".equals(type)) {
            idocumentParsingService.reParsingWithOCR(resourceId);
        } else {
            idocumentParsingService.reParsing(resourceId);
        }
        return R.success();
    }

    @javax.annotation.Resource
    private DocumentParsingProducer documentParsingProducer;

    @ApiOperation(value = "批量重新解析")
    @PostMapping("/all/re-parsing")
    public R<Void> allReParsing(@RequestBody ReParsDTO reParsDTO){
        ThreadLocalUtil.set("user", request.getHeader("user"));
        final List<String> resourceIds = reParsDTO.getResourceIds();
        final String type = reParsDTO.getType();

        final List<org.irm.lab.repository.entity.Resource> byId = resourceRepository.findById(resourceIds);
        byId.forEach(resource -> {
            resource.setResolveStatus(DocumentResolvedStatus.RESOLVING);
        });
        resourceRepository.saveAll(byId);
        for (String id : resourceIds) {
            if (type.equals("ocr")) {
                ocrFrontLoadedProducer.send(id,"ocr",request.getHeader("user"));

                //idocumentParsingService.reParsingWithOCR(id);
            } else {
                documentParsingProducer.sendMessage(request.getHeader("user"), id);
            }

        }

        log.info("发送完成");
        return R.success();
    }
}
