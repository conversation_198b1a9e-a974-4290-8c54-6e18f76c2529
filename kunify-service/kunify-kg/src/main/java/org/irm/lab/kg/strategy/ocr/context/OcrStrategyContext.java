package org.irm.lab.kg.strategy.ocr.context;

import java.io.IOException;
import java.util.Map;

import javax.annotation.Resource;

import org.irm.lab.kg.enums.OcrEnum;
import org.irm.lab.kg.strategy.ocr.OcrStrategy;
import org.springframework.stereotype.Service;

import cn.hutool.core.util.ObjectUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR> <br/>
 * @date 2024/5/1 <br/>
 * @Copyright 博客：https://eliauku.gitee.io/  ||  per aspera and astra <br/>
 */
@Service
@Slf4j
@RequiredArgsConstructor
public class OcrStrategyContext {

    @Resource
    private Map<String, OcrStrategy> fileInferStrategyMap;

    public boolean processOcr(String strategy, org.irm.lab.repository.entity.Resource message) throws IOException {

        final OcrEnum ocrEnum = OcrEnum.getByAlgorithmType(strategy);
        if (ObjectUtil.isEmpty(ocrEnum)) {
            log.error("不存在对应的策略，当前传递的策略code为{}" , strategy);
            return false;
        }
        log.info("开始执行策略：{}", ocrEnum.getName());
        final String name = OcrEnum.getByAlgorithmType(strategy).getName();

        if (ObjectUtil.isEmpty(fileInferStrategyMap.get(name))) {
            log.error("不存在对应的策略，当前传递的策略code为{}" , strategy);
            return false;
        }
        log.info("开始执行策略：{}", name);

        final OcrStrategy ocrStrategy = fileInferStrategyMap.get(name);
        log.info("开始执行策略：{}", ocrStrategy);

        return ocrStrategy.ocr(strategy, message);
    }
}
