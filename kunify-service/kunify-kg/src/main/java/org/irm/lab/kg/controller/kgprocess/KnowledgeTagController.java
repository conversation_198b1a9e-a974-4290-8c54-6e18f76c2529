package org.irm.lab.kg.controller.kgprocess;

import cn.hutool.core.util.ObjectUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.irm.lab.common.annotation.MyLog;
import org.irm.lab.common.api.R;
import org.irm.lab.common.constant.LogConstant;
import org.irm.lab.common.utils.Func;
import org.irm.lab.config.entity.Label;
import org.irm.lab.kg.algorithm.DocumentTextLight;
import org.irm.lab.kg.algorithm.DocumentUnit;
import org.irm.lab.kg.dto.CacheLabelDataDTO;
import org.irm.lab.kg.dto.ReKnowledgeTagDTO;
import org.irm.lab.kg.entity.processing.CacheTripleLabelData;
import org.irm.lab.kg.entity.processing.LabelPropertyVO;
import org.irm.lab.kg.service.IKnowledgeTagService;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/2/23 11:54
 * @description 知识标注控制器
 */
@RestController
@RequestMapping("/knowledge-tag")
@Api(value = "知识标注控制器")
@RequiredArgsConstructor
public class KnowledgeTagController {
    private final IKnowledgeTagService iKnowledgeTagService;

    /**
     * 新增高亮词
     *
     * @param documentUnitId    段落Id
     * @param documentTextLight {@link DocumentTextLight}
     * @return {@link DocumentUnit}
     */
    @MyLog(menu = LogConstant.MENU_KNOWLEDGE, dataType = LogConstant.DATA_HIGHLIGHT_WORD, operation = LogConstant.OPERATION_ADD_MARK)
    @ApiOperation(value = "新增高亮词")
    @PostMapping("/save")
    public R<DocumentUnit> save(@RequestParam("documentUnitId") String documentUnitId, @RequestBody DocumentTextLight documentTextLight) {
        if (ObjectUtil.isEmpty(documentTextLight.getStart()) || ObjectUtil.isEmpty(documentTextLight.getEnd()))
            return R.failed("下标错误!");
        return R.data(iKnowledgeTagService.save(documentUnitId, documentTextLight));
    }

    /**
     * 更新高亮词
     *
     * @param documentUnitId    段落Id
     * @param documentTextLight {@link DocumentTextLight}
     * @return {@link DocumentUnit}
     */
    @MyLog(menu = LogConstant.MENU_KNOWLEDGE, dataType = LogConstant.DATA_HIGHLIGHT_WORD, operation = LogConstant.OPERATION_ALTER)
    @ApiOperation(value = "更新高亮词")
    @PostMapping("/update")
    public R<DocumentUnit> update(@RequestParam("documentUnitId") String documentUnitId, @RequestBody DocumentTextLight documentTextLight) {
        if (ObjectUtil.isEmpty(documentTextLight.getStart()) || ObjectUtil.isEmpty(documentTextLight.getEnd()))
            return R.failed("下标错误!");
        return R.data(iKnowledgeTagService.update(documentUnitId, documentTextLight));
    }


    /**
     * 清除指定资源下的所有高亮词
     *
     * @param resourceId 资源Id
     */
    @MyLog(menu = LogConstant.MENU_KNOWLEDGE, dataType = LogConstant.DATA_HIGHLIGHT_WORD, operation = LogConstant.OPERATION_REMOVE)
    @ApiOperation(value = "清除指定资源下的所有高亮词")
    @PostMapping("/clear-all")
    public R<String> clearAll(@RequestParam("resourceId") String resourceId) {
        iKnowledgeTagService.clearAll(resourceId);
        return R.success();
    }

    /**
     * 清除当前页面的所有高亮词
     *
     * @param ids 语料单元Id列表
     */
    @MyLog(menu = LogConstant.MENU_KNOWLEDGE, dataType = LogConstant.DATA_HIGHLIGHT_WORD, operation = LogConstant.OPERATION_REMOVE)
    @ApiOperation(value = "清除当前页面的所有高亮词")
    @PostMapping("/clear-current-page")
    public R<String> clearCurrentPage(@RequestBody String ids) {
        List<String> idList = Func.objToStrList(ids);
        iKnowledgeTagService.removeByIds(idList);
        return R.success();
    }

    /**
     * 删除指定的高亮词
     *
     * @param identifier 高亮词唯一标识
     */
    @MyLog(menu = LogConstant.MENU_KNOWLEDGE, dataType = LogConstant.DATA_HIGHLIGHT_WORD, operation = LogConstant.OPERATION_REMOVE)
    @ApiOperation(value = "删除指定的高亮词")
    @PostMapping("/clear-by-identify")
    public R<String> clearByIdentify(@RequestParam("documentUnitId") String documentUnitId, @RequestParam("identifier") String identifier) {
        iKnowledgeTagService.clearByIdentify(documentUnitId, identifier);
        return R.success();
    }

    /**
     * 根据工作任务Id和类型 获取指定类型的标签
     *
     * @param workTaskId 工作任务Id
     * @param type       标签类型 概念、关系、属性
     * @return {@link Label}
     */
    @ApiOperation(value = "根据工作任务Id和类型 获取指定类型的标签", hidden = true)
    @GetMapping("/list-label")
    public R<List<Label>> listLabel(@RequestParam("workTaskId") String workTaskId, @RequestParam("type") String type) {
        return R.data(iKnowledgeTagService.listLabelOrPropertyOrPredicate(workTaskId, type));
    }

    /**
     * 根据资源Id获取实例（高亮词）对象
     *
     * @param resourceId 资源Id
     * @return {@link DocumentTextLight}
     */
    @ApiOperation(value = "根据资源Id获取实例（高亮词）对象", hidden = true)
    @GetMapping("/list-entity")
    public R<List<DocumentTextLight>> listEntity(@RequestParam("resourceId") String resourceId) {
        return R.data(iKnowledgeTagService.listEntity(resourceId));
    }


    /**
     * 为实例 新增/修改属性
     *
     * @param cacheLabelDataId 缓存标签Id
     * @param labelPropertyVO  {@link LabelPropertyVO}
     */
    @MyLog(menu = LogConstant.MENU_KNOWLEDGE, dataType = LogConstant.DATA_PROPERTY, operation = LogConstant.OPERATION_ADD_OR_ALTER)
    @PostMapping("/save-property")
    public R<Void> saveOrUpdateProperty(@RequestParam("id") String cacheLabelDataId, @RequestBody LabelPropertyVO labelPropertyVO) {
        iKnowledgeTagService.saveOrUpdateProperty(cacheLabelDataId,labelPropertyVO);
        return R.success();
    }

    /**
     * 删除指定的属性
     *
     * @param cacheLabelDataId 缓存标签Id
     * @param identifier 属性唯一标识
     */
    @MyLog(menu = LogConstant.MENU_KNOWLEDGE, dataType = LogConstant.DATA_PROPERTY, operation = LogConstant.OPERATION_REMOVE)
    @PostMapping("/remove-property")
    public R<Void> removeProperty(@RequestParam("id") String cacheLabelDataId,@RequestParam("identifier") String identifier){
        iKnowledgeTagService.removeProperty(cacheLabelDataId,identifier);
        return R.success();
    }

    /**
     * 为实例 新增/修改关系和属性
     *
     * @param cacheTripleLabelData {@link CacheTripleLabelData}
     */
    @MyLog(menu = LogConstant.MENU_KNOWLEDGE, dataType = LogConstant.DATA_RELATION, operation = LogConstant.OPERATION_ADD_OR_ALTER)
    @PostMapping("/save-relation")
    public R<Void> saveOrUpdateRelation(@RequestBody CacheTripleLabelData cacheTripleLabelData){
        iKnowledgeTagService.saveOrUpdateRelation(cacheTripleLabelData);
        return R.success();
    }

    /**
     * 删除指定关系
     *
     * @param cacheTripleLabelDataId 关系标签Id
     */
    @MyLog(menu = LogConstant.MENU_KNOWLEDGE, dataType = LogConstant.DATA_RELATION, operation = LogConstant.OPERATION_REMOVE)
    @PostMapping("/remove-relation")
    public R<Void> removeRelation(@RequestParam("id") String cacheTripleLabelDataId){
        iKnowledgeTagService.removeRelation(cacheTripleLabelDataId);
        return R.success();
    }


    /**
     * 根据标签数据缓存Id，获取该高亮词的属性和关系详情
     *
     * @param cacheLabelDataId 标签数据缓存Id
     * @return {@link CacheLabelDataDTO}
     */
    @ApiOperation(value = "根据标签数据缓存Id，获取该高亮词的属性和关系详情", hidden = true)
    @GetMapping("/info-cache-label")
    public R<CacheLabelDataDTO> infoCacheLabelData(@RequestParam String cacheLabelDataId) {
        return R.data(iKnowledgeTagService.infoCacheLabelData(cacheLabelDataId));
    }

    /**
     * 知识标注  重新调用算法识别
     *
     * @param resourceId 资源Id
     */
    @MyLog(menu = LogConstant.MENU_KNOWLEDGE, dataType = LogConstant.DATA_FILE, operation = LogConstant.OPERATION_REANALYSIS)
    @ApiOperation(value = "知识标注重新调用算法识别", hidden = true)
    @GetMapping("/re-knowledge-tag")
    public R<String> reKnowledgeTag(@RequestParam String resourceId) {
        iKnowledgeTagService.reKnowledgeTag(resourceId);
        return R.success();
    }

    @MyLog(menu = LogConstant.MENU_KNOWLEDGE, dataType = LogConstant.DATA_FILE, operation = LogConstant.OPERATION_REANALYSIS)
    @ApiOperation(value = "知识标注重新调用算法识别", hidden = true)
    @PostMapping("/all/re-knowledge-tag")
    public R<String> reKnowledgeTag(@RequestBody ReKnowledgeTagDTO reKnowledgeTagDTO) {
        final List<String> resourceIds = reKnowledgeTagDTO.getResourceIds();
        resourceIds.forEach(iKnowledgeTagService::reKnowledgeTag);
        return R.success();
    }


    /**
     * 知识标注确认
     *
     * @param resourceId 资源Id
     */
    @MyLog(menu = LogConstant.MENU_KNOWLEDGE, dataType = LogConstant.DATA_FILE, operation = LogConstant.OPERATION_MARK_CONFIRMATION)
    @ApiOperation(value = "知识标注确认")
    @GetMapping("/confirm")
    public R<String> confirm(@RequestParam String resourceId) {
        iKnowledgeTagService.confirm(resourceId);
        return R.success();
    }
}
