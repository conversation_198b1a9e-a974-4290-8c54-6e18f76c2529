package org.irm.lab.kg.service.annex;

import java.util.List;

public interface IAnnexDocumentImageService {
    /**
     * 根据附件唯一标识和页码获取图片预览地址
     *
     * @param annexIdentifier 附件唯一标识
     * @param page            页码
     * @return 图片预览地址
     */
    String getPagePicture(String annexIdentifier, Integer page);

    /**
     * 根据附件唯一标识获取该附件的所有图片
     *
     * @param annexIdentifier 附件唯一标识
     * @return 图片预览地址列表
     */
    List<String> getResourceImage(String annexIdentifier);
}
