package org.irm.lab.kg.service.impl.kgprocess;

import ch.qos.logback.core.recovery.ResilientSyslogOutputStream;
import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.mongodb.client.model.Filters;
import lombok.RequiredArgsConstructor;
import org.irm.lab.common.api.R;
import org.irm.lab.common.constant.ModelConceptConst;
import org.irm.lab.common.exception.ServiceException;
import org.irm.lab.common.provider.TenantProvider;
import org.irm.lab.common.utils.ThreadLocalUtil;
import org.irm.lab.kg.algorithm.DocumentUnit;
import org.irm.lab.kg.dto.AlignmentDTO;
import org.irm.lab.kg.dto.EntityPropertyDTO;
import org.irm.lab.kg.entity.KnowledgeConcept;
import org.irm.lab.kg.entity.KnowledgeProperty;
import org.irm.lab.kg.entity.neo4j.node.NodeEntity;
import org.irm.lab.kg.entity.neo4j.node.NodeRelation;
import org.irm.lab.kg.mongoService.KnowledgeConceptService;
import org.irm.lab.kg.repository.DocumentUnitRepository;
import org.irm.lab.kg.repository.KnowledgeConceptRepository;
import org.irm.lab.kg.repository.neo4j.node.NodeEntityRepository;
import org.irm.lab.kg.repository.neo4j.node.NodeRelationRepository;
import org.irm.lab.kg.service.IKnowledgeAlignmentService;
import org.irm.lab.kg.service.IKnowledgeConceptService;
import org.irm.lab.kg.service.IKnowledgePropertyService;
import org.irm.lab.kg.vo.echarts.EchartsNode;
import org.irm.lab.kg.vo.echarts.EchartsRelation;
import org.irm.lab.kg.vo.echarts.EchartsVO;
import org.irm.lab.kg.vo.node.NodeEntityVO;
import org.irm.lab.kg.vo.node.NodeRelationVO;
import org.irm.lab.repository.constant.KnowledgeAlignmentStatus;
import org.irm.lab.repository.constant.KnowledgeMatchStatus;
import org.irm.lab.repository.entity.Resource;
import org.irm.lab.repository.entity.ResourceAnnex;
import org.irm.lab.repository.feign.ResourceFeign;
import org.irm.lab.repository.repository.ResourceAnnexRepository;
import org.neo4j.ogm.cypher.ComparisonOperator;
import org.neo4j.ogm.cypher.Filter;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * <AUTHOR>
 * @date 2023/3/21 11:12
 * @description 知识对齐业务实现类
 */
@Service
@RequiredArgsConstructor

public class KnowledgeAlignmentServiceImpl implements IKnowledgeAlignmentService {

    private static final Logger log = LoggerFactory.getLogger(KnowledgeAlignmentServiceImpl.class);
    private final NodeEntityRepository nodeEntityRepository;
    private final NodeRelationRepository nodeRelationRepository;
    private final IKnowledgeConceptService iKnowledgeConceptService;
    private final IKnowledgePropertyService iKnowledgePropertyService;
    private final DocumentUnitRepository documentUnitRepository;
    private final ResourceFeign resourceFeign;
    @javax.annotation.Resource
    private KnowledgeConceptRepository knowledgeConceptRepository;

    private final KnowledgeConceptService knowledgeConceptService;

    private final TenantProvider tenantProvider;

    private final ResourceAnnexRepository resourceAnnexRepository;

    @Override
    public JSONArray alignmentTreeStep1(String resourceId, String modelId) {
        // 根据资源Id查询该资源下的所有 实例节点
        String entityCypher = "MATCH (n:ENTITY) WHERE ANY(x IN n.docIds WHERE x = $resourceId ) and n.modelId = $modelId  RETURN n";
        long millis1 = System.currentTimeMillis();
        List<NodeEntity> nodeEntityList = nodeEntityRepository.findEntityByCypher(entityCypher, Map.of("resourceId", resourceId, "modelId", modelId));
        // 过滤筛选，相同概念和名称的只留一个
        List<NodeEntity> entityArrayList = nodeEntityList.stream()
                .collect(Collectors
                        .collectingAndThen(Collectors.toCollection(
                                        () -> new TreeSet<>(Comparator.comparing(nodeEntity -> nodeEntity.getConceptId() + nodeEntity.getEntityName())))
                                , ArrayList::new));
        // 创建树结构
        JSONArray treeArray = new JSONArray();
        // 封装 key：概念 value：该概念下的所有实例
        Map<String, String> conceptNameCache = new HashMap<>();
        for (NodeEntity nodeEntity : entityArrayList) {
            String conceptId = nodeEntity.getConceptId();
            // 获取同概念、同名称的所有 实例节点
            org.neo4j.ogm.cypher.Filters filters = new org.neo4j.ogm.cypher.Filters();
            filters.and(new Filter("conceptId", ComparisonOperator.EQUALS, conceptId));
            filters.and(new Filter("entityName", ComparisonOperator.EQUALS, nodeEntity.getEntityName()));
            Long count = nodeEntityRepository.countByCondition(filters);
            if (count == 1) continue;
            JSONObject sameEntities = JSONUtil.createObj();
            String conceptName = conceptNameCache.computeIfAbsent(conceptId, id -> iKnowledgeConceptService.simpleInfo(id).getName());
            sameEntities.putOpt("conceptId", conceptId)
                    .putOpt("conceptName", conceptName)
                    .putOpt("entityName", nodeEntity.getEntityName())
                    .putOpt("entityId", nodeEntity.getId())
                    .putOpt("num", count - 1);
            treeArray.add(sameEntities);
        }
        return treeArray;
    }

    public JSONObject alignmentTreeStep2(String entityId, String conceptId, String entityName, String modelId) {
        List<NodeEntity> sameEntityList = nodeEntityRepository.findByCondition(Map.of("conceptId", conceptId, "entityName", entityName));
        JSONObject topEntity = new JSONObject();
        JSONArray jsonArray = new JSONArray();
        for (NodeEntity nodeEntity : sameEntityList) {
            if (nodeEntity.getId().equals(entityId)) {
                topEntity = new JSONObject(generatePropertiesAndRelation(nodeEntity));
                continue;
            }
            NodeEntityVO otherNodeEntityVO = generatePropertiesAndRelation(nodeEntity);
            jsonArray.add(new JSONObject(otherNodeEntityVO));
        }
        topEntity.putOpt("children", jsonArray);
        return topEntity;
    }

    /**
     * 知识对齐树结构
     *
     * @param resourceId 资源Id
     * @return 知识对齐树
     */
    @Override
    public JSONArray alignmentTree(String resourceId, String modelId) {
        // 根据资源Id查询该资源下的所有 实例节点
        String entityCypher = "MATCH (n:ENTITY) WHERE ANY(x IN n.docIds WHERE x = $resourceId ) and n.modelId = $modelId  RETURN n";
        long millis1 = System.currentTimeMillis();
        List<NodeEntity> nodeEntityList = nodeEntityRepository.findEntityByCypher(entityCypher, Map.of("resourceId", resourceId, "modelId", modelId));
        long millis2 = System.currentTimeMillis();
        System.out.println("1:" + (millis2 - millis1));
        // 过滤筛选，相同概念和名称的只留一个
        List<NodeEntity> entityArrayList = nodeEntityList.stream()
                .collect(Collectors
                        .collectingAndThen(Collectors.toCollection(
                                        () -> new TreeSet<>(Comparator.comparing(nodeEntity -> nodeEntity.getConceptId() + nodeEntity.getEntityName())))
                                , ArrayList::new));
        // 创建map，存储概念和对应的冲突实例
        HashMap<String, List<NodeEntity>> sameEntityMap = new HashMap<>();
        // 创建树结构
        JSONArray treeArray = new JSONArray();
        // 封装 key：概念 value：该概念下的所有实例
        for (NodeEntity nodeEntity : entityArrayList) {
            // 获取概念Id
            String conceptId = nodeEntity.getConceptId();
            // 获取同概念、同名称的所有 实例节点
            List<NodeEntity> sameEntityList = nodeEntityRepository.findByCondition(Map.of("conceptId", conceptId, "entityName", nodeEntity.getEntityName()));
            // 如果
            if (sameEntityList.size() > 1) {
                // 判断是否存在
                List<NodeEntity> entityList = sameEntityMap.get(conceptId);
                // 新增冲突的实例
                if (ObjectUtil.isEmpty(entityList)) {
                    sameEntityMap.put(conceptId, sameEntityList);
                } else {
                    entityList.addAll(sameEntityList);
                }
            }
        }
        long millis3 = System.currentTimeMillis();
        System.out.println("2:" + (millis3 - millis2));

        // 组装树结构
        for (Map.Entry<String, List<NodeEntity>> entry : sameEntityMap.entrySet()) {
            JSONObject entries = new JSONObject();
            // 概念
            String conceptId = entry.getKey();
            entries.putOpt("conceptId", conceptId);
            String name;
            try {
                name = iKnowledgeConceptService.simpleInfo(conceptId).getName();
            } catch (Exception e) {
                continue;
            }
            entries.putOpt("conceptName", name);
            // 实例列表
            List<NodeEntity> entityList = entry.getValue();
            // 根据实例名称分组
            Map<String, List<NodeEntity>> entityNameMap = entityList.stream().collect(Collectors.groupingBy(NodeEntity::getEntityName));
            JSONArray objects = new JSONArray();
            for (Map.Entry<String, List<NodeEntity>> stringListEntry : entityNameMap.entrySet()) {
                // 创建主要实例对象
                JSONObject mainEntity;
                JSONArray jsonArray = new JSONArray();
                // 获取所有冲突的实例
                List<NodeEntity> nodeEntities = stringListEntry.getValue();
                // 找到主要实例（当前公文的主要实例）
                NodeEntity firstEntity = nodeEntities.stream()
                        .filter(v -> ObjectUtil.isNotEmpty(v.getDocumentUnitIds()) && v.getDocIds().contains(resourceId))
                        .findFirst()
                        .orElse(nodeEntities.get(0));
                // 封装属性
                NodeEntityVO firstEntityVO = generatePropertiesAndRelation(firstEntity);
                mainEntity = new JSONObject(firstEntityVO);
                // 遍历其他实例
                for (int i = 0; i < stringListEntry.getValue().size(); i++) {
                    // 添加与之冲突的实例
                    NodeEntity otherNodeEntity = stringListEntry.getValue().get(i);
                    if (otherNodeEntity.getId().equals(firstEntity.getId())) continue;
                    NodeEntityVO otherNodeEntityVO = generatePropertiesAndRelation(otherNodeEntity);
                    jsonArray.add(new JSONObject(otherNodeEntityVO));
                }
                // 添加冲突实例
                mainEntity.putOpt("children", jsonArray);
                objects.add(mainEntity);
            }
            if (!objects.isEmpty()) {
                entries.putOpt("children", objects);
            }
            treeArray.add(entries);
        }
        long millis4 = System.currentTimeMillis();
        System.out.println("3:" + (millis4 - millis3));
        return treeArray;
    }

    /**
     * 封装实例的属性和关系
     */
    private NodeEntityVO generatePropertiesAndRelation(NodeEntity nodeEntity) {
        // 复制对象
        NodeEntityVO nodeEntityVO = BeanUtil.copyProperties(nodeEntity, NodeEntityVO.class, "documentUnit", "relations");
        // 创建属性Map
        HashMap<String, Object> propertyMap = new HashMap<>();
        // 根据属性的唯一标识，查询属性对象
        Map<String, Object> properties = nodeEntity.getProperties();
        for (String propIdentifier : properties.keySet()) {
            String[] split = propIdentifier.split("_");
            List<KnowledgeProperty> propertyList = iKnowledgePropertyService.findByBson(Filters.eq("identifier", split[1]));
            if (ObjectUtil.isNotEmpty(propertyList)) {
                KnowledgeProperty knowledgeProperty = propertyList.get(0);
                // 封装属性
                propertyMap.put(knowledgeProperty.getName(), properties.get(propIdentifier));

            }
        }
        // 设置属性
        nodeEntityVO.setRealProperties(propertyMap);
        // 获取实例对象
        NodeEntity newNodeEntity = nodeEntityRepository.findByCondition(Map.of("id", nodeEntity.getId()), 1).get(0);
        // 封装关系
        List<NodeRelationVO> nodeRelationVOS = new ArrayList<>();
        // 设置宾语关系
        Set<NodeRelation> fromEntity = newNodeEntity.getFromEntity();
        if (ObjectUtil.isNotEmpty(fromEntity)) generateRelation(nodeRelationVOS, fromEntity);

        // 设置主语关系
        Set<NodeRelation> toEntity = newNodeEntity.getToEntity();
        if (ObjectUtil.isNotEmpty(toEntity)) generateRelation(nodeRelationVOS, toEntity);
        // 设置关系
        nodeEntityVO.setRelations(nodeRelationVOS);
        // 封装高亮词对象
        try {
            if (ObjectUtil.isNotEmpty(nodeEntity.getDocumentUnitIds())) {
                List<DocumentUnit> documentUnitList = nodeEntity.getDocumentUnitIds().stream().map(documentUnitRepository::findById).collect(Collectors.toList());
                nodeEntityVO.setDocumentUnits(documentUnitList);
            }
        } catch (Exception ignored) {

        }
        // 返回实例对象
        return nodeEntityVO;
    }

    /**
     * 封装关系
     */
    private static void generateRelation(List<NodeRelationVO> nodeRelationVOS, Set<NodeRelation> relationSet) {
        for (NodeRelation nodeRelation : relationSet) {
            NodeRelationVO nodeRelationVO = new NodeRelationVO();
            // 主语
            nodeRelationVO.setStartEntityName(nodeRelation.getStart().getEntityName());
            // 谓语
            nodeRelationVO.setRelationName(nodeRelation.getPredicateName());
            // 宾语
            nodeRelationVO.setEndEntityName(nodeRelation.getEnd().getEntityName());
            // 添加多个关系
            nodeRelationVOS.add(nodeRelationVO);
        }
    }

    /**
     * 知识可视化
     *
     * @param resourceId 资源Id
     * @return {@link EchartsVO}
     */
    @Override
    public EchartsVO visual(String resourceId, String name, String showCount) {
        // 创建视图对象
        EchartsVO echartsVO = new EchartsVO();

        // 根据资源Id获取该资源的所有实例节点
        // cypher语句
        String entityCypher = "MATCH (n:ENTITY) WHERE ANY(x IN n.docIds WHERE x = $resourceId )  RETURN n";
        List<NodeEntity> nodeEntityList = nodeEntityRepository.findEntityByCypher(entityCypher, Map.of("resourceId", resourceId));
        // 设置节点总数
        echartsVO.setNodeTotal(nodeEntityList.size());
        // 生成node节点集合
        Set<EchartsNode> echartsNodeSet = nodeEntityList.stream()
                .flatMap(cacheNodeEntity -> {
                    try {
                        final KnowledgeConcept knowledgeConcept = iKnowledgeConceptService.simpleInfo(cacheNodeEntity.getConceptId());
                        EchartsNode echartsNode = new EchartsNode();
                        echartsNode.setId(cacheNodeEntity.getId());
                        echartsNode.setConceptId(cacheNodeEntity.getConceptId());
                        echartsNode.setName(cacheNodeEntity.getEntityName());
                        echartsNode.setType(knowledgeConcept.getName());
                        return Stream.of(echartsNode);
                    } catch (Exception e) {
                        log.info("当前没找到的数据为conceptId:{},name:{}", cacheNodeEntity.getConceptId(), cacheNodeEntity.getEntityName());
                        return Stream.empty();
                    }
                })
                .collect(Collectors.toSet());


        if (ObjectUtil.isNotEmpty(name)) {
            echartsNodeSet = echartsNodeSet.stream().filter(echartsNode -> ObjectUtil.isNotEmpty(echartsNode.getName()) && echartsNode.getName().contains(name)).collect(Collectors.toSet());
        }

        if (ObjectUtil.isNotEmpty(showCount)) {
            final Integer anInt = Convert.toInt(showCount);
            echartsNodeSet = echartsNodeSet.stream().limit(anInt).collect(Collectors.toSet());
        }

        // 设置实例
        echartsVO.setNodes(echartsNodeSet);

        String relationCypher = "MATCH (end:ENTITY)<-[r:RELATION]-(start) WHERE ANY(x IN r.docIds WHERE x = $resourceId) return start,r,end";
        // 根据资源Id获取该资源的所有关系节点
        List<NodeRelation> nodeRelationList = nodeRelationRepository.findRelationByCypher(relationCypher, Map.of("resourceId", resourceId));
        //只展示节点之间的正向关系
        nodeRelationList = nodeRelationList.stream().filter(NodeRelation::isPositive).collect(Collectors.toList());
        final HashSet<String> echartsRelationIDSet = new HashSet<>();

        Set<EchartsRelation> echartsRelationSet = nodeRelationList.stream()
                .filter(nodeRelation -> echartsRelationIDSet.add(nodeRelation.getStart().getId() + nodeRelation.getEnd().getId()))
                .map(nodeRelation -> {
                    EchartsRelation echartsRelation = new EchartsRelation();
                    echartsRelation.setId(nodeRelation.getId());
                    echartsRelation.setName(nodeRelation.getPredicateName());
                    echartsRelation.setConceptId(nodeRelation.getPredicateId());
                    echartsRelation.setSource(nodeRelation.getStart().getId());
                    echartsRelation.setTarget(nodeRelation.getEnd().getId());
                    return echartsRelation;
                })
                .collect(Collectors.toCollection(HashSet::new)); // 直接收集到HashSet

        // 设置关系
        echartsVO.setRelations(echartsRelationSet);
        return wrapperMeeting(echartsVO);
    }

    private EchartsVO wrapperMeeting(EchartsVO echartsVO) {
        // 现场需求：如果是会议，需要把会议指向的边也纳入，不考虑是否是当前docId
        Set<EchartsNode> echartsNodes = echartsVO.getNodes();
        Set<EchartsRelation> echartsRelations = echartsVO.getRelations();

        // 获得会议指出的宾语
        List<NodeRelation> relations = echartsNodes.stream()
                .filter(echartsNode -> ModelConceptConst.CONCEPT_HUIYI.equals(echartsNode.getType()))
                .flatMap(echartsMeetingNode -> nodeEntityRepository.findById(echartsMeetingNode.getId(), 1).getToEntity().stream()).collect(Collectors.toList());

        relations.forEach(relation -> {
            String conceptId = relation.getEnd().getConceptId();
            KnowledgeConcept concept = knowledgeConceptRepository.findById(conceptId);

            EchartsNode echartsNode = new EchartsNode();
            echartsNode.setId(relation.getEnd().getId());
            echartsNode.setType(concept.getName());
            echartsNode.setConceptId(conceptId);
            echartsNode.setName(relation.getEnd().getEntityName());
            echartsNodes.add(echartsNode);


            EchartsRelation echartsRelation = new EchartsRelation();
            echartsRelation.setConceptId(relation.getPredicateId());
            echartsRelation.setId(relation.getId());
            echartsRelation.setName(relation.getPredicateName());
            echartsRelation.setSource(relation.getStart().getId());
            echartsRelation.setTarget(relation.getEnd().getId());
            echartsRelations.add(echartsRelation);
        });
        echartsVO.setNodes(echartsNodes);
        echartsVO.setRelations(echartsRelations);
        echartsVO.setNodeTotal(echartsNodes.size());
        return echartsVO;
    }

    private void traverseGraph(NodeEntity nodeEntity, List<NodeEntity> nodeEntitySet, List<NodeRelation> nodeRelationSet, Set<String> existIds, int depth) {
        if (!existIds.contains("node" + nodeEntity.getId())) nodeEntitySet.add(nodeEntity);
        existIds.add("node" + nodeEntity.getId());
        if (depth <= 0) return;
        Set<NodeRelation> toEntity = nodeEntity.getToEntity();
        if (toEntity == null) return;
        for (NodeRelation nodeRelation : toEntity) {
            if (!nodeRelation.isPositive()) continue;
            if (!existIds.contains("relation" + nodeRelation.getId())) nodeRelationSet.add(nodeRelation);
            existIds.add("relation" + nodeRelation.getId());
            NodeEntity end = nodeRelation.getEnd();
            traverseGraph(end, nodeEntitySet, nodeRelationSet, existIds, depth - 1);
        }

    }

    /**
     * 一键知识对齐
     *
     * @param resourceId 资源Id
     */
    @Override
    public void passAlignment(String resourceId) {
        R<Resource> resourceR = resourceFeign.info(resourceId);
        Resource resource = resourceR.getData();
        if (!resourceR.isSuccess() || resource == null) {
            throw new ServiceException("当前资源不存在!");
        }
        // 修改对齐状态为“已完成”,对齐已确认
        resource.setAlignmentStatus(KnowledgeAlignmentStatus.ALIGNMENT);
        resource.setKnowledgeAlignmentConfirm(true);
        resourceFeign.save(resource);
    }

    /**
     * 批量知识对齐
     */
    @Override
    public void passAlignmentMulti(List<String> resourceIds) {
        resourceIds.forEach(this::passAlignment);
    }

    /**
     * 知识对齐
     *
     * @param alignmentDTO {@link AlignmentDTO}
     */
    @Override
    public synchronized void alignment(AlignmentDTO alignmentDTO) {
        NodeEntity sourceEntity;
        NodeEntity targetEntity;
        try {
            // 获取要对齐的实例对象
            String sourceEntityId = alignmentDTO.getSourceEntityId();
            sourceEntity = nodeEntityRepository.findByCondition(Map.of("id", sourceEntityId), 1).get(0);
            // 获取被对齐的实例Id
            String targetEntityId = alignmentDTO.getTargetEntityId();
            targetEntity = nodeEntityRepository.findByCondition(Map.of("id", targetEntityId), 1).get(0);
        } catch (Exception e) {
            throw new ServiceException("当前冲突已对齐!");
        }

        // 清空要对齐的实例的属性
        sourceEntity.setProperties(new HashMap<>());
        // 设置新的属性
        HashMap<String, Object> properties = new HashMap<>();
        for (EntityPropertyDTO alignmentProperty : alignmentDTO.getAlignmentProperties()) {
            if (ObjectUtil.isNotEmpty(alignmentProperty.getPropertyName()) && ObjectUtil.isNotEmpty(alignmentProperty.getValue())) {
                properties.put(alignmentProperty.getPropertyName(), alignmentProperty.getValue());
            }
        }
        sourceEntity.setProperties(properties);
        // 设置来源文档
        Set<String> docIds = sourceEntity.getDocIds();
        docIds.addAll(targetEntity.getDocIds());
        sourceEntity.setDocIds(docIds);
        // 如果被对齐的实例，存在关系，则把关系复制到要对齐的实例上
        // 指入关系
        Set<NodeRelation> fromEntity = targetEntity.getFromEntity();
        // 更新要对齐的实例
        sourceEntity = nodeEntityRepository.save(sourceEntity);
        if (ObjectUtil.isNotEmpty(fromEntity)) {
            // 把所有指出的end改为sourceEntity
            for (NodeRelation nodeRelation : fromEntity) {
                nodeRelation.setEnd(sourceEntity);
                nodeRelation.setDocIds(docIds);
                nodeRelationRepository.save(nodeRelation);
            }
        }
        // 指出关系
        Set<NodeRelation> toEntity = targetEntity.getToEntity();
        if (ObjectUtil.isNotEmpty(toEntity)) {
            // 把所有指入的start改为sourceEntity
            for (NodeRelation nodeRelation : toEntity) {
                nodeRelation.setStart(sourceEntity);
                nodeRelation.setDocIds(docIds);
                nodeRelationRepository.save(nodeRelation);
            }

        }
        // 更新要对齐的实例
        nodeEntityRepository.save(sourceEntity);
        // 删除被对齐的实例
        nodeEntityRepository.deleteById(targetEntity.getId());

        // 解决所有冲突后，改变资源状态为“已完成”
        if (alignmentDTO.isFlag()) {
            String resourceId = alignmentDTO.getResourceId();
            // 修改资源状态
            passAlignment(resourceId);
        }
    }

    /**
     * 知识对齐
     *
     * @param alignmentDTO {@link AlignmentDTO}
     */
    @Override
    public void alignmentPlus(AlignmentDTO alignmentDTO) {
        NodeEntity sourceEntity;
        NodeEntity targetEntity;
        try {
            // 获取要对齐的实例对象
            String sourceEntityId = alignmentDTO.getSourceEntityId();
            sourceEntity = nodeEntityRepository.findByCondition(Map.of("id", sourceEntityId), 1).get(0);
            // 获取被对齐的实例Id
            String targetEntityId = alignmentDTO.getTargetEntityId();
            targetEntity = nodeEntityRepository.findByCondition(Map.of("id", targetEntityId), 1).get(0);
        } catch (Exception e) {
            e.printStackTrace();
            return;
        }
        // 清空要对齐的实例的属性
        sourceEntity.setProperties(new HashMap<>());
        // 设置新的属性
        HashMap<String, Object> properties = new HashMap<>();
        for (EntityPropertyDTO alignmentProperty : alignmentDTO.getAlignmentProperties()) {
            if (ObjectUtil.isNotEmpty(alignmentProperty.getPropertyName()) && ObjectUtil.isNotEmpty(alignmentProperty.getValue())) {
                properties.put(alignmentProperty.getPropertyName(), alignmentProperty.getValue());
            }
        }
        sourceEntity.setProperties(properties);
        // 设置来源文档
        Set<String> docIds = sourceEntity.getDocIds();
        docIds.addAll(targetEntity.getDocIds());
        sourceEntity.setDocIds(docIds);
        // 如果被对齐的实例，存在关系，则把关系复制到要对齐的实例上
        // 指入关系
        Set<NodeRelation> fromEntity = targetEntity.getFromEntity();
        // 更新要对齐的实例
        sourceEntity = nodeEntityRepository.save(sourceEntity);

//        sourceEntity.getToEntity().addAll(targetEntity.getToEntity());
//        sourceEntity.getFromEntity().addAll(targetEntity.getFromEntity());

//        nodeEntityRepository.mergeNodesWithSpecificIds(sourceEntity.getId(),targetEntity.getId());
        // 修订指出关系
        nodeEntityRepository.redirectRelations(sourceEntity.getId(), targetEntity.getId(), docIds);
        nodeEntityRepository.incoming(sourceEntity.getId(), targetEntity.getId(), docIds);
        // 修订指入关系

//        if (ObjectUtil.isNotEmpty(fromEntity)) {
//            // 把所有指出的end改为sourceEntity
//            for (NodeRelation nodeRelation : fromEntity) {
//                log.info("指入关系{}",nodeRelation.toString());
////                NodeRelation newNodeRelation = new NodeRelation();
////                BeanUtil.copyProperties(nodeRelation,newNodeRelation);
////                log.info("指入关系{}",newNodeRelation.toString());
////                nodeRelationRepository.deleteById(nodeRelation.getId());
////                newNodeRelation.setEnd(sourceEntity);// 原有的关系指向保留的节点
//                nodeRelation.setEnd(sourceEntity);
////                newNodeRelation.setDocIds(docIds);
//                nodeRelation.setDocIds(docIds);
////                sourceEntity.getFromEntity().add(nodeRelation);
////                NodeRelation save = nodeRelationRepository.save(newNodeRelation);
//                NodeRelation save = nodeRelationRepository.save(nodeRelation);
//                log.info("保存后的指入关系{}",save.toString());
//            }
//        }
//        // 指出关系
//        Set<NodeRelation> toEntity = targetEntity.getToEntity();
//        if (ObjectUtil.isNotEmpty(toEntity)) {
//            // 把所有指入的start改为sourceEntity
//            for (NodeRelation nodeRelation : toEntity) {
////                NodeRelation newNodeRelation = new NodeRelation();
////                BeanUtil.copyProperties(nodeRelation,newNodeRelation);
////                nodeRelationRepository.deleteById(nodeRelation.getId());
//                log.info("指出关系{}",nodeRelation.toString());
////                newNodeRelation.setStart(sourceEntity);
////                newNodeRelation.setDocIds(docIds);
//                nodeRelation.setStart(sourceEntity);
//                nodeRelation.setDocIds(docIds);
////                sourceEntity.getToEntity().add(nodeRelation);
//                NodeRelation save = nodeRelationRepository.save(nodeRelation);
//                log.info("保存后的指出关系{}",save.toString());
//            }
//        }
        // 更新要对齐的实例
//        nodeEntityRepository.save(sourceEntity);
//        // 删除被对齐的实例  删除的点应该考虑它是否也可能是其他图谱的关联点
//        nodeEntityRepository.deleteById(targetEntity.getId());
        nodeEntityRepository.deleteByProperty("id", targetEntity.getId());

        // 解决所有冲突后，改变资源状态为“已完成”
        if (alignmentDTO.isFlag()) {
            String resourceId = alignmentDTO.getResourceId();
            // 修改资源状态
            passAlignment(resourceId);
        }
    }

    @Override
    public synchronized void alignmentBatch(List<AlignmentDTO> alignmentDTOs) {
        for (AlignmentDTO alignmentDTO : alignmentDTOs) {
            System.out.println("当前对齐的实体数据为 ： " + alignmentDTO);
            this.alignmentPlus(alignmentDTO);
        }
    }

    @Override
    public synchronized void rollback(List<String> resourceIds) {
        for (String resourceId : resourceIds) {
            this.rollback(resourceId);
        }
    }


    private void rollback(String resourceId) {
        // 根据资源Id获取资源对象
        R<Resource> resourceR = resourceFeign.info(resourceId);
        Resource resource = resourceR.getData();
        if (!resourceR.isSuccess() || ObjectUtil.isEmpty(resource)) {
            throw new ServiceException("当前资源不存在!");
        }
//        String originAlignmentStatus = resource.getAlignmentStatus();
//        boolean originKnowledgeMatchConfirm = resource.isKnowledgeMatchConfirm();
//        String originMatchStatus = resource.getMatchStatus();
//        boolean originKnowledgeAlignmentConfirm = resource.isKnowledgeAlignmentConfirm();

        // 删除neo4j数据
        log.info("<<<<<<<<<<<<<<<<<<<<<<<<<<<<<当前主文件名称{}、id:{}及所属附件正在回退图谱节点.........>>>>>>>>>>>>>>>>>>>>>>>>>>>>>", resource.getName(), resource.getId());
        nodeEntityRepository.deleteByDocIds(resourceId);
        log.info("<<<<<<<<<<<<<<<<<<<<<<<<<<<<<当当前主文件名称{}、id:{}及所属附件图谱节点回退完成.........>>>>>>>>>>>>>>>>>>>>>>>>>>>>>", resource.getName(), resource.getId());
        //  回退资源状态
        resource.setAlignmentStatus(KnowledgeAlignmentStatus.UN_ALIGNMENT);
        resource.setKnowledgeMatchConfirm(false);
        // 修改资源状态为“匹配完成"
        resource.setMatchStatus(KnowledgeMatchStatus.MATCHED);
        // 修改资源状态为”知识对齐未确认“
        resource.setKnowledgeAlignmentConfirm(false);
        // 先修改附件的状态及图普节点
        List<ResourceAnnex> annexResource = resourceAnnexRepository.findByCondition(Filters.eq("resourceId", resource.getId()));
        if (!annexResource.isEmpty()) {
            for (ResourceAnnex resourceAnnex : annexResource) {
                resourceAnnex.setAlignmentStatus(KnowledgeAlignmentStatus.UN_ALIGNMENT);
                resourceAnnex.setKnowledgeMatchConfirm(false);
                // 修改资源状态为“匹配完成"
                resourceAnnex.setMatchStatus(KnowledgeMatchStatus.MATCHED);
                // 修改资源状态为”知识对齐未确认“
                resourceAnnex.setKnowledgeAlignmentConfirm(false);
                log.info("<<<<<<<<<<<<<<<<<<<<<<<<<<<<<当前附件名称{}、id:{}状态回退完成.........>>>>>>>>>>>>>>>>>>>>>>>>>>>>>", resourceAnnex.getName(), resourceAnnex.getId());
            }
        }
        resourceFeign.save(resource);
        log.info("<<<<<<<<<<<<<<<<<<<<<<<<<<<<<当前主文件名称{}、id:{}文件回退状态完成.........>>>>>>>>>>>>>>>>>>>>>>>>>>>>>", resource.getName(), resource.getId());
        if (!annexResource.isEmpty())
            resourceAnnexRepository.saveAll(annexResource);
        log.info("<<<<<<<<<<<<<<<<<<<<<<<<<<<<<附件回退状态完成.........>>>>>>>>>>>>>>>>>>>>>>>>>>>>>");
    }
}
