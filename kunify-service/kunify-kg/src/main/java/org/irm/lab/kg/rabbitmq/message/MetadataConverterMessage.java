package org.irm.lab.kg.rabbitmq.message;

import lombok.Data;


import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2023/3/8 16:29
 * @description 元数据转化消息
 */
@Data
public class MetadataConverterMessage implements Serializable {
    /**
     * 元数据转化 交换机
     */
    public static final String EXCHANGE = "KUNIFY4_EXCHANGE_METADATA_CONVERTER_";
    /**
     * 元数据转化 消息队列
     */
    public static final String QUEUE = "KUNIFY4_QUEUE_METADATA_CONVERTER_";
    /**
     * 元数据转化 routingKey
     */
    public static final String ROUTING_KEY = "KUNIFY4_ROUTING_METADATA_CONVERTER_";
    /**
     * 当前用户信息
     */
    private String user;
    /**
     * 顶级标签Id
     */
    private String topCacheLabelDataId;
    /**
     * 流程对象Id
     */
    private String processId;
    /**
     * 资源Id
     */
    private String resourceId;
}
