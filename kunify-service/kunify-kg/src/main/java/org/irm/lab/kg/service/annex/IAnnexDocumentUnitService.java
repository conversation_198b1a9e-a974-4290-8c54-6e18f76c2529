package org.irm.lab.kg.service.annex;

import org.irm.lab.common.support.MyPage;
import org.irm.lab.kg.algorithm.TableHeader;
import org.irm.lab.kg.entity.annex.AnnexDocumentUnit;
import org.irm.lab.kg.vo.annex.AnnexDocumentUnitVO;

import java.io.FileNotFoundException;
import java.util.List;
import java.util.Map;

public interface IAnnexDocumentUnitService {

    /**
     * 语料单元分页
     *
     * @return 根据页码获取符合当前type的DocumentUnit
     */
    MyPage<AnnexDocumentUnitVO> page(Map<String, Object> map);

    /**
     * 语料单元添加/编辑
     *
     * @param annexDocumentUnit 附件语料单元
     * @return {@link  AnnexDocumentUnit}
     */
    AnnexDocumentUnit save(AnnexDocumentUnit annexDocumentUnit);

    /**
     * 语料单元删除
     *
     * @param ids 需删除的附件语料单元Id列表
     */
    void removeAndUpdateSortInCurrentPage(List<String> ids);

    /**
     * 根据预料单元Id删除
     *
     * @param ids 语料单元ID
     */
    void remove(List<String> ids);

    /**
     * 修改表头
     *
     * @param unitId      预料单元Id
     * @param tableHeader 表头
     * @return {@link AnnexDocumentUnit}
     */
    AnnexDocumentUnit saveKeys(String unitId, TableHeader tableHeader);

    /**
     * 表头删除
     *
     * @param unitId  语料单元id
     * @param tableId 表头id
     */
    void removeKeys(String unitId, String tableId);

    /**
     * 表格行信息修改
     *
     * @param unitId 语料单元id
     * @param map    操作信息
     * @return 表格信息
     */
    AnnexDocumentUnit saveRow(String unitId, String map);

    /**
     * 删除表格行信息
     *
     * @param unitId 语料单元id
     * @param index  行索引
     */
    void removeRow(String unitId, String index);

    /**
     * 图片预览
     *
     * @param id 当前语料单元id
     * @return 标注后的图片
     */
    String preview(String id) throws FileNotFoundException;

    /**
     * 根据Id查询附件预料单元
     *
     * @param id 附件语料单元Id
     * @return {@link AnnexDocumentUnit}
     */
    AnnexDocumentUnit info(String id);

    /**
     * 根据附件Id查询附件的所有语料单元
     *
     * @param annexId 附件Id
     * @return {@link AnnexDocumentUnit}
     */
    List<AnnexDocumentUnit> findByAnnexId(String annexId);


}
