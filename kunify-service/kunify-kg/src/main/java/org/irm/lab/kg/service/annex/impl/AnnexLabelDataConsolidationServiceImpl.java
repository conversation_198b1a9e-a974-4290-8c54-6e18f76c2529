package org.irm.lab.kg.service.annex.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.ObjectUtil;
import com.mongodb.client.model.Filters;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.bson.conversions.Bson;
import org.irm.lab.kg.algorithm.DocumentTextLight;
import org.irm.lab.kg.algorithm.DocumentUnit;
import org.irm.lab.kg.entity.annex.AnnexDocumentUnit;
import org.irm.lab.kg.entity.annex.processing.AnnexCacheLabelData;
import org.irm.lab.kg.entity.annex.processing.AnnexCacheTripleLabelData;
import org.irm.lab.kg.entity.annex.processing.AnnexLabelData;
import org.irm.lab.kg.entity.annex.processing.AnnexTripleLabelData;
import org.irm.lab.kg.entity.processing.*;
import org.irm.lab.kg.repository.annex.AnnexCacheLabelDataRepository;
import org.irm.lab.kg.repository.annex.AnnexCacheTripleLabelDataRepository;
import org.irm.lab.kg.repository.annex.AnnexLabelDataRepository;
import org.irm.lab.kg.repository.annex.AnnexTripleLabelDataRepository;
import org.irm.lab.kg.repository.annex.processing.AnnexDocumentUnitRepository;
import org.irm.lab.kg.service.annex.IAnnexLabelDataConsolidationService;
import org.irm.lab.kg.service.impl.kgprocess.CommonKgProcess;
import org.irm.lab.repository.constant.KnowledgeMatchStatus;
import org.irm.lab.repository.entity.ResourceAnnex;
import org.irm.lab.repository.feign.ResourceAnnexFeign;
import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2023/5/10 11:42
 * @description 附件缓存标签合并业务实现类
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class AnnexLabelDataConsolidationServiceImpl extends CommonKgProcess implements IAnnexLabelDataConsolidationService {

    private final ResourceAnnexFeign resourceAnnexFeign;
    private final AnnexCacheLabelDataRepository annexCacheLabelDataRepository;
    private final AnnexCacheTripleLabelDataRepository annexCacheTripleLabelDataRepository;
    private final AnnexLabelDataRepository annexLabelDataRepository;
    private final AnnexTripleLabelDataRepository annexTripleLabelDataRepository;
    private final AnnexDocumentUnitRepository annexDocumentUnitRepository;

    /**
     * 重新匹配
     *
     * @param annexId 附件Id
     */
    @Override
    public void reConsolidation(String annexId) {
        // 删除已经生成的标签数据
        List<AnnexLabelData> annexLabelDataList = annexLabelDataRepository.findByCondition(Filters.eq("annexId", annexId));
        if (ObjectUtil.isNotEmpty(annexLabelDataList)) {
            annexLabelDataRepository.deleteByIdFake(annexLabelDataList.stream().map(AnnexLabelData::getId).collect(Collectors.toList()));
        }
        // 删除已经生成的关系三元组数据
        List<AnnexTripleLabelData> annexTripleLabelDataList = annexTripleLabelDataRepository.findByCondition(Filters.eq("annexId", annexId));
        if (ObjectUtil.isNotEmpty(annexTripleLabelDataList)) {
            annexTripleLabelDataRepository.deleteByIdFake(annexTripleLabelDataList.stream().map(AnnexTripleLabelData::getId).collect(Collectors.toList()));
        }
        // 重新合并
        consolidation(resourceAnnexFeign.info(annexId).getData());
    }


    /**
     * 知识匹配 ===> 缓存标签数据合并
     *
     * @param resourceAnnex 附件对象
     */
    @Override
    @Async
    public CompletableFuture<Void> consolidation(ResourceAnnex resourceAnnex) {
        log.info("【附件】开始知识匹配 ===> 合并缓存标签数据");
        // 修改附件状态
        resourceAnnex.setMatchStatus(KnowledgeMatchStatus.MATCHING);
        resourceAnnexFeign.save(resourceAnnex);
        try {
            // 1、同步顶级标签数据
            AnnexCacheLabelData annexTopCacheLabelData = generateTopLabelData(resourceAnnex);
            if (annexTopCacheLabelData == null) {
                log.warn("{} 该附件无顶级缓存标签数据!", resourceAnnex.getName());
                // 终止异步任务
                return CompletableFuture.completedFuture(null);
            }

            // 2、同步知识标注产生的 实例
            // 获取所有【知识标注】需要同步的缓存标签数据（存在高亮词的）
            List<AnnexCacheLabelData> usefulCacheLabelDataList = getUsefulCacheLabelDataList(resourceAnnex);
            // 同步知识标注产生的实例
            Map<String, String> labelIdMap = generateLabelData(usefulCacheLabelDataList, resourceAnnex, annexTopCacheLabelData);
            log.info("【附件】【知识标注】 标签数据合并完毕");


            // 3、同步知识标注 产生的 关系三元组
            // 获取所有知识标注产生的 标签数据关系三元组
            Bson and1 = Filters.and(Filters.eq("annexId", resourceAnnex.getId()), Filters.eq("type", "知识标注"));
            List<AnnexCacheTripleLabelData> annexCacheTripleLabelDataList = annexCacheTripleLabelDataRepository.findByCondition(and1);
            // 同步知识标注产生的关系
            generateTripleLabelData(annexCacheTripleLabelDataList, labelIdMap);
            log.info("【附件】【知识标注】 标签数据关系三元组合并完毕");


            // 4、合并规则解析产生的实例
            // 获取【规则解析】产生的所有缓存标签数据
            Bson and = Filters.and(Filters.eq("annexId", resourceAnnex.getId()), Filters.eq("type", "规则解析"));
            List<AnnexCacheLabelData> annexCacheLabelDataList = annexCacheLabelDataRepository.findByCondition(and);
            // 同步规则解析产生的实例
            labelIdMap = generateLabelData(annexCacheLabelDataList, resourceAnnex, annexTopCacheLabelData);
            log.info("【附件】【规则解析】 标签数据合并完毕");

            /*usefulCacheLabelDataList.addAll(annexCacheLabelDataList);*/



            // 5、合并规则解析产生的关系
            List<AnnexCacheTripleLabelData> ruleMatchAnnexCacheTripleLabelDataList = annexCacheTripleLabelDataRepository.findByCondition(and);
            generateTripleLabelData(ruleMatchAnnexCacheTripleLabelDataList, labelIdMap);

            /*// 生成标签关系三元组
            for (AnnexCacheLabelData usefulCacheLabelData : usefulCacheLabelDataList) {
                final AnnexTripleLabelData annexTripleLabelData = new AnnexTripleLabelData();
                // 设置关系的起点
                annexTripleLabelData.setStartLabelDataId(annexTopCacheLabelData.getId());
                // 设置关系的终点
                annexTripleLabelData.setEndLabelDataId(usefulCacheLabelData.getId());
                annexTripleLabelData.setRelationName("涉及");
                // 新增关系三元组
                annexTripleLabelDataRepository.save(annexTripleLabelData);
            }*/



            log.info("【附件】【规则解析】 标签数据关系三元组合并完毕");
            // 设置知识匹配状态为 “匹配完成”
            resourceAnnex.setMatchStatus(KnowledgeMatchStatus.MATCHED);
        } catch (Exception e) {
            // 设置知识匹配状态为 “匹配失败”
            resourceAnnex.setMatchStatus(KnowledgeMatchStatus.MATCH_FAILED);
            // 打印错误信息
            log.error(e.getMessage());
            // 终止异步任务
            return CompletableFuture.completedFuture(null);
        } finally {
            resourceAnnexFeign.save(resourceAnnex);
        }
        // 终止异步任务
        return CompletableFuture.completedFuture(null);
    }


    /**
     * 合并顶级缓存标签数据
     *
     * @param resourceAnnex 附件对象
     */
    @Nullable
    private AnnexCacheLabelData generateTopLabelData(ResourceAnnex resourceAnnex) {
        // 1. 同步顶级标签数据缓存
        // 先获取该附件的顶级缓存标签数据
        Bson and = Filters.and(Filters.eq("annexId", resourceAnnex.getId()),
                Filters.eq("topLabel", true)
                , Filters.eq("labelName", "附件"));
        List<AnnexCacheLabelData> topAnnexCacheLabelDataList = annexCacheLabelDataRepository.findByCondition(and);
        AnnexCacheLabelData annexTopCacheLabelData = null;
        if (ObjectUtil.isNotEmpty(topAnnexCacheLabelDataList)) {
            annexTopCacheLabelData = topAnnexCacheLabelDataList.get(0);
            // 同步顶级标签数据
            AnnexLabelData annexLabelData = BeanUtil.copyProperties(annexTopCacheLabelData, AnnexLabelData.class, "id");
            // 存储顶级标签
            annexLabelDataRepository.save(annexLabelData);
            log.info("【附件】合并顶级标签数据 ===> {}", annexLabelData.getContent());
        }
        return annexTopCacheLabelData;
    }

    /**
     * 获取所有需要合并的知识标注产生的缓存标签数据（存在高亮词的）
     */
    @NotNull
    private List<AnnexCacheLabelData> getUsefulCacheLabelDataList(ResourceAnnex resourceAnnex) {
        // 获取该附件的所有语料单元
        List<AnnexDocumentUnit> annexDocumentUnitList = annexDocumentUnitRepository.findByCondition(Filters.eq("annexId", resourceAnnex.getId()));
        // 提取出该资源下的所有高亮词对象
        List<DocumentTextLight> documentTextLightList = new ArrayList<>();
        annexDocumentUnitList.stream()
                .map(DocumentUnit::getDocumentTextLights).
                forEach(documentTextLightList::addAll);

        // 获取所有知识标注产生的 标签数据缓存对象
        Bson and = Filters.and(Filters.eq("annexId", resourceAnnex.getId()), Filters.eq("type", "知识标注"));
        List<AnnexCacheLabelData> annexCacheLabelDataList = annexCacheLabelDataRepository.findByCondition(and);

        // 过滤筛选，只保留存在高亮词的标签数据缓存
        return annexCacheLabelDataList.stream()
                .filter(annexCacheLabelData -> documentTextLightList
                        .stream()
                        .map(DocumentTextLight::getCacheLabelDataId)
                        .collect(Collectors.toList())
                        .contains(annexCacheLabelData.getId()))
                .collect(Collectors.toList());
    }


    /**
     * 合并缓存标签数据
     *
     * @param annexCacheLabelDataList 需要合并的缓存标签集合
     * @param resourceAnnex           附件对象
     * @return key：缓存标签数据Id value：标签数据Id
     */
    private Map<String, String> generateLabelData(List<AnnexCacheLabelData> annexCacheLabelDataList, ResourceAnnex resourceAnnex, AnnexCacheLabelData annexTopCacheLabelData) {
        // 创建Map存储 key：缓存标签数据Id value：标签数据Id
        Map<String, String> labelIdMap = new HashMap<>();
        // 获取附件顶级标签
        Bson and = Filters.and(Filters.eq("annexId", resourceAnnex.getId()), Filters.eq("topLabel", true));
        AnnexLabelData annexTopLabelData = annexLabelDataRepository.findByCondition(and).get(0);
        labelIdMap.put(annexTopCacheLabelData.getId(), annexTopLabelData.getId());
        //  合并已经存在的标签数据（labelId相同、content相同）
        for (AnnexCacheLabelData annexCacheLabelData : annexCacheLabelDataList) {
            if (annexCacheLabelData.getLabelId() == null) continue;
            // 判断是否已经存在对应的标签数据
            Bson bson = Filters.and(Filters.eq("labelId", annexCacheLabelData.getLabelId()), Filters.eq("content", annexCacheLabelData.getContent()), Filters.eq("annexId", resourceAnnex.getId()));
            List<AnnexLabelData> annexLabelDataList = annexLabelDataRepository.findByCondition(bson);
            // 如果已经存在该标签数据
            if (ObjectUtil.isNotEmpty(annexLabelDataList)) {
                AnnexLabelData annexLabelData = annexLabelDataList.get(0);
                // 获取缓存上的属性，添加到标签上
                List<LabelPropertyVO> CacheLabelProperties = annexCacheLabelData.getLabelProperties();
                if (ObjectUtil.isNotEmpty(CacheLabelProperties)) {
                    List<LabelPropertyVO> labelProperties = new ArrayList<>();
                    // 获取原标签上的所有属性
                    Map<String, List<LabelPropertyVO>> collect = annexLabelData.getLabelProperties().stream().collect(Collectors.groupingBy(LabelPropertyVO::getIdentifier));
                    Collection<List<LabelPropertyVO>> values = collect.values();
                    for (List<LabelPropertyVO> labelPropertyVOS : values) {
                        labelProperties.add(labelPropertyVOS.get(0));
                    }
                    // 设置新的属性
                    annexLabelData.setLabelProperties(labelProperties);
                    // 更新标签数据
                    annexLabelDataRepository.save(annexLabelData);
                }
                labelIdMap.put(annexCacheLabelData.getId(), annexLabelData.getId());
            } else {
                // 不相同的，直接同步到标签数据中
                AnnexLabelData newAnnexLabelData = BeanUtil.copyProperties(annexCacheLabelData, AnnexLabelData.class, "id");
                String labelId = annexLabelDataRepository.save(newAnnexLabelData);
                // 存储 缓存标签数据Id 和 标签数据Id
                labelIdMap.put(annexCacheLabelData.getId(), labelId);
            }
        }
        return labelIdMap;
    }


    /**
     * 合并缓存关系标签数据
     *
     * @param labelIdMap                    标签id Map
     * @param annexCacheTripleLabelDataList 附件缓存关系标签集合
     */
    private void generateTripleLabelData(List<AnnexCacheTripleLabelData> annexCacheTripleLabelDataList, Map<String, String> labelIdMap) {
        // 生成标签关系三元组
        for (AnnexCacheTripleLabelData annexCacheTripleLabelData : annexCacheTripleLabelDataList) {
            AnnexTripleLabelData annexTripleLabelData = BeanUtil.copyProperties(annexCacheTripleLabelData, AnnexTripleLabelData.class, "id");
            // 设置关系的起点
            annexTripleLabelData.setStartLabelDataId(labelIdMap.get(annexTripleLabelData.getStartLabelDataId()));
            // 设置关系的终点
            annexTripleLabelData.setEndLabelDataId(labelIdMap.get(annexTripleLabelData.getEndLabelDataId()));
            // 新增关系三元组
            annexTripleLabelDataRepository.save(annexTripleLabelData);
        }
    }


}
