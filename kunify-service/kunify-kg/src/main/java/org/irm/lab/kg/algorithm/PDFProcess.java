package org.irm.lab.kg.algorithm;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import org.irm.lab.common.pojo.BaseStatus;
import org.irm.lab.repository.entity.Resource;
import org.irm.lab.repository.entity.ResourceAnnex;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/2/15 15:59
 */
public abstract class PDFProcess implements IAlgorithmProcessor{

    public void process(Resource resource) {

    }

    public void annexProcess(ResourceAnnex resourceAnnex){

    }
    /**
     * 合并长句子
     *
     * @param size 300:摘要生成   400：ner
     */
    public List<DocumentSummary> mergeParagraph(List<DocumentUnit> lines, int size) {
        if (ObjectUtil.isEmpty(lines)) return new ArrayList<>();
        String resourceId = lines.get(0).getResourceId();
        List<DocumentSummary> mergeList = new ArrayList<>();
        StringBuilder preMerge = new StringBuilder();
        int order = 1;
        for (DocumentUnit line : lines) {
            if (line.getContent().length() <= size) {
                preMerge.append(line.getContent());
            } else {
                DocumentSummary summary = new DocumentSummary(
                        resourceId
                        , preMerge + line.getContent()
                        , BaseStatus.UNREADY
                        ,order++);
                mergeList.add(summary);
                preMerge = new StringBuilder();
                continue;
            }
            if (preMerge.length() > size && (line.getContent().endsWith("。") || line.getContent().endsWith("；"))) {
                DocumentSummary summary = new DocumentSummary(
                        resourceId
                        , String.valueOf(preMerge)
                        , BaseStatus.UNREADY
                        ,order++);
                mergeList.add(summary);
                preMerge = new StringBuilder();
            }
        }
        if (StrUtil.isNotBlank(preMerge)) {
            if (preMerge.length() < 100 && mergeList.size() > 0) {
                String s = String.valueOf(mergeList.get(mergeList.size() - 1)) + preMerge;
                DocumentSummary summary = new DocumentSummary(
                        resourceId
                        , s
                        , BaseStatus.UNREADY
                        ,order);
                mergeList.set(mergeList.size() - 1, summary);
            } else {
                DocumentSummary summary = new DocumentSummary(
                        resourceId
                        , String.valueOf(preMerge)
                        , BaseStatus.UNREADY
                        ,order);
                mergeList.add(summary);
            }
        }
        return mergeList;
    }
}
