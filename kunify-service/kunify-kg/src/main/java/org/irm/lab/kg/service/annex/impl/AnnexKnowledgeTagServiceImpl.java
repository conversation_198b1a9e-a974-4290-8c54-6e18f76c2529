package org.irm.lab.kg.service.annex.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.ObjectUtil;
import com.mongodb.client.model.Filters;
import com.mongodb.client.model.Updates;
import lombok.RequiredArgsConstructor;
import org.bson.Document;
import org.bson.conversions.Bson;
import org.bson.types.ObjectId;
import org.irm.lab.common.api.R;
import org.irm.lab.common.enums.AlgorithmType;
import org.irm.lab.common.enums.ResourceProcessingEnum;
import org.irm.lab.common.exception.ServiceException;
import org.irm.lab.config.entity.Label;
import org.irm.lab.config.entity.Process;
import org.irm.lab.config.entity.ProcessNode;
import org.irm.lab.config.entity.WorkTask;
import org.irm.lab.config.enums.ProcessNodeEnum;
import org.irm.lab.config.feign.LabelFeign;
import org.irm.lab.config.feign.WorkTaskFeign;
import org.irm.lab.kg.algorithm.AlgorithmProcessFactory;
import org.irm.lab.kg.algorithm.DocumentTextLight;
import org.irm.lab.kg.algorithm.DocumentUnit;
import org.irm.lab.kg.dto.AnnexCacheLabelDataDTO;
import org.irm.lab.kg.dto.CacheLabelDataDTO;
import org.irm.lab.kg.entity.annex.AnnexDocumentUnit;
import org.irm.lab.kg.entity.annex.processing.AnnexCacheLabelData;
import org.irm.lab.kg.entity.annex.processing.AnnexCacheTripleLabelData;
import org.irm.lab.kg.entity.processing.CacheTripleLabelData;
import org.irm.lab.kg.entity.processing.LabelPropertyVO;
import org.irm.lab.kg.repository.annex.AnnexCacheLabelDataRepository;
import org.irm.lab.kg.repository.annex.processing.AnnexDocumentUnitRepository;
import org.irm.lab.kg.service.annex.IAnnexCacheLabelDataService;
import org.irm.lab.kg.service.annex.IAnnexCacheTripleLabelDataService;
import org.irm.lab.kg.service.annex.IAnnexKnowledgeTagService;
import org.irm.lab.kg.service.impl.ieInfer.AnnexIeInferServiceImpl;
import org.irm.lab.kg.service.impl.kgprocess.tag.KnowledgeTagAlgorithm;
import org.irm.lab.repository.constant.KnowledgeTagStatus;
import org.irm.lab.repository.entity.ResourceAnnex;
import org.irm.lab.repository.feign.ResourceAnnexFeign;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2023/5/6 15:06
 * @description 附件知识标注业务层实现类
 */
@Service
@RequiredArgsConstructor
public class AnnexKnowledgeTagServiceImpl implements IAnnexKnowledgeTagService {

    private final AnnexDocumentUnitRepository annexDocumentUnitRepository;
    private final IAnnexCacheLabelDataService iAnnexCacheLabelDataService;
    private final IAnnexCacheTripleLabelDataService iAnnexCacheTripleLabelDataService;
    private final AnnexCacheLabelDataRepository annexCacheLabelDataRepository;
    @javax.annotation.Resource(name = "annexKnowledgeTag")
    private KnowledgeTagAlgorithm annexKnowledgeTagAlgorithm;
    private final AlgorithmProcessFactory algorithmProcessFactory;
    private final ResourceAnnexFeign resourceAnnexFeign;
    private final WorkTaskFeign workTaskFeign;
    private final LabelFeign labelFeign;
    private final AnnexIeInferServiceImpl annexIeInferService;



    /**
     * 新增高亮词
     *
     * @param documentUnitId    段落Id
     * @param documentTextLight {@link DocumentTextLight}
     * @return {@link DocumentUnit}
     */
    @Override
    public AnnexDocumentUnit save(String documentUnitId, DocumentTextLight documentTextLight) {
        // 根据段落Id先查询出段落对象
        AnnexDocumentUnit annexDocumentUnit = annexDocumentUnitRepository.findById(documentUnitId);
        // 判断该附件解析是否已确认
        R<ResourceAnnex> resourceAnnexR = resourceAnnexFeign.info(annexDocumentUnit.getAnnexId());
        ResourceAnnex resourceAnnex = resourceAnnexR.getData();
        if (!resourceAnnexR.isSuccess() || ObjectUtil.isEmpty(resourceAnnex)) {
            throw new ServiceException("当前资源不存在!");
        }
        if (!resourceAnnex.isDocParsingConfirm()) {
            throw new ServiceException("请先确认文档解析!");
        }
        // 为高亮词生成唯一标识
        documentTextLight.setIdentifier(IdUtil.objectId());
        // 根据，附件Id、标签Id、高亮词内容 查询是否存在指定的附件标签数据缓存
        AnnexCacheLabelData annexCacheLabelData = iAnnexCacheLabelDataService.findAnnexCacheLabelDataByCondition(annexDocumentUnit.getAnnexId(), documentTextLight.getLabelId(), documentTextLight.getText());
        // 如果存在对应的节点，就把该高亮词与之关联
        if (ObjectUtil.isNotEmpty(annexCacheLabelData)) {
            // 直接获取标签数据缓存对象的唯一表示
            documentTextLight.setCacheLabelDataId(annexCacheLabelData.getId());
            // 并且更新原本的缓存标签上的语料单元Id
            annexCacheLabelData.getDocumentUnitIds().add(documentUnitId);
            // 更新缓存标签
            iAnnexCacheLabelDataService.save(annexCacheLabelData);
        } else {
            // 不存在对应的附件缓存标签，就新生成一个附件缓存标签
            AnnexCacheLabelData newAnnexCacheLabelData = new AnnexCacheLabelData();
            // 设置标签数据所属资源Id
            newAnnexCacheLabelData.setResourceId(annexDocumentUnit.getResourceId());
            newAnnexCacheLabelData.setAnnexId(annexDocumentUnit.getAnnexId());
            // 设置所属语料单元Id
            newAnnexCacheLabelData.getDocumentUnitIds().add(documentUnitId);
            // 设置标签数据Id
            newAnnexCacheLabelData.setLabelId(documentTextLight.getLabelId());
            // 设置标签名称
            newAnnexCacheLabelData.setLabelName(documentTextLight.getLabelName());
            // 设置 实例值
            newAnnexCacheLabelData.setContent(documentTextLight.getText());
            // 设置类型
            newAnnexCacheLabelData.setType(ResourceProcessingEnum.KNOWLEDGE_TAG.getType());
            // 新增一个标签数据缓存对象
            newAnnexCacheLabelData = iAnnexCacheLabelDataService.save(newAnnexCacheLabelData);
            // 在高亮词上绑定标签数据的唯一标识
            documentTextLight.setCacheLabelDataId(newAnnexCacheLabelData.getId());
        }
        // 转换为Map对象
        Map<String, Object> documentTextLightMap = BeanUtil.beanToMap(documentTextLight, false, true);
        // 新增一个高亮词对象
        annexDocumentUnitRepository.pushDistinct(documentUnitId, Map.of("documentTextLights", new Document(documentTextLightMap)));
        // 并获取完整的预料单元对象
        return annexDocumentUnitRepository.findById(documentUnitId);
    }


    /**
     * 更新高亮词数据
     *
     * @param documentUnitId    段落Id
     * @param documentTextLight {@link DocumentTextLight}
     * @return {@link DocumentUnit}
     */
    @Override
    public AnnexDocumentUnit update(String documentUnitId, DocumentTextLight documentTextLight) {
        // 在高亮词表中删除原本的高亮词对象
        annexDocumentUnitRepository.pullDistinct(documentUnitId, Map.of("documentTextLights", new Document("identifier", documentTextLight.getIdentifier())));
        // 再次生成新的高亮词
        save(documentUnitId, documentTextLight);
        // 返回
        return annexDocumentUnitRepository.findById(documentUnitId);
    }


    /**
     * 清除指定附件下的所有高亮词
     *
     * @param annexId 附件Id
     */
    @Override
    public void clearAll(String annexId) {
        // 获取当前资源下的所有语料单元对象
        List<AnnexDocumentUnit> annexDocumentUnitList = annexDocumentUnitRepository.findByCondition(Filters.eq("annexId", annexId));
        // 清除所有预料单元的高亮词
        for (AnnexDocumentUnit annexDocumentUnit : annexDocumentUnitList) {
            if (ObjectUtil.isNotEmpty(annexDocumentUnit.getDocumentTextLights())) {
                annexDocumentUnit.setDocumentTextLights(new ArrayList<>());
            }
        }
        // 批量更新
        annexDocumentUnitRepository.saveAll(annexDocumentUnitList);
        // 删除标签数据缓存里的数据
        iAnnexCacheLabelDataService.removeByResourceIdAndType(annexId, "知识标注");
    }

    /**
     * 批量删除指定语料单元内的所有高亮词
     *
     * @param idList 语料单元Id列表
     */
    @Override
    public void removeByIds(List<String> idList) {
        // 根据id查询指定预料单元对象
        List<AnnexDocumentUnit> annexDocumentUnitList = annexDocumentUnitRepository.findById(idList);
        // 清除这些语料单元下的高亮词
        for (AnnexDocumentUnit annexDocumentUnit : annexDocumentUnitList) {
            if (ObjectUtil.isNotEmpty(annexDocumentUnit.getDocumentTextLights())) {
                annexDocumentUnit.setDocumentTextLights(new ArrayList<>());
            }
        }
        // 批量更新
        annexDocumentUnitRepository.saveAll(annexDocumentUnitList);
    }

    /**
     * 根据唯一标识删除高亮词
     *
     * @param documentUnitId 预料单元Id
     * @param identify       高亮词唯一标识
     */
    @Override
    public void clearByIdentify(String documentUnitId, String identify) {
        annexDocumentUnitRepository.pullDistinct(documentUnitId, Map.of("documentTextLights", new Document("identifier", identify)));
    }

    /**
     * 根据工作任务Id获取数据集标签列表
     *
     * @param workTaskId 工作任务Id
     * @param type       标签类型 概念、关系、属性
     * @return {@link Label}
     */
    @Override
    public List<Label> listLabelOrPropertyOrPredicate(String workTaskId, String type) {
        // 获取工作任务对象
        R<WorkTask> workTaskR = workTaskFeign.info(workTaskId);
        if (!workTaskR.isSuccess() || ObjectUtil.isEmpty(workTaskR.getData())) {
            throw new ServiceException("该工作任务不存在！");
        }
        WorkTask workTask = workTaskR.getData();
        // 获取工作任务下的流程对象
        Process process = workTask.getProcess();
        // 获取流程下的数据集Id
        String dataSetId = process.getDataSetId();
        if (ObjectUtil.isEmpty(dataSetId)) {
            throw new ServiceException("该工作任务下的 流程未绑定数据集!");
        }
        // 根据数据集Id获取数据集标签
        R<List<Label>> labelR = labelFeign.list(Map.of("dataSetId", dataSetId, "type", type));
        if (labelR.isSuccess()) {
            // 获取所有标签数据
            return labelR.getData();
        }
        throw new ServiceException("该数据集下不存在标签");
    }


    /**
     * 根据附件Id获取实例（高亮词）对象
     *
     * @param annexId 附件Id
     * @return {@link DocumentTextLight}
     */
    @Override
    public List<DocumentTextLight> listEntity(String annexId) {
        // 现根据资源Id获取所有预料单元对象
        List<AnnexDocumentUnit> annexDocumentUnitList = annexDocumentUnitRepository.findByCondition(Filters.eq("annexId", annexId));
        // 创建集合存储所有高亮词对象
        List<DocumentTextLight> documentTextLightList = new ArrayList<>();
        // 获取所有高亮词对象
        annexDocumentUnitList.stream()
                .map(AnnexDocumentUnit::getDocumentTextLights)
                .collect(Collectors.toList())
                .forEach(documentTextLightList::addAll);
        // 去重
        // 返回实例（高亮词）
        return documentTextLightList.stream().collect(
                Collectors.collectingAndThen(
                        Collectors.toCollection(
                                () -> new TreeSet<>(Comparator.comparing(DocumentTextLight::getCacheLabelDataId))), ArrayList::new));
    }


    /**
     * 为实例 新增/修改属性
     *
     * @param cacheLabelDataId 缓存标签Id
     * @param labelPropertyVO  {@link LabelPropertyVO}
     */
    @Override
    public void saveOrUpdateProperty(String cacheLabelDataId, LabelPropertyVO labelPropertyVO) {
        String identifier = labelPropertyVO.getIdentifier();
        if (ObjectUtil.isEmpty(identifier)) {
            // 设置唯一标识
            labelPropertyVO.setIdentifier(IdUtil.objectId());
            // 新增属性
            annexCacheLabelDataRepository.pushDistinct(cacheLabelDataId, Map.of("labelProperties", new Document(BeanUtil.beanToMap(labelPropertyVO, false, true))));
        } else {
            // 修改属性
            Bson filter = Filters.and(Filters.eq("_id", new ObjectId(cacheLabelDataId))
                    , Filters.eq("labelProperties.identifier", labelPropertyVO.getIdentifier()));
            Bson update = Updates.set("labelProperties.$", labelPropertyVO);
            annexCacheLabelDataRepository.updateOne(filter, update);
        }

    }

    /**
     * 删除指定的属性
     *
     * @param cacheLabelDataId 缓存标签Id
     * @param identifier       属性唯一标识
     */
    @Override
    public void removeProperty(String cacheLabelDataId, String identifier) {
        annexCacheLabelDataRepository.pullDistinct(cacheLabelDataId, Map.of("labelProperties", new Document("identifier", identifier)));
    }

    /**
     * 为实例 新增/修改关系和属性
     *
     * @param annexCacheTripleLabelData {@link CacheTripleLabelData}
     */
    @Override
    public void saveOrUpdateRelation(AnnexCacheTripleLabelData annexCacheTripleLabelData) {
        // 如果是新增，则为属性设置identifier
        if (ObjectUtil.isEmpty(annexCacheTripleLabelData.getId())) {
            List<LabelPropertyVO> relationProperties = annexCacheTripleLabelData.getRelationProperties();
            List<LabelPropertyVO> collect = relationProperties.stream()
                    .peek(relationProperty -> relationProperty.setIdentifier(IdUtil.objectId()))
                    .collect(Collectors.toList());
            annexCacheTripleLabelData.setRelationProperties(collect);
        }
        iAnnexCacheTripleLabelDataService.save(annexCacheTripleLabelData);
    }


    /**
     * 删除指定关系
     *
     * @param cacheTripleLabelDataId 关系标签Id
     */
    @Override
    public void removeRelation(String cacheTripleLabelDataId) {
        iAnnexCacheTripleLabelDataService.remove(cacheTripleLabelDataId);
    }


    /**
     * 根据标签数据缓存Id，获取该高亮词的属性和关系详情
     *
     * @param cacheLabelDataId 标签数据缓存Id
     * @return {@link CacheLabelDataDTO}
     */
    @Override
    public AnnexCacheLabelDataDTO infoCacheLabelData(String cacheLabelDataId) {
        // 先根据标签数据缓存Id查询
        AnnexCacheLabelData annexCacheLabelData = iAnnexCacheLabelDataService.info(cacheLabelDataId);
        // 为空的话返回空数组
        if (annexCacheLabelData.getLabelProperties() == null) {
            annexCacheLabelData.setLabelProperties(new ArrayList<>());
        }
        // 再根据标签数据缓存对象Id 查询该对象的正向关系
        List<AnnexCacheTripleLabelData> cacheTripleLabelDataList = iAnnexCacheTripleLabelDataService.listByCacheLabelDataId(cacheLabelDataId);
        AnnexCacheLabelDataDTO annexCacheLabelDataDTO = new AnnexCacheLabelDataDTO();
        annexCacheLabelDataDTO.setCacheLabelData(annexCacheLabelData);
        annexCacheLabelDataDTO.setCacheTripleLabelDataList(cacheTripleLabelDataList);
        return annexCacheLabelDataDTO;
    }


    /**
     * 附件知识标注  重新调用算法识别
     *
     * @param annexId 资源Id
     */
    @Override
    public void reKnowledgeTag(String annexId) {
        // 获取该附件的所有语料单元
        List<AnnexDocumentUnit> annexDocumentUnitList = annexDocumentUnitRepository.findByCondition(Filters.eq("annexId", annexId));
        // 清空附件语料单元的高亮词
        annexDocumentUnitList = annexDocumentUnitList.stream().peek(annexDocumentUnit -> annexDocumentUnit.setDocumentTextLights(new ArrayList<>())).collect(Collectors.toList());
        // 更新
        annexDocumentUnitRepository.saveAll(annexDocumentUnitList);
        // 删除原本的附件缓存标签
        Bson and = Filters.and(Filters.eq("annexId", annexId), Filters.eq("type", "知识标注"));
        List<AnnexCacheLabelData> annexCacheLabelDataList = annexCacheLabelDataRepository.findByCondition(and);
        if (ObjectUtil.isNotEmpty(annexCacheLabelDataList)) {
            annexCacheLabelDataRepository.deleteByIdFake(annexCacheLabelDataList.stream().map(AnnexCacheLabelData::getId).collect(Collectors.toList()));
        }
        // 获取附件对象
        R<ResourceAnnex> resourceAnnexR = resourceAnnexFeign.info(annexId);
        ResourceAnnex resourceAnnex = resourceAnnexR.getData();
        if (!resourceAnnexR.isSuccess() || resourceAnnex == null) {
            throw new ServiceException("该附件不存在!");
        }
        // 修改附件状态
        resourceAnnex.setTagStatus(KnowledgeTagStatus.TAG_ING);
        resourceAnnex = resourceAnnexFeign.save(resourceAnnex).getData();
        // 重新调用附件知识标注算法识别
        annexIeInferService.knowledgeTagWithAlgorithm(resourceAnnex.getWorkTaskId(), resourceAnnex);
    }

    /**
     * 知识标注确认 ===> 进行附件的规则解析
     *
     * @param annexId 资源Id
     */
    @Override
    public void confirm(String annexId) {
        R<ResourceAnnex> resourceAnnexR = resourceAnnexFeign.info(annexId);
        ResourceAnnex resourceAnnex = resourceAnnexR.getData();
        if (!resourceAnnexR.isSuccess() || resourceAnnex==null){
            throw new ServiceException("该附件不存在!");
        }
        if (!resourceAnnex.isDocParsingConfirm()){
            throw new ServiceException("请先确认文档解析!");
        }
        // 更新附件状态
        resourceAnnex.setKnowledgeTagConfirm(true);
        resourceAnnexFeign.save(resourceAnnex);
        // 获取工作任务
        R<WorkTask> workTaskR = workTaskFeign.info(resourceAnnex.getWorkTaskId());
        WorkTask workTask = workTaskR.getData();
        if (!workTaskR.isSuccess() || workTask == null) {
            throw new ServiceException("当前工作任务不存在!");
        }
        Process process = workTask.getProcess();
        List<ProcessNode> processNodeList = process.getKgProcessNodes();
        for (ProcessNode processNode : processNodeList) {
            // 资源加工环节
            if (ProcessNodeEnum.RESOURCE_PROCESS.getNodeName().equals(processNode.getStageName())) {
                // 获取规则解析器
                List<String> docRuleParsingNameList = processNode.getDocRuleParsing();
                for (String name : docRuleParsingNameList) {
                    // 调用规则解析
                    algorithmProcessFactory.algorithmApply(AlgorithmType.name(name)).annexProcess(resourceAnnex);
                }
            }
        }
    }
}
