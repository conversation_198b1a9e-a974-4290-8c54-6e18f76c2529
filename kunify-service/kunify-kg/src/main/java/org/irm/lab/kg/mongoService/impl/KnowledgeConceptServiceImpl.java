package org.irm.lab.kg.mongoService.impl;

import org.irm.lab.kg.entity.KnowledgeConcept;
import org.irm.lab.kg.mongoService.KnowledgeConceptService;
import org.springframework.stereotype.Service;

import com.anwen.mongo.service.impl.ServiceImpl;

import cn.hutool.core.util.ObjectUtil;

/**
 * <AUTHOR> <br/>
 * @date 2024/4/10 <br/>
 * @Copyright 博客：https://eliauku.gitee.io/  ||  per aspera and astra <br/>
 */
@Service("KnowledgeConceptImpl")
public class KnowledgeConceptServiceImpl extends ServiceImpl<KnowledgeConcept> implements KnowledgeConceptService {


    @Override
    public boolean existById(String id) {
        final KnowledgeConcept knowledgeConcept = getById(id);

        return ObjectUtil.isNotEmpty(knowledgeConcept);
    }
}
