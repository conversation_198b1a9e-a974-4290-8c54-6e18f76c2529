package org.irm.lab.kg.service.impl;

import cn.hutool.core.util.ObjectUtil;
import com.mongodb.client.model.Filters;
import lombok.RequiredArgsConstructor;
import org.bson.conversions.Bson;
import org.irm.lab.common.constant.FiledNameConst;
import org.irm.lab.common.constant.FilterTypeConst;
import org.irm.lab.common.entity.AbstractBaseEntity;
import org.irm.lab.common.exception.ServiceException;
import org.irm.lab.common.support.Condition;
import org.irm.lab.kg.entity.*;
import org.irm.lab.kg.exception.ModelExceptionConstant;
import org.irm.lab.kg.repository.KnowledgeConceptRepository;
import org.irm.lab.kg.repository.KnowledgeModelRepository;
import org.irm.lab.kg.repository.KnowledgeRelationRepository;
import org.irm.lab.kg.service.IKnowledgePropertyService;
import org.irm.lab.kg.service.IKnowledgeRelationService;
import org.irm.lab.kg.vo.KnowledgeRelationVO;
import org.irm.lab.kg.vo.ReferenceMapVO;
import org.irm.lab.kg.wrapper.KnowledgeRelationWrapper;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

@Service
@RequiredArgsConstructor
public class KnowledgeRelationServiceImpl implements IKnowledgeRelationService {


    private final KnowledgeRelationRepository knowledgeRelationRepository;
    private final KnowledgeModelRepository knowledgeModelRepository;
    private final IKnowledgePropertyService knowledgePropertyService;
    private final KnowledgeConceptRepository knowledgeConceptRepository;

    /**
     * 关系增改
     *
     * @param relation 关系实体
     * @return 操作完后的实体信息
     */
    @Override
    public KnowledgeRelation save(KnowledgeRelation relation) {
        String relationId = relation.getId();
        // 如果是对称性 反向谓词就是正向谓词
        relation.setInversePre(relation.getIsSymmetric() ? relation.getForwardPre() : relation.getInversePre());
        // 新增关系
        if (relationId == null) {
            // 判断是否已经存在 主、谓、宾 均相同的关系
            Bson filter = Filters.and(Filters.eq("conceptOne", relation.getConceptOne()), Filters.eq("conceptTwo", relation.getConceptTwo()), Filters.eq("forwardPre", relation.getForwardPre()));
            List<KnowledgeRelation> relations = knowledgeRelationRepository.findByCondition(filter);
            if (ObjectUtil.isNotEmpty(relations)) {
                throw new ServiceException("已存在相同关系!");
            }
            return knowledgeRelationRepository.findById(knowledgeRelationRepository.save(relation));
        }
        // 修改关系
        // 判断该关系是否已经被模型引用，引用则无法修改
        List<KnowledgeModel> models = knowledgeModelRepository.findByCondition(Filters.elemMatch("relations", Filters.eq("_id", relationId)));
        if (ObjectUtil.isNotEmpty(models)) {
            throw new ServiceException(ModelExceptionConstant.RELATION_ALREADY_BIND);
        }
        return knowledgeRelationRepository.findById(knowledgeRelationRepository.save(relation));
    }

    /**
     * 删除多个关系
     *
     * @param ids 关系Id集合
     */
    @Override
    public void remove(List<String> ids) {
        //若当前关系中有被被模型引用，则不可删除
        relationCheck(ids);
        //删除关系
        knowledgeRelationRepository.deleteByIdFake(ids);
    }

    /**
     * 判断关系是否被模型引用
     *
     * @param ids 关系Id列表
     */
    public void relationCheck(List<String> ids) {
        Bson bson = Filters.elemMatch("relations", Filters.in("_id", ids));
        List<KnowledgeModel> models = knowledgeModelRepository.findByCondition(bson);
        if (ObjectUtil.isNotEmpty(models))
            throw new ServiceException(ModelExceptionConstant.RELATION_ALREADY_BIND);
    }


    /**
     * 获取当前关系可用属性
     *
     * @param relationId 关系Id
     * @return 可用属性列表
     */
    @Override
    public List<KnowledgeProperty> listAvailableProperty(String relationId) {
        KnowledgeRelation knowledgeRelation = knowledgeRelationRepository.findById(relationId);
        // 获取属性库
        List<KnowledgeProperty> propertyList = knowledgePropertyService.list();
        // 过滤掉当前关系已经存在的属性
        return propertyList.stream()
                .filter(knowledgeProperty -> !knowledgeRelation.getPropertyIds().contains(knowledgeProperty.getId()))
                .collect(Collectors.toList());
    }

    /**
     * 引用属性
     *
     * @param id  关系Id
     * @param ids 属性Id列表
     * @return {@link KnowledgeRelation}
     */
    @Override
    public KnowledgeRelation bind(String id, List<String> ids) {
        // 判断关系是否被模型引用
        relationCheck(List.of(id));
        KnowledgeRelation relation = knowledgeRelationRepository.findById(id);
        // 设置关系属性
        if (relation.getPropertyIds().stream().anyMatch(ids::contains)) {
            throw new ServiceException("操作失败，重复添加属性!");
        }
        relation.getPropertyIds().addAll(ids);
        return knowledgeRelationRepository.findById(knowledgeRelationRepository.save(relation));
    }


    /**
     * 取消关系属性引用
     *
     * @param id 关系Id
     * @param ids 属性Id列表
     */
    @Override
    public void removeRelationProperty(String id,List<String> ids) {
        // 判断关系是否被模型引用
        relationCheck(List.of(id));
        KnowledgeRelation relation = knowledgeRelationRepository.findById(id);
        List<String> propertyIds = relation.getPropertyIds();
        propertyIds.removeAll(ids);
        knowledgeRelationRepository.save(relation);
    }

    /**
     * 关系分组
     *
     * @return 分组关系
     */
    @Override
    public Map<String, List<KnowledgeRelationVO>> groupList() {
        List<KnowledgeRelationVO> relationVOS = KnowledgeRelationWrapper.build().listVO(list());
        relationVOS = relationVOS.stream().sorted(Comparator.comparing(AbstractBaseEntity::getCreateTime).reversed())
                .collect(Collectors.toList());
        return relationVOS.stream().collect(Collectors.groupingBy(KnowledgeRelationVO::getClassify));
    }

    /**
     * 获取指定概念作为主语的所有关联关系对象
     *
     * @param modelId     模型Id
     * @param conceptName 概念名称
     */
    @Override
    public List<KnowledgeRelation> relationInverse(String modelId, String conceptName) {
        // 获取概念对象
        KnowledgeConcept concept = knowledgeConceptRepository.findByCondition(Filters.eq("name", conceptName)).get(0);
        // 获取模型对象
        KnowledgeModel model = knowledgeModelRepository.findById(modelId);
        // 保留主语是 concept的所有关系对象
        return model.getRelations()
                .stream()
                .map(referenceMap -> {
                    String relationId = referenceMap.getId();
                    return knowledgeRelationRepository.findById(relationId);
                })
                .filter(knowledgeRelation -> knowledgeRelation.getConceptOne().equals(concept.getId())).collect(Collectors.toList());
    }

    /**
     * 查询所有的关系
     *
     * @return 所有关系
     */
    public List<KnowledgeRelation> list() {
        return knowledgeRelationRepository.findAll();
    }


    /**
     * @param id 关系id
     * @return 当前关系
     */
    @Override
    public KnowledgeRelationVO infoVO(String id) {
        KnowledgeRelationVO entityVO = KnowledgeRelationWrapper.build().entityVO(knowledgeRelationRepository.findById(id));
        //查询当前关系是否被引用
        Bson bson = Filters.elemMatch("relations", Filters.eq("_id", id));
        List<KnowledgeModel> models = knowledgeModelRepository.findByCondition(bson);
        if (ObjectUtil.isNotEmpty(models)) {
            entityVO.setQuote(true);
            entityVO.setModelList(models);
        }
        return entityVO;
    }

    /**
     * 根据主语/宾语查找相关关系
     *
     * @param firstId 主语id
     * @param endId   宾语id
     * @return 关系集合
     */
    @Override
    public List<KnowledgeRelation> relationListByConcept(String firstId, String endId) {
        Map<String, Object> query = new HashMap<>();
        Map<String, Object> queryType = new HashMap<>();
        if (!"".equals(firstId)) {
            query.put(FiledNameConst.CONCEPT_ONE, firstId);
            queryType.put(FiledNameConst.CONCEPT_ONE, FilterTypeConst.EQ);
        }
        if (!"".equals(endId)) {
            query.put(FiledNameConst.CONCEPT_TWO, endId);
            queryType.put(FiledNameConst.CONCEPT_TWO, FilterTypeConst.EQ);
        }
        Bson filter = Condition.getFilter(query, KnowledgeRelation.class, queryType);
        return knowledgeRelationRepository.findByCondition(filter);
    }

    /**
     * 根据概念id集合查询当前所有概念之间存在的关系
     *
     * @param conceptIds 概念id集合
     * @return 关系集合
     */
    @Override
    public List<String> relationIdsByConceptIds(List<String> conceptIds) {
        List<KnowledgeRelation> relationList = new ArrayList<>();
        //以conceptIds作为主语，查出所有符合的条件
        conceptIds.forEach(conceptId -> {
            Bson filter = Condition.getFilter(Map.of(FiledNameConst.CONCEPT_ONE, conceptId),
                    KnowledgeRelation.class, Map.of(FiledNameConst.CONCEPT_ONE, FilterTypeConst.EQ));
            relationList.addAll(knowledgeRelationRepository.findByCondition(filter));
        });
        //过滤掉不符合条件的关系
        List<KnowledgeRelation> collect = relationList.stream().filter(relation ->
                !conceptIds.contains(relation.getConceptTwo())).collect(Collectors.toList());
        return collect.stream().map(AbstractBaseEntity::getId).collect(Collectors.toList());
    }


    /**
     * 当前概念，和所有概念之间的边关系
     *
     * @return 关系集合
     */
    @Override
    public List<KnowledgeRelation> relationsByConcepts(KnowledgeConcept currentConcept, List<KnowledgeConcept> concepts) {
        ArrayList<KnowledgeRelation> knowledgeRelations = new ArrayList<>();
        concepts.forEach(concept -> {
            List<KnowledgeRelation> currentToRelation = knowledgeRelationRepository.findByCondition(Condition.getFilter(Map.of(FiledNameConst.CONCEPT_ONE, currentConcept.getId(), FiledNameConst.CONCEPT_TWO, concept.getId())));
            List<KnowledgeRelation> currentFromRelation = knowledgeRelationRepository.findByCondition(Condition.getFilter(Map.of(FiledNameConst.CONCEPT_ONE, concept.getId(), FiledNameConst.CONCEPT_TWO, currentConcept.getId())));
            knowledgeRelations.addAll(currentFromRelation);
            knowledgeRelations.addAll(currentToRelation);
        });
        return knowledgeRelations;
    }


    @Override
    public List<KnowledgeRelation> listInfo(List<String> ids) {
        return knowledgeRelationRepository.findById(ids);
    }


    @Override
    public List<ReferenceMapVO> relationtListByModelId(String modelId) {
    /*    return knowledgeModelService.info(modelId).getRelations().stream().map(r -> {
            ReferenceMapVO referenceMapVO = new ReferenceMapVO();
            referenceMapVO.setRelation(knowledgeRelationRepository.findById(r.getId()));
            referenceMapVO.setProperties(knowledgePropertyService.listByIds(r.getPropertyIds()));
            return referenceMapVO;
        }).collect(Collectors.toList());*/
        return null;
    }

    @Override
    public List<KnowledgeRelation> relationListByModelIdAndConceptOne(String modelId, String conceptId) {
        List<KnowledgeRelation> relationList = relationtListByModelId(modelId).stream().map(ReferenceMapVO::getRelation).collect(Collectors.toList());
        return relationList.stream().filter(relation -> !relation.getConceptOne().equals(conceptId)).collect(Collectors.toList());
    }

    /**
     * 根据概念id删除关联的关系
     *
     * @param conceptId 概念id
     */
    @Override
    public void removeRelationByConceptId(String conceptId) {
        remove(getRelationIdByConceptId(conceptId));
    }

    /**
     * 查询当前关系涉及到的所有的关系
     *
     * @param conceptId 概念id
     * @return List<ConceptRelationVO>
     */
    public List<KnowledgeRelation> getRelationByConceptId(String conceptId) {
        List<KnowledgeRelation> relationList = new ArrayList<>();
        relationList.addAll(getForwardRelationByConceptId(conceptId));
        relationList.addAll(getInverseRelationByConceptId(conceptId));
        return relationList;
    }

    /**
     * 获取当前属性涉及到的所有关系的id
     *
     * @param conceptId 概念id
     * @return ids
     */
    public List<String> getRelationIdByConceptId(String conceptId) {
        List<String> relationIds = new ArrayList<>();
        //获取当前概念作为主语涉及到的关系
        getForwardRelationByConceptId(conceptId).forEach(relation -> relationIds.add(relation.getId()));
        //获取当前概念作为宾语涉及到的关系
        getInverseRelationByConceptId(conceptId).forEach(relation -> relationIds.add(relation.getId()));
        return relationIds;
    }

    /**
     * 获取当前概念作为主语涉及到的关系
     *
     * @param conceptId 概念id
     * @return 关系集合
     */
    public List<KnowledgeRelation> getForwardRelationByConceptId(String conceptId) {
        Bson filterStart = Condition.getFilter(Map.of(FiledNameConst.CONCEPT_ONE, conceptId),
                KnowledgeRelation.class, Map.of(FiledNameConst.CONCEPT_ONE, FilterTypeConst.EQ));
        return new ArrayList<>(knowledgeRelationRepository.findByCondition(filterStart));
    }

    /**
     * 获取当前概念作为宾语涉及到的关系
     *
     * @param conceptId 概念id
     * @return 关系集合
     */
    public List<KnowledgeRelation> getInverseRelationByConceptId(String conceptId) {
        Bson filterEnd = Condition.getFilter(Map.of(FiledNameConst.CONCEPT_TWO, conceptId),
                KnowledgeRelation.class, Map.of(FiledNameConst.CONCEPT_TWO, FilterTypeConst.EQ));
        return new ArrayList<>(knowledgeRelationRepository.findByCondition(filterEnd));
    }

    /**
     * 根据Id列表获取关系列表
     *
     * @param ids id列表
     * @return {@link KnowledgeRelation}
     */
    @Override
    public List<KnowledgeRelation> findByIds(List<String> ids) {
        return knowledgeRelationRepository.findById(ids);
    }

    /**
     * 根据id获取关系对象
     *
     * @param id 关系Id
     * @return {@link KnowledgeRelation}
     */
    @Override
    public KnowledgeRelation info(String id) {
        return knowledgeRelationRepository.findById(id);
    }
}
