package org.irm.lab.kg.service.annex;

import org.irm.lab.repository.entity.ResourceAnnex;

import java.util.concurrent.CompletableFuture;

/**
 * <AUTHOR>
 * @date 2023/5/10 11:41
 * @description 附件缓存标签数据合并
 */
public interface IAnnexLabelDataConsolidationService {

    /**
     * 知识匹配 ===> 缓存标签数据合并
     *
     * @param resourceAnnex 附件对象
     */
    CompletableFuture<Void> consolidation(ResourceAnnex resourceAnnex);


    /**
     * 重新匹配
     *
     * @param annexId 附件Id
     */
    void reConsolidation(String annexId);
}
