package org.irm.lab.kg.service;

import org.irm.lab.kg.entity.KnowledgeConcept;
import org.irm.lab.kg.entity.KnowledgeProperty;
import org.irm.lab.kg.entity.KnowledgeRelation;
import org.irm.lab.kg.vo.KnowledgeRelationVO;
import org.irm.lab.kg.vo.ReferenceMapVO;

import java.util.List;
import java.util.Map;

public interface IKnowledgeRelationService {

    /**
     * 关系增改
     *
     * @param knowledgeRelation 关系实体
     * @return 操作完后的实体信息
     */
    KnowledgeRelation save(KnowledgeRelation knowledgeRelation);

    /**
     * 删除多个关系
     *
     * @param ids 关系Id集合
     */
    void remove(List<String> ids);

    /**
     * 获取当前关系可用属性
     *
     * @param relationId 关系Id
     * @return 可用属性列表
     */
    List<KnowledgeProperty> listAvailableProperty(String relationId);

    /**
     * 引用属性
     *
     * @param id  关系Id
     * @param ids 属性Id列表
     * @return {@link KnowledgeRelation}
     */
    KnowledgeRelation bind(String id, List<String> ids);

    /**
     * 取消关系属性引用
     *
     * @param id 关系ID
     * @param objToStrList 属性Id列表
     */
    void removeRelationProperty(String id,List<String> objToStrList);

    /**
     * 获取指定概念作为主语的所有关联关系对象
     *
     * @param modelId 模型Id
     * @param conceptName 概念名称
     */
    List<KnowledgeRelation> relationInverse(String modelId, String conceptName);

    /**
     * 关系分组查询
     *
     * @return 分组查询
     */
    Map<String, List<KnowledgeRelationVO>> groupList();

    KnowledgeRelationVO infoVO(String id);

    List<KnowledgeRelation> relationListByConcept(String firstId, String endId);

    List<String> relationIdsByConceptIds(List<String> conceptIds);

    List<KnowledgeRelation> relationsByConcepts(KnowledgeConcept currentConcept, List<KnowledgeConcept> conceptList);

    List<KnowledgeRelation> listInfo(List<String> ids);

    void removeRelationByConceptId(String id);

    List<ReferenceMapVO> relationtListByModelId(String modelId);

    List<KnowledgeRelation> relationListByModelIdAndConceptOne(String modelId, String conceptId);

    List<KnowledgeRelation> list();

    List<KnowledgeRelation> getForwardRelationByConceptId(String conceptId);

    /**
     * 根据Id列表获取关系列表
     *
     * @param ids id列表
     * @return {@link KnowledgeRelation}
     */
    List<KnowledgeRelation> findByIds(List<String> ids);

    /**
     * 根据id获取关系对象
     *
     * @param id 关系Id
     * @return {@link KnowledgeRelation}
     */
    KnowledgeRelation info(String id);

    List<KnowledgeRelation> getRelationByConceptId(String conceptId);


}
