package org.irm.lab.kg.strategy.annexOcr.impl;

import java.io.IOException;
import java.util.Map;

import org.irm.lab.kg.strategy.annexOcr.AnnexOcrStrategy;
import org.irm.lab.kg.strategy.ocr.impl.AbstractFileOcrStrategyImpl;
import org.irm.lab.repository.constant.DocumentResolvedStatus;
import org.irm.lab.repository.entity.ResourceAnnex;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import cn.hutool.core.util.ObjectUtil;

/**
 * <AUTHOR> <br/>
 * @date 2024/6/5 <br/>
 * @Copyright 博客：https://eliauku.gitee.io/  ||  per aspera and astra <br/>
 */
public abstract class AbstractAnnexFileOcrStrategyImpl2 implements AnnexOcrStrategy {

    final Logger logger = LoggerFactory.getLogger(AbstractAnnexFileOcrStrategyImpl2.class);

    /**
     * 得到每页的OCR识别结果
     */
    abstract Map<Integer, Map<Integer, String>> getOcrResult(String strategy, org.irm.lab.repository.entity.ResourceAnnex message);

    @Override
    public boolean ocr(String strategy, org.irm.lab.repository.entity.ResourceAnnex message) throws IOException {

        Map<Integer, Map<Integer, String>> ocrResult;
        try {
            ocrResult = getOcrResult(strategy, message);
        }catch (Exception e){
            logger.error("精确解析 error ", e);
            return false;
        }

        if (ObjectUtil.isEmpty(ocrResult)) {
            logger.error("ocrResult is null");
            return false;
        } else {
            message.setResolveStatus(DocumentResolvedStatus.RESOLVED);
            saveOcrResult(strategy, message, ocrResult);
            return true;
        }
    }

    /**
     * 保存每页的OCR识别结果
     */
    abstract void saveOcrResult(String strategy, org.irm.lab.repository.entity.ResourceAnnex message, Map<Integer, Map<Integer, String>> ocrResult);

}
