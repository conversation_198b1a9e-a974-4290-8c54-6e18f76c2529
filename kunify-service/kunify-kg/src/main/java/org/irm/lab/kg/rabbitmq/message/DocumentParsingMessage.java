package org.irm.lab.kg.rabbitmq.message;

import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2023/3/8 16:28
 * @description 文档解析消息
 */
@Data
public class DocumentParsingMessage implements Serializable {
    /**
     * 文档解析 交换机
     */
    public static final String EXCHANGE = "KUNIFY4_EXCHANGE_DOCUMENT_PARSING_";
    /**
     * 文档解析 消息队列
     */
    public static final String QUEUE = "KUNIFY4_QUEUE_DOCUMENT_PARSING_";
    /**
     * 文档解析 routingKey
     */
    public static final String ROUTING_KEY = "KUNIFY4_ROUTING_DOCUMENT_PARSING_";
    /**
     * 当前用户信息
     */
    private String user;
    /**
     * 资源Id
     */
    private String resourceId;
}
