package org.irm.lab.kg.service;

import org.irm.lab.repository.entity.Resource;

/**
 * <AUTHOR>
 * @date 2023/5/12 17:29
 * @description 资源匹配确认异步任务接口
 */
public interface IResourceAsyncLabelGraphService {
    /**
     * 匹配确认
     *
     * @param resource  资源对象
     * @param dataSetId 数据集ID
     * @param modelId   模型Id
     */
    void matchConfirm(Resource resource, String dataSetId, String modelId);
}
