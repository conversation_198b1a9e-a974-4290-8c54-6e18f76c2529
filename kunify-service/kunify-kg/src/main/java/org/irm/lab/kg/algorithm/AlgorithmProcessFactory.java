package org.irm.lab.kg.algorithm;

import org.irm.lab.common.enums.AlgorithmType;
import org.springframework.beans.BeansException;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2023/4/18 20:08
 */
@Service
public class AlgorithmProcessFactory implements ApplicationContextAware {
    private static final Map<AlgorithmType, IAlgorithmProcessor> beanMap = new HashMap<>();
    @Override
    public void setApplicationContext(ApplicationContext applicationContext) throws BeansException {
        Map<String, IAlgorithmProcessor> map = applicationContext.getBeansOfType(IAlgorithmProcessor.class);
        map.forEach((k, v) -> beanMap.put(v.getCode(), v));
    }

    public IAlgorithmProcessor algorithmApply(AlgorithmType code){
        return beanMap.get(code);
    }
}
