package org.irm.lab.kg.wrapper;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.ObjectUtil;
import org.irm.lab.common.support.BaseEntityWrapper;
import org.irm.lab.common.utils.SpringUtil;
import org.irm.lab.kg.entity.KnowledgeModel;
import org.irm.lab.kg.entity.ReferenceMap;
import org.irm.lab.kg.vo.KnowledgeModelVO;
import org.irm.lab.user.feign.UserFeign;

import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

public class KnowledgeModelWapper extends BaseEntityWrapper<KnowledgeModel, KnowledgeModelVO> {
    public static KnowledgeModelWapper build() {
        return new KnowledgeModelWapper();
    }

    private static final UserFeign userFeign = SpringUtil.getBean(UserFeign.class);

    @Override
    public KnowledgeModelVO entityVO(KnowledgeModel entity) {
        KnowledgeModelVO knowledgeModelVO = BeanUtil.copyProperties(entity, KnowledgeModelVO.class);
        String userId = knowledgeModelVO.getCreateUser();
        if (ObjectUtil.isNotEmpty(userId)) {
            knowledgeModelVO.setUserName(userFeign.info(userId).getData().getUsername());
        }
        Set<ReferenceMap> concepts = knowledgeModelVO.getConcepts();
        knowledgeModelVO.setConceptNum(concepts.size());
        Set<ReferenceMap> relations = knowledgeModelVO.getRelations();
        knowledgeModelVO.setRelationNum(relations.size());
        Set<String> propertyIds = new HashSet<>();
        concepts.forEach(referenceMap -> propertyIds.addAll(referenceMap.getPropertyIds()));
        relations.forEach(referenceMap -> propertyIds.addAll(referenceMap.getPropertyIds()));
        knowledgeModelVO.setPropertyNum(propertyIds.size());
        return knowledgeModelVO;
    }

    public List<KnowledgeModelVO> entityVOList(List<KnowledgeModel> knowledgeModelList) {
        List<KnowledgeModelVO> knowledgeModelVOS = new ArrayList<>();
        knowledgeModelList.forEach(knowledgeModel -> {
            knowledgeModelVOS.add(entityVO(knowledgeModel));
        });
        return knowledgeModelVOS;
    }
}
