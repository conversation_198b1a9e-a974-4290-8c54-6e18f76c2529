package org.irm.lab.kg.rabbitmq.ieInfer;

import java.io.Serializable;

import lombok.Data;

@Data
public class IeInferMessage implements Serializable {

    private static final long serialVersionUID = -8480619077095563000L;

    public static final String QUEUE = "QUEUE_IE_INFER";

    public static final String EXCHANGE = "EXCHANGE_IE_INFER";

    public static final String ROUTING_KEY = "ROUTING_KEY_IE_INFER";

    public  static final String FILE_INFER_ID = "FILE_INFER_ID";


    /**
     *  实体抽取 ["姓名","出生地","出生时间","去世时间","所在部队","籍贯","民族","荣誉称号","性别"]
     *  关系抽取 [{"姓名":["出生地","出生时间","去世时间","所在部队","籍贯","民族","荣誉称号","性别"]}]
     */
    private String schemas;

    /**
     * 语料
     */
    private String text;

    /**
     * 实体抽取1
     * 关系抽取2
     */
    private Integer type;

    /**
     * 租户
     */
    private String tenant;

    /**
     * 文件id
     */
    private String resourceId;

    /**
     * 附件id
     */
    private String annexId;

    /**
     *  用户id
     */
    private String userId;

    /**
     * 语料id
     */
    private String corpusId;

    /**
     * 是否是当前资源的最后一条语料
     */
    private Boolean isEnd;
}
