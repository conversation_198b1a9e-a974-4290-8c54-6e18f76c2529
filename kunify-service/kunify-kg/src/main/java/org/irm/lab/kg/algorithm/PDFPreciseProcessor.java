package org.irm.lab.kg.algorithm;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.aspose.pdf.*;
import lombok.RequiredArgsConstructor;
import lombok.extern.log4j.Log4j2;
import org.irm.lab.common.api.R;
import org.irm.lab.common.constant.ExceptionMessageConst;
import org.irm.lab.common.enums.AlgorithmType;
import org.irm.lab.common.exception.ServiceException;
import org.irm.lab.common.provider.MinioLinkProvider;
import org.irm.lab.common.utils.FileUtil;
import org.irm.lab.common.utils.NetWorkFileUtil;
import org.irm.lab.kg.entity.annex.AnnexDocumentUnit;
import org.irm.lab.kg.repository.DocumentUnitRepository;
import org.irm.lab.kg.repository.annex.processing.AnnexDocumentUnitRepository;
import org.irm.lab.kg.utils.PDFUtils;
import org.irm.lab.repository.constant.DocumentResolvedStatus;
import org.irm.lab.repository.entity.Resource;
import org.irm.lab.repository.entity.ResourceAnnex;
import org.irm.lab.repository.feign.ResourceAnnexFeign;
import org.irm.lab.repository.feign.ResourceFeign;
import org.irm.lab.resource.entity.Attach;
import org.jetbrains.annotations.NotNull;
import org.springframework.stereotype.Service;

import java.io.*;
import java.util.ArrayList;
import java.util.List;
import java.util.Locale;
import java.util.Map;
import java.util.Set;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

/**
 * pdf精确抽取
 *
 * <AUTHOR>
 * @date 2023/2/15 14:28
 */
@Log4j2
@Service
@RequiredArgsConstructor
public class PDFPreciseProcessor extends PDFProcess {
    private final ResourceFeign resourceFeign;
    private final ResourceAnnexFeign resourceAnnexFeign;
    private final DocumentUnitRepository documentUnitRepository;
    private final AnnexDocumentUnitRepository annexDocumentUnitRepository;
    private final MinioLinkProvider minioLinkProvider;
    private static final double rationX = 4.10;
    private static final double rationY = 4.12;
    private static final double rationH = 4.16;
    private static final double rationW = 4.2;
    private static final Pattern compileA = Pattern.compile("([\\u4e00\\u4e8c\\u4e09\\u56db\\u4e94\\u516d\\u4e03\\u516b\\u4e5d\\u5341]{1,10})(?=、)");
    private static final Pattern compileB = Pattern.compile("(?<=（)([\\u4e00\\u4e8c\\u4e09\\u56db\\u4e94\\u516d\\u4e03\\u516b\\u4e5d\\u5341]{1,10})(?=）)");
    private static final Pattern compileC = Pattern.compile("[0-9]{1,3}(?=\\.)");

    private static final Pattern compileD = Pattern.compile("(?<=（)([0-9]{1,3})(?=）)");

    private static final Set<String> FILTER_WORDS = Set.of("H !N~");
    private static final Set<String> END_PUNCTUATION = Set.of(":", "。", "：");

    @Override
    public AlgorithmType getCode() {
        return AlgorithmType.PDF_PRECISE_STRIPPER;
    }

    @Override
    public void process(Resource resource) {
        // 特殊文件，不做文档解析，直接解析成功
        if (FileUtil.isSpecialFile(resource.getMediaType())) {
            log.info("【{}】 非文书类资源，无需解析 ", resource.getName());
            resource.setResolveStatus(DocumentResolvedStatus.RESOLVED);
            return;
        }
        if (DocumentResolvedStatus.RESOLVED.equals(resource.getResolveStatus())) {
            log.info("【主文件】 {} 已文档解析，无需再次解析", resource.getName());
            return;
        }
        List<ResourceAnnex> resourceAnnexList;
        //获取资源路径
        String pdfLink = minioLinkProvider.getMinioLinkIntranet(resource.getPdfAttachName());
        if (pdfLink == null) return;
        try (InputStream inputStream = NetWorkFileUtil.urlToInputStream(pdfLink)) {
            resource.setTextStripperStatus(ExceptionMessageConst.PDF_STRIPPER_SUCCESS);
            List<Page> pages = getDocumentPages(inputStream);
            List<DocumentUnit> units = nr(pages, 1, resource.getId());
            // 无法进行pdf精确解析，修改状态并结束解析
            if (units.isEmpty()) {
                resource.setTextStripperStatus(ExceptionMessageConst.OCR_GEN_BEGIN);
                // 主文件无法精确解析，调用附件的精确解析
                // 获取该资源的所有附件
                R<List<ResourceAnnex>> resourceAnnexListR = resourceAnnexFeign.listByResourceId(resource.getId());
                resourceAnnexList = resourceAnnexListR.getData();
                // 资源解析成功后，开始解析附件
                for (ResourceAnnex resourceAnnex : resourceAnnexList) {
                    annexProcess(resourceAnnex);
                }
                return;
            }
/*            for (DocumentUnit unit : units) {
                log.info("type：【{}】，content：【{}】,page:【{}】，sortInPage：【{}】，x:【{}】，y：【{}】，h：【{}】w：【{}】"
                        , unit.getType(), unit.getContent(), unit.getPage(), unit.getSortInCurrentPage(), unit.getLlx(), unit.getLly(), unit.getHeight(), unit.getWidth());
            }*/
            final Map<Integer, List<DocumentUnit>> collect = units.stream().collect(Collectors.groupingBy(DocumentUnit::getPage));
            collect.forEach((page, units1) -> {
                log.info("page:【{}】,size:【{}】", page, units1.size());
                final List<DocumentUnit> documentUnits = mergeDocumentUnit(units1, 400);
                documentUnitRepository.saveAll(documentUnits);
            });


            //mergeParagraph(documentUnits, 300);
        } catch (IOException e) {
            resource.setTextStripperStatus(ExceptionMessageConst.OCR_GEN_BEGIN);
            resource.setTextStripperStatus(ExceptionMessageConst.PDF_STRIPPER);
            throw new ServiceException(ExceptionMessageConst.PDF_STRIPPER + "【" + e.getMessage() + "】");
        } finally {
            resourceFeign.save(resource);
        }

        R<List<ResourceAnnex>> resourceAnnexListR = resourceAnnexFeign.listByResourceId(resource.getId());
        resourceAnnexList = resourceAnnexListR.getData();
        if (ObjectUtil.isEmpty(resourceAnnexList)) {
            log.info("该资源不存在附件，无需附件解析!");
            return;
        }
        // 循环解析附件
        log.info(">>>>>>>>>>开始解析附件>>>>>>>>>>");
        // 资源解析成功后，开始解析附件
        for (ResourceAnnex resourceAnnex : resourceAnnexList) {
            annexProcess(resourceAnnex);
        }
        log.info("<<<<<<<<<<全部附件解析完成<<<<<<<<<<");
    }

    /**
     * 附件解析
     */
    @Override
    public void annexProcess(ResourceAnnex resourceAnnex) {
        // 特殊文件，不做文档解析，直接解析成功
        if (FileUtil.isSpecialFile(resourceAnnex.getMediaType())) {
            log.info("【{}】 非文书类资源，无需解析 ", resourceAnnex.getName());
            resourceAnnex.setResolveStatus(DocumentResolvedStatus.RESOLVED);
            return;
        }
        if (DocumentResolvedStatus.RESOLVED.equals(resourceAnnex.getResolveStatus())) {
            log.info("【附件】 {} 已解析，无需再次解析", resourceAnnex.getName());
            return;
        }
        log.info("附件解析  ===>  【{}】", resourceAnnex.getName());
        // 对于无法转换为pdf类型的doc资源，修改资源状态为
        if (resourceAnnex.getPdfAttachName() == null) {
            resourceAnnex.setResolveStatus("不支持解析");
            resourceAnnexFeign.save(resourceAnnex);
            return;
        }
        //获取资源路径
        String annexPdfLink = minioLinkProvider.getMinioLinkIntranet(resourceAnnex.getPdfAttachName());
        if (annexPdfLink == null) return;
        try (InputStream inputStream = NetWorkFileUtil.urlToInputStream(annexPdfLink)) {
            // 修改附件状态 “解析中”
            resourceAnnex.setResolveStatus(DocumentResolvedStatus.RESOLVING);
            R<ResourceAnnex> resourceAnnexR = resourceAnnexFeign.save(resourceAnnex);
            if (resourceAnnexR.isSuccess()) {
                resourceAnnex = resourceAnnexR.getData();
            }
            ResourceAnnex finalResourceAnnex = resourceAnnex;
            // 获取文档页
            List<Page> pages = getDocumentPages(inputStream);
            List<DocumentUnit> units = nr(pages, 1, resourceAnnex.getResourceId());
            // 转换为附件预料单元
            List<AnnexDocumentUnit> annexDocumentUnitList = units.stream()
                    .map(documentUnit -> {
                        AnnexDocumentUnit annexDocumentUnit = new AnnexDocumentUnit();
                        BeanUtil.copyProperties(documentUnit, annexDocumentUnit);
                        annexDocumentUnit.setAnnexId(finalResourceAnnex.getId());
                        return annexDocumentUnit;
                    })
                    .collect(Collectors.toList());
            // 无法进行pdf精确解析，修改状态并结束解析
            if (annexDocumentUnitList.isEmpty()) {
                resourceAnnex.setTextStripperStatus(ExceptionMessageConst.OCR_GEN_BEGIN);
                return;
            }

            final List<AnnexDocumentUnit> annexDocumentUnits = mergeAnnexDocumentUnit(annexDocumentUnitList, 400);
            annexDocumentUnitRepository.saveAll(annexDocumentUnits);
            resourceAnnex.setTextStripperStatus(ExceptionMessageConst.PDF_STRIPPER_SUCCESS);
            resourceAnnex.setResolveStatus(DocumentResolvedStatus.RESOLVED);
            log.info("解析完毕  ===>  【{}】", resourceAnnex.getName());
        } catch (IOException e) {
            resourceAnnex.setTextStripperStatus(ExceptionMessageConst.PDF_STRIPPER);
            resourceAnnex.setResolveStatus(DocumentResolvedStatus.RESOLVED_FAILED);
            resourceAnnex.setTextStripperStatus(ExceptionMessageConst.OCR_GEN_BEGIN);
            throw new ServiceException(ExceptionMessageConst.PDF_STRIPPER + "【" + e.getMessage() + "】");
        } finally {
            resourceAnnexFeign.save(resourceAnnex);
        }

    }


    /**
     * 获取划词语料
     *
     * @param file 文件对象
     * @return 语料集合
     */
    public List<String> generateUnitTxt(File file) throws Exception {
        List<Page> pages = getDocumentPages(new FileInputStream(file));
        List<DocumentUnit> nr = nr(pages, 1, "");
        nr = mergeDocumentUnit(nr, 400);
        return nr.stream().map(DocumentUnit::getContent).collect(Collectors.toList());
    }

    /**
     * 获取划词语料
     *
     * @param fileInputStream 文件输入流
     * @return 语料集合
     */
    public List<String> generateUnitTxt(FileInputStream fileInputStream) throws Exception {
        List<Page> pages = getDocumentPages(fileInputStream);
        List<DocumentUnit> nr = nr(pages, 1, "");
        nr = mergeDocumentUnit(nr, 400);
        return nr.stream().map(DocumentUnit::getContent).collect(Collectors.toList());
    }

    /**
     * 合并语料
     *
     * @param units 语料集合
     * @param size  语料长度
     * @return 合并后的语料集合
     */
    public List<DocumentUnit> mergeDocumentUnit(List<DocumentUnit> units, int size) {
        List<DocumentUnit> documentUnits = new ArrayList<>();
        StringBuilder str = new StringBuilder();
        Integer page = null;
        Integer sort = null;
        if(units.size() != Integer.MAX_VALUE){
            // 合并内容过断的语料
            for (int i = 0; i < units.size(); i++) {
                DocumentUnit unit = units.get(i);
                if (str.length() < size) {
                    if (page == null) page = unit.getPage();
                    if (sort == null) sort = unit.getSortInCurrentPage();
                    str.append(unit.getContent().replaceAll("—\\d+——", ""));
                    if (i != units.size() - 1) {
                        continue;
                    }
                }
                // 拼接完成
                DocumentUnit documentUnit = new DocumentUnit();
                documentUnit.setContent(str.toString());
                str = new StringBuilder();
                documentUnit.setResourceId(unit.getResourceId());
                documentUnit.setPage(page);
                documentUnit.setSortInCurrentPage(sort);
                documentUnit.setType("0");
                documentUnits.add(documentUnit);
            }
        }

        //去除documentUnit中的空格与换行
        documentUnits.forEach(documentUnit -> {
            String originText = documentUnit.getContent();
            String cleanBlankText = StrUtil.cleanBlank(originText);
            String removeAllLineBreaksText = StrUtil.removeAllLineBreaks(cleanBlankText);
            documentUnit.setContent(removeAllLineBreaksText);
        });
        return documentUnits;
    }

    public List<AnnexDocumentUnit> mergeAnnexDocumentUnit(List<AnnexDocumentUnit> units, int size) {
        List<AnnexDocumentUnit> documentUnits = new ArrayList<>();
        StringBuilder str = new StringBuilder();
        Integer page = null;
        Integer sort = null;
        // 合并内容过断的语料
        for (int i = 0; i < units.size(); i++) {
            AnnexDocumentUnit unit = units.get(i);
            if (str.length() < size) {
                if (page == null) page = unit.getPage();
                if (sort == null) sort = unit.getSortInCurrentPage();
                str.append(processContent(unit.getContent()));
                if (i != units.size() - 1) {
                    continue;
                }
            }
            // 拼接完成
            AnnexDocumentUnit documentUnit = new AnnexDocumentUnit();
            documentUnit.setContent(str.toString());
            str = new StringBuilder();
            documentUnit.setResourceId(unit.getId());
            documentUnit.setAnnexId(unit.getAnnexId());
            documentUnit.setPage(page);
            documentUnit.setSortInCurrentPage(sort);
            documentUnit.setType("0");
            documentUnits.add(documentUnit);
        }
        return documentUnits;
    }

    public List<AnnexDocumentUnit> mergeAnnexDocumentUnitPro(List<AnnexDocumentUnit> units, int size) {
        List<AnnexDocumentUnit> documentUnits = new ArrayList<>();
        StringBuilder str = new StringBuilder();
        int currentPage = -1;
        int currentSort = 0; // 用于当前页的排序

        for (AnnexDocumentUnit unit : units) {
            // 检查是否移动到了新的一页
            if (unit.getPage() != currentPage) {
                if (currentPage != -1 && str.length() > 0) {
                    // 保存上一页的最后一个单位
                    addAnnexDocumentUnit(documentUnits, str, currentPage, currentSort,unit.getAnnexId());
                }
                // 重置变量以开始新一页
                currentPage = unit.getPage();
                currentSort = 0;
                str = new StringBuilder();
            }

            // 删除特定模式并处理当前单位
            //String content = unit.getContent().replaceAll("—\\d+——", "");
            String content = processContent(unit.getContent());
            while (content.length() > 0) {
                int spaceLeft = size - str.length();
                if (spaceLeft <= 0) {
                    // 当前缓冲区已满，保存并开始新条目
                    addAnnexDocumentUnit(documentUnits, str, currentPage, currentSort++,unit.getAnnexId());
                    str = new StringBuilder();
                    spaceLeft = size;
                }

                if (content.length() <= spaceLeft) {
                    str.append(content);
                    content = ""; // 已经添加完毕
                } else {
                    str.append(content.substring(0, spaceLeft));
                    content = content.substring(spaceLeft);
                }
            }
        }

        // 添加最后一页的最后一个单位
        if (str.length() > 0) {
            addAnnexDocumentUnit(documentUnits, str, currentPage, currentSort,units.get(0).getAnnexId());
        }

        return documentUnits;
    }



    private void addAnnexDocumentUnit(List<AnnexDocumentUnit> documentUnits, StringBuilder str, int page, int sort,String annexId) {
        AnnexDocumentUnit documentUnit = new AnnexDocumentUnit();
        documentUnit.setContent(str.toString());
        documentUnit.setAnnexId(annexId);
        documentUnit.setPage(page);
        documentUnit.setSortInCurrentPage(sort);
        documentUnit.setType("0");
        documentUnits.add(documentUnit);
    }


    public List<DocumentUnit> mergeDocumentUnitPro(List<DocumentUnit> units, int size) {
        List<DocumentUnit> documentUnits = new ArrayList<>();
        StringBuilder str = new StringBuilder();
        int currentPage = -1;
        int currentSort = 0; // 用于当前页的排序

        for (DocumentUnit unit : units) {
            // 检查是否移动到了新的一页
            if (unit.getPage() != currentPage) {
                if (currentPage != -1 && str.length() > 0) {
                    // 保存上一页的最后一个单位
                    addDocumentUnit(documentUnits, str, currentPage, currentSort, unit.getResourceId());
                }
                // 重置变量以开始新一页
                currentPage = unit.getPage();
                currentSort = 0;
                str = new StringBuilder();
            }

            // 删除特定模式并处理当前单位
            //String content = unit.getContent().replaceAll("—\\d+——", "");
            String content = processContent(unit.getContent());
            while (content.length() > 0) {
                int spaceLeft = size - str.length();
                if (spaceLeft <= 0) {
                    // 当前缓冲区已满，保存并开始新条目
                    addDocumentUnit(documentUnits, str, currentPage, currentSort++, unit.getResourceId());
                    str = new StringBuilder();
                    spaceLeft = size;
                }

                if (content.length() <= spaceLeft) {
                    str.append(content);
                    content = ""; // 已经添加完毕
                } else {
                    str.append(content.substring(0, spaceLeft));
                    content = content.substring(spaceLeft);
                }
            }
        }

        // 添加最后一页的最后一个单位
        if (str.length() > 0) {
            addDocumentUnit(documentUnits, str, currentPage, currentSort, units.get(0).getResourceId());
        }

        return documentUnits;
    }



    private void addDocumentUnit(List<DocumentUnit> documentUnits, StringBuilder str, int page, int sort,String resourceID) {
        DocumentUnit documentUnit = new DocumentUnit();
        documentUnit.setResourceId(resourceID);
        documentUnit.setContent(str.toString());
        documentUnit.setPage(page);
        documentUnit.setSortInCurrentPage(sort);
        documentUnit.setType("0");
        documentUnits.add(documentUnit);
    }

    private List<DocumentUnit> nr(List<Page> pages, int pageStart, String resourceId) {
        List<DocumentUnit> units = new ArrayList<>();
        for (int page = 0; page < pages.size(); page++) {
            boolean isMergeIng = false;
            DocumentUnit preUnit = null;
            int orderIdx = 0;
            Rectangle paraRec = new Rectangle(0, 0, 0, 0);
            Page pagePDF = pages.get(page);
            double height = pagePDF.getArtBox().getHeight();
//            log.info("高度：【{}】", height);
            // 处理段落
            ParagraphAbsorber absorber = new ParagraphAbsorber();
            absorber.visit(pagePDF);
            TableAbsorber tableAbsorber = new TableAbsorber();
            //不进行识别的区域
            List<Rectangle> tableRecList = new ArrayList<>();
            //页眉
            tableRecList.add(new Rectangle(10, 770, 560, 900));
            //页尾
            tableRecList.add(new Rectangle(10, 10, 560, 72));
            try {
                tableAbsorber.visit(pagePDF);
            } catch (Exception e) {
                e.printStackTrace();
            }
//            log.info("表格数量：【{}】",tableAbsorber.getTableList().size());
            for (PageMarkup markup : absorber.getPageMarkups()) {
                //当前页,大部分情况下，markup的size一直都是1
                for (MarkupSection section : markup.getSections()) {
                    //遍历行,大部分情况下，section的size一直都是1
                    for (MarkupParagraph paragraph : section.getParagraphs()) {
                        Rectangle rec = PDFUtils.getParaRec(paragraph);
                        if (PDFUtils.inRectangle(rec, tableRecList)) {
                            continue;
                        }
                        String[] title = getTitle(paragraph);
//                      log.info("段落:【{}】",Arrays.asList(title));
                        if (!"0".equals(title[0])) {
                            if (isMergeIng) units.add(preUnit);
                            if (isSeparateRow(rec)) {
                                DocumentUnit unit = createUnit(title[1], fixRectangle(rec, height), orderIdx++, page + 1, title[0], resourceId);
                                unit.setPage(page + 1);
                                units.add(unit);
//                                log.info("直接创建单元-----------------");
                            } else {
                                preUnit = createUnit(title[1], fixRectangle(rec, height), orderIdx++, page + 1, title[0], resourceId);
//                                log.info("开始创建单元-----------------");
                                preUnit.setPage(page + 1);
                                isMergeIng = true;
                            }
                        } else {
                            if (!isMergeIng) {
                                if (isSeparateRow(rec)) {
                                    //如果是单独的行，则直接创建单元
                                    DocumentUnit unit = createUnit(title[1], fixRectangle(rec, height), orderIdx++, page + 1, title[0], resourceId);
//                                    log.info("直接创建单元-----------------");
                                    unit.setPage(page + 1);
                                    units.add(unit);
                                } else {
                                    preUnit = createUnit(title[1], fixRectangle(rec, height), orderIdx++, page + 1, title[0], resourceId);
//                                    log.info("开始创建单元-----------------");
                                    preUnit.setPage(page + 1);
                                    isMergeIng = true;
                                }
                            } else {
                                if (isSeparateRow(rec) || END_PUNCTUATION.contains(StrUtil.subSufByLength(title[1], 1))) {
                                    isMergeIng = false;
                                    units.add(mergeUnit(preUnit, fixRectangle(rec, height), title[1]));
                                    preUnit = null;
//                                    log.info("开始生成单元----------------");
                                } else {
                                    preUnit = mergeUnit(preUnit, fixRectangle(rec, height), title[1]);
                                }
                            }
                        }
                    }
                }
            }
            if (isMergeIng) {
                units.add(preUnit);
            }

            // 处理图片
//            XImageCollection xImageCollection = pagePDF.getResources().getImages();
//            for (int i = 1; i <= xImageCollection.size(); i++) {
//                XImage image = xImageCollection.get_Item(i);
//                String imgName = UUID.randomUUID() + ".jpg";
//                MockMultipartFile mockMultipartFile;
//                try (ByteArrayOutputStream outputStream = new ByteArrayOutputStream()) {
//                    image.save(outputStream);
//                    mockMultipartFile = new MockMultipartFile(imgName, imgName, null, outputStream.toByteArray());
//                } catch (Exception e) {
//                    throw new RuntimeException(e);
//                }
//                Attach attach = uploadFileService.uploadSingleFile(mockMultipartFile);
//                DocumentUnit img = createImg(orderIdx++, attach, page + 1, resourceId);
//                units.add(img);
////                    FileUtil.del(imageFileName);
//            }
        }
        return units;
    }

    /**
     * 返回文档页列表
     *
     * @param inputStream 输入流
     * @return {@link Page}
     */
    @NotNull
    private static List<Page> getDocumentPages(InputStream inputStream) {
        final Locale locale = new Locale("zh", "cn");
        Locale.setDefault(locale);
        Document document = new Document(inputStream);
        int pageSize = document.getPages().size();
        PageCollection pageCollection = document.getPages();
        List<Page> pages = new ArrayList<>();
        for (int page = 1; page <= pageSize; page++) {
            pages.add(pageCollection.get_Item(page));
        }
        return pages;
    }

    /**
     * 通过最右侧横坐标来判断是否是单独的一行
     *
     * @param rec 矩阵
     * @return true：是单独的一行
     */
    private static Boolean isSeparateRow(Rectangle rec) {
        return rec.getURX() <= 450;
    }

    private static DocumentUnit mergeUnit(DocumentUnit unit1, Rectangle rectangle, String content) {
        DocumentUnit unit2 = BeanUtil.copyProperties(unit1, DocumentUnit.class);
        unit2.setContent(unit1.getContent() + content);
        unit2.setHeight(rectangle.getLLY() * rationH - unit1.getLly());
        double v = unit1.getLlx() + unit1.getWidth();
        double width = (NumberUtil.max(v, rectangle.getURX() * rationH) - NumberUtil.min(unit1.getLlx(), rectangle.getLLX() * rationW));
        unit2.setWidth(width);
        unit2.setLlx(NumberUtil.min(unit1.getLlx(), rectangle.getLLX() * rationW));
        return unit2;
    }

    /**
     * 一级标题  一、二、三、
     * 二级标题  （一）（二）（三）
     * 三级标题 1. 2. 3.
     * 四级标题 （1）（2）（3）
     */
    private String[] getTitle(MarkupParagraph paragraph) {
        String paraText = paragraph.getText();
        filterWords(paraText);
//        System.out.println(paraText);
        Matcher matcherA = compileA.matcher(paraText);
        if (matcherA.find()) return new String[]{"1", paraText};
        Matcher matcherB = compileB.matcher(paraText);
        if (matcherB.find()) return new String[]{"2", paraText};
//        Matcher matcherC = compileC.matcher(paraText);
//        if (matcherC.find()) return new String[]{"3",paraText};
//        Matcher matcherD = compileD.matcher(paraText);
//        if (matcherD.find())return new String[]{"4",paraText};
        return new String[]{"0", paraText};
    }

    private DocumentUnit createUnit(String text, Rectangle rectangle, int orderInPage, int page, String type, String resourceId) {
        DocumentUnit documentUnit = new DocumentUnit();
        documentUnit.setLlx(rectangle.getLLX() * rationX);
        documentUnit.setHeight((rectangle.getLLY() - rectangle.getURY()) * rationH);
        //在实际坐标定位时，y坐标总是差一个高度，所以用ury
        documentUnit.setLly(rectangle.getURY() * rationY);
        documentUnit.setWidth((rectangle.getURX() - rectangle.getLLX()) * rationW);
        documentUnit.setType(type);
        documentUnit.setContent(text);
        documentUnit.setSortInCurrentPage(orderInPage);
        documentUnit.setResourceId(resourceId);
        documentUnit.setPage(page);
        return documentUnit;
    }

    private DocumentUnit createImg(int orderInPage, Attach attach, int page, String resourceId) {
        DocumentUnit documentUnit = new DocumentUnit();
        documentUnit.setSortInCurrentPage(orderInPage);
        documentUnit.setAttachName(attach.getOssObjectName());
        documentUnit.setAttachId(attach.getId());
        documentUnit.setType("B");
        documentUnit.setResourceId(resourceId);
        documentUnit.setPage(page);
        return documentUnit;
    }

    public Rectangle fixRectangle(Rectangle rec, double height) {
        Rectangle result = new Rectangle(0, 0, 0, 0);
        result.setLLX(rec.getLLX());
        result.setLLY((height - rec.getLLY()));
        result.setURX(rec.getURX());
        result.setURY((height - rec.getURY()));
        return result;
    }

    private String filterWords(String text) {
        for (String filterWord : FILTER_WORDS) {
            if (text.contains(filterWord)) return text.replace(filterWord, "");
        }
        return text;
    }
    private String processContent(String content) {
        if (content == null || content.trim().isEmpty()) {
            throw new IllegalArgumentException("输入内容不能为空");
        }
        return content.replaceAll("—\\d+——", "");
    }
}
