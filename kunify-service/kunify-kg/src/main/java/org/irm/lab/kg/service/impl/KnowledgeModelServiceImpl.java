package org.irm.lab.kg.service.impl;

import cn.hutool.core.util.ObjectUtil;
import com.mongodb.client.model.Filters;
import lombok.RequiredArgsConstructor;
import lombok.SneakyThrows;
import org.bson.conversions.Bson;
import org.irm.lab.common.constant.AbstractBaseEntityFieldConstant;
import org.irm.lab.common.constant.ExceptionMessageConst;
import org.irm.lab.common.constant.FiledNameConst;
import org.irm.lab.common.entity.AbstractBaseEntity;
import org.irm.lab.common.exception.ServiceException;
import org.irm.lab.common.pojo.BaseStatus;
import org.irm.lab.common.support.Condition;
import org.irm.lab.common.support.MyPage;
import org.irm.lab.common.utils.JudgmentDuplication;
import org.irm.lab.kg.constant.EchartsConstant;
import org.irm.lab.kg.entity.*;
import org.irm.lab.kg.repository.KnowledgeModelRepository;
import org.irm.lab.kg.repository.KnowledgeRelationRepository;
import org.irm.lab.kg.service.IKnowledgeConceptService;
import org.irm.lab.kg.service.IKnowledgeModelService;
import org.irm.lab.kg.service.IKnowledgePropertyService;
import org.irm.lab.kg.service.IKnowledgeRelationService;
import org.irm.lab.kg.vo.KnowledgeConceptVO;
import org.irm.lab.kg.vo.KnowledgeModelVO;
import org.irm.lab.kg.vo.echarts.EchartsNode;
import org.irm.lab.kg.vo.echarts.EchartsRelation;
import org.irm.lab.kg.vo.echarts.EchartsVO;
import org.irm.lab.kg.wrapper.KnowledgeModelWapper;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

@Service
@RequiredArgsConstructor
public class KnowledgeModelServiceImpl implements IKnowledgeModelService {

    private final KnowledgeModelRepository knowledgeModelRepository;
    private final IKnowledgeRelationService knowledgeRelationService;
    private final IKnowledgeConceptService knowledgeConceptService;
    private final IKnowledgePropertyService knowledgePropertyService;
    private final KnowledgeRelationRepository knowledgeRelationRepository;

    final
    /**
     * 知识模型分页查询
     *
     * @param pageMap 查询条件
     * @param page    当前页码
     * @param size    每页数据条数
     * @return {@link MyPage<KnowledgeModel>}
     */
    @Override
    public MyPage<KnowledgeModelVO> page(Map<String, Object> pageMap, int page, int size) {
        Bson filter = Condition.getFilter(pageMap, KnowledgeModel.class);
        MyPage<KnowledgeModel> pageModels = knowledgeModelRepository.findPageByConditionAndSorted(
                filter, Filters.eq(AbstractBaseEntityFieldConstant.CREATE_TIME, -1), page, size);
        List<KnowledgeModelVO> knowledgeModelVOS = KnowledgeModelWapper.build().entityVOList(pageModels.getContent());
        return new MyPage<>(page, size, pageModels.getTotalElements(), knowledgeModelVOS);
    }

    /**
     * 生成主模型
     *
     * @return KnowledgeModelVO
     */
    @Override
    public KnowledgeModelVO getMainModel() {
        KnowledgeModelVO mainModel = new KnowledgeModelVO();
        mainModel.setId("0");
        mainModel.setName("主模型");
        mainModel.setDescription("当前模型为主模型");
        //添加所有概念
        Set<ReferenceMap> concepts = new HashSet<>();
        knowledgeConceptService.list().forEach(knowledgeConcept -> {
            ReferenceMap referenceMap = new ReferenceMap();
            referenceMap.setId(knowledgeConcept.getId());
            referenceMap.setPropertyIds(knowledgeConcept.getPropertyIds());
            concepts.add(referenceMap);
        });
        //添加所有关系
        Set<ReferenceMap> relations = new HashSet<>();
        knowledgeRelationService.list().forEach(knowledgeRelation -> {
            ReferenceMap referenceMap = new ReferenceMap();
            referenceMap.setId(knowledgeRelation.getId());
            referenceMap.setPropertyIds(knowledgeRelation.getPropertyIds());
            relations.add(referenceMap);
        });
        mainModel.setConcepts(concepts);
        mainModel.setRelations(relations);
        return mainModel;
    }

    /**
     * 查询所有的知识模型
     *
     * @return 模型集合
     */
    @Override
    public List<KnowledgeModelVO> list() {
        List<KnowledgeModel> knowledgeModelList = knowledgeModelRepository.findAll();
        return KnowledgeModelWapper.build().entityVOList(knowledgeModelList);
    }

    /**
     * 模型增改
     *
     * @param knowledgeModel 模型实体
     * @return 操作完后的实体信息
     */
    @Override
    @SneakyThrows
    public KnowledgeModel save(KnowledgeModel knowledgeModel) {
        HashMap<String, Object> modelMap = new HashMap<>();
        modelMap.put(FiledNameConst.IDENTIFIER, knowledgeModel.getIdentifier());
        List<KnowledgeModel> models = knowledgeModelRepository.findByCondition(Condition.getFilter(modelMap, KnowledgeModel.class));
        // 字段重复性校验
        JudgmentDuplication.judgment(knowledgeModel, models, Set.of(FiledNameConst.IDENTIFIER));
        // id不为null,并且概念和关系size都为0的话就是修改knowledgeModel模型对象（不改变已配置的概念和关系）。应该把概念和关系设置为null，防止被置空
        if (knowledgeModel.getId() != null) {
            KnowledgeModel model = knowledgeModelRepository.findById(knowledgeModel.getId());
            if (BaseStatus.DEPLOYED.equals(model.getDeployed()))
                throw new ServiceException("模型已部署，无法修改!");
            if (ObjectUtil.isEmpty(knowledgeModel.getConcepts())) {
                knowledgeModel.setConcepts(null);
            }
            if (ObjectUtil.isEmpty(knowledgeModel.getRelations())) {
                knowledgeModel.setRelations(null);
            }
        }
        return knowledgeModelRepository.findById(knowledgeModelRepository.save(knowledgeModel));
    }

    /**
     * 删除多个模型
     *
     * @param ids 模型Id集合
     */
    @Override
    public void remove(List<String> ids) {
        for (String id : ids) {
            KnowledgeModel knowledgeModel = info(id);
            //若当前模型已部署则不可删除
            if (knowledgeModel.getDeployed().equals(BaseStatus.DEPLOYED))
                throw new ServiceException(ExceptionMessageConst.MODEL_ALREADY_DEPLOYED);
            knowledgeModelRepository.deleteByIdFake(id);
        }
    }

    /**
     * 将知识模型修改为已部署状态
     *
     * @param id 模型id
     */
    @Override
    public void deployed(String id) {
        //TODO 此处只是部署了当前的模型，对应的内容部署需后续添加
        KnowledgeModel knowledgeModel = info(id);
        knowledgeModel.setDeployed(BaseStatus.DEPLOYED);
        save(knowledgeModel);
    }

    /**
     * 查询单知识模型
     *
     * @param id 模型id
     * @return 单个模型实体
     */
    @Override
    public KnowledgeModel info(String id) {
        return knowledgeModelRepository.findById(id);
    }

    /**
     * 添加引用的概念
     */
    @Override
    public KnowledgeModel addModelConcept(String modelId, String conceptId) {
        KnowledgeModel knowledgeModel = info(modelId);
        // 获取要配置的概念对象
        KnowledgeConceptVO conceptVO = knowledgeConceptService.infoVO(conceptId);
        // 添加新概念
        knowledgeModel.getConcepts().add(new ReferenceMap(conceptVO.getId(), conceptVO.getProperties().stream().map(AbstractBaseEntity::getId).collect(Collectors.toList())));
        // 获取已经配置的概念对象
        List<String> conceptIdList = knowledgeModel.getConcepts().stream().map(ReferenceMap::getId).collect(Collectors.toList());
        // 只添加存在的概念之间的关系
        Bson bson1 = Filters.and(Filters.eq("conceptOne", conceptId), Filters.in("conceptTwo", conceptIdList));
        Bson bson2 = Filters.and(Filters.in("conceptOne", conceptIdList), Filters.eq("conceptTwo", conceptId));
        List<KnowledgeRelation> relationList = knowledgeRelationRepository.findByCondition(Filters.or(bson1,bson2));
        relationList.forEach(relation -> {
            // 模型中设置边，并且设置边的属性
            knowledgeModel.getRelations().add(new ReferenceMap(relation.getId(), relation.getPropertyIds()));
        });
        return save(knowledgeModel);
    }

    /**
     * 删除引用的概念
     *
     * @return 更新后的模型
     */
    @Override
    public KnowledgeModel removeModelConcept(String modelId, String conceptId) {
        KnowledgeModel knowledgeModel = info(modelId);
        if (BaseStatus.DEPLOYED.equals(knowledgeModel.getDeployed()))
            throw new ServiceException(ExceptionMessageConst.MODEL_ALREADY_DEPLOYED);
        // 删除概念
        knowledgeModel.getConcepts().removeIf(referenceMap -> referenceMap.getId().equals(conceptId));
        // 删除关系
        Bson bson = Filters.or(Filters.eq("conceptOne", conceptId), Filters.eq("conceptTwo", conceptId));
        List<KnowledgeRelation> relationList = knowledgeRelationRepository.findByCondition(bson);
        if (ObjectUtil.isNotEmpty(relationList)){
            Set<String> relationIds = relationList.stream().map(KnowledgeRelation::getId).collect(Collectors.toSet());
            knowledgeModel.getRelations().removeIf(referenceMap -> relationIds.contains(referenceMap.getId()));
        }
        return knowledgeModelRepository.findById(knowledgeModelRepository.save(knowledgeModel));
    }


    /**
     * 删除引用的关系
     *
     * @return 更新后的模型
     */
    @Override
    public KnowledgeModel removeModelRelation(String modelId, String relationId) {
        KnowledgeModel knowledgeModel = info(modelId);
        if (BaseStatus.DEPLOYED.equals(knowledgeModel.getDeployed()))
            throw new ServiceException(ExceptionMessageConst.MODEL_ALREADY_DEPLOYED);
        knowledgeModel.getRelations().removeIf(referenceMap -> referenceMap.getId().equals(relationId));
        return knowledgeModelRepository.findById(knowledgeModelRepository.save(knowledgeModel));
    }

    /**
     * 删除引用的概念的属性
     *
     * @return 更新后的模型
     */
    @Override
    public KnowledgeModel removeModelConceptProperty(String modelId, String conceptId, String propertyId) {
        KnowledgeModel knowledgeModel = info(modelId);
        if (BaseStatus.DEPLOYED.equals(knowledgeModel.getDeployed()))
            throw new ServiceException(ExceptionMessageConst.MODEL_ALREADY_DEPLOYED);
        knowledgeModel.getConcepts().stream()
                .filter(referenceMap -> conceptId.equals(referenceMap.getId()))
                .findFirst()
                .ifPresent(
                        referenceMap -> referenceMap.getPropertyIds()
                                .removeIf(propertyId::equals));
        return save(knowledgeModel);
    }

    /**
     * 删除引用的关系的属性
     *
     * @return 更新后的模型
     */
    @Override
    public KnowledgeModel removeModelRelationProperty(String modelId, String relationId, String propertyId) {
        KnowledgeModel knowledgeModel = info(modelId);
        if (BaseStatus.DEPLOYED.equals(knowledgeModel.getDeployed()))
            throw new ServiceException(ExceptionMessageConst.MODEL_ALREADY_DEPLOYED);
        knowledgeModel.getRelations().stream().filter(referenceMap -> relationId.equals(referenceMap.getId())).findFirst()
                .ifPresent(referenceMap -> referenceMap.getPropertyIds().removeIf(propertyId::equals));
        return save(knowledgeModel);
    }

    /**
     * 根据字段查找模型
     *
     * @return 模型集合
     */
    @Override
    public List<KnowledgeModel> list(Map<String, Object> queryParam) {
        return knowledgeModelRepository.findByCondition(Condition.getFilter(queryParam, KnowledgeModel.class));
    }


    @Override
    public EchartsVO infoMap(String modelId) {
        EchartsVO echartsVO = new EchartsVO();
        HashSet<EchartsNode> echartsNodes = new HashSet<>();
        HashSet<EchartsRelation> echartsRelations = new HashSet<>();
        KnowledgeModel model = info(modelId);
        List<KnowledgeConcept> concepts = new ArrayList<>();
        ArrayList<KnowledgeRelation> relations = new ArrayList<>();
        model.getConcepts().forEach(referenceMap -> {
            concepts.add(knowledgeConceptService.simpleInfo(referenceMap.getId()));
        });
        model.getRelations().forEach(referenceMap -> {
            relations.add(knowledgeRelationService.info(referenceMap.getId()));
        });

        concepts.forEach(concept -> {
            echartsNodes.add(
                    new EchartsNode()
                            .setName(concept.getName())
                            .setType(EchartsConstant.CONCEPT)
                            .setId(concept.getId())
            );
        });

        relations.forEach(relation -> {
            echartsRelations.add(
                    new EchartsRelation()
                            .setName(relation.getName())
                            .setTarget(relation.getConceptTwo())
                            .setSource(relation.getConceptOne())
                            .setConceptId(relation.getId())
                            .setIsDirection(false)
            );

        });

        echartsVO.setNodes(echartsNodes);
        echartsVO.setRelations(echartsRelations);
        echartsVO.setNodeTotal(echartsNodes.size());
        return echartsVO;
    }

    /**
     * 可用概念列表（去除已经配置的概念）
     *
     * @param modelId 模型ID
     * @return
     */
    @Override
    public List<KnowledgeConcept> availableConceptList(String modelId) {
        KnowledgeModel knowledgeModel = info(modelId);
        Set<String> conceptIds = knowledgeModel.getConcepts().stream().map(ReferenceMap::getId).collect(Collectors.toSet());
        return knowledgeConceptService.list().stream().filter(concept -> !conceptIds.contains(concept.getId())).collect(Collectors.toList());
    }

    @Override
    public List<KnowledgeProperty> infoModelConceptProperty(String modelId, String conceptId) {
        ArrayList<KnowledgeProperty> result = new ArrayList<>();
        KnowledgeModel knowledgeModel = info(modelId);
        knowledgeModel.getConcepts().stream().filter(referenceMap -> conceptId.equals(referenceMap.getId())).findFirst().ifPresent(
                referenceMap -> result.addAll(knowledgePropertyService.listByIds(referenceMap.getPropertyIds()))
        );
        return result;
    }

    @Override
    public List<KnowledgeProperty> infoModelRelationProperty(String modelId, String relationId) {
        ArrayList<KnowledgeProperty> result = new ArrayList<>();
        KnowledgeModel knowledgeModel = info(modelId);
        knowledgeModel.getRelations().stream().filter(referenceMap -> relationId.equals(referenceMap.getId())).findFirst().ifPresent(
                referenceMap -> result.addAll(knowledgePropertyService.listByIds(referenceMap.getPropertyIds()))
        );
        return result;
    }

    @Override
    public Set<String> getAlreadyBindPropertyByConcept(String conceptId) {
        Set<String> propertyIds = new HashSet<>();
        knowledgeModelRepository.findAll().forEach(model -> {
            List<ReferenceMap> referenceMaps = model.getConcepts().stream().filter(s -> conceptId.equals(s.getId())).collect(Collectors.toList());
            referenceMaps.forEach(s -> {
                propertyIds.addAll(s.getPropertyIds());
            });
        });
        return propertyIds;
    }

    @Override
    public Set<String> getAlreadyBindPropertyByRelation(String relationId) {
        Set<String> propertyIds = new HashSet<>();
        knowledgeModelRepository.findAll().forEach(model -> {
            List<ReferenceMap> referenceMaps = model.getRelations().stream().filter(s -> relationId.equals(s.getId())).collect(Collectors.toList());
            referenceMaps.forEach(s -> {
                propertyIds.addAll(s.getPropertyIds());
            });
        });
        return propertyIds;
    }

    /**
     * 获取可用的模型（已部署）
     *
     * @return {@link KnowledgeModel}
     */
    @Override
    public List<KnowledgeModel> listEnableModel() {
        return knowledgeModelRepository.findByCondition(Filters.eq("deployed", BaseStatus.DEPLOYED));
    }

    //    /**
//     * 判断概念是否存在被模型引用
//     *
//     * @param conceptId 概念ID
//     * @return
//     */
//    @Override
//    public Boolean hasConcept(String conceptId) {
//        return knowledgeModelRepository.findAll()
//                .stream()
//                .anyMatch(model ->
//                        model.getConcepts()
//                                .stream()
//                                .map(AbstractBaseEntity::getId)
//                                .collect(Collectors.toList())
//                                .contains(conceptId));
//    }
//
//    @Override
//    public Boolean hasRelation(String relationId) {
//        return knowledgeModelRepository.findAll()
//                .stream()
//                .anyMatch(model ->
//                        model.getRelations()
//                                .stream()
//                                .map(AbstractBaseEntity::getId)
//                                .collect(Collectors.toList())
//                                .contains(relationId));
//    }
}
