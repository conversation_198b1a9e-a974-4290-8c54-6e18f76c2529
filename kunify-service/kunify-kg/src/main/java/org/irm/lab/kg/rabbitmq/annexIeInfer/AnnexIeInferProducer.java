package org.irm.lab.kg.rabbitmq.annexIeInfer;

import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import lombok.RequiredArgsConstructor;

/**
 *
 */
@Component
@RequiredArgsConstructor
public class AnnexIeInferProducer {

    private final RabbitTemplate rabbitTemplate;

    @Value("${spring.application.name}")
    private String APP_KEY;

    public void send(AnnexIeInferMessage message) {

        rabbitTemplate.convertAndSend(
                AnnexIeInferMessage.EXCHANGE ,
                AnnexIeInferMessage.ROUTING_KEY + APP_KEY ,
                message
        );
    }

}
