package org.irm.lab.kg.strategy.ocr.impl;

import java.io.InputStream;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.dromara.streamquery.stream.core.stream.Steam;
import org.irm.lab.common.utils.NetWorkFileUtil;
import org.irm.lab.common.utils.ThreadLocalUtil;
import org.irm.lab.kg.algorithm.DocumentUnit;
import org.irm.lab.kg.algorithm.PDFPreciseProcessor;
import org.irm.lab.kg.rabbitmq.producer.EsSyncProducer;
import org.irm.lab.kg.repository.DocumentUnitRepository;
import org.irm.lab.kg.utils.SplitterUtil;
import org.irm.lab.repository.entity.Resource;
import org.irm.lab.repository.repository.ResourceAnnexRepository;
import org.irm.lab.repository.repository.ResourceRepository;
import org.irm.lab.resource.entity.Attach;
import org.irm.lab.resource.feign.AttachFeign;
import org.irm.lab.resource.feign.IOssEndPoint;
import org.irm.lab.resource.repository.AttachRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.aspose.words.Document;
import com.aspose.words.LayoutCollector;
import com.aspose.words.NodeType;
import com.aspose.words.Paragraph;
import com.mongodb.client.model.Filters;

import cn.hutool.core.util.ObjectUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR> <br/>
 * @date 2024/5/2 <br/>
 * @Copyright 博客：https://eliauku.gitee.io/  ||  per aspera and astra <br/>
 */
@Service("resourceOcrWordStrategy")
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
@Slf4j
public class ResourceOcrWordStrategyImpl extends AbstractFileOcrStrategyImpl {
    private final ResourceRepository resourceRepository;
    private final AttachFeign attachFeign;
    private final IOssEndPoint iOssEndPoint;
    private final AttachRepository attachRepository;
    private final ResourceAnnexRepository resourceAnnexRepository;
    private final EsSyncProducer esSyncProducer;
    private final PDFPreciseProcessor pdfPreciseProcessor;
    private final DocumentUnitRepository documentUnitRepository;

    @Override
    Map<Integer, Map<Integer, String>> getOcrResult(String strategy, Resource message) {
        Map<Integer, Map<Integer, String>> resourceText = new HashMap<>(Map.of());
        final Attach attach = attachRepository.findById(message.getPrimaryFileId());

        try (InputStream inputStream = NetWorkFileUtil.urlToInputStream(attach.getLink());) {

            log.info("开始对主文件进行word抽取");

            resourceText.put(0,processWordDocument(inputStream, message));

        } catch (Exception e) {
            throw new RuntimeException(e);
        }

        return resourceText;
    }

    @Override
    void saveOcrResult(String strategy, Resource message, Map<Integer, Map<Integer, String>> ocrResult) {

        List<DocumentUnit> units = new ArrayList<>();
        DocumentUnit documentUnit = new DocumentUnit();
        documentUnit.setResourceId(message.getId());
        StringBuffer stringBuffer = new StringBuffer();
        ocrResult.get(0).forEach((key, value) -> {

            stringBuffer.append(SplitterUtil.removeLineBreak(value));

        });
        units.add(documentUnit);
        documentUnit.setSortInCurrentPage(1);
        documentUnit.setPage(1);
        documentUnit.setType("0");
        documentUnit.setContent(stringBuffer.toString());

        documentUnitRepository.saveAll(units);
        List<DocumentUnit> documentUnitList = documentUnitRepository.findByCondition(Filters.eq("resourceId", message.getId()));
        if (ObjectUtil.isNotEmpty(documentUnitList)) {
            // 合并内容

            esSyncProducer.sendMessage(ThreadLocalUtil.get("user"), message, null, stringBuffer.toString(), "主文件");
            log.info("【主文件】{} 同步到ES中", message.getName());
        }
        resourceRepository.save(message);

    }

    public Map<Integer, String> processWordDocument(InputStream inputStream, Resource resource) throws Exception {
        Document doc = new Document(inputStream);
        LayoutCollector layoutCollector = new LayoutCollector(doc);
        Map<Integer, StringBuilder> pagesText = new HashMap<>();

        for (Paragraph para : (Iterable<Paragraph>) doc.getChildNodes(NodeType.PARAGRAPH, true)) {
            int pageNumber = layoutCollector.getStartPageIndex(para);
            pagesText.putIfAbsent(pageNumber, new StringBuilder());
            pagesText.get(pageNumber).append(para.getText());
            logger.info("当前资源名称为：{}，解析到当前资源的第{}页", resource.getName(), pageNumber);
        }

        Map<Integer, String> textByPages = new HashMap<>();
        pagesText.forEach((key, value) -> textByPages.put(key, value.toString()));
        return textByPages;
    }
}
