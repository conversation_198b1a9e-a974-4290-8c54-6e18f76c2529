package org.irm.lab.kg.service;

import org.irm.lab.kg.entity.KnowledgeConcept;
import org.irm.lab.kg.entity.KnowledgeModel;
import org.irm.lab.kg.entity.KnowledgeProperty;
import org.irm.lab.kg.vo.KnowledgeConceptVO;
import org.irm.lab.kg.vo.ReferenceMapVO;
import org.irm.lab.kg.vo.echarts.EchartsVO;

import java.util.List;
import java.util.Set;

public interface IKnowledgeConceptService {


    KnowledgeConcept save(KnowledgeConcept knowledgeConcept);

    void remove(List<String> ids);

    KnowledgeConceptVO infoTree(String id);

    KnowledgeConceptVO infoVO(String id);

    KnowledgeConcept simpleInfo(String id);

    List<KnowledgeConceptVO> listTree();

    EchartsVO infoMap(String id);

    List<KnowledgeConcept> list();


    List<String> alreadyBindModelProperty(String conceptId);

    List<ReferenceMapVO> conceptListByModelId(String modelId);

    /**
     * 根据Id列表获取多个知识模型
     *
     * @param ids id列表
     * @return {@link KnowledgeModel}
     */
    List<KnowledgeConcept> findByIds(List<String> ids);

    /**
     * 根据Id查询
     *
     * @param id 概念Id
     * @return {@link KnowledgeConcept}
     */
    KnowledgeConcept info(String id);

    Set<String> getAllChildConceptIds(String parentConceptId, Set<String> conceptIds);
    /**
     * 获取当前概念可用属性
     *
     * @param conceptId 关系Id
     * @return 可用属性列表
     */
    List<KnowledgeProperty> listAvailableProperty(String conceptId);

    /**
     * 概念引用属性
     *
     * @param id  概念Id
     * @param ids 属性Id列表
     * @return {@link KnowledgeConcept}
     */
    KnowledgeConcept bind(String id, List<String> ids);


    /**
     * 取消概念属性引用
     *
     * @param id 概念Id
     * @param ids 属性Id
     */
    void removeRelationProperty(String id, List<String> ids);

    /**
     * 通过概念名称获取概念对象
     *
     * @param name 概念名称
     * @return {@link KnowledgeConcept}
     */
    KnowledgeConcept findByConceptName(String name);

    List<KnowledgeConcept> findAllDelete();

}
