package org.irm.lab.kg.controller.annex;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.irm.lab.common.annotation.MyLog;
import org.irm.lab.common.api.R;
import org.irm.lab.common.constant.LogConstant;
import org.irm.lab.kg.service.annex.IAnnexDocumentParsingService;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 * @date 2023/5/8 15:26
 * @description
 */
@RestController
@RequestMapping("/annex-doc-parsing")
@RequiredArgsConstructor
@Api("附件文档解析")
public class AnnexDocumentParsingController {

    private final IAnnexDocumentParsingService iAnnexDocumentParsingService;

    /**
     * 重新进行附件文档解析
     *
     * @param resourceAnnexId 附件Id
     */
    @MyLog(menu = LogConstant.MENU_KNOWLEDGE, dataType = LogConstant.DATA_ANNEX, operation = LogConstant.OPERATION_REANALYSIS)
    @ApiOperation(value = "重新解析", hidden = true)
    @PostMapping("/re-parsing")
    public R<Void> reParsing(@RequestParam String resourceAnnexId, @RequestParam String type) {
        if ("ocr".equals(type)) {
            iAnnexDocumentParsingService.reParsingWithOCR(resourceAnnexId);
        } else {
            iAnnexDocumentParsingService.reParsing(resourceAnnexId);
        }
        return R.success();
    }

    /**
     * 附件文档解析结果确认
     *
     * @param resourceAnnexId 附件Id
     */
    @MyLog(menu = LogConstant.MENU_KNOWLEDGE, dataType = LogConstant.DATA_ANNEX, operation = LogConstant.OPERATION_ANALYSIS_TRUE)
    @ApiOperation(value = "解析确认")
    @PostMapping("/confirm")
    public R<Void> confirm(@RequestParam String resourceAnnexId) {
        iAnnexDocumentParsingService.confirm(resourceAnnexId);
        return R.success();
    }


}
