//package org.irm.lab.kg.algorithm.ruleMatching.huiyijiyao;
//
//import cn.hutool.core.lang.Opt;
//import cn.hutool.core.util.IdUtil;
//import cn.hutool.core.util.ReUtil;
//import cn.hutool.core.util.StrUtil;
//import cn.hutool.json.JSONArray;
//import cn.hutool.json.JSONObject;
//import cn.hutool.json.JSONUtil;
//import lombok.RequiredArgsConstructor;
//import lombok.extern.log4j.Log4j2;
//import org.apache.pdfbox.pdmodel.PDDocument;
//import org.apache.pdfbox.text.PDFTextStripper;
//import org.irm.lab.common.enums.AlgorithmType;
//import org.irm.lab.common.provider.MinioLinkProvider;
//import org.irm.lab.kg.algorithm.DocumentUnit;
//import org.irm.lab.kg.algorithm.IAlgorithmProcessor;
//import org.irm.lab.kg.algorithm.ruleMatching.RuleParsingEntity;
//import org.irm.lab.kg.algorithm.ruleMatching.RuleParsingRelation;
//import org.irm.lab.kg.algorithm.ruleMatching.RuleParsingResult;
//import org.irm.lab.kg.service.IDocumentUnitService;
//import org.irm.lab.kg.service.impl.kgprocess.ruleMatching.DocRuleParsing;
//import org.irm.lab.repository.constant.DocumentResolvedStatus;
//import org.irm.lab.repository.entity.Resource;
//import org.irm.lab.repository.feign.ResourceFeign;
//import org.jetbrains.annotations.NotNull;
//import org.springframework.stereotype.Service;
//
//import java.util.*;
//import java.util.concurrent.atomic.AtomicInteger;
//import java.util.concurrent.atomic.AtomicReference;
//import java.util.concurrent.atomic.AtomicReferenceArray;
//import java.util.regex.Matcher;
//import java.util.regex.Pattern;
//import java.util.stream.Collectors;
//
///**
// * 会议纪要
// *
// * <AUTHOR>
// * @date 2023/4/17 10:32
// */
//@Log4j2
//@Service
//@RequiredArgsConstructor
//public class JI_YAO_02 implements IAlgorithmProcessor {
//
//    private final ResourceFeign resourceFeign;
//    private final MinioLinkProvider minioLinkProvider;
//    @javax.annotation.Resource(name = "resourceDocRuleParsing")
//    private DocRuleParsing docRuleParsing;
//    private final IDocumentUnitService documentUnitService;
//
//    @Override
//    public AlgorithmType getCode() {
//        return AlgorithmType.PDF_RULE_JI_YAO_02;
//    }
//
//
//    @Override
//    public void process(Resource resource) {
//        String processName = AlgorithmType.PDF_RULE_JI_YAO_02.getName();
//        log.info(">>>>>>>>>>【主文件】【{}】【规则解析】开始>>>>>>>>>>", processName);
//        try {
//            resource.setRuleStatus(DocumentResolvedStatus.RESOLVING);
//            resourceFeign.save(resource);
//
//            // 获取规则解析结果
//            JSONObject results = ruleProcess(resource);
//            // 对结果转换为实体
//            RuleParsingResult ruleParsingResult = JSONUtil.toBean(results, RuleParsingResult.class, true);
//            // 存储处理后的实体
//            List<RuleParsingEntity> entityList = new ArrayList<>();
//            // 存储处理后的属性(实例的属性)
//            List<RuleParsingEntity> propertyList = new ArrayList<>();
//            // 存储处理后关系
//            List<RuleParsingRelation> relationList = ruleParsingResult.getRelation();
//            // 实例处理
//            processEntity(ruleParsingResult.getResult(), entityList, propertyList);
//            // 生成主文件缓存标签
//            docRuleParsing.ruleParsingCacheLabelData(resource, entityList, propertyList, relationList);
//            resource.setRuleStatus(DocumentResolvedStatus.RESOLVED);
//            log.info("<<<<<<<<<<【主文件】【{}】【规则解析】结束<<<<<<<<<<", processName);
//        } catch (Exception e) {
//            resource.setRuleStatus(DocumentResolvedStatus.RESOLVED_FAILED);
//            log.error("解析失败 {}", e.getMessage());
//        } finally {
//            resourceFeign.save(resource);
//        }
//    }
//
//    /**
//     * 对识别出的实体进行处理
//     *
//     * @param entityList    实体列表
//     * @param newEntityList 处理后的实体列表
//     * @param propertyList  处理后的属性列表
//     */
//    @Override
//    public void processEntity(List<RuleParsingEntity> entityList, List<RuleParsingEntity> newEntityList, List<RuleParsingEntity> propertyList) {
//        for (RuleParsingEntity ruleParsingEntity : entityList) {
//            if (StrUtil.equals(ruleParsingEntity.getType(), "概念")) newEntityList.add(ruleParsingEntity);
//            else propertyList.add(ruleParsingEntity);
//        }
//    }
//
//
//    /**
//     * 规则解析
//     *
//     * @param resource 资源对象
//     * @return 解析接轨
//     */
//    public JSONObject ruleProcess(Resource resource) {
//        //获取资源对应语料
//        List<DocumentUnit> documentUnits = documentUnitService.infoByResource(resource.getId())
//                .stream().sorted(Comparator.comparingInt(DocumentUnit::getSortInCurrentPage)).collect(Collectors.toList());
//
//        //提取纪要,合并结果
//        return mergeResult(extractMinutesByUnit(documentUnits));
//    }
//
//    /**
//     * 抽取纪要和其相关属性
//     */
//    private JSONObject extractMinutesByUnit(List<DocumentUnit> units) {
//        JSONObject entries = initJSONObject();
//        JSONArray result = entries.getJSONArray("result");
//        JSONArray relation = entries.getJSONArray("relation");
//
//        //获取纪要的段落语料
//        log.info("=========开始获取纪要的段落语料=========");
//        List<DocumentUnit> minutesUnit = getMinutesDocumentUnitAndSort(units);
//        log.info("=========纪要的段落个数{}=========", minutesUnit.size());
//
//        //解析（名称、纪要内容、顺序）
//        for (int i = 1; i <= minutesUnit.size(); i++) {
//            DocumentUnit unit = minutesUnit.get(i - 1);
//            log.info("当前【纪要】段落为：{}", unit.getContent());
//
//            //第一句话，设置为纪要名称
//            String firstSentence = utilFirstSentence(unit.getContent());
//            String minutesNameId = IdUtil.simpleUUID();
//
//            result.add(newEntity(minutesNameId, "概念", "纪要", firstSentence));
//            log.info("纪要名称：{}", firstSentence);
//
//
//            //整个段落，设置为纪要内容
//            String content = unit.getContent();
//            String minutesContentId = IdUtil.simpleUUID();
//
//            result.add(newEntity(minutesContentId, "属性", "纪要内容", content));
//            relation.add(newRelation("属性", minutesNameId, minutesContentId));
//            log.info("纪要内容：{}", content);
//
//
//            //当前序号，设置为纪要顺序
//            String minutesSortId = IdUtil.simpleUUID();
//            result.add(newEntity(minutesSortId, "属性", "顺序", String.valueOf(i)));
//            relation.add(newRelation("属性", minutesNameId, minutesSortId));
//            log.info("纪要顺序：{}", i);
//        }
//
//
//        return entries;
//    }
//
//
//    //工具：从段落中获取第一句
//    private static String utilFirstSentence(String content) {
//        Pattern sentencePattern = Pattern.compile("([^\\。]+\\。)");
//        Matcher matcher = sentencePattern.matcher(content);
//        if (matcher.find()) return matcher.group();
//
//        return content;
//    }
//
//    /**
//     * 获取纪要的语料，并且按照段落排序
//     *
//     * @return
//     */
//    private List<DocumentUnit> getMinutesDocumentUnitAndSort(List<DocumentUnit> units) {
//        AtomicReference<List<DocumentUnit>> documentUnits = new AtomicReference<>(new ArrayList<>());
//        //获取纪要的开始和结束段落序号
//        AtomicInteger startIndex = new AtomicInteger(0);
//        AtomicInteger endIndex = new AtomicInteger(units.size());
//        units.stream()
//                .filter(unit -> ReUtil.isMatch(".*纪要如下.*", unit.getContent()) ||
//                        ReUtil.isMatch(".*如下事项.*", unit.getContent()))
//                .findFirst().ifPresent(unit -> startIndex.set(unit.getSortInCurrentPage()));
//
//        units.stream()
//                .filter(unit ->
//                        unit.getContent().startsWith("参加") ||
//                                unit.getContent().startsWith("列席") ||
//                                unit.getContent().startsWith("主持") ||
//                                unit.getContent().startsWith("缺席") ||
//                                unit.getContent().startsWith("记录") ||
//                                unit.getContent().startsWith("出席") ||
//                                unit.getContent().startsWith("参会") ||
//                                unit.getContent().startsWith("附件") ||
//                                unit.getContent().startsWith("打印") ||
//                                unit.getContent().startsWith("校对") ||
//                                unit.getContent().startsWith("分送") ||
//                                unit.getContent().startsWith("抄送") ||
//                                unit.getContent().startsWith("主送") ||
//                                unit.getContent().startsWith("时间") ||
//                                unit.getContent().startsWith("地点")
//                )
//                .findFirst().ifPresent(unit -> endIndex.set(unit.getSortInCurrentPage()));
//
//        log.info("===========纪要段落从【{}】至【{}】===========", startIndex, endIndex);
//        if (startIndex.get() == 0 && endIndex.get() == units.size()) {
//            //未找到纪要所在段落
//            return documentUnits.get();
//        }
//
//        documentUnits.set(
//                units.stream()
//                        .filter(stayUnit -> stayUnit.getSortInCurrentPage() > startIndex.get() &&
//                                stayUnit.getSortInCurrentPage() < endIndex.get())
//                        .sorted(Comparator.comparingInt(DocumentUnit::getSortInCurrentPage))
//                        .collect(Collectors.toList()));
//        return documentUnits.get();
//    }
//}
