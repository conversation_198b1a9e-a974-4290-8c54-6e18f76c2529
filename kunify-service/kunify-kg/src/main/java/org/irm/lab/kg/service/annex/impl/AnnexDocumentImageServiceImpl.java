package org.irm.lab.kg.service.annex.impl;

import cn.hutool.core.util.ObjectUtil;
import com.mongodb.client.model.Filters;
import lombok.RequiredArgsConstructor;
import org.bson.conversions.Bson;
import org.irm.lab.common.exception.ServiceException;
import org.irm.lab.common.provider.MinioLinkProvider;
import org.irm.lab.kg.algorithm.DocumentImage;
import org.irm.lab.kg.entity.annex.AnnexDocumentImage;
import org.irm.lab.kg.repository.annex.processing.AnnexDocumentImageRepository;
import org.irm.lab.kg.service.annex.IAnnexDocumentImageService;
import org.springframework.stereotype.Service;

import java.util.Comparator;
import java.util.List;
import java.util.stream.Collectors;

@Service
@RequiredArgsConstructor
public class AnnexDocumentImageServiceImpl implements IAnnexDocumentImageService {

    private final AnnexDocumentImageRepository annexDocumentImageRepository;
    private final MinioLinkProvider minioLinkProvider;

    /**
     * 根据附件唯一标识和页码获取图片预览地址
     *
     * @param annexIdentifier 附件唯一标识
     * @param page            页码
     * @return 图片预览地址
     */
    @Override
    public String getPagePicture(String annexIdentifier, Integer page) {
        Bson bson = Filters.and(Filters.eq("page", page), Filters.eq("annexId", annexIdentifier));
        List<AnnexDocumentImage> byCondition = annexDocumentImageRepository.findByCondition(bson);
        DocumentImage image = null;
        if (ObjectUtil.isNotEmpty(byCondition)) {
            image = byCondition.get(0);
        }
        if (image == null) return "此类文档暂不支持图片预览!";
        return minioLinkProvider.getMinioLink(image.getAttachName());
    }


    /**
     * 根据附件Id识获取该附件的所有图片
     *
     * @param annexId 附件Id
     * @return 图片预览地址列表
     */
    @Override
    public List<String> getResourceImage(String annexId) {
        Bson filter = Filters.eq("annexId", annexId);
        List<AnnexDocumentImage> annexDocumentImageList = annexDocumentImageRepository.findByCondition(filter);
        if (ObjectUtil.isEmpty(annexDocumentImageList)) throw new ServiceException("该附件暂无图片资源");
        return annexDocumentImageList.stream().
                sorted(Comparator.comparing(DocumentImage::getPage)).
                map(documentImage -> minioLinkProvider.getMinioLink(documentImage.getAttachName())).collect(Collectors.toList());
    }
}
