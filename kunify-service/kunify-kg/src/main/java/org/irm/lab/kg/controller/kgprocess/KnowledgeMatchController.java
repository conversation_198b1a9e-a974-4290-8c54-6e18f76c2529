package org.irm.lab.kg.controller.kgprocess;

import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONUtil;
import com.aspose.pdf.facades.Facade;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.irm.lab.common.annotation.MyLog;
import org.irm.lab.common.api.R;
import org.irm.lab.common.constant.LogConstant;
import org.irm.lab.kg.entity.processing.LabelData;
import org.irm.lab.kg.entity.processing.TripleLabelData;
import org.irm.lab.kg.service.ILabelDataConsolidationService;
import org.irm.lab.kg.service.ILabelDataService;
import org.irm.lab.kg.service.ILabelGraphService;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2023/3/14 14:35
 * @description 知识匹配控制器
 */
@RestController
@RequestMapping("/knowledge-match")
@Api(value = "知识匹配控制器")
@RequiredArgsConstructor
public class KnowledgeMatchController {

    private final ILabelGraphService iLabelGraphService;
    private final ILabelDataService iLabelDataService;
    private final ILabelDataConsolidationService iLabelDataConsolidationService;

    /**
     * 知识匹配 ===> 获取标签数据树
     *
     * @param resourceId 资源Id
     * @param dataSetId  数据集Id
     * @return 标签数据
     */
    @ApiOperation(value = "获取标签数据树")
    @GetMapping("/relevance")
    public R<JSONArray> relevance(@RequestParam String resourceId, @RequestParam String dataSetId) {
        return R.data(iLabelGraphService.relevanceShow(resourceId, dataSetId));
    }


    /**
     * 获取该资源下的所有标签数据
     *
     * @param resourceId 资源Id
     * @return {@link LabelData}
     */
    @ApiOperation(value = "获取该资源下的所有标签数据", hidden = true)
    @GetMapping("/list-label-data")
    public R<List<LabelData>> listLabelData(@RequestParam String resourceId) {
        return R.data(iLabelGraphService.listLabelData(resourceId));
    }

    /**
     * 获取指定概念下的所有实例标签
     *
     * @param labelId 标签Id（概念Id）
     * @return {@link LabelData}
     */
    @ApiOperation(value = "获取指定概念下的所有实例标签", hidden = true)
    @GetMapping("/list-label-data-condition")
    public R<List<LabelData>> listLabelDataCondition(@RequestParam String resourceId, @RequestParam String labelId) {
        return R.data(iLabelDataService.findByCondition(Map.of("resourceId", resourceId, "labelId", labelId)));
    }

    /**
     * 新增或修改标签数据对象
     *
     * @param labelData {@link LabelData}
     */
    @ApiOperation(value = "新增或修改标签数据对象", hidden = true)
    @PostMapping("/save-label-data")
    public R<String> saveOrUpdateLabelData(@RequestBody LabelData labelData) {
        iLabelGraphService.saveOrUpdateLabelData(labelData);
        return R.success();
    }

    /**
     * 删除实例/实例的属性
     *
     * @param labelData 标签数据
     */
    @ApiOperation(value = "删除实例/实例的属性", hidden = true)
    @PostMapping("/remove-label-data")
    public R<String> removeLabelData(@RequestBody LabelData labelData) {
        iLabelGraphService.removeLabelData(labelData);
        return R.success();
    }


    /**
     * 新增/修改关系三元组
     *
     * @param tripleLabelDataList {@link TripleLabelData}
     */
    @ApiOperation(value = "新增/修改关系三元组", hidden = true)
    @PostMapping("/save-triple-label-data")
    public R<String> saveOrUpdateTripleLabelData(@RequestBody List<TripleLabelData> tripleLabelDataList) {
        iLabelGraphService.saveOrUpdateTripleLabelData(tripleLabelDataList);
        return R.success();
    }

    /**
     * 删除关系三元组/属性
     *
     * @param tripleLabelData 关系三元组
     */
    @ApiOperation(value = "删除关系三元组/属性", hidden = true)
    @PostMapping("/remove-triple-label-data")
    public R<String> removeTripleLabelData(@RequestBody TripleLabelData tripleLabelData) {
        iLabelGraphService.removeTripleLabelData(tripleLabelData);
        return R.success();
    }


    /**
     * 匹配确认
     *
     * @param resourceId 资源Id
     */
    @MyLog(menu = LogConstant.MENU_KNOWLEDGE, dataType = LogConstant.DATA_FILE, operation = LogConstant.OPERATION_CONFIRM_MATCH)
    @ApiOperation(value = "匹配确认")
    @PostMapping("/match-confirm")
    public R<String> matchConfirm(@RequestParam String resourceId, @RequestParam String dataSetId, @RequestParam String modelId) {
        iLabelGraphService.matchConfirm(resourceId, dataSetId, modelId);
        return R.success();
    }

    /**
     * 批量匹配确认
     */
    @MyLog(menu = LogConstant.MENU_KNOWLEDGE, dataType = LogConstant.DATA_FILE, operation = LogConstant.OPERATION_CONFIRM_MATCH)
    @ApiOperation(value = "批量匹配确认")
    @PostMapping("/match-confirm-multi")
    public R<String> matchConfirmMulti(@RequestBody Map<String, Object> params) {
        List<String> resourceIds = JSONUtil.toList(JSONUtil.parseArray(params.get("resourceIds")), String.class);
        String dataSetId = params.get("dataSetId").toString();
        String modelId = params.get("modelId").toString();
        iLabelGraphService.matchConfirmMulti(resourceIds, dataSetId, modelId);
        return R.success();
    }


    /**
     * 重新匹配
     *
     * @param resourceId 资源Id
     */
    @MyLog(menu = LogConstant.MENU_KNOWLEDGE, dataType = LogConstant.DATA_FILE, operation = LogConstant.OPERATION_REMATCH)
    @ApiOperation(value = "重新匹配")
    @PostMapping("/re-match")
    public R<String> reMatch(@RequestParam String resourceId) {
        iLabelDataConsolidationService.reConsolidation(resourceId);
        return R.success();
    }


}
