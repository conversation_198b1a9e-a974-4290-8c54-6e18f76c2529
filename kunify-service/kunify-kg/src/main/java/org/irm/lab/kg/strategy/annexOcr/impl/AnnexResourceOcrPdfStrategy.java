package org.irm.lab.kg.strategy.annexOcr.impl;

import java.io.InputStream;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

import org.dromara.streamquery.stream.core.stream.Steam;
import org.irm.lab.common.utils.NetWorkFileUtil;
import org.irm.lab.common.utils.ThreadLocalUtil;
import org.irm.lab.kg.algorithm.DocumentUnit;
import org.irm.lab.kg.algorithm.PDFPreciseProcessor;
import org.irm.lab.kg.entity.annex.AnnexDocumentUnit;
import org.irm.lab.kg.rabbitmq.producer.EsSyncProducer;
import org.irm.lab.kg.repository.DocumentUnitRepository;
import org.irm.lab.kg.repository.annex.processing.AnnexDocumentUnitRepository;
import org.irm.lab.repository.entity.Resource;
import org.irm.lab.repository.entity.ResourceAnnex;
import org.irm.lab.resource.entity.Attach;
import org.irm.lab.resource.repository.AttachRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.mongodb.client.model.Filters;

import cn.hutool.core.util.ObjectUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR> <br/>
 * @date 2024/5/2 <br/>
 * @Copyright 博客：https://eliauku.gitee.io/  ||  per aspera and astra <br/>
 */
@Service("annexResourceOcrPdfStrategy")
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
@Slf4j
public class AnnexResourceOcrPdfStrategy extends AbstractAnnexFileOcrStrategyImpl {


    private final AttachRepository attachRepository;
    private final EsSyncProducer esSyncProducer;
    private final PDFPreciseProcessor pdfPreciseProcessor;
    private final AnnexDocumentUnitRepository annexDocumentUnitRepository;


    @Override
    Map<Integer, String> getOcrResult(String strategy, org.irm.lab.repository.entity.ResourceAnnex message) {

        Map<Integer, String> resourceText;
        final Attach attach = attachRepository.findById(message.getPdfAttachId());

        try (InputStream inputStream = NetWorkFileUtil.urlToInputStream(attach.getLink());) {

            log.info("开始对副文件进行pdf抽取");

            resourceText = processPDFDocument(inputStream, message);

        } catch (Exception e) {
            throw new RuntimeException(e);
        }

        return resourceText;

    }

    @Override
    void saveOcrResult(String strategy, ResourceAnnex message, Map<Integer, String> ocrResult) {

        List<AnnexDocumentUnit> units = new ArrayList<>();

        Steam.of(ocrResult).forEach(data -> {
            AnnexDocumentUnit documentUnit = new AnnexDocumentUnit();
            documentUnit.setAnnexId(message.getId());
            documentUnit.setContent(data.getValue());
            documentUnit.setSortInCurrentPage(data.getKey());
            documentUnit.setPage(data.getKey());
            documentUnit.setType("0");
            units.add(documentUnit);
        });

        final List<AnnexDocumentUnit> documentUnits = pdfPreciseProcessor.mergeAnnexDocumentUnitPro(units, 1500);
        annexDocumentUnitRepository.saveAll(documentUnits);
        List<AnnexDocumentUnit> documentUnitList = annexDocumentUnitRepository.findByCondition(Filters.eq("annexId", message.getId()));
        if (ObjectUtil.isNotEmpty(documentUnitList)) {
            // 合并内容
            StringBuffer stringBuffer = new StringBuffer();
            documentUnitList.forEach(documentUnit -> stringBuffer.append(documentUnit.getContent()));
            esSyncProducer.sendMessage(ThreadLocalUtil.get("user"), null, message, stringBuffer.toString(), "副文件");
            log.info("【副文件】{} 同步到ES中", message.getName());
        }

    }


}
