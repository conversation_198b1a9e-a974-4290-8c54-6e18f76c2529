package org.irm.lab.kg.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.ObjectUtil;
import com.mongodb.client.model.Filters;
import lombok.SneakyThrows;
import org.bson.conversions.Bson;
import org.irm.lab.common.constant.FiledNameConst;
import org.irm.lab.common.entity.AbstractBaseEntity;
import org.irm.lab.common.support.Condition;
import org.irm.lab.common.exception.ServiceException;
import org.irm.lab.common.utils.ForestNodeMerger;
import org.irm.lab.common.utils.JudgmentDuplication;
import org.irm.lab.kg.builder.KnowledgeConceptBuilder;
import org.irm.lab.kg.builder.KnowledgeRelationBuilder;
import org.irm.lab.kg.constant.EchartsConstant;
import org.irm.lab.kg.constant.TopKnowledgeConceptConstant;
import org.irm.lab.kg.entity.KnowledgeConcept;
import org.irm.lab.kg.entity.KnowledgeModel;
import org.irm.lab.kg.entity.KnowledgeProperty;
import org.irm.lab.kg.exception.ModelExceptionConstant;
import org.irm.lab.kg.repository.KnowledgeConceptRepository;
import org.irm.lab.kg.repository.KnowledgeModelRepository;
import org.irm.lab.kg.service.IKnowledgeConceptService;
import org.irm.lab.kg.service.IKnowledgeModelService;
import org.irm.lab.kg.service.IKnowledgePropertyService;
import org.irm.lab.kg.service.IKnowledgeRelationService;
import org.irm.lab.kg.vo.KnowledgeConceptVO;
import org.irm.lab.kg.vo.KnowledgePropertyVO;
import org.irm.lab.kg.vo.ReferenceMapVO;
import org.irm.lab.kg.vo.echarts.EchartsNode;
import org.irm.lab.kg.vo.echarts.EchartsRelation;
import org.irm.lab.kg.vo.echarts.EchartsVO;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

@Service
public class KnowledgeConceptServiceImpl implements IKnowledgeConceptService {

    @Resource
    private KnowledgeConceptRepository knowledgeConceptRepository;
    @Resource
    private IKnowledgeRelationService knowledgeRelationService;
    @Resource
    private KnowledgeConceptBuilder knowledgeConceptBuilder;
    @Resource
    private KnowledgeRelationBuilder knowledgeRelationBuilder;
    @Resource
    @Lazy
    private KnowledgeModelRepository knowledgeModelRepository;
    @Resource
    @Lazy
    private IKnowledgeModelService knowledgeModelService;
    @Resource
    private IKnowledgePropertyService knowledgePropertyService;


    private void initTopKnowledge() {
        HashMap<String, Object> queryParam = new HashMap<>();
        queryParam.put(FiledNameConst.IDENTIFIER, TopKnowledgeConceptConstant.IDENTIFIER);
        List<KnowledgeConcept> topConcept = knowledgeConceptRepository.findByCondition(Condition.getFilter(queryParam, KnowledgeConcept.class));
        if (topConcept.isEmpty()) {
            //创建顶级概念
            //todo 属性与关系是否有需要初始化的
            knowledgeConceptRepository.save(new KnowledgeConcept()
                    .setName(TopKnowledgeConceptConstant.NAME)
                    .setIdentifier(TopKnowledgeConceptConstant.IDENTIFIER)
                    .setDescription(TopKnowledgeConceptConstant.DESCRIPTION)
            );
        }
    }


    /**
     * 概念增改
     *
     * @param knowledgeConcept 概念实体
     * @return 操作完后的实体信息
     */
    @Override
    @SneakyThrows
    public KnowledgeConcept save(KnowledgeConcept knowledgeConcept) {
        if (knowledgeConcept.getId() != null) {
            if (ObjectUtil.isNotEmpty(findModelByConceptId(List.of(knowledgeConcept.getId()))))
                throw new ServiceException(ModelExceptionConstant.CONCEPT_ALREADY_BIND);
        }
        // 如果没有parentId，就是添加到根概念下
        if (knowledgeConcept.getParentId().equals("0")) {
            List<KnowledgeConcept> conceptList = knowledgeConceptRepository.findByCondition(Filters.eq("identifier", TopKnowledgeConceptConstant.IDENTIFIER));
            if (ObjectUtil.isNotEmpty(conceptList)) {
                KnowledgeConcept topConcept = conceptList.get(0);
                knowledgeConcept.setParentId(topConcept.getId());
            }
        }
        HashMap<String, Object> conceptMap = new HashMap<>();
        conceptMap.put(FiledNameConst.IDENTIFIER, knowledgeConcept.getIdentifier());
        List<KnowledgeConcept> concepts = knowledgeConceptRepository.findByCondition(Condition.getFilter(conceptMap, KnowledgeConcept.class));
        // 字段重复性校验
        JudgmentDuplication.judgment(knowledgeConcept, concepts, Set.of(FiledNameConst.IDENTIFIER));
        return knowledgeConceptRepository.findById(knowledgeConceptRepository.save(knowledgeConcept));
    }


    /**
     * 删除多个概念
     *
     * @param ids 概念Id集合
     */
    @Override
    public void remove(List<String> ids) {
        //删除逻辑（概念被引用无法删除，同时删除相关关系）
        //同时删除子概念
        if (ObjectUtil.isNotEmpty(findModelByConceptId(ids)))
            throw new ServiceException(ModelExceptionConstant.CONCEPT_ALREADY_BIND);
        ids.forEach(id -> {
            //删除当前概念关联的关系
            knowledgeRelationService.removeRelationByConceptId(id);
            knowledgeConceptRepository.deleteByIdFake(id);
            //递归删除子集
            removeChild(id);
        });
    }

    public List<KnowledgeModel> findModelByConceptId(List<String> ids) {
        Bson bson = Filters.elemMatch("concepts", Filters.in("_id", ids));
        return knowledgeModelRepository.findByCondition(bson);
    }

    private void removeChild(String conceptId) {
        HashMap<String, Object> queryParam = new HashMap<>();
        queryParam.put(FiledNameConst.PARENT_ID, conceptId);
        knowledgeConceptRepository.findByCondition(Condition.getFilter(queryParam, KnowledgeConcept.class))
                .stream().findFirst().ifPresent((child) -> {
                    //递归删除子概念
                    //删除当前概念关联的关系
                    knowledgeRelationService.removeRelationByConceptId(child.getId());
                    knowledgeConceptRepository.deleteByIdFake(child.getId());
                    removeChild(child.getId());
                });
    }

    /**
     * 查询单个概念（返回嵌套树结构）
     *
     * @param id 概念id
     * @return 单个概念实体
     */
    @Override
    public KnowledgeConceptVO infoTree(String id) {
        KnowledgeConceptVO knowledgeConceptVO = BeanUtil.copyProperties(knowledgeConceptRepository.findById(id), KnowledgeConceptVO.class);
        //构建父级树结构
        knowledgeConceptBuilder.buildTreeParents(knowledgeConceptVO);
        return knowledgeConceptVO;
    }


    /**
     * 查询单个概念（返回列表结构）
     *
     * @param id 概念id
     * @return 单个概念实体
     */
    @Override
    public KnowledgeConceptVO infoVO(String id) {
        KnowledgeConceptVO knowledgeConceptVO = BeanUtil.copyProperties(knowledgeConceptRepository.findById(id), KnowledgeConceptVO.class);
        List<KnowledgeModel> modelList = findModelByConceptId(List.of(id));
        //若当前概念被模型引用，添加被应用的模型信息
        if (ObjectUtil.isNotEmpty(modelList)) {
            knowledgeConceptVO.setQuote(true);
            knowledgeConceptVO.setModelList(modelList);
        }
        //构建父级列表结构
        knowledgeConceptBuilder.buildListParents(knowledgeConceptVO, new ArrayList<>(), new ArrayList<>(), new ArrayList<>());
        return knowledgeConceptVO;
    }

    @Override
    public KnowledgeConcept simpleInfo(String id) {
        return knowledgeConceptRepository.findById(id);
    }

    @Override
    public List<KnowledgeConceptVO> listTree() {
        //初始化顶级概念
//        initTopKnowledge();

        List<KnowledgeConceptVO> collect = knowledgeConceptRepository.findAll().stream()
                .map(concept ->
                        BeanUtil.copyProperties(concept, KnowledgeConceptVO.class))
                .sorted(Comparator.comparing(AbstractBaseEntity::getCreateTime).reversed())
                .collect(Collectors.toList());
        return ForestNodeMerger.merge(collect);
    }

    @Override
    public EchartsVO infoMap(String id) {
        EchartsVO echartsVO = new EchartsVO();
        HashSet<EchartsNode> echartsNodes = new HashSet<>();
        HashSet<EchartsRelation> echartsRelations = new HashSet<>();
        //中心节点
        KnowledgeConceptVO centerNode = infoTree(id);
        setConceptNodeEcharts(centerNode, echartsNodes, echartsRelations);
        //获取父级
        setConceptParentEcharts(centerNode, echartsNodes, echartsRelations);

        echartsVO.setNodes(echartsNodes);
        echartsVO.setRelations(echartsRelations);
        echartsVO.setNodeTotal(echartsNodes.size());
        return echartsVO;
    }


    private void setConceptNodeEcharts(KnowledgeConceptVO node, Set<EchartsNode> echartsNodes, Set<EchartsRelation> echartsRelations) {
        echartsNodes.add(new EchartsNode().setName(node.getName()).setType(EchartsConstant.CONCEPT).setId(node.getId()));
        //构造节点属性
        List<KnowledgePropertyVO> properties = node.getProperties();
        if (properties != null) {
            properties.forEach(property -> {
                echartsNodes.add(new EchartsNode().setName(property.getName()).setId(property.getId()).setType(EchartsConstant.PROPERTY));
                echartsRelations.add(new EchartsRelation().setName(EchartsConstant.PROPERTY).setSource(node.getId()).setTarget(property.getId()));
            });
        }

        //构造节点关系
        knowledgeRelationBuilder
                .buildRelations(node)
                .build()
                .forEach(relation -> {
                    if (node.getId().equals(relation.getConceptOne())) {
                        //主语
                        KnowledgeConcept endConcept = knowledgeConceptRepository.findById(relation.getConceptTwo());
                        echartsNodes.add(new EchartsNode().setId(endConcept.getId()).setType(EchartsConstant.CONCEPT).setName(endConcept.getName()));
//                        setConceptRelationEcharts(node, endConcept, relation, echartsRelations);
                    }
                    if (node.getId().equals(relation.getConceptTwo())) {
                        //宾语
                        KnowledgeConcept startConcept = knowledgeConceptRepository.findById(relation.getConceptOne());
                        echartsNodes.add(new EchartsNode().setId(startConcept.getId()).setType(EchartsConstant.CONCEPT).setName(startConcept.getName()));
//                        setConceptRelationEcharts(startConcept, node, relation, echartsRelations);
                    }
                });
    }

//    private void setConceptRelationEcharts(KnowledgeConcept sourceConcept, KnowledgeConcept endConcept, KnowledgeRelation relation, Set<EchartsRelation> echartsRelations) {
//        echartsRelations.add(new EchartsRelation().setName(relation.getPredicateName()).setSource(sourceConcept.getId()).setTarget(endConcept.getId()));
//    }

    private void setConceptParentEcharts(KnowledgeConceptVO concept, Set<EchartsNode> echartsNodes, Set<EchartsRelation> echartsRelations) {
        KnowledgeConceptVO parent = concept.getParent();
        if (parent == null) {
            return;
        }
        setConceptNodeEcharts(parent, echartsNodes, echartsRelations);
        echartsRelations.add(new EchartsRelation().setName(EchartsConstant.EXTENDS).setSource(concept.getId()).setTarget(parent.getId()));
        setConceptParentEcharts(concept.getParent(), echartsNodes, echartsRelations);
    }


    /**
     * 获取概念已经被绑定的属性
     *
     * @param conceptId 概念id
     * @return 属性ids
     */
    @Override
    public List<String> alreadyBindModelProperty(String conceptId) {
        return new ArrayList<>(knowledgeModelService.getAlreadyBindPropertyByConcept(conceptId));
    }

    @Override
    public List<ReferenceMapVO> conceptListByModelId(String modelId) {
        List<ReferenceMapVO> mainModel = new ArrayList<>();
        if (modelId.equals("0")) {
            list().forEach(knowledgeConcept -> {
                if (!"0".equals(knowledgeConcept.getParentId())) {
                    ReferenceMapVO referenceMapVO = new ReferenceMapVO();
                    referenceMapVO.setConcept(knowledgeConcept);
                    if (ObjectUtil.isNotEmpty(knowledgeConcept.getPropertyIds()))
                        referenceMapVO.setProperties(knowledgePropertyService.listByIds(knowledgeConcept.getPropertyIds()));
                    mainModel.add(referenceMapVO);
                }
            });
        } else {
            knowledgeModelService.info(modelId).getConcepts().forEach(s -> {
                ReferenceMapVO referenceMapVO = new ReferenceMapVO();
                referenceMapVO.setConcept(knowledgeConceptRepository.findById(s.getId()));
                referenceMapVO.setProperties(knowledgePropertyService.listByIds(s.getPropertyIds()));
                mainModel.add(referenceMapVO);
            });
        }
        return mainModel;
    }


    @Override
    public List<KnowledgeConcept> list() {
        return knowledgeConceptRepository.findAll();
    }

    /**
     * 根据Id列表获取多个概念
     *
     * @param ids id列表
     * @return {@link KnowledgeConcept}
     */
    @Override
    public List<KnowledgeConcept> findByIds(List<String> ids) {
        return knowledgeConceptRepository.findById(ids);
    }

    /**
     * 根据Id查询
     *
     * @param id 概念Id
     * @return {@link KnowledgeConcept}
     */
    @Override
    public KnowledgeConcept info(String id) {
        return knowledgeConceptRepository.findById(id);
    }

    /**
     * 查询当前概念下的所有子概念id,
     *
     * @param parentConceptId 当前概念id
     * @return ids(不包含当前id)
     */
    @Override
    public Set<String> getAllChildConceptIds(String parentConceptId, Set<String> conceptIds) {
        Bson bson = Filters.eq("parentId", parentConceptId);
        List<KnowledgeConcept> conceptList = knowledgeConceptRepository.findByCondition(bson);
        conceptList.forEach(knowledgeConcept -> {
            Set<String> childConceptIds = getAllChildConceptIds(knowledgeConcept.getId(), conceptIds);
            childConceptIds = ObjectUtil.isEmpty(childConceptIds) ? new HashSet<>() : childConceptIds;
            childConceptIds.add(knowledgeConcept.getId());
            conceptIds.addAll(childConceptIds);
        });
        return conceptIds;
    }

    /**
     * 获取当前概念可用属性
     *
     * @param conceptId 关系Id
     * @return 可用属性列表
     */
    @Override
    public List<KnowledgeProperty> listAvailableProperty(String conceptId) {
        KnowledgeConcept knowledgeConcept = knowledgeConceptRepository.findById(conceptId);
        List<KnowledgeProperty> propertyList = knowledgePropertyService.list();
        return propertyList.stream()
                .filter(knowledgeProperty -> !knowledgeConcept.getPropertyIds().contains(knowledgeProperty.getId()))
                .collect(Collectors.toList());
    }

    /**
     * 概念引用属性
     *
     * @param id  概念Id
     * @param ids 属性Id列表
     * @return {@link KnowledgeConcept}
     */
    @Override
    public KnowledgeConcept bind(String id, List<String> ids) {
        conceptCheck(List.of(id));
        KnowledgeConcept concept = knowledgeConceptRepository.findById(id);
        if (concept.getPropertyIds().stream().anyMatch(ids::contains)) {
            throw new ServiceException("操作失败，重复添加属性！");
        }
        concept.getPropertyIds().addAll(ids);
        return knowledgeConceptRepository.findById(knowledgeConceptRepository.save(concept));
    }

    /**
     * 判断概念是否被模型引用
     *
     * @param ids 概念Id列表
     */
    public void conceptCheck(List<String> ids) {
        Bson bson = Filters.elemMatch("concepts", Filters.in("_id", ids));
        List<KnowledgeModel> models = knowledgeModelRepository.findByCondition(bson);
        if (ObjectUtil.isNotEmpty(models))
            throw new ServiceException(ModelExceptionConstant.CONCEPT_ALREADY_BIND);
    }


    /**
     * 取消概念属性引用
     *
     * @param id  概念Id
     * @param ids 属性Id
     */
    @Override
    public void removeRelationProperty(String id, List<String> ids) {
        conceptCheck(List.of(id));
        KnowledgeConcept knowledgeConcept = knowledgeConceptRepository.findById(id);
        List<String> propertyIds = knowledgeConcept.getPropertyIds();
        propertyIds.removeAll(ids);
        knowledgeConceptRepository.save(knowledgeConcept);
    }

    /**
     * 通过概念名称获取概念对象
     *
     * @param name 概念名称
     * @return {@link KnowledgeConcept}
     */
    @Override
    public KnowledgeConcept findByConceptName(String name) {
        List<KnowledgeConcept> conceptList = knowledgeConceptRepository.findByCondition(Filters.eq("name", name));
        return ObjectUtil.isNotEmpty(conceptList) ? conceptList.get(0) : null;
    }

    @Override
    public List<KnowledgeConcept> findAllDelete() {
        return knowledgeConceptRepository.findAllDelete();
    }
}
