package org.irm.lab.kg.rabbitmq.consumer;

import com.mongodb.client.model.Filters;
import com.rabbitmq.client.Channel;

import cn.hutool.core.map.TableMap;
import cn.hutool.core.util.ObjectUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;


import org.irm.lab.common.utils.FileUtil;
import org.irm.lab.common.utils.ThreadLocalUtil;
import org.irm.lab.kg.algorithm.DocumentUnit;
import org.irm.lab.kg.entity.annex.AnnexDocumentUnit;
import org.irm.lab.kg.rabbitmq.message.DocumentParsingMessage;

import org.irm.lab.kg.rabbitmq.ocrFrontLoaded.OcrFrontLoadedProducer;
import org.irm.lab.kg.service.IDocumentParsingService;
import org.irm.lab.kg.service.IDocumentUnitService;
import org.irm.lab.kg.service.IKgResourceService;
import org.irm.lab.kg.service.annex.IAnnexDocumentUnitService;
import org.irm.lab.kg.service.annex.impl.AnnexDocumentParsingServiceImpl;
import org.irm.lab.kg.service.impl.WordToPdfServiceImpl;
import org.irm.lab.kg.strategy.annexOcr.context.AnnexOcrStrategyContext;
import org.irm.lab.kg.strategy.ocr.OcrStrategy;
import org.irm.lab.kg.strategy.ocr.context.OcrStrategyContext;
import org.irm.lab.repository.constant.DocumentResolvedStatus;
import org.irm.lab.repository.entity.Resource;
import org.irm.lab.repository.entity.ResourceAnnex;
import org.irm.lab.repository.feign.ResourceAnnexFeign;
import org.irm.lab.repository.repository.ResourceAnnexRepository;
import org.irm.lab.repository.repository.ResourceRepository;
import org.irm.lab.resource.entity.Attach;
import org.irm.lab.resource.feign.IOssEndPoint;
import org.springframework.amqp.core.Message;
import org.springframework.amqp.rabbit.annotation.*;
import org.springframework.mock.web.MockMultipartFile;
import org.springframework.stereotype.Component;
import org.springframework.web.multipart.MultipartFile;

import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.util.Arrays;
import java.util.Collection;
import java.util.HashMap;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2023/3/8 18:08
 * @description 文档解析消费者
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class DocumentParsingConsumer {

    private final OcrStrategyContext ocrStrategyContext;
    private final AnnexOcrStrategyContext annexOcrStrategyContext;
    private final IDocumentParsingService iDocumentParsingService;
    private final ResourceRepository resourceRepository;
    private final ResourceAnnexRepository resourceAnnexRepository;
    private final IAnnexDocumentUnitService iAnnexDocumentUnitService;
    private final IDocumentUnitService documentUnitService;
    private final IKgResourceService kgResourceService;
    private final OcrFrontLoadedProducer ocrFrontLoadedProducer;

    public static final String[] NOT_SYNC_FILE = {"办理单", "办理过程", "稿纸", "痕迹", "发文单"};

    /**
     * 消息监听
     */
    @RabbitListener(bindings = @QueueBinding(
            value = @Queue(DocumentParsingMessage.QUEUE + "${spring.application.name}"),
            exchange = @Exchange(name = DocumentParsingMessage.EXCHANGE),
            key = DocumentParsingMessage.ROUTING_KEY + "${spring.application.name}"
    ),concurrency = "${ocr}")
    @RabbitHandler
    public void onMessage(DocumentParsingMessage message, Channel channel, Message messageStatus) throws IOException {
        // 获取消息状态信息
        final HashMap<String, Boolean> stringBooleanHashMap = new HashMap<>();
        long deliveryTag = messageStatus.getMessageProperties().getDeliveryTag();
        log.info("[onMessage][线程编号:{} 消息内容：{}]", Thread.currentThread().getId(), message);
        Resource resource = null;
        try {
            // 设置用户信息，防止租户问题
            ThreadLocalUtil.set("user", message.getUser());
            // 发送消息开启文档解析
            //iDocumentParsingService.process(message.getResourceId());

            iDocumentParsingService.getResource(message.getResourceId());
            final String resourceId = message.getResourceId();

            resource = resourceRepository.findOne(resourceId);
            if (FileUtil.isSpecialFile(resource.getMediaType())) {
                log.info("【{}】 非文书类资源，无需解析 ", resource.getName());
                resource.setResolveStatus(DocumentResolvedStatus.RESOLVED);
                return;
            }
            if (FileUtil.isjingque(resource.getMediaType())) {
                log.info("【{}】 后缀为「{}」，直接解析失败 ", resource.getName(), resource.getMediaType());
                resource.setResolveStatus(DocumentResolvedStatus.RESOLVED_FAILED);
            }

            if (ObjectUtil.isEmpty(resource)) return;

            // 判断文件的类型
            final String fileType = resource.getMediaType();
            List<DocumentUnit> unitList = documentUnitService.infoByResource(resourceId);
            List<String> unitIdList = unitList.stream().map(DocumentUnit::getId).collect(Collectors.toList());
            documentUnitService.remove(unitIdList);
            final String classifyByResourceOrAnnexId = kgResourceService.getClassifyByResourceOrAnnexId(resourceId);
            try {
                if ("docx".equalsIgnoreCase(fileType) || "doc".equalsIgnoreCase(fileType)) {

                    if (ObjectUtil.equals("规章制度", classifyByResourceOrAnnexId)) {
                        // 段落拆分
                        final boolean b = ocrStrategyContext.processOcr("5", resource);
                        stringBooleanHashMap.put(resourceId, b);
                    }
                    // 整页放一起
                    else if (ObjectUtil.equals("奖章荣誉", classifyByResourceOrAnnexId)) {
                        final boolean b = ocrStrategyContext.processOcr("1", resource);
                        stringBooleanHashMap.put(resourceId, b);
                    }
                    // 整页放一起 数科pdf拆分
                    else if (ObjectUtil.equals("任职任免", classifyByResourceOrAnnexId)) {
                        final boolean b = ocrStrategyContext.processOcr("20", resource);
                        stringBooleanHashMap.put(resourceId, b);
                    }else if (ObjectUtil.equals("会议纪要", classifyByResourceOrAnnexId)){
                        // 整页放一起
                        final boolean b = ocrStrategyContext.processOcr("20", resource);
                        stringBooleanHashMap.put(resourceId, b);
                    }else if (ObjectUtil.equals("领导批示", classifyByResourceOrAnnexId)) {
                        //// 整页放一起 数科pdf拆分
                        final boolean b = ocrStrategyContext.processOcr("20", resource);
                        stringBooleanHashMap.put(resourceId, b);
                    }
                    else {
                        final boolean b = ocrStrategyContext.processOcr("8", resource);
                        stringBooleanHashMap.put(resourceId, b);
                    }
                } else {
                    log.info("开始解析pdf文件");
                    if (ObjectUtil.equals("规章制度", classifyByResourceOrAnnexId)) {
                        // 正常拆分段落
                        final boolean b = ocrStrategyContext.processOcr("6", resource);
                        stringBooleanHashMap.put(resourceId, b);
                    }else if (ObjectUtil.equals("奖章荣誉", classifyByResourceOrAnnexId)) {
                        // java拆分放一起
                        final boolean b = ocrStrategyContext.processOcr("2", resource);
                        stringBooleanHashMap.put(resourceId, b);
                    }else if (ObjectUtil.equals("任职任免", classifyByResourceOrAnnexId)) {
                        final boolean b = ocrStrategyContext.processOcr("20", resource);
                        stringBooleanHashMap.put(resourceId, b);
                    }else if (ObjectUtil.equals("会议纪要", classifyByResourceOrAnnexId)){
                        // java拆分放一起
                        final boolean b = ocrStrategyContext.processOcr("20", resource);
                        stringBooleanHashMap.put(resourceId, b);
                    }else if (ObjectUtil.equals("领导批示", classifyByResourceOrAnnexId)) {
                        //// 整页放一起 数科pdf拆分
                        final boolean b = ocrStrategyContext.processOcr("20", resource);
                        stringBooleanHashMap.put(resourceId, b);
                    }else {
                        //// 整页放一起 数科pdf拆分
                        final boolean b = ocrStrategyContext.processOcr("8", resource);
                        stringBooleanHashMap.put(resourceId, b);
                    }
                }
            } catch (Exception e) {
                // 设置为失败
                log.info("主文件解析失败，准备执行ocr算法重新解析当前文件的id为：{}", resourceId);
                log.error("主文件解析失败{}", e.toString());
                stringBooleanHashMap.put(resourceId, false);

            }

            final List<ResourceAnnex> resourceId1 = resourceAnnexRepository.findByCondition(Filters.eq("resourceId", resourceId));
            //R<List<ResourceAnnex>> resourceAnnexListR = resourceAnnexFeign.listByResourceId(resource.getId());
            //final List<ResourceAnnex> data1 = resourceAnnexListR.getData();
            if (ObjectUtil.isNotEmpty(resourceId1)) {

                // 处理附件逻辑
                // 循环解析附件
                log.info(">>>>>>>>>>开始解析附件>>>>>>>>>>");
                // 资源解析成功后，开始解析附件

                for (ResourceAnnex resourceAnnex : resourceId1) {
                    final String id = resourceAnnex.getId();
                    try {

                        // 删除该附件的所有语料单元
                        List<AnnexDocumentUnit> annexUnitList = iAnnexDocumentUnitService.findByAnnexId(id);
                        List<String> annexUnitIdList = annexUnitList.stream().map(AnnexDocumentUnit::getId).collect(Collectors.toList());
                        iAnnexDocumentUnitService.remove(annexUnitIdList);
                        final String mediaType = resourceAnnex.getMediaType();

                        if (FileUtil.isSpecialFile(resourceAnnex.getMediaType())) {
                            log.info("【{}】 非文书类资源，无需解析 ", resourceAnnex.getName());
                            resourceAnnex.setResolveStatus(DocumentResolvedStatus.RESOLVED);
                            resourceAnnexRepository.save(resourceAnnex);
                            continue;
                        }
                        final String fileName = getFileNameBeforeFirstDot(resourceAnnex.getName());


                        final boolean b = annexOcrStrategyContext.processOcr("9", resourceAnnex);
                        stringBooleanHashMap.put(id, b);

                    /*if ("docx".equalsIgnoreCase(mediaType) || "doc".equalsIgnoreCase(mediaType)) {
                        final boolean b;
                        if (fileName.endsWith("名单")) {
                            b = annexOcrStrategyContext.processOcr("15", resourceAnnex);
                        }else {
                            b = annexOcrStrategyContext.processOcr("3", resourceAnnex);
                        }
                        stringBooleanHashMap.put(id, b);
                    } else {
                        final boolean b;
                        if (fileName.endsWith("名单")) {
                            b = annexOcrStrategyContext.processOcr("14", resourceAnnex);
                        }else {
                            b = annexOcrStrategyContext.processOcr("4", resourceAnnex);
                        }
                        stringBooleanHashMap.put(id, b);

                    }*/
                    } catch (Exception e) {
                        log.info("附件解析失败，开始执行ocr算法重新解析");
                        // 设置为失败
                        stringBooleanHashMap.put(id, false);
                    }
                }

                final Collection<Boolean> values = stringBooleanHashMap.values();
                if (values.contains(false)) {
                    log.info("解析失败修改文件");
                    TableMap<String, Boolean> tableMap = new TableMap<>();
                    tableMap.putAll(stringBooleanHashMap);

                    final List<String> keys = tableMap.getKeys(false);

                    log.error("解析失败的文件id为{}", keys);

                    //ocrFrontLoadedProducer.send(resourceId,"ocr", message.getUser());
                    resource.setResolveStatus(DocumentResolvedStatus.RESOLVED_FAILED);
                    resourceId1.forEach(resourceAnnex -> resourceAnnex.setResolveStatus(DocumentResolvedStatus.RESOLVED_FAILED));
                    resourceAnnexRepository.saveAll(resourceId1);
                    //idocumentParsingService.reParsingWithOCR(resourceId);// 重新ocr

                    // 此时为主文件失败的处理逻辑
                }

            }

            // 先处理主文件  主文件为false 则直接 调用主文件重新ocr逻辑，而且isEnd都是false
            // 这些逻辑都要先清除语料

            // 处理副文件  副文件为false 则直接调用副文件重新ocr逻辑，而且isEnd都是false
        } catch (Exception e) {
            if (messageStatus.getMessageProperties().getRedelivered()) {
                e.printStackTrace();
                log.error("消息已重复处理失败，拒绝再次接收.....{}", e.toString());
                channel.basicReject(deliveryTag, false);
            } else {
                log.info("消息即将再次返回队列中进行处理....");
                channel.basicNack(deliveryTag, false, true);
            }
        } finally {
            assert resource != null;
            resourceRepository.save(resource);
        }
    }
    private final IOssEndPoint iOssEndPoint;

    public String getFileNameBeforeFirstDot(String fileName) {

        int dotIndex = fileName.indexOf('.');
        if (dotIndex != -1) { // 检查是否找到了点（防止 indexOf 返回 -1）
            return fileName.substring(0, dotIndex);
        }
        return fileName; // 如果没有点，返回整个文件名
    }

    public static MultipartFile convert(ByteArrayOutputStream byteArrayOutputStream, String fileName) throws IOException {
        // 获取 ByteArrayOutputStream 中的字节数组
        byte[] content = byteArrayOutputStream.toByteArray();

        // 使用 MockMultipartFile 构造函数创建 MultipartFile 对象
        return new MockMultipartFile(fileName, fileName, "application/pdf", content);
    }
}
