package org.irm.lab.kg.strategy.ocr.impl;

import java.io.InputStream;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Locale;
import java.util.Map;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

import org.dromara.streamquery.stream.core.stream.Steam;
import org.irm.lab.common.utils.NetWorkFileUtil;
import org.irm.lab.common.utils.ThreadLocalUtil;
import org.irm.lab.kg.algorithm.DocumentUnit;
import org.irm.lab.kg.algorithm.PDFPreciseProcessor;
import org.irm.lab.kg.rabbitmq.producer.EsSyncProducer;
import org.irm.lab.kg.repository.DocumentUnitRepository;
import org.irm.lab.kg.utils.SplitterUtil;
import org.irm.lab.repository.entity.Resource;
import org.irm.lab.repository.repository.ResourceAnnexRepository;
import org.irm.lab.repository.repository.ResourceRepository;
import org.irm.lab.resource.entity.Attach;
import org.irm.lab.resource.feign.AttachFeign;
import org.irm.lab.resource.feign.IOssEndPoint;
import org.irm.lab.resource.repository.AttachRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.aspose.pdf.TextAbsorber;
import com.mongodb.client.model.Filters;

import cn.hutool.core.util.ObjectUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR> <br/>
 * @date 2024/5/28 <br/>
 * @Copyright 博客：https://eliauku.gitee.io/  ||  per aspera and astra <br/>
 */
@Service("zdresourceOcrPdfStrategy")
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
@Slf4j
public class ZDResourceOcrPdfStrategy extends AbstractFileOcrStrategyImpl{
    private final ResourceRepository resourceRepository;
    private final AttachRepository attachRepository;
    private final EsSyncProducer esSyncProducer;
    private final DocumentUnitRepository documentUnitRepository;
    @Override
    Map<Integer, Map<Integer, String>> getOcrResult(String strategy, Resource message) {
        Map<Integer, Map<Integer, String>> resourceText;
        final Attach attach = attachRepository.findById(message.getPdfAttachId());

        try (InputStream inputStream = NetWorkFileUtil.urlToInputStream(attach.getLink());) {

            log.info("开始对主文件进行pdf抽取");

            resourceText = processPDFDocument(inputStream, message);

        } catch (Exception e) {
            throw new RuntimeException(e);
        }

        return resourceText;
    }

    @Override
    void saveOcrResult(String strategy, Resource message, Map<Integer, Map<Integer, String>> ocrResult) {
        List<DocumentUnit> units = new ArrayList<>();

        Steam.of(ocrResult).forEach(data -> {
            final Map<Integer, String> value = data.getValue();
            value.forEach((key, value1) -> {
                        DocumentUnit documentUnit = new DocumentUnit();
                        documentUnit.setResourceId(message.getId());
                        documentUnit.setContent(value1);
                        documentUnit.setSortInCurrentPage(key);
                        documentUnit.setPage(data.getKey());
                        documentUnit.setType("0");
                        units.add(documentUnit);
                    }
            );
        });// 9900    17:01
        // 一小时1000个

        documentUnitRepository.saveAll(units);
        List<DocumentUnit> documentUnitList = documentUnitRepository.findByCondition(Filters.eq("resourceId", message.getId()));
        if (ObjectUtil.isNotEmpty(documentUnitList)) {
            // 合并内容
            StringBuffer stringBuffer = new StringBuffer();
            documentUnitList.forEach(documentUnit -> stringBuffer.append(documentUnit.getContent()));
            esSyncProducer.sendMessage(ThreadLocalUtil.get("user"), message, null, stringBuffer.toString(), "主文件");
            log.info("【主文件】{} 同步到ES中", message.getName());
        }
        resourceRepository.save(message);
    }


    public Map<Integer, Map<Integer, String>> processPDFDocument(InputStream inputStream, Resource resource) throws Exception {
        final Locale originalLocale = Locale.getDefault();
        Locale.setDefault(new Locale("zh", "cn"));
        Map<Integer, Map<Integer, String>> result = new HashMap<>();

        com.aspose.pdf.Document pdfDoc = new com.aspose.pdf.Document(inputStream);
        Map<Integer, String> pagesText = new HashMap<>();
        int totalPages = pdfDoc.getPages().size();

        try {
            for (int pageNum = 1; pageNum <= totalPages; pageNum++) {
                TextAbsorber textAbsorber = new TextAbsorber();
                pdfDoc.getPages().get_Item(pageNum).accept(textAbsorber);
                String pageText = textAbsorber.getText();

                pagesText.put(pageNum, SplitterUtil.secondaryProcessing(pageText));
                logger.info("当前资源名称为{}, 解析到当前资源的第{}页", resource.getName(), pageNum);
            }
        } finally {
            pdfDoc.close();
            Locale.setDefault(originalLocale); // 恢复原始的Locale设置
        }
// 遍历 textByPages，使用正则表达式匹配指定格式字符串之前的文本，并移动到前一页
        totalPages = pagesText.size();
        Pattern pattern = Pattern.compile("第(\\S+?)[条章]");
        for (int i = 2; i < totalPages; i++) {
            String currentPageText = pagesText.get(i);
            Matcher matcher = pattern.matcher(currentPageText);
            if (matcher.find()) {
                int index = matcher.start();
                // 找到指定格式字符串，将其之前的内容移动到前一页
                // 更新前一页的文本
                pagesText.put(i - 1, pagesText.get(i - 1) + currentPageText.substring(0, index)
                        // 更新前一页的文本
                );
                // 更新当前页的文本，删除已移动的内容
                currentPageText = currentPageText.substring(index);
                pagesText.put(i, currentPageText);
            }
        }
        // Pattern to identify "第x章" or "第x条"
        Pattern pattern1 = Pattern.compile("第(\\S+?)章|第(\\S+?)条");

        // Iterate over the pages to split and merge text based on the pattern
        for (int i = 1; i < pagesText.size(); i++) {
            String currentPageText = pagesText.get(i);
            Matcher matcher = pattern1.matcher(currentPageText);
            int previousMatchEnd = 0;
            int corpusIndex = 1;
            Map<Integer, String> currentPageCorpora = new HashMap<>();

            while (matcher.find()) {
                int matchStart = matcher.start();
                // Append text from previous match to this match as a separate corpus
                if (matchStart > previousMatchEnd) {
                    String corpus = currentPageText.substring(previousMatchEnd, matchStart);
                    currentPageCorpora.put(corpusIndex++, corpus);
                }
                previousMatchEnd = matchStart;
            }

            // Append the remaining text after the last match as another corpus
            if (previousMatchEnd < currentPageText.length()) {
                String remainingText = currentPageText.substring(previousMatchEnd);
                currentPageCorpora.put(corpusIndex, remainingText);
            }

            // Save corpora to result
            result.put(i, currentPageCorpora);
        }


        return result;

    }
}
