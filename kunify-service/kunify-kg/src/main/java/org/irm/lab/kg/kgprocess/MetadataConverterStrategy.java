package org.irm.lab.kg.kgprocess;

import com.mongodb.client.model.Filters;
import lombok.extern.slf4j.Slf4j;
import org.bson.conversions.Bson;
import org.irm.lab.config.entity.Process;
import org.irm.lab.kg.entity.processing.CacheLabelData;
import org.irm.lab.kg.enums.KgProcessEnum;
import org.irm.lab.kg.kgprocess.strategy.ProcessingStrategy;
import org.irm.lab.kg.rabbitmq.producer.MetadataConverterProducer;
import org.irm.lab.kg.repository.processing.CacheLabelDataRepository;
import org.irm.lab.kg.service.impl.kgprocess.CommonKgProcess;
import org.irm.lab.repository.entity.Resource;
import org.irm.lab.repository.feign.ResourceFeign;
import org.springframework.stereotype.Component;

import javax.servlet.http.HttpServletRequest;
import java.util.List;


/**
 * <AUTHOR>
 * @date 2023/4/25 18:28
 * @description 元数据转化
 */
@Slf4j
@Component
public class MetadataConverterStrategy extends CommonKgProcess implements ProcessingStrategy {
    private final static String CODE = KgProcessEnum.METADATA_CONVERSION.getCode();
    private final static String NAME = KgProcessEnum.METADATA_CONVERSION.getName();

    @javax.annotation.Resource
    private CacheLabelDataRepository cacheLabelDataRepository;
    @javax.annotation.Resource
    private MetadataConverterProducer metadataConverterProducer;
    @javax.annotation.Resource
    private HttpServletRequest request;
    @javax.annotation.Resource
    private ResourceFeign resourceFeign;

    @Override
    public void process(Resource resource) {
        String resourceId = resource.getId();
        resource = resourceFeign.info(resourceId).getData();
        // 获取该资源的顶级top标签
        Bson and = Filters.and(Filters.eq("resourceId", resourceId), Filters.eq("topLabel", true));
        List<CacheLabelData> topCacheLabelDataList = cacheLabelDataRepository.findByCondition(and);
        if (topCacheLabelDataList == null || topCacheLabelDataList.size() == 0) {
            log.error("该资源不存在顶级标签，跳过元数据转化");
            return;
        }
        CacheLabelData topCacheLabelData = topCacheLabelDataList.get(0);
        Process process = getProcess(resource);
        // 更新资源对象的 元数据转化字段为 是
        resource.setMetadataConverter(true);
        updateResource(resource);
        // 元数据转化
        metadataConverterProducer.sendMessage(request.getHeader("user"), topCacheLabelData.getId(), process.getId(), resourceId);
    }

    @Override
    public String getCode() {
        return CODE;
    }

    @Override
    public String getName() {
        return NAME;
    }
}
