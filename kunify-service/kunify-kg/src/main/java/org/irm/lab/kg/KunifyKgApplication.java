package org.irm.lab.kg;

import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import javax.annotation.Resource;

import org.irm.lab.common.constant.AppConstant;
import org.irm.lab.common.support.Condition;
import org.irm.lab.common.utils.SpringUtil;
import org.irm.lab.common.utils.ThreadLocalUtil;
import org.irm.lab.kg.algorithm.DocumentUnit;
import org.irm.lab.kg.rabbitmq.producer.EsSyncProducer;
import org.irm.lab.kg.repository.DocumentUnitRepository;
import org.irm.lab.kg.service.IDocumentUnitService;
import org.irm.lab.repository.repository.ResourceAnnexRepository;
import org.irm.lab.repository.repository.ResourceRepository;
import org.irm.lab.resource.repository.AttachRepository;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.boot.CommandLineRunner;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.autoconfigure.data.jpa.JpaRepositoriesAutoConfiguration;
import org.springframework.boot.autoconfigure.jdbc.DataSourceAutoConfiguration;
import org.springframework.cloud.client.discovery.EnableDiscoveryClient;
import org.springframework.cloud.openfeign.EnableFeignClients;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.EnableAspectJAutoProxy;
import org.springframework.context.annotation.Import;
import org.springframework.scheduling.annotation.EnableAsync;

import com.alibaba.cloud.nacos.NacosConfigManager;
import com.alibaba.cloud.nacos.NacosConfigProperties;
import com.alibaba.nacos.api.config.ConfigService;
import com.alibaba.nacos.api.exception.NacosException;

@SpringBootApplication(scanBasePackages = AppConstant.BASE_PACKAGES, exclude = {JpaRepositoriesAutoConfiguration.class,
        DataSourceAutoConfiguration.class})
@EnableDiscoveryClient
@EnableAsync
@EnableFeignClients(basePackages = AppConstant.BASE_PACKAGES)
@Import(SpringUtil.class)
@EnableAspectJAutoProxy
public class KunifyKgApplication implements CommandLineRunner {

    @Resource
    private NacosConfigManager nacosConfigManager;

    @Resource
    private NacosConfigProperties nacosConfigProperties;
    private static final Logger log = LoggerFactory.getLogger(KunifyKgApplication.class);
    @Resource
    private AttachRepository attachRepository;
    @Resource
    private ResourceAnnexRepository resourceAnnexRepository;
    @Resource
    private ResourceRepository resourceRepository;
    @Resource
    private DocumentUnitRepository documentUnitRepository;

    @Resource
    private  IDocumentUnitService documentUnitService;
    @Resource
    private EsSyncProducer esSyncProducer;

    public static void main(String[] args) {
        SpringApplication.run(KunifyKgApplication.class, args);
    }

    @Override
    public void run(String... args) throws Exception {
        System.out.println("Printing remote Nacos configuration:");
        printRemoteNacosConfig();
    }

    private void printRemoteNacosConfig() throws NacosException {
        ConfigService configService = nacosConfigManager.getConfigService();
        String dataId = nacosConfigProperties.getName();
        String group = nacosConfigProperties.getGroup();
        String config = configService.getConfig(dataId, group, 5000);

        if (config != null) {
            System.out.println("=============配置加载成功=============");
            System.out.println(config);
        } else {
            System.out.println("=============配置加载失败=============");
        }
    }

    /*@Bean
    public CommandLineRunner commandLineRunner() {
        return args -> {
            final List<org.irm.lab.repository.entity.Resource> workTaskId = resourceRepository.findByCondition(Condition.getFilter(Map.of("workTaskId", "6675318d2da0f33cf10f2aa9"),org.irm.lab.repository.entity.Resource.class));
            final List<org.irm.lab.repository.entity.Resource> workTaskId2 = resourceRepository.findByCondition(Condition.getFilter(Map.of("workTaskId", "667531d52da0f33cf10f2aaa"),org.irm.lab.repository.entity.Resource.class));
            workTaskId.addAll(workTaskId2);

            final List<String> collect = workTaskId.stream().map(org.irm.lab.repository.entity.Resource::getId).collect(Collectors.toList());
            final ArrayList<String> strings = new ArrayList<>();
            collect.stream().forEach(data -> {
                final List<DocumentUnit> byId = documentUnitRepository.findByCondition(Condition.getFilter(Map.of("resourceId", data),DocumentUnit.class));
                byId.stream().map(DocumentUnit::getId).forEach(strings::add);

            });
            final List<DocumentUnit> byId = documentUnitRepository.findById(strings);

            Map<String, List<DocumentUnit>> sortedGroupedMap = byId.stream()
                    .collect(Collectors.groupingBy(
                            DocumentUnit::getResourceId,
                            Collectors.collectingAndThen(
                                    Collectors.toList(),
                                    list -> {
                                        list.sort(Comparator.comparing(DocumentUnit::getPage));
                                        return list;
                                    }
                            )
                    ));
            sortedGroupedMap.forEach((resourceId, documentUnits) -> {

                final StringBuffer stringBuffer = new StringBuffer();
                documentUnits.forEach(data -> {

                    stringBuffer.append(data.getContent());
                });
                DocumentUnit documentUnit = new DocumentUnit();
                documentUnit.setResourceId(resourceId);

                documentUnit.setSortInCurrentPage(1);
                documentUnit.setPage(1);
                documentUnit.setType("0");
                documentUnit.setContent(stringBuffer.toString());

                log.info("【主文件】{} 开始删除", documentUnits);
                documentUnitService.remove(documentUnits.stream().map(DocumentUnit::getId).collect(Collectors.toList()));
                documentUnitRepository.save(documentUnit);
                esSyncProducer.sendMessage(ThreadLocalUtil.get("user"), resourceRepository.findById(resourceId), null, stringBuffer.toString(), "主文件");

            })
            ;;
        };
    }*/
}
