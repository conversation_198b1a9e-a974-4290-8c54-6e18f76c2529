package org.irm.lab.kg.service;

import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONObject;
import org.irm.lab.kg.dto.AlignmentDTO;
import org.irm.lab.kg.vo.echarts.EchartsVO;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/3/21 11:12
 * @description 知识对齐业务接口
 */
public interface IKnowledgeAlignmentService {
    /**
     * 知识可视化
     *
     * @param resourceId 资源Id
     * @return {@link EchartsVO}
     */
    EchartsVO visual(String resourceId, String name, String showCount);


    /**
     * 知识对齐
     *
     * @param alignmentDTO {@link AlignmentDTO}
     */
    void alignmentPlus(AlignmentDTO alignmentDTO);

    /**
     * 一键知识对齐
     *
     * @param alignmentDTOs {@link AlignmentDTO}
     */
    void alignmentBatch(List<AlignmentDTO> alignmentDTOs);

    /**
     * 知识对齐树结构
     *
     * @param resourceId 资源Id
     * @return 知识对齐树
     */
    JSONArray alignmentTree(String resourceId, String modelId);

    JSONArray alignmentTreeStep1(String resourceId, String modelId);

    JSONObject alignmentTreeStep2(String entityId, String conceptId, String entityName, String modelId);

    /**
     * 知识对齐
     *
     * @param alignmentDTO {@link AlignmentDTO}
     */
    void alignment(AlignmentDTO alignmentDTO);

    /**
     * 一键知识对齐
     *
     * @param resourceId 资源Id
     */
    void passAlignment(String resourceId);

    void passAlignmentMulti(List<String> resourceIds);

    /**
     * 知识对齐回退
     * @param resourceId
     */
    void rollback(List<String> resourceId);
}
