package org.irm.lab.kg.service.annex.impl;

import cn.hutool.core.convert.Convert;
import cn.hutool.core.lang.Opt;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONObject;
import com.mongodb.client.model.Filters;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.bson.Document;
import org.bson.conversions.Bson;
import org.irm.lab.common.constant.AbstractBaseEntityFieldConstant;
import org.irm.lab.common.exception.ServiceException;
import org.irm.lab.config.entity.Label;
import org.irm.lab.config.repository.LabelRepository;
import org.irm.lab.kg.constant.LabelConstant;
import org.irm.lab.kg.entity.KnowledgeRelation;
import org.irm.lab.kg.entity.annex.processing.AnnexLabelData;
import org.irm.lab.kg.entity.annex.processing.AnnexTripleLabelData;
import org.irm.lab.kg.entity.neo4j.node.NodeEntity;
import org.irm.lab.kg.entity.neo4j.node.NodeRelation;
import org.irm.lab.kg.entity.processing.LabelPropertyVO;
import org.irm.lab.kg.repository.KnowledgeRelationRepository;
import org.irm.lab.kg.repository.annex.AnnexLabelDataRepository;
import org.irm.lab.kg.repository.annex.AnnexTripleLabelDataRepository;
import org.irm.lab.kg.repository.neo4j.node.NodeEntityRepository;
import org.irm.lab.kg.repository.neo4j.node.NodeRelationRepository;
import org.irm.lab.kg.service.IKnowledgeAlignmentService;
import org.irm.lab.kg.service.annex.IAnnexLabelGraphService;
import org.irm.lab.repository.constant.KnowledgeAlignmentStatus;
import org.irm.lab.repository.entity.ResourceAnnex;
import org.irm.lab.repository.feign.ResourceAnnexFeign;
import org.jetbrains.annotations.NotNull;
import org.neo4j.ogm.cypher.ComparisonOperator;
import org.neo4j.ogm.cypher.Filter;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;


/**
 * <AUTHOR>
 * @date 2023/5/10 17:47
 * @description 附件标签树结构业务实现类
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class AnnexLabelGraphServiceImpl implements IAnnexLabelGraphService {

    private final LabelRepository labelRepository;
    private final AnnexLabelDataRepository annexLabelDataRepository;
    private final AnnexTripleLabelDataRepository annexTripleLabelDataRepository;
    private final ResourceAnnexFeign resourceAnnexFeign;
    private final NodeEntityRepository nodeEntityRepository;
    private final NodeRelationRepository nodeRelationRepository;
    private final KnowledgeRelationRepository knowledgeRelationRepository;
    private final IKnowledgeAlignmentService iKnowledgeAlignmentService;

    /**
     * 知识匹配 ===> 获取标签数据树
     *
     * @param annexId   附件Id
     * @param dataSetId 数据集Id
     * @return 标签数据
     */
    @Override
    public JSONArray relevanceShow(String annexId, String dataSetId) {
        Map<String, List<JSONObject>> labelDatamap = entitySortByKeyValue(dataSetId);
        // 获取该资源的所有标签数据·
        List<AnnexLabelData> annexLabelDataList = annexLabelDataRepository.findByCondition(Filters.eq("annexId", annexId));
        // 遍历所以标签数据
        for (AnnexLabelData annexLabelData : annexLabelDataList) {
            // 创建数组存储关系
            List<JSONObject> relations = new ArrayList<>();
            // 把标签数据的属性放在和关系平级的位置
            // 给属性封装名称
            for (LabelPropertyVO labelProperty : annexLabelData.getLabelProperties()) {
                // 获取属性标签
                Label propertyLabel = Opt.ofNullable(labelRepository.findById(labelProperty.getLabelPropertyId())).orElseThrow(ServiceException::new, "该标签不存在!");
                // 为属性设置标签名称
                labelProperty.setLabelPropertyName(propertyLabel.getName());
                // 把属性封装为指定JSONObject
                JSONObject labelDataPropertyObject = new JSONObject(labelProperty);
                // 添加层级
                labelDataPropertyObject.putOpt("level", 3);
                // 把属性放在关系前面
                if (ObjectUtil.isNotEmpty(labelDataPropertyObject)) relations.add(labelDataPropertyObject);
            }
            // 获取每个数据标签 作为主语的 关系三元组
            List<AnnexTripleLabelData> annexTripleLabelDataList = annexTripleLabelDataRepository.findByConditionAndSorted(Filters.eq("startLabelDataId", annexLabelData.getId()), Filters.eq(AbstractBaseEntityFieldConstant.CREATE_TIME, -1));
            for (AnnexTripleLabelData annexTripleLabelData : annexTripleLabelDataList) {
                // 生成关系对象
                generateRelation(relations, annexTripleLabelData);
            }
            // 给标签数据的标签Id封装为labelName
            Label conceptLabel = Opt.ofNullable(labelRepository.findById(annexLabelData.getLabelId())).orElseThrow(ServiceException::new, "该标签不存在");
            annexLabelData.setLabelName(conceptLabel.getName());
            // 转换为JSONObject
            JSONObject labelDataJsonObject = new JSONObject(annexLabelData);
            // 添加 层级
            labelDataJsonObject.putOpt("level", 2);
            // 添加关系
            labelDataJsonObject.putOpt("children", relations);
            // 根据标签Id查询标签对象，并获取标签名称
            Label label = labelRepository.findById(annexLabelData.getLabelId());
            // 添加结果
            List<JSONObject> list = labelDatamap.getOrDefault(label.getName(), new ArrayList<>());
            list.add(labelDataJsonObject);
            labelDatamap.put(label.getName(), list);

        }
        // 封装标签数据
        JSONArray jsonArray = new JSONArray();
        Set<String> keySet = labelDatamap.keySet();
        for (String key : keySet) {
            JSONObject jsonObject = new JSONObject();
            // 获取标签ID
            String labelId = labelDatamap.get(key).get(0).getStr("labelId");
            jsonObject.putOpt("labelId", labelId);
            jsonObject.putOpt("labelName", key);
            jsonObject.putOpt("children", labelDatamap.get(key));
            jsonObject.putOpt("level", 1);
            jsonArray.add(jsonObject);
        }
        // 返回结果
        return jsonArray;
    }

    /**
     * 获取该资源下的所有标签数据
     *
     * @param annexId 资源Id
     * @return {@link AnnexLabelData}
     */
    @Override
    public List<AnnexLabelData> listLabelData(String annexId) {
        return annexLabelDataRepository.findByCondition(Filters.eq("annexId", annexId));
    }


    /**
     * 新增或修改附件标签数据对象
     *
     * @param annexLabelData {@link AnnexLabelData}
     */
    @Override
    public void saveOrUpdateLabelData(AnnexLabelData annexLabelData) {
        // 新增属性 和 修改属性
        if (ObjectUtil.isNotEmpty(annexLabelData.getLabelProperties())) {
            // 新增属性 没有 identifier
            List<LabelPropertyVO> labelProperties = annexLabelData.getLabelProperties();
            for (LabelPropertyVO labelProperty : labelProperties) {
                // 构建属性对象
                HashMap<String, Object> filterMap = new HashMap<>();
                filterMap.put("labelPropertyId", labelProperty.getLabelPropertyId());
                filterMap.put("value", labelProperty.getValue());
                // 如果不存在identifier 就是新增属性
                if (ObjectUtil.isEmpty(labelProperty.getIdentifier())) {
                    // 设置唯一标识
                    filterMap.put("identifier", IdUtil.simpleUUID());
                } else {
                    // 修改属性，先删除原来的属性
                    annexLabelDataRepository.pullDistinct(annexLabelData.getId(), Map.of("labelProperties", new Document("identifier", labelProperty.getIdentifier())));
                    // 设置原来的唯一标识
                    filterMap.put("identifier", labelProperty.getIdentifier());
                }
                // 添加新的属性
                annexLabelDataRepository.pushDistinct(annexLabelData.getId(), Map.of("labelProperties", new Document(filterMap)));
            }
            return;
        }
        // 如果属性为空，新增或修改实例
        if (ObjectUtil.isEmpty(annexLabelData.getLabelProperties())) {
            // 新增实例时,把属性字段置空
            annexLabelData.setLabelProperties(null);
        }
        // 新增或修改标签数据
        annexLabelDataRepository.save(annexLabelData);
    }


    /**
     * 删除实例/实例的属性
     *
     * @param annexLabelData 附件标签数据
     */
    @Override
    public void removeLabelData(AnnexLabelData annexLabelData) {
        // 获取标签数据ID
        String labelDataId = annexLabelData.getId();
        AnnexLabelData deleteAnnexLabelData = annexLabelDataRepository.findById(labelDataId);
        // 删除实例
        if (annexLabelData.getLabelProperties() == null || annexLabelData.getLabelProperties().isEmpty()) {
            if (deleteAnnexLabelData.isTopLabel()) {
                throw new ServiceException("顶级附件标签无法删除!");
            }
            // 删除标签数据
            annexLabelDataRepository.deleteByIdFake(labelDataId);
            // 删除该标签数据涉及的关系三元组
            Bson or = Filters.or(Filters.eq("startLabelDataId", labelDataId), Filters.eq("endLabelDataId", labelDataId));
            List<AnnexTripleLabelData> annexTripleLabelDataList = annexTripleLabelDataRepository.findByCondition(or);
            // 删除多个三元组
            if (ObjectUtil.isNotEmpty(annexTripleLabelDataList)) {
                List<String> tripleLabelDataId = annexTripleLabelDataList.stream().map(AnnexTripleLabelData::getId).collect(Collectors.toList());
                annexTripleLabelDataRepository.deleteByIdFake(tripleLabelDataId);
            }
        }
        // 删除属性
        if (ObjectUtil.isNotEmpty(annexLabelData.getLabelProperties())) {
            for (LabelPropertyVO labelProperty : annexLabelData.getLabelProperties()) {
                // 删除属性
                annexLabelDataRepository.pullDistinct(labelDataId, Map.of("labelProperties", new Document("identifier", labelProperty.getIdentifier())));
            }
        }
    }


    /**
     * 新增/修改附件关系三元组
     *
     * @param annexTripleLabelDataList {@link AnnexTripleLabelData}
     */
    @Override
    public void saveOrUpdateTripleLabelData(List<AnnexTripleLabelData> annexTripleLabelDataList) {
        for (AnnexTripleLabelData annexTripleLabelData : annexTripleLabelDataList) {
            // 新增或修改属性
            if (ObjectUtil.isNotEmpty(annexTripleLabelData.getRelationProperties())) {
                for (LabelPropertyVO relationProperty : annexTripleLabelData.getRelationProperties()) {
                    // 构建属性对象
                    Map<String, Object> map = new HashMap<>();
                    map.put("labelPropertyId", relationProperty.getLabelPropertyId());
                    map.put("value", relationProperty.getValue());
                    // 如果不存在identifier 就是新增属性
                    if (ObjectUtil.isEmpty(relationProperty.getIdentifier())) {
                        // 设置唯一标识
                        map.put("identifier", IdUtil.simpleUUID());
                    } else {
                        // 修改属性，先删除原来的属性
                        annexTripleLabelDataRepository.pullDistinct(annexTripleLabelData.getId(), Map.of("relationProperties", new Document("identifier", relationProperty.getIdentifier())));
                        // 设置原来的唯一标识
                        map.put("identifier", relationProperty.getIdentifier());
                    }
                    // 添加新的属性
                    annexTripleLabelDataRepository.pushDistinct(annexTripleLabelData.getId(), Map.of("relationProperties", new Document(map)));
                }
                return;
            }
            // 如果属性为空，就是新增或修改关系
            if (ObjectUtil.isEmpty(annexTripleLabelData.getRelationProperties())) {
                annexTripleLabelData.setRelationProperties(null);
            }
            // 新增或修改关系
            annexTripleLabelDataRepository.save(annexTripleLabelData);
        }
    }


    /**
     * 删除附件关系三元组/属性
     *
     * @param annexTripleLabelData 附件关系三元组
     */
    @Override
    public void removeTripleLabelData(AnnexTripleLabelData annexTripleLabelData) {
        // 获取关系三元组Id
        String tripleLabelDataId = annexTripleLabelData.getId();
        // 删除关系
        if (annexTripleLabelData.getRelationProperties() == null || annexTripleLabelData.getRelationProperties().isEmpty()) {
            annexTripleLabelDataRepository.deleteByIdFake(tripleLabelDataId);
        }
        // 删除属性
        if (ObjectUtil.isNotEmpty(annexTripleLabelData.getRelationProperties())) {
            for (LabelPropertyVO relationProperty : annexTripleLabelData.getRelationProperties()) {
                annexTripleLabelDataRepository.pullDistinct(tripleLabelDataId, Map.of("relationProperties", new Document("identifier", relationProperty.getIdentifier())));
            }
        }
    }

    /**
     * 结果排序TreeMap
     *
     * @param dataSetId 数据集Id
     */
    private Map<String, List<JSONObject>> entitySortByKeyValue(String dataSetId) {
        Map<String, Integer> labelSort = new HashMap<>();
        List<Label> labelList = labelRepository.findByCondition(new Document("dataSetId", dataSetId));
        labelList.forEach(m -> labelSort.put(m.getName(), m.getSort()));
        return new TreeMap<>(((o1, o2) -> {
            if (!(labelSort.containsKey(o1) && labelSort.containsKey(o2))) return 1;
            if (!Objects.equals(o1, o2)) {
                Integer o1Sort = labelSort.get(o1);
                Integer o2Sort = labelSort.get(o2);
                return Objects.equals(o1Sort, o2Sort) ? 0 : (o1Sort > o2Sort ? 1 : -1);
            }
            return 0;
        }));
    }


    /**
     * 关系生成
     *
     * @param relations            关系JSONObject
     * @param annexTripleLabelData 附件关系三元组
     */
    private void generateRelation(List<JSONObject> relations, AnnexTripleLabelData annexTripleLabelData) {
        // 创建关系对象
        JSONObject relationJsonObject = new JSONObject();
        // 添加关系三元组的Id
        relationJsonObject.putOpt("id", annexTripleLabelData.getId());
        // 如果关系有属性的话就添加
        if (ObjectUtil.isNotEmpty(annexTripleLabelData.getRelationProperties())) {
            JSONArray relationArray = new JSONArray();
            for (LabelPropertyVO relationProperty : annexTripleLabelData.getRelationProperties()) {
                JSONObject relationPropertyObject = new JSONObject(relationProperty);
                // 根据关系标签Id获取关系标签名称
                Label label = Opt.ofNullable(labelRepository.findById(relationProperty.getLabelPropertyId())).orElseThrow(ServiceException::new, "当前标签不存在!");
                relationPropertyObject.putOpt("labelName", label.getName());
                // 层级三
                relationPropertyObject.putOpt("level", 4);
                // 添加关系的属性
                relationArray.add(relationPropertyObject);
            }
            relationJsonObject.putOpt("children", relationArray);
        }

        // 谓词标签Id
        relationJsonObject.putOpt("relationLabelId", annexTripleLabelData.getRelationLabelId());
        // 谓词标签名称
        Label relationLabel = Optional.ofNullable(labelRepository.findById(annexTripleLabelData.getRelationLabelId()))
                .orElseThrow(() -> new ServiceException("当前标签不存在!"));
        relationJsonObject.putOpt("relationLabelName", relationLabel.getName());
        // 宾语的标签Id
        relationJsonObject.putOpt("labelId", annexTripleLabelData.getEndLabelId());
        // 宾语的标签名称
        Label label = Optional.ofNullable(labelRepository.findById(annexTripleLabelData.getEndLabelId()))
                .orElseThrow(() -> new ServiceException("当前标签不存在!"));
        relationJsonObject.putOpt("labelName", label.getName());
        // 宾语的标签数据Id
        relationJsonObject.putOpt("labelDataId", annexTripleLabelData.getEndLabelDataId());
        // 宾语的标签数据名称
        AnnexLabelData endLabelData = Optional.ofNullable(annexLabelDataRepository.findById(annexTripleLabelData.getEndLabelDataId()))
                .orElseThrow(() -> new ServiceException("当前标签数据不存在!"));
        relationJsonObject.putOpt("labelDataName", endLabelData.getContent());
        // 添加层级
        relationJsonObject.putOpt("level", 3);
        // 添加关系
        relations.add(relationJsonObject);
    }

    /**
     * 匹配确认
     *
     * @param resourceAnnex 附件对象
     * @param dataSetId     数据集Id
     * @param modelId       模型Id
     * @param autoAlignment 自动实体对齐
     */
    @Override
    public void matchConfirm(ResourceAnnex resourceAnnex, String dataSetId, String modelId, Boolean autoAlignment) {
        try {
            // 修改资源状态为匹配确认 为 true
            resourceAnnex.setKnowledgeMatchConfirm(true);
            // 修改资源状态为 “匹配确认中”
            resourceAnnex.setAlignmentStatus(KnowledgeAlignmentStatus.MATCH_CONFIRMING);
            resourceAnnexFeign.save(resourceAnnex);
            // 生成实例
            Map<String, NodeEntity> entityMap = generateNodeEntity(modelId, resourceAnnex, autoAlignment);
            // 建立所有实例间的关系
            generateRelation(resourceAnnex, entityMap);
            // 修改资源为 “待对齐”
            resourceAnnex.setAlignmentStatus(KnowledgeAlignmentStatus.UN_ALIGNMENT);
            resourceAnnexFeign.save(resourceAnnex);
            // 判断是否开启自动对齐
            if (autoAlignment) {
                // 自动实体对齐
                iKnowledgeAlignmentService.passAlignment(resourceAnnex.getResourceId());
            }
        } catch (Exception e) {
            // 匹配确认失败
            resourceAnnex.setAlignmentStatus(KnowledgeAlignmentStatus.MATCH_CONFIRM_FAILED);
            resourceAnnexFeign.save(resourceAnnex);
            // 打印错误信息
            log.error(e.getMessage());
        }
    }

    /**
     * 生成缓存实例
     */
    private Map<String, NodeEntity> generateNodeEntity(String modelId, ResourceAnnex resourceAnnex, boolean autoAlignment) {
        log.info(">>>>>>>>>>>>>>>>>>>>【附件】开始生成实例>>>>>>>>>>>>>>>>>>>>");
        // 生成附件顶级node节点
        Map<String, NodeEntity> entityMap = generateAnnexTopNodeEntity(modelId, resourceAnnex);
        // 如果附件在加工过程中 产生的 公文顶级标签的复制体。这里要进行合并
        mergeResourceTopNodeEntity(resourceAnnex, entityMap);
        // 获取该附件的所有标签数据
        List<AnnexLabelData> annexLabelDataList = annexLabelDataRepository.findByCondition(Filters.and(Filters.eq("annexId", resourceAnnex.getId()), Filters.eq("topLabel", false)));
        if (ObjectUtil.isEmpty(annexLabelDataList)) {
            log.info("该附件不存在其它数据标签，无需生成知识图谱!");
            return null;
        }
        // 再生成其它节点
        generateOtherEntity(modelId, resourceAnnex, annexLabelDataList, entityMap, autoAlignment);
        log.info("<<<<<<<<<<<<<<<<<<<<【附件】实例生成完毕<<<<<<<<<<<<<<<<<<<<");
        return entityMap;
    }

    /**
     * 如果附件在加工过程中 产生的 公文顶级标签的复制体。这里要进行合并
     *
     * @param resourceAnnex 附件对象
     * @param entityMap     map
     */
    private void mergeResourceTopNodeEntity(ResourceAnnex resourceAnnex, Map<String, NodeEntity> entityMap) {
        // 再判断附件是否生成了主文件的顶级标签（复制了主文件顶级标签）
        List<AnnexLabelData> resourceTopLabelDataList = annexLabelDataRepository.findByCondition(Filters.and(
                Filters.eq("annexId", resourceAnnex.getId())
                , Filters.eq("topLabel", true)
                , Filters.eq("labelName", LabelConstant.TOP_LABEL)));
        if (ObjectUtil.isNotEmpty(resourceTopLabelDataList)) {
            // 获取公文顶级标签
            AnnexLabelData resourceTopLabelData = resourceTopLabelDataList.get(0);
            // 获取主文件生成的顶级neo4j节点
            String cypher = "MATCH (n:ENTITY) WHERE ANY(x IN n.docIds WHERE x = $resourceId ) and n.topNodeEntity=true and size(n.annexIds)=0 return n";
            List<NodeEntity> resourceTopLabelList = nodeEntityRepository.findEntityByCypher(cypher, Map.of("resourceId", resourceAnnex.getResourceId()));
            if (ObjectUtil.isNotEmpty(resourceTopLabelList)) {
                // 获取主文件顶级标签
                NodeEntity resourceTopLabel = resourceTopLabelList.get(0);
                // 把附件生成的公文标签的属性 复制到 主文件生成的公文标签上
                Map<String, Object> properties = resourceTopLabel.getProperties();
                List<LabelPropertyVO> labelProperties = resourceTopLabelData.getLabelProperties();
                for (LabelPropertyVO labelProperty : labelProperties) {
                    Object value = labelProperty.getValue();
                    Date dateValue = labelProperty.getDateValue();
                    properties.put("prop_" + labelProperty.getIdentifier(), Opt.ofNullable(value).orElse(dateValue));
                }
                // key：附件生成的公文标签Id value：主文件生成的node节点对象
                // 附件中生成的所有与顶级标签有关联的关系，都会与主文件的顶级标签相连
                entityMap.put(resourceTopLabelData.getId(), resourceTopLabel);
            }
        }
    }

    /**
     * 生成附件顶级实例
     *
     * @param modelId       模型ID
     * @param resourceAnnex 附件对象
     * @return map key:labelDattaId value：实例对象
     */
    @NotNull
    private Map<String, NodeEntity> generateAnnexTopNodeEntity(String modelId, ResourceAnnex resourceAnnex) {
        // key: labelDataId value：对应实例
        Map<String, NodeEntity> entityMap = new HashMap<>();
        // 先获取附件顶级标签数据，并生成实例
        List<AnnexLabelData> topAnnexLabelDataList = annexLabelDataRepository.findByCondition(Filters.and
                (Filters.eq("annexId", resourceAnnex.getId())
                        , Filters.eq("topLabel", true)
                        , Filters.eq("labelName", LabelConstant.ANNEX_LABEL)));
        AnnexLabelData topAnnexLabelData;
        // 如果存在顶级节点
        if (ObjectUtil.isNotEmpty(topAnnexLabelDataList)) {
            topAnnexLabelData = topAnnexLabelDataList.get(0);
            NodeEntity nodeEntity = new NodeEntity();
            nodeEntity.setEntityName(topAnnexLabelData.getContent());
            // 获取标签数据对应的标签对象
            Label label = labelRepository.findById(topAnnexLabelData.getLabelId());
            // 设置概念Id（根据labelId查询label对象，并获取该label的sourceId）
            nodeEntity.setConceptId(Optional.ofNullable(label.getSourceId()).orElse("附件"));
            setNodeEntity(modelId, resourceAnnex, topAnnexLabelData, nodeEntity);
            // 设置顶级节点标识
            nodeEntity.setTopNodeEntity(true);
            // 生成 顶级缓存实例
            NodeEntity topNodeEntity = nodeEntityRepository.save(nodeEntity);
            // 设置资源的顶级节点Id
            resourceAnnex.setTopNodeId(topNodeEntity.getId());
            // 更新资源状态
            resourceAnnexFeign.save(resourceAnnex);
            log.info("【附件】生成顶级节点 ===> {}", nodeEntity.getEntityName());
            // 存储顶级实例
            entityMap.put(topAnnexLabelData.getId(), nodeEntity);
            // 建立附件与主文件的关联
            relationWithResource(resourceAnnex, nodeEntity, topNodeEntity);
        }
        return entityMap;
    }

    /**
     * 建立附件和主文件的关联
     *
     * @param resourceAnnex 附件对象
     * @param nodeEntity    实例
     * @param topNodeEntity 顶级实例
     */
    private void relationWithResource(ResourceAnnex resourceAnnex, NodeEntity nodeEntity, NodeEntity topNodeEntity) {
        // 获取主文件的顶级node节点,并建立附件与其的联系
        String cypher = "MATCH (n:ENTITY) WHERE ANY(x IN n.docIds WHERE x = $resourceId ) and n.topNodeEntity=true and size(n.annexIds)=0 return n";
        List<NodeEntity> resourceTopLabelList = nodeEntityRepository.findEntityByCypher(cypher, Map.of("resourceId", resourceAnnex.getResourceId()));
        if (ObjectUtil.isNotEmpty(resourceTopLabelList)) {
            // 获取主文件顶级标签
            NodeEntity resourceTopLabel = resourceTopLabelList.get(0);
            // 建立附件与主文件的关联
            // 默认为所有实例添加与顶级节点的涉及关系
            NodeRelation forwardRelation = new NodeRelation();
            forwardRelation.setPredicateName(LabelConstant.RELEVANCY_ANNEX);
            forwardRelation.setPredicateId("默认");
            forwardRelation.setDocIds(nodeEntity.getDocIds());
            forwardRelation.setAnnexIds(nodeEntity.getAnnexIds());
            forwardRelation.setStart(resourceTopLabel);
            forwardRelation.setEnd(topNodeEntity);
            forwardRelation.setPositive(true);
            // 新增正向关系
            nodeRelationRepository.save(forwardRelation);

            NodeRelation inverseRelation = new NodeRelation();
            inverseRelation.setPredicateName(LabelConstant.BELONGS);
            inverseRelation.setPredicateId("默认");
            inverseRelation.setDocIds(nodeEntity.getDocIds());
            inverseRelation.setAnnexIds(nodeEntity.getAnnexIds());
            inverseRelation.setStart(nodeEntity);
            inverseRelation.setEnd(resourceTopLabel);
            inverseRelation.setPositive(false);
            // 新增反向关系
            nodeRelationRepository.save(inverseRelation);

        }
    }


    /**
     * 生成剩余实例节点
     *
     * @param modelId            模型Id
     * @param resourceAnnex      附件
     * @param annexLabelDataList 附件标签列表
     * @param entityMap          key：标签Id value：对应实例
     * @param autoAlignment      自动对齐
     */
    private void generateOtherEntity(String modelId, ResourceAnnex resourceAnnex, List<AnnexLabelData> annexLabelDataList, Map<String, NodeEntity> entityMap, boolean autoAlignment) {
        try {
            // 生成neo4j实例
            for1:
            for (AnnexLabelData annexLabelData : annexLabelDataList) {
                // 获取标签数据对应的标签对象
                Label label = labelRepository.findById(annexLabelData.getLabelId());
                if (label == null) continue;
                // 获取概念Id
                String conceptId = Opt.ofNullable(label.getSourceId()).orElse("");
                // 如果开启了自动对齐，则在生成实例前，查询是否已经存在相同实例
                if (autoAlignment) {
                    org.neo4j.ogm.cypher.Filters filters = new org.neo4j.ogm.cypher.Filters(new Filter("entityName", ComparisonOperator.EQUALS, annexLabelData.getContent()));
                    filters.and(new Filter("conceptId", ComparisonOperator.EQUALS, conceptId));
                    List<NodeEntity> nodeEntityList = nodeEntityRepository.findByCondition(filters);
                    // 存在疑似相同实例，在判断属性，如果属性也相同则就是相同实例，属性不同，则是不同实例
                    if (ObjectUtil.isNotEmpty(nodeEntityList)) {
                        for (NodeEntity nodeEntity : nodeEntityList) {
                            // 判断属性
                            Map<String, Object> properties = nodeEntity.getProperties();
                            List<LabelPropertyVO> labelProperties = annexLabelData.getLabelProperties();
                            // 如果存在属性
                            if (ObjectUtil.isNotEmpty(properties) && ObjectUtil.isNotEmpty(labelProperties)) {
                                // 遍历标签的属性
                                for (LabelPropertyVO labelProperty : labelProperties) {
                                    // 判断是否存在相同属性
                                    Object value = properties.get("prop_" + labelProperty.getIdentifier());
                                    if (value != null) {
                                        try {
                                            String valueStr = Convert.toStr(value);
                                            String propertyValue = labelProperty.getValue();
                                            if (ObjectUtil.equal(valueStr, propertyValue)) {
                                                log.info("【附件】【{}】当前实例已存在，跳过该实例生成!", annexLabelData.getContent());
                                                nodeEntity.getDocIds().add(annexLabelData.getResourceId());
                                                entityMap.put(annexLabelData.getId(), nodeEntity);
                                                continue for1;
                                            }
                                        } catch (Exception ignored) {
                                        }
                                    }
                                }
                            }
                            // 如果都不存在属性
                            if (ObjectUtil.isEmpty(properties) && ObjectUtil.isEmpty(labelProperties)) {
                                nodeEntity.getDocIds().add(annexLabelData.getResourceId());
                                log.info("【附件】【{}】当前实例已存在，跳过该实例生成!", annexLabelData.getContent());
                                entityMap.put(annexLabelData.getId(), nodeEntity);
                            }
                        }

                    }
                }

                // 创建缓存实例
                NodeEntity nodeEntity = new NodeEntity();
                // 设置实例名称
                nodeEntity.setEntityName(annexLabelData.getContent());
                // 设置概念Id（根据labelId查询label对象，并获取该label的sourceId）
                nodeEntity.setConceptId(conceptId);
                setNodeEntity(modelId, resourceAnnex, annexLabelData, nodeEntity);
                log.info("【附件】生成实例 ===> {}", nodeEntity.getEntityName());
                // 新增实例
                nodeEntity = nodeEntityRepository.save(nodeEntity);
                // 存储其它实例
                entityMap.put(annexLabelData.getId(), nodeEntity);
            }
        } catch (ServiceException ignored) {
        }
    }

    /**
     * 为实例节点设置通用属性
     *
     * @param modelId        模型Id
     * @param resourceAnnex  附件
     * @param annexLabelData 附件标签数据
     * @param nodeEntity     实例
     */
    private void setNodeEntity(String modelId, ResourceAnnex resourceAnnex, AnnexLabelData annexLabelData, NodeEntity nodeEntity) {
        nodeEntity.setModelId(modelId);
        nodeEntity.getDocIds().add(resourceAnnex.getResourceId());
        nodeEntity.getAnnexIds().add(resourceAnnex.getId());
        nodeEntity.getDocumentUnitIds().addAll(annexLabelData.getDocumentUnitIds());
        nodeEntity.setLabelDataId(annexLabelData.getId());
        // 属性Map
        Map<String, Object> properties = Opt.ofNullable(nodeEntity.getProperties()).orElse(new HashMap<>());
        // 设置属性
        for (LabelPropertyVO labelProperty : annexLabelData.getLabelProperties()) {
            // 根据属性标签Id，获取属性标签对象
            Label propertyLabel = Opt.ofNullable(labelRepository.findById(labelProperty.getLabelPropertyId())).orElseThrow(ServiceException::new, "该标签不存在");
            // 设置 key：属性名 value：属性值
            Object value = labelProperty.getValue();
            Date dateValue = labelProperty.getDateValue();
            properties.put("prop_" + propertyLabel.getIdentifier(), Opt.ofNullable(value).orElse(dateValue));
        }
        nodeEntity.setProperties(properties);
    }

    /**
     * 生成关系
     */
    private void generateRelation(ResourceAnnex resourceAnnex, Map<String, NodeEntity> entityMap) {
        log.info(">>>>>>>>>>>>>>>>>>>>【附件】开始建立关系>>>>>>>>>>>>>>>>>>>>");
        // 获取该资源下的所有关系三元组对象
        List<AnnexTripleLabelData> annexTripleLabelDataList = annexTripleLabelDataRepository.findByCondition(Filters.eq("annexId", resourceAnnex.getId()));
        if (ObjectUtil.isEmpty(annexTripleLabelDataList)) {
            log.info("该附件不存在关系标签!");
            return;
        }
        try {
            final HashMap<String, String> lableData = new HashMap<>();
            // 遍历关系三元组
            for (AnnexTripleLabelData annexTripleLabelData : annexTripleLabelDataList) {
                if (annexTripleLabelData.getStartLabelDataId() == null || annexTripleLabelData.getEndLabelDataId() == null)
                    continue;
                // 获取关系主语的缓存节点实例
                NodeEntity fromEntity = entityMap.get(annexTripleLabelData.getStartLabelDataId());
                if (fromEntity == null) continue;
                // 获取关系宾语的缓存节点实例
                NodeEntity toEntity = entityMap.get(annexTripleLabelData.getEndLabelDataId());
                if (toEntity == null) continue;
                // 根据relationId，获取该谓词标签
                Label relationLabel = labelRepository.findById(annexTripleLabelData.getRelationLabelId());
                if (relationLabel == null) continue;
                // 获取对应关系库的关系Id
                KnowledgeRelation knowledgeRelation = knowledgeRelationRepository.findById(relationLabel.getSourceId());
                if (knowledgeRelation == null) continue;

                /*String s = lableData.getOrDefault(annexTripleLabelData.getRelationLabelId(), "");
                final String startLabelId = annexTripleLabelData.getStartLabelId();
                final String endLabelId = annexTripleLabelData.getEndLabelId();
                if(ObjectUtil.isEmpty(s)) {
                    lableData.put(annexTripleLabelData.getRelationLabelId(), startLabelId + endLabelId);
                } else {
                    if(ObjectUtil.equals(s, startLabelId + endLabelId)) {
                        continue;
                    }
                }*/
                // 生成正向关系
                NodeRelation forwardRelation = new NodeRelation();
                forwardRelation.setPositive(true);
                forwardRelation.setPredicateName(knowledgeRelation.getForwardPre());
                generateNodeRelation(resourceAnnex, annexTripleLabelData, fromEntity, toEntity, knowledgeRelation, forwardRelation);

                // 非对称性关系，生成反向关系
                if (!knowledgeRelation.getIsSymmetric()) {
                    NodeRelation inverseRelation = new NodeRelation();
                    inverseRelation.setPositive(false);
                    inverseRelation.setPredicateName(knowledgeRelation.getInversePre());
                    generateNodeRelation(resourceAnnex, annexTripleLabelData, toEntity, fromEntity, knowledgeRelation, inverseRelation);
                }
            }
        } catch (Exception ignored) {
        }
        log.info("<<<<<<<<<<<<<<<<<<<<【附件】关系建立完毕<<<<<<<<<<<<<<<<<<<<");
    }

    /**
     * 生成关系
     *
     * @param resourceAnnex        附件
     * @param annexTripleLabelData 附件关系标签
     * @param fromEntity           主语
     * @param toEntity             宾语
     * @param knowledgeRelation    关系库
     * @param nodeRelation         关系对象
     */
    private void generateNodeRelation(ResourceAnnex resourceAnnex, AnnexTripleLabelData annexTripleLabelData, NodeEntity fromEntity, NodeEntity toEntity, KnowledgeRelation knowledgeRelation, NodeRelation nodeRelation) {
        // 谓词Id
        nodeRelation.setPredicateId(knowledgeRelation.getId());
        // 来源公文
        nodeRelation.getDocIds().add(resourceAnnex.getResourceId());
        // 来源附件
        nodeRelation.getAnnexIds().add(resourceAnnex.getId());
        Map<String, Object> forwardRelationProperties = Opt.ofNullable(nodeRelation.getProperties()).orElse(new HashMap<>());
        // 设置属性
        for (LabelPropertyVO relationProperty : annexTripleLabelData.getRelationProperties()) {
            // 获取属性名称
            Label propertyLabel = Opt.ofNullable(labelRepository.findById(relationProperty.getLabelPropertyId())).orElseThrow(ServiceException::new, "当前属性标签不存在！");
            // 添加属性
            Object value = relationProperty.getValue();
            Date dateValue = relationProperty.getDateValue();
            forwardRelationProperties.put("prop_" + propertyLabel.getIdentifier(), Opt.ofNullable(value).orElse(dateValue));
        }
        nodeRelation.setProperties(forwardRelationProperties);
        // 设置正向关系的起点和终点
        nodeRelation.setStart(fromEntity);
        nodeRelation.setEnd(toEntity);
        // 更新关系
        nodeRelationRepository.save(nodeRelation);
    }


}
