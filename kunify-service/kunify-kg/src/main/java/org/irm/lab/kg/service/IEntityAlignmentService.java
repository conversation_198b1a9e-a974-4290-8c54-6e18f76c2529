package org.irm.lab.kg.service;

import cn.hutool.json.JSONObject;
import org.bson.json.JsonObject;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/3/20 15:15
 */
public interface IEntityAlignmentService {
    JsonObject startAlignment(String resourceId);

    /**
     * 实例对照组
     *
     * @param cacheNodeId 需要对照的实例
     * @param nodeIds     知识库同名实例id集合
     * @return 对照组
     */
    JSONObject nodeCompareGroup(Long cacheNodeId, List<Long> nodeIds);

    /**
     * 获取单个复杂实例的对照组
     *
     * @param cacheNodeId 需要对齐的实例id
     * @return 对比组
     */
    JSONObject nodeCompareGroup(Long cacheNodeId);

    /**
     * 实例对齐
     *
     * @param cacheNodeId 需要对齐的实例
     * @param nodeId      选中的知识库实例
     * @param resourceId       文档id
     */
    void nodeAlignment(String cacheNodeId, String nodeId, String resourceId);

}
