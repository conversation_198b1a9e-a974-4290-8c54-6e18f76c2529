package org.irm.lab.kg.algorithm;

import cn.hutool.json.JSONObject;
import com.mongodb.client.model.Filters;
import lombok.RequiredArgsConstructor;
import org.bson.conversions.Bson;
import org.irm.ai.api.nlp_basic.NLPApi;
import org.irm.lab.common.enums.AlgorithmType;
import org.irm.lab.common.pojo.BaseStatus;
import org.irm.lab.kg.config.AiSdkLink;
import org.irm.lab.kg.repository.DocumentSummaryRepository;
import org.irm.lab.repository.entity.Resource;
import org.springframework.stereotype.Service;
import java.util.List;

/**
 * 摘要算法
 *
 * <AUTHOR>
 * @date 2023/1/30 16:39
 */
@Service
@RequiredArgsConstructor
public class SummaryProcessor implements IAlgorithmProcessor {

    private final DocumentSummaryRepository documentSummaryRepository;


    @Override
    public AlgorithmType getCode() {
        return AlgorithmType.SUMMARY;
    }

    @Override
    public void process(Resource resource) {
        NLPApi nlpApi = NLPApi.build(AiSdkLink.sdkApikey, AiSdkLink.sdkSecretKey, AiSdkLink.sdkHost);
        String resourceId = resource.getId();
        Bson filter = Filters.eq("resourceId", resourceId);
        List<DocumentSummary> summaries = documentSummaryRepository.findByCondition(filter);
        for (DocumentSummary s : summaries) {
            JSONObject summaryRes = nlpApi.summary(s.getOriginContent());
            if ("500".equals(summaryRes.getStr("code"))) {
                s.setSummaryStatus(summaryRes.getStr("message"));
            } else {
                s.setSummaryStatus(BaseStatus.OK);
                s.setSummaryContent(summaryRes.getStr("data"));
            }
        }
        documentSummaryRepository.saveAll(summaries);
    }
}
