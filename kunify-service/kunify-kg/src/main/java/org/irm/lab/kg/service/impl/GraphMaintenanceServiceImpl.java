package org.irm.lab.kg.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.mongodb.client.model.Filters;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.bson.conversions.Bson;
import org.irm.lab.common.api.R;
import org.irm.lab.common.constant.ExceptionMessageConst;
import org.irm.lab.common.constant.FiledNameConst;
import org.irm.lab.common.constant.ModelConceptConst;
import org.irm.lab.common.exception.ServiceException;
import org.irm.lab.common.support.MyPage;
import org.irm.lab.kg.constant.CompositeConstant;
import org.irm.lab.kg.dto.NodeRelationDTO;
import org.irm.lab.kg.entity.KnowledgeConcept;
import org.irm.lab.kg.entity.KnowledgeModel;
import org.irm.lab.kg.entity.KnowledgeProperty;
import org.irm.lab.kg.entity.ReferenceMap;
import org.irm.lab.kg.entity.neo4j.base.Neo4jAbstractBaseEntity;
import org.irm.lab.kg.entity.neo4j.node.NodeEntity;
import org.irm.lab.kg.entity.neo4j.node.NodeRelation;
import org.irm.lab.kg.repository.KnowledgeConceptRepository;
import org.irm.lab.kg.repository.KnowledgeModelRepository;
import org.irm.lab.kg.repository.neo4j.node.NodeEntityRepository;
import org.irm.lab.kg.repository.neo4j.node.NodeRelationRepository;
import org.irm.lab.kg.service.*;
import org.irm.lab.kg.vo.KnowledgeRelationVO;
import org.irm.lab.kg.vo.PropMessageVO;
import org.irm.lab.kg.vo.echarts.EchartsNode;
import org.irm.lab.kg.vo.echarts.EchartsRelation;
import org.irm.lab.kg.vo.echarts.EchartsVO;
import org.irm.lab.kg.vo.node.NodeEntityVO;
import org.irm.lab.kg.vo.node.NodeRelationVO;
import org.irm.lab.kg.vo.node.PropertyVO;
import org.irm.lab.repository.entity.Resource;
import org.irm.lab.repository.feign.ResourceFeign;
import org.neo4j.ogm.cypher.ComparisonOperator;
import org.neo4j.ogm.cypher.Filter;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

@Service
@Slf4j
@RequiredArgsConstructor
public class GraphMaintenanceServiceImpl implements IGraphMaintenanceService {


    private final IKnowledgeAlignmentService knowledgeAlignmentService;
    private final NodeEntityRepository nodeEntityRepository;
    private final NodeRelationRepository nodeRelationRepository;
    private final IKnowledgeConceptService conceptService;
    private final IKnowledgeRelationService relationService;
    private final IKnowledgePropertyService propertyService;
    private final KnowledgeConceptRepository conceptRepository;
    private final ResourceFeign resourceFeign;
    private final IKnowledgePropertyService iKnowledgePropertyService;
    private final KnowledgeModelRepository knowledgeModelRepository;

    /**
     * 图谱-概念获取实例以及关系
     *
     * @param conceptId 概念id
     * @param modelId   模型id
     * @return EchartsVO
     */
    @Override
    public EchartsVO echartsNodesByConcept(String conceptId, String modelId, String showCount) {
        EchartsVO echartsVO = new EchartsVO();
        Set<EchartsNode> nodeSet = new HashSet<>();
        Set<EchartsRelation> relationSet = new HashSet<>();
        // 根据概念Id获取指定的概念对象
        if (StrUtil.isBlank(conceptId)) throw new ServiceException("该知识模型无概念！");
        KnowledgeConcept knowledgeConcept = conceptRepository.findById(conceptId);
        // 创建根概念
        EchartsNode topNode = new EchartsNode();
        topNode.setId("根节点_" + knowledgeConcept.getId());

        topNode.setConceptId(knowledgeConcept.getId());
        topNode.setName(knowledgeConcept.getName());
        topNode.setType("概念类");
        nodeSet.add(topNode);
        // 获取指定模型、指定概念下的所有实例
        org.neo4j.ogm.cypher.Filters filters = new org.neo4j.ogm.cypher.Filters(new Filter("conceptId", ComparisonOperator.EQUALS, knowledgeConcept.getId()));
        // 判断是否指定模型
        if ((!"0".equals(modelId))) {
            filters.and(new Filter("modelId", ComparisonOperator.EQUALS, modelId));
        }
        // 获取满足条件的所有实例
        List<NodeEntity> entityList = nodeEntityRepository.findByCondition(filters, 0);
        // 设置节点总数
        echartsVO.setNodeTotal(entityList.size());
        // 限制数量
        if (ObjectUtil.isNotEmpty(showCount)) {
            entityList = entityList.stream().limit(Long.parseLong(showCount)).collect(Collectors.toList());
        }
        // 生成node
        List<EchartsNode> echartsNodeList = entityList.stream().map(entity -> {
            EchartsNode node = new EchartsNode();
            node.setId(entity.getId());
            node.setConceptId(entity.getConceptId());
            node.setName(entity.getEntityName());
            node.setType(knowledgeConcept.getName());
            nodeSet.add(node);
            return node;
        }).collect(Collectors.toList());
        // 生成所有关系
        for (EchartsNode node : echartsNodeList) {
            EchartsRelation relation = new EchartsRelation();
            relation.setName("包含");
            relation.setSource(topNode.getId());
            relation.setTarget(node.getId());
            relationSet.add(relation);
        }
        echartsVO.setNodes(nodeSet);
        echartsVO.setRelations(relationSet);
        // 返回视图
        return echartsVO;
    }


    /**
     * 实例新增
     *
     * @param nodeEntity 实例信息
     * @return 添加后的实例节点信息
     */
    @Override
    public NodeEntity saveNode(NodeEntity nodeEntity) {
        return nodeEntityRepository.save(nodeEntity, 1);
    }


    /**
     * 节点删除
     *
     * @param ids 节点id集合
     */
    @Override
    public void nodeRemove(List<String> ids) {
        //删除当前实例
        ids.forEach(nodeEntityRepository::deleteById);
    }

    /**
     * 图谱-实例及其关系
     *
     * @param nodeId 节点id
     * @return EchartsVO
     */
    @Override
    public EchartsVO echartsNodesWithRelation(String nodeId, String modelId, String showCount) {
        EchartsVO echartsVO = new EchartsVO();
        Set<EchartsNode> nodeSet = new HashSet<>();  // 概念相关数据
        Set<EchartsRelation> relationSet = new HashSet<>();
        // 获取指定的实例
        NodeEntity nodeEntity = nodeEntityRepository.findById(nodeId, 1);
//        log.info("获取id为{}的节点数据，深度为1，结果为{}",nodeId,nodeEntity.toString());
        // 获取该实例的概念对象
        KnowledgeConcept knowledgeConcept = conceptRepository.findById(nodeEntity.getConceptId());
        // 创建根概念
        EchartsNode topNode = new EchartsNode();
        // 根节点的id为概念的id
        topNode.setId("根节点_" + knowledgeConcept.getId());

        topNode.setConceptId(knowledgeConcept.getId());
        topNode.setName(knowledgeConcept.getName());
        topNode.setType("概念类");
        nodeSet.add(topNode); //
        // 创建当前概念
        EchartsNode currentNode = new EchartsNode();
        currentNode.setId(nodeEntity.getId());
        currentNode.setConceptId(knowledgeConcept.getId());
        currentNode.setName(nodeEntity.getEntityName());
        currentNode.setType(knowledgeConcept.getName());
        nodeSet.add(currentNode);
        // 创建默认关系
        EchartsRelation defaultRelation = new EchartsRelation();
        defaultRelation.setName("包含");
        defaultRelation.setSource(topNode.getId());
        defaultRelation.setTarget(currentNode.getId());
        relationSet.add(defaultRelation);
        // 获取该实例指向的宾语
        Set<NodeRelation> toEntity = nodeEntity.getToEntity();
        // 如果存在宾语
        if (ObjectUtil.isNotEmpty(toEntity)) {
            int size = 1;
            for (NodeRelation nodeRelation : toEntity) {
                // 限制节点数量
                if (ObjectUtil.isNotEmpty(showCount)) {
                    if (size >= Convert.toInt(showCount)) break;
                }
                // 创建实例
                EchartsNode node = new EchartsNode();
                NodeEntity end = nodeRelation.getEnd();
                node.setId(end.getId());
                node.setConceptId(end.getConceptId());
                node.setName(end.getEntityName());
                node.setType(conceptRepository.findById(end.getConceptId()).getName());
                nodeSet.add(node);
                // 生成关系
                EchartsRelation relation = new EchartsRelation();
                // 关系的Id
                relation.setId(nodeRelation.getId());
                // 关系的谓语Id
                relation.setConceptId(nodeRelation.getPredicateId());
                relation.setName(nodeRelation.getPredicateName());
                relation.setSource(currentNode.getId());
                relation.setTarget(node.getId());
                relationSet.add(relation);
                size++;
            }
        }

        echartsVO.setNodes(nodeSet);
        echartsVO.setRelations(relationSet);
        return echartsVO;
    }


    /**
     * 实例详情
     *
     * @param id 实例id
     * @return NodeEntityVO
     */
    @Override
    public NodeEntityVO nodeVOInfo(String id) {
        // 获取指定的实例
        NodeEntity nodeEntity = nodeEntityRepository.findById(id, 1);
        // 生成VO
        NodeEntityVO nodeEntityVO = BeanUtil.copyProperties(nodeEntity, NodeEntityVO.class);
        //添加关联文件
        List<Resource> resourceList = new ArrayList<>();
        if (ObjectUtil.isNotEmpty(nodeEntity.getDocIds())) {
            Set<String> docIds = nodeEntity.getDocIds();
            R<List<Resource>> listR = resourceFeign.listByIds(docIds);
            try {
                resourceList = listR.getData();
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
        nodeEntityVO.setResources(resourceList);
        // 设置实例属性
        List<PropertyVO> propertyVOS = new ArrayList<>();
        Map<String, Object> properties = nodeEntity.getProperties();
        for (String propIdentifier : properties.keySet()) {
            String[] split = propIdentifier.split("_");
            List<KnowledgeProperty> propertyList = iKnowledgePropertyService.findByBson(Filters.eq("identifier", split[1]));
            if (ObjectUtil.isNotEmpty(propertyList)) {
                KnowledgeProperty knowledgeProperty = propertyList.get(0);
                PropertyVO propertyVO = new PropertyVO();
                propertyVO.setIdentifier(propIdentifier);
                propertyVO.setName(knowledgeProperty.getName());
                propertyVO.setValue(properties.get(propIdentifier));
                propertyVO.setType(knowledgeProperty.getDatatype());
                propertyVOS.add(propertyVO);
            }
        }
        nodeEntityVO.setPropertyList(propertyVOS);
        // 设置关系
        Set<NodeRelation> toEntity = nodeEntityVO.getToEntity();
        List<List<NodeRelationVO>> nodeRelations = new ArrayList<>();
        // 存在关系时
        if (ObjectUtil.isNotEmpty(toEntity)) {
            // 设置正向关系
            for (NodeRelation nodeRelation : toEntity) {
                NodeRelationVO reverseRelationVO = BeanUtil.copyProperties(nodeRelation, NodeRelationVO.class);
                List<PropertyVO> reverseRelationPropertyVOS = new ArrayList<>();
                Map<String, Object> relationVOProperties = reverseRelationVO.getProperties();
                // 如果存在关系属性
                if (ObjectUtil.isNotEmpty(relationVOProperties)) {
                    for (String propIdentifier : relationVOProperties.keySet()) {
                        String[] split = propIdentifier.split("_");
                        List<KnowledgeProperty> propertyList = iKnowledgePropertyService.findByBson(Filters.eq("identifier", split[1]));
                        if (ObjectUtil.isNotEmpty(propertyList)) {
                            KnowledgeProperty knowledgeProperty = propertyList.get(0);
                            PropertyVO propertyVO = new PropertyVO();
                            propertyVO.setIdentifier(propIdentifier);
                            propertyVO.setName(knowledgeProperty.getName());
                            propertyVO.setValue(relationVOProperties.get(propIdentifier));
                            propertyVO.setType(knowledgeProperty.getDatatype());
                            reverseRelationPropertyVOS.add(propertyVO);
                        }
                    }
                    // 设置关系的属性
                    reverseRelationVO.setPropertyList(reverseRelationPropertyVOS);
                }
                nodeRelations.add(List.of(reverseRelationVO));
                // 设置关系
                nodeEntityVO.setRelationGroupList(nodeRelations);
            }
        }
        return nodeEntityVO;
    }

    /**
     * 关系删除
     *
     * @param ids 关系id集合
     */
    @Override
    public void relationRemove(List<String> ids) {
        ids.forEach(id -> {
            // 删除正向关系
            NodeRelation nodeRelation = relationInfo(id, 1);
            //如果主语是公文（制度），删除宾语中对应的docId
            removeObjectDocId(nodeRelation);
            nodeRelationRepository.deleteById(nodeRelation.getId());
            // 删除反向关系
            String cypher = "MATCH (s:ENTITY)-[r:RELATION]-(e:ENTITY) where s.id=$startId and r.predicateId=$predicateId and e.id=$endId return s,r,e";
            List<NodeRelation> relationList = nodeRelationRepository.findRelationByCypher(cypher, Map.of("startId", nodeRelation.getEnd().getId(), "predicateId", nodeRelation.getPredicateId(), "endId", nodeRelation.getStart().getId()));
            if (ObjectUtil.isNotEmpty(relationList)) {
                for (NodeRelation relation : relationList) {
                    nodeRelationRepository.deleteById(relation.getId());
                }
            }
        });
    }

    private void removeObjectDocId(NodeRelation nodeRelation) {
        KnowledgeConcept zdConcept = conceptService.findByConceptName(ModelConceptConst.CONCEPT_ZHIDU);
        KnowledgeConcept gwConcept = conceptService.findByConceptName(ModelConceptConst.CONCEPT_GONGWEN);
        NodeEntity start = nodeRelation.getStart();
        if (start.getConceptId().equals(zdConcept.getId()) ||
                start.getConceptId().equals(gwConcept.getId())) {
            //只删除公文、制度作为主语时，宾语中的docId
            Set<String> docIds = start.getDocIds();
            NodeEntity end = nodeRelation.getEnd();
            end.getDocIds().removeAll(docIds);
            saveNode(end);
        }
    }

    /**
     * 获取该模型下的指定概念的属性列表
     *
     * @param modelId   模型Id
     * @param conceptId 概念Id
     * @return {@link KnowledgeProperty}
     */
    @Override
    public List<KnowledgeProperty> listPropertyByModelId(String modelId, String conceptId) {
        // 如果是主模型，则获取属性库里的所有属性
        if ("0".equals(modelId)) {
            return iKnowledgePropertyService.list();
        }
        // 获取模型对象
        KnowledgeModel knowledgeModel = knowledgeModelRepository.findById(modelId);
        // 获取该模型的所有概念
        Set<ReferenceMap> concepts = knowledgeModel.getConcepts();
        // 创建集合存储所有属性ID
        List<String> propertyIdList = new ArrayList<>();
        if (ObjectUtil.isNotEmpty(concepts)) {
            for (ReferenceMap concept : concepts) {
                // 获取指定概念的属性
                if (conceptId.equals(concept.getId())) {
                    propertyIdList.addAll(concept.getPropertyIds());
                }
            }
        }
        // 获取所有属性对象
        return propertyIdList.stream().map(iKnowledgePropertyService::info).collect(Collectors.toList());
    }


    /**
     * 获取该模型下的指定关系的属性列表
     *
     * @param modelId    模型Id
     * @param relationId 关系Id
     * @return {@link KnowledgeProperty}
     */
    @Override
    public List<KnowledgeProperty> listRelationProperty(String modelId, String relationId) {
        // 如果是主模型，则获取属性库里的所有属性
        if ("0".equals(modelId)) {
            return iKnowledgePropertyService.list();
        }
        // 获取模型对象
        KnowledgeModel knowledgeModel = knowledgeModelRepository.findById(modelId);
        // 获取该模型的所有概念
        Set<ReferenceMap> relations = knowledgeModel.getRelations();
        // 创建集合存储所有属性ID
        List<String> propertyIdList = new ArrayList<>();
        if (ObjectUtil.isNotEmpty(relations)) {
            for (ReferenceMap relation : relations) {
                // 获取指定概念的属性
                if (relationId.equals(relation.getId())) {
                    propertyIdList.addAll(relation.getPropertyIds());
                }
            }
        }
        // 获取所有属性对象
        return propertyIdList.stream().map(iKnowledgePropertyService::info).collect(Collectors.toList());
    }


    /**
     * 删除实例属性
     *
     * @param propMessageVO {@link PropMessageVO}
     */
    @Override
    public void removeNodeProperty(PropMessageVO propMessageVO) {
        NodeEntity nodeEntity = nodeEntityRepository.findById(propMessageVO.getNodeId());
        String identifier = propMessageVO.getIdentifier();
        Map<String, Object> properties = nodeEntity.getProperties();
        // 删除属性
        properties.put(identifier, null);
        nodeEntity.setProperties(properties);
        // 更新实例
        nodeEntityRepository.save(nodeEntity);
    }


    /**
     * 删除关系属性
     *
     * @param propMessageVO {@link PropMessageVO}
     */
    @Override
    public void removeRelationProperty(PropMessageVO propMessageVO) {
        // 删除正向关系的属性
        NodeRelation nodeRelation = nodeRelationRepository.findById(propMessageVO.getNodeId());
        String identifier = propMessageVO.getIdentifier();
        Map<String, Object> properties = nodeRelation.getProperties();
        properties.put("prop_" + identifier, null);
        nodeRelation.setProperties(properties);
        nodeRelationRepository.save(nodeRelation);

        // 删除反向关系的属性
        String cypher = "MATCH (s:ENTITY)-[r:RELATION]-(e:ENTITY) where s.id=$startId and r.predicateId=$predicateId and e.id=$endId return s,r,e";
        List<NodeRelation> relationList = nodeRelationRepository.findRelationByCypher(cypher, Map.of("startId", nodeRelation.getEnd().getId(), "predicateId", nodeRelation.getPredicateId(), "endId", nodeRelation.getStart().getId()));
        if (ObjectUtil.isNotEmpty(relationList)) {
            for (NodeRelation relation : relationList) {
                relation.setProperties(properties);
                nodeRelationRepository.save(relation);
            }
        }
    }

    /**
     * 根据概念和关系获取可用实例
     *
     * @param conceptId 概念Id
     * @param nodeId    实例Id
     * @param modelId   实例Id
     * @return {@link NodeEntity}
     */
    @Override
    public List<NodeEntity> listNodeRelation(String conceptId, String nodeId, String modelId) {
        // 获取主语实例
        NodeEntity startEntity = nodeEntityRepository.findById(nodeId);
        List<NodeEntity> endEntityList = new ArrayList<>();
        // 获取主语指向的所有宾语实例
        Set<NodeRelation> toEntity = startEntity.getToEntity();
        if (toEntity != null) {
            for (NodeRelation nodeRelation : toEntity) {
                endEntityList.add(nodeRelation.getEnd());
            }
        }
        // 获取指定概念和模型下的所有实例
        List<NodeEntity> nodeEntityList = nodeList(conceptId, modelId);
        if (ObjectUtil.isNotEmpty(nodeEntityList) && ObjectUtil.isNotEmpty(endEntityList)) {
            nodeEntityList = nodeEntityList.stream().filter(nodeEntity -> endEntityList.stream().noneMatch(endEntity -> nodeEntity.getId().equals(endEntity.getId()))).collect(Collectors.toList());
        }
        return nodeEntityList;
    }


    @Override
    public MyPage<NodeEntity> pageNodeRelation(String conceptId, String nodeId, String modelId, Integer page, Integer size, String entityName) {
        Map<String, Object> map = new HashMap<>();
        map.put("entityName", entityName);
        if (!"0".equals(modelId))
            map.put("modelId", modelId);
        if (ObjectUtil.isNotEmpty(conceptId))
            map.put("conceptId", conceptId);
        return nodeEntityRepository.findPageByCondition(map, page, size);

//        // 获取主语实例
//        NodeEntity startEntity = nodeEntityRepository.findById(nodeId);
//        List<NodeEntity> endEntityList = new ArrayList<>();
//        // 获取主语指向的所有宾语实例
//        Set<NodeRelation> toEntity = startEntity.getToEntity();
//        if (toEntity != null) {
//            for (NodeRelation nodeRelation : toEntity) {
//                endEntityList.add(nodeRelation.getEnd());
//            }
//        }
//        // 获取指定概念和模型下的所有实例
//        List<NodeEntity> nodeEntityList = nodeList(conceptId, modelId);
//        if (ObjectUtil.isNotEmpty(nodeEntityList) && ObjectUtil.isNotEmpty(endEntityList)) {
//            nodeEntityList = nodeEntityList.stream().filter(nodeEntity -> endEntityList.stream().noneMatch(endEntity -> nodeEntity.getId().equals(endEntity.getId()))).collect(Collectors.toList());
//        }
//        return nodeEntityList;
    }

    /**
     * 节点分页 查询当前模型下指定概念的实例
     *
     * @param queryParam 查询条件
     * @param page       页码
     * @param size       条数
     * @param depth      深度
     * @return 分页信息
     */
    @Override
    public MyPage<NodeEntity> nodePage(Map<String, Object> queryParam, int page, int size, int depth) {
        if ("0".equals(queryParam.get("modelId"))) {
            queryParam.remove("modelId");
        }
        return nodeEntityRepository.findPageByCondition(queryParam, page, size, depth);
    }

    /**
     * 查询当前模型下概念的所有实例
     *
     * @param conceptId 概念id
     * @param modelId   模型id
     * @return 实例集合
     */
    @Override
    public List<NodeEntity> nodeList(String conceptId, String modelId) {
        Map<String, Object> map = new HashMap<>();
        if (!"0".equals(modelId))
            map.put("modelId", modelId);
        if (ObjectUtil.isNotEmpty(conceptId))
            map.put("conceptId", conceptId);
        return nodeEntityRepository.findByCondition(map);
    }


    /**
     * 节点属性修改
     *
     * @param propMessageVO NodePropValueVO
     */
    @Override
    public void updateNodeProperties(PropMessageVO propMessageVO) {
        NodeEntity node = nodeInfo(propMessageVO.getNodeId());
        Map<String, Object> properties = node.getProperties();
        properties.put(propMessageVO.getIdentifier(), propMessageVO.getMessage());
        node.setProperties(properties);
        nodeEntityRepository.save(node, 1);
    }

    /**
     * 实例关系属性修改
     *
     * @param propMessageVO propMessageVO
     */
    @Override
    public void updateRelationProperties(PropMessageVO propMessageVO) {
        // 修改正向关系
        NodeRelation relation = nodeRelationRepository.findById(propMessageVO.getNodeId());
        Map<String, Object> properties = relation.getProperties();
        properties.put(CompositeConstant.prefix + propMessageVO.getIdentifier(), propMessageVO.getMessage());
        relation.setProperties(properties);
        nodeRelationRepository.save(relation, 1);

        // 修改反向关系
        String cypher = "MATCH (s:ENTITY)-[r:RELATION]-(e:ENTITY) where s.id=$startId and r.predicateId=$predicateId and e.id=$endId return s,r,e";
        List<NodeRelation> relationList = nodeRelationRepository.findRelationByCypher(cypher, Map.of("startId", relation.getEnd().getId(), "predicateId", relation.getPredicateId(), "endId", relation.getStart().getId()));
        relationList.forEach(predicateRelation -> {
            Map<String, Object> predicateProperties = predicateRelation.getProperties();
            properties.put(CompositeConstant.prefix + propMessageVO.getIdentifier(), propMessageVO.getMessage());
            predicateRelation.setProperties(predicateProperties);
            nodeRelationRepository.save(predicateRelation);
        });
    }

    /**
     * 模糊查询
     *
     * @param queryParam 查询条件
     * @return 节点集合
     */
    @Override
    public List<NodeEntity> getFilter(Map<String, Object> queryParam) {
        return nodeEntityRepository.findByCondition(queryParam);
    }

    @Override
    public Long countByFilter(Map<String, Object> queryParam) {
        return nodeEntityRepository.countByCondition(queryParam);
    }

    /**
     * 分类知识可视化
     *
     * @param classify 文件分类
     * @return EchartsVO
     */
    @Override
    public EchartsVO documentVisual(String classify, String showCount, String name) {
        EchartsVO echartsVO = new EchartsVO();
        Set<EchartsNode> nodes = new HashSet<>();
        Set<EchartsRelation> relations = new HashSet<>();
        List<KnowledgeConcept> concepts = conceptRepository.findByCondition(Filters.eq(FiledNameConst.NAME, "公文"));
        String conceptId = ObjectUtil.isNotEmpty(concepts) ? concepts.get(0).getId() : "";
        //查询到对应分类型的文件的id集合
        List<String> stringList = resourceFeign.documentIdsByClassify(classify).getData();
        if (ObjectUtil.isNotEmpty(stringList)) {
            stringList.forEach(id -> {
                EchartsVO visual = knowledgeAlignmentService.visual(id, null, null);
                Set<EchartsNode> visualNodes = ObjectUtil.isEmpty(visual.getNodes()) ? new HashSet<>() : visual.getNodes();
                visualNodes = visualNodes.stream().filter(node -> node.getName().contains(name) && node.getConceptId().equals(conceptId)).collect(Collectors.toSet());
                Set<EchartsRelation> visualRelations = ObjectUtil.isEmpty(visual.getRelations()) ? new HashSet<>() : visual.getRelations();
                nodes.addAll(visualNodes);
                relations.addAll(visualRelations);
            });
        }
        List<EchartsNode> nodeList = new ArrayList<>(nodes);
        Set<EchartsNode> newNodes = new HashSet<>();
        for (int i = 0; i < ("all".equals(showCount) ? nodes.size() : Math.min(Integer.parseInt(showCount), nodes.size())); i++) {
            newNodes.add(nodeList.get(i));
        }
        echartsVO.setNodes(newNodes);
        echartsVO.setRelations(relations);
        return echartsVO;
    }


    /**
     * 根据nodeId查找关联关系
     *
     * @param nodeId 实例id
     * @return 关系集合
     */
    public List<String> getRelationIdsByNodeId(String nodeId) {
        Set<NodeRelation> set = new HashSet<>();
        NodeEntity node = nodeInfo(nodeId);
        if (ObjectUtil.isNotEmpty(node.getFromEntity()))
            set.addAll(node.getFromEntity());
        if (ObjectUtil.isNotEmpty(node.getToEntity()))
            set.addAll(node.getToEntity());
        return set.stream().map(Neo4jAbstractBaseEntity::getId).collect(Collectors.toList());
    }

    /**
     * 节点详情
     *
     * @param id 节点id
     * @return 节点实体
     */
    @Override
    public NodeEntity nodeInfo(String id) {
        return nodeEntityRepository.findById(id);
    }


    public void setGroupRelation(NodeEntity node, List<List<NodeRelation>> groupRelation) {
        if (node.getFromEntity() != null && node.getToEntity() != null) {
            node.getToEntity().forEach(nodeRelation -> {
                List<NodeRelation> nodeRelations = new ArrayList<>();
                if (nodeRelation.isPositive()) {
                    nodeRelations.add(nodeRelation);
                } else {
                    nodeRelations.add(relationInfo(nodeRelation.getReverseRelationId(), 1));
                }
                groupRelation.add(nodeRelations);
            });
        }
    }

    /**
     * 查询公文包含的所有节点
     *
     * @param resourceId 资源id
     * @return 实例列表
     */
    @Override
    public List<NodeEntity> getNodeListByResourceId(String resourceId) {
        // cypher语句
        String entityCypher = "MATCH (n:ENTITY) WHERE ANY(x IN n.docIds WHERE x = $resourceId )  RETURN n";
        return nodeEntityRepository.findEntityByCypher(entityCypher, Map.of("resourceId", resourceId));
    }

    /**
     * 用于添加主实例的关系
     *
     * @param start 开始实例
     * @param end   结束实例
     * @return 实例关系
     */
    public NodeRelation getTopRelation(NodeEntity start, NodeEntity end) {
        NodeRelation nodeRelation = new NodeRelation();
        nodeRelation.setStart(start);
        nodeRelation.setEnd(end);
        nodeRelation.setPredicateName("包含");
        return nodeRelation;
    }

    /**
     * 用于添加主实例
     *
     * @param topId     主实例id
     * @param conceptId 概念id
     * @return 实例
     */
    public NodeEntity getTopEntity(String topId, String conceptId) {
        NodeEntity nodeEntity = new NodeEntity();
        nodeEntity.setId(topId);
        nodeEntity.setConceptId(conceptId);
        nodeEntity.setEntityName(conceptService.info(conceptId).getName());
        return nodeEntity;
    }

    public List<PropMessageVO> getNodePropValueVO(NodeEntity node) {
        List<PropMessageVO> nodePropValueVOS = new ArrayList<>();
        Map<String, Object> properties = node.getProperties();
        if (ObjectUtil.isEmpty(properties))
            return nodePropValueVOS;
        return propertiesForEach(nodePropValueVOS, properties, node.getId());
    }

    public List<PropMessageVO> propertiesForEach(List<PropMessageVO> propValueVOS, Map<String, Object> properties, String nodeId) {
        properties.forEach((s, o) -> {
            PropMessageVO propValueVO = new PropMessageVO();
            String propIdentifier = s.substring(s.lastIndexOf("_") + 1);
            Bson bson = Filters.eq("identifier", propIdentifier);
            KnowledgeProperty property = propertyService.findByBson(bson).get(0);
            propValueVO.setNodeId(nodeId);
            propValueVO.setName(property.getName());
            propValueVO.setType(property.getDatatype());
            propValueVO.setMessage(o.toString());
            propValueVO.setIdentifier(propIdentifier);
            propValueVOS.add(propValueVO);
        });
        return propValueVOS;
    }


    /**
     * 关系添加
     *
     * @param relationDTO
     */
    @Override
    public void saveRelation(NodeRelationDTO relationDTO) {
        //数据准备
        NodeEntity start = nodeInfo(relationDTO.getStartNodeId());
        NodeEntity end = nodeInfo(relationDTO.getEndNodeId());
        KnowledgeRelationVO relationVO = relationService.infoVO(relationDTO.getRelationId());
        //给新增的关系添加属性
        Map<String, Object> map = new HashMap<>();
        relationVO.getProperties().forEach(pro -> map.put(CompositeConstant.prefix + pro.getIdentifier(), ""));
        //非空判断
        if (ObjectUtil.isEmpty(start) || ObjectUtil.isEmpty(end))
            throw new ServiceException(ExceptionMessageConst.NODE_NOT_EXIST);
        //设置关系一
        NodeRelation nodeRelationOne = new NodeRelation();
        nodeRelationOne.setStart(start);
        nodeRelationOne.setEnd(end);
        nodeRelationOne.setProperties(map);
        nodeRelationOne.setPredicateId(relationDTO.getRelationId());
        //设置关系二
        NodeRelation nodeRelationTwo = new NodeRelation();
        nodeRelationTwo.setStart(end);
        nodeRelationTwo.setEnd(start);
        nodeRelationTwo.setProperties(map);
        nodeRelationTwo.setPredicateId(relationDTO.getRelationId());
        //根据conceptId判断正反向关系
        if (start.getConceptId().equals(relationVO.getConceptOne()) && end.getConceptId().equals(relationVO.getConceptTwo())) {
            nodeRelationOne.setPredicateName(relationVO.getForwardPre());
            nodeRelationOne.setPositive(true);
            nodeRelationTwo.setPredicateName(relationVO.getInversePre());
            nodeRelationTwo.setPositive(false);
        } else {
            nodeRelationOne.setPredicateName(relationVO.getInversePre());
            nodeRelationOne.setPositive(false);
            nodeRelationTwo.setPredicateName(relationVO.getForwardPre());
            nodeRelationTwo.setPositive(true);
        }
        //判断主语是否是公文/制度，给宾语添加docId
        addObjectDocId(nodeRelationOne);
        addSubjectDocId(nodeRelationTwo);
        //关系上添加docID
        nodeRelationOne.getDocIds().addAll(end.getDocIds());
        nodeRelationOne.getDocIds().addAll(start.getDocIds());
        nodeRelationOne.setDocIds(nodeRelationOne.getDocIds());

        nodeRelationTwo.getDocIds().addAll(end.getDocIds());
        nodeRelationTwo.getDocIds().addAll(start.getDocIds());
        nodeRelationTwo.setDocIds(nodeRelationTwo.getDocIds());

        //添加两个关系
        NodeRelation relationOne = nodeRelationRepository.save(nodeRelationOne);
        nodeRelationTwo.setReverseRelationId(relationOne.getId());
        NodeRelation relationTwo = nodeRelationRepository.save(nodeRelationTwo);
        relationOne.setReverseRelationId(relationTwo.getId());
        //关联两条关系
        nodeRelationRepository.save(relationOne);
    }


    private void addObjectDocId(NodeRelation nodeRelation) {
        KnowledgeConcept zdConcept = conceptService.findByConceptName(ModelConceptConst.CONCEPT_ZHIDU);
        KnowledgeConcept gwConcept = conceptService.findByConceptName(ModelConceptConst.CONCEPT_GONGWEN);
        NodeEntity start = nodeRelation.getStart();
        if (start.getConceptId().equals(zdConcept.getId()) ||
                start.getConceptId().equals(gwConcept.getId())) {
            //只添加公文、制度作为主语时，宾语中的docId
            Set<String> docIds = start.getDocIds();
            NodeEntity end = nodeRelation.getEnd();
            end.getDocIds().addAll(docIds);
        }
    }

    private void addSubjectDocId(NodeRelation nodeRelation) {
        KnowledgeConcept zdConcept = conceptService.findByConceptName(ModelConceptConst.CONCEPT_ZHIDU);
        KnowledgeConcept gwConcept = conceptService.findByConceptName(ModelConceptConst.CONCEPT_GONGWEN);
        NodeEntity end = nodeRelation.getEnd();
        if (end.getConceptId().equals(zdConcept.getId()) ||
                end.getConceptId().equals(gwConcept.getId())) {
            //只添加公文、制度作为主语时，宾语中的docId
            Set<String> docIds = end.getDocIds();
            NodeEntity start = nodeRelation.getStart();
            start.getDocIds().addAll(docIds);
        }
    }

    /**
     * 关系详情
     *
     * @param id    关系id
     * @param depth 深度
     * @return NodeRelation
     */
    @Override
    public NodeRelation relationInfo(String id, int depth) {
        return nodeRelationRepository.findById(id, depth);
    }

    /**
     * 关系VO
     *
     * @param id 关系id
     * @return NodeRelation
     */
    @Override
    public NodeRelationVO relationVOInfo(String id) {
        NodeRelation relation = relationInfo(id, 1);
        NodeRelationVO relationVO = BeanUtil.copyProperties(relation, NodeRelationVO.class);
        relationVO.setPropValueVOS(getRelationPropValueVO(relation));
        return relationVO;
    }

    public List<PropMessageVO> getRelationPropValueVO(NodeRelation relation) {
        List<PropMessageVO> propValueVOS = new ArrayList<>();
        Map<String, Object> properties = relation.getProperties();
        if (ObjectUtil.isEmpty(properties))
            return propValueVOS;
        return propertiesForEach(propValueVOS, properties, relation.getId());
    }


}
