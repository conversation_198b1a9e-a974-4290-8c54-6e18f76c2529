package org.irm.lab.kg.controller.node;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.http.HttpUtil;
import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.irm.lab.common.annotation.MyLog;
import org.irm.lab.common.api.R;
import org.irm.lab.common.constant.AuthConstant;
import org.irm.lab.common.constant.LogConstant;
import org.irm.lab.common.support.MyPage;
import org.irm.lab.common.utils.Func;
import org.irm.lab.common.utils.ThreadLocalUtil;
import org.irm.lab.config.entity.Label;
import org.irm.lab.config.entity.WorkTask;
import org.irm.lab.config.feign.LabelFeign;
import org.irm.lab.config.repository.WorkTaskRepository;
import org.irm.lab.kg.constant.CompositeConstant;
import org.irm.lab.kg.entity.KnowledgeRelation;
import org.irm.lab.kg.entity.neo4j.node.NodeEntity;
import org.irm.lab.kg.entity.neo4j.node.NodeRelation;
import org.irm.lab.kg.repository.KnowledgeRelationRepository;
import org.irm.lab.kg.repository.neo4j.node.NodeEntityRepository;
import org.irm.lab.kg.repository.neo4j.node.NodeRelationRepository;
import org.irm.lab.kg.service.IDocumentUnitService;
import org.irm.lab.kg.service.INodeEntityService;
import org.irm.lab.kg.vo.DocumentUnitVO;
import org.irm.lab.kg.vo.echarts.EchartsVO;
import org.irm.lab.repository.feign.ResourceFeign;
import org.neo4j.ogm.cypher.ComparisonOperator;
import org.neo4j.ogm.cypher.Filter;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2023-02-09
 */

@RestController
@RequestMapping("/knowledge-node")
@Api(value = "知识实例")
@Slf4j
public class NodeEntityController {
    @Resource
    private INodeEntityService nodeEntityService;

    @ApiOperation("分页获取实例")
    @GetMapping("/page")
    public R<MyPage<NodeEntity>> page(@RequestParam Map<String, Object> queryParam) {
        // 获取分页条件
        Integer page = Convert.toInt(queryParam.getOrDefault("page", 1));
        Integer size = Convert.toInt(queryParam.getOrDefault("size", 10));
        return R.data(nodeEntityService.page(queryParam, page, size, 0));
    }

    @ApiOperation("新增实例")
    @PostMapping("/save")
    public R<NodeEntity> save(@RequestBody NodeEntity nodeEntity) {
        return R.data(nodeEntityService.save(nodeEntity));
    }

    @ApiOperation("修改实例名称")
    @GetMapping("/update-name")
    public R<NodeEntity> updateName(@RequestParam String nodeId, String nodeName) {
        return R.data(nodeEntityService.updateName(nodeId, nodeName));
    }


    @ApiOperation("实例详情")
    @GetMapping("/info")
    public R<? extends NodeEntity> info(@RequestParam String id) {
        return R.data(nodeEntityService.info(id, 0));
    }

    @MyLog(menu = LogConstant.MENU_VIEW, dataType = LogConstant.DATA_ENTITY, operation = LogConstant.OPERATION_REMOVE)
    @ApiOperation("删除实例")
    @PostMapping("/remove")
    public R<Void> remove(@RequestBody String ids) {
        nodeEntityService.remove(Func.objToStrList(ids));
        return R.success();
    }

    @ApiOperation("图谱-概念获取实例")
    @GetMapping("/echarts-node-by-concept")
    public R<EchartsVO> echartsNodesByConcept(@RequestParam String conceptId, @RequestParam String modelId, @RequestParam String showCount) {
        return R.data(nodeEntityService.echartsNodesByConcept(conceptId, modelId, showCount));
    }

    @ApiOperation("图谱-实例及其关系")
    @GetMapping("/echarts-node-with-relation")
    public R<EchartsVO> echartsNodesWithRelation(@RequestParam String nodeId, @RequestParam String showCount) {
        return R.data(nodeEntityService.echartsNodesWithRelation(nodeId, showCount));
    }


    @ApiOperation("脚本-补充提及机构部门、提及人员的实例和边")
    @GetMapping("/script-supply-node-relation")
    public R<Void> scriptSupplyNodeAndRelation(@RequestParam String resourceId, @RequestParam String predicate1, @RequestParam String predicate2) {
        genEntityOfDepartment(resourceId, predicate1, predicate2);
        return R.success();
    }

    @ApiOperation("脚本-补充提及机构部门、提及人员的实例和边-by任务")
    @GetMapping("/script-supply-node-relation-work")
    public R<Void> scriptSupplyNodeAndRelationWork(@RequestParam String workId, @RequestParam String predicate1, @RequestParam String predicate2) {
        genEntityOfDepartmentWork(workId, predicate1, predicate2);
        return R.success();
    }


    @Resource
    private WorkTaskRepository workTaskRepository;
    @Resource
    private LabelFeign labelFeign;
    @Resource
    private NodeEntityRepository nodeEntityRepository;
    @Resource
    private KnowledgeRelationRepository knowledgeRelationRepository;
    @Resource
    private NodeRelationRepository nodeRelationRepository;
    @Resource
    private ResourceFeign resourceFeign;
    @Resource
    private IDocumentUnitService iDocumentUnitService;


//    public static final String relation1 = "发文单位";
//    public static final String relation2 = "发文人员";

    public static final String relation3 = "提及机构/部门";
    public static final String relation4 = "提及人员";

    public static Label gong_wen = new Label();
    public static Label ji_gou = new Label();
    public static Label ren_yuan = new Label();
    public static Label ti_ji_ji_gou = new Label();
    public static Label ti_ji_ren_yuan = new Label();

    void genEntityOfDepartment(String resourceId, String predicate1, String predicate2) {
        //可自行选择需要处理的任务
        org.irm.lab.repository.entity.Resource prepareResource = resourceFeign.info(resourceId).getData();
        WorkTask workTask = workTaskRepository.findById(prepareResource.getWorkTaskId());
        String dataSetId = workTask.getProcess().getDataSetId();
        List<Label> labelOfConcept = labelFeign.list(Map.of("dataSetId", dataSetId, "type", "概念")).getData();
        List<Label> labelOfRelation = labelFeign.list(Map.of("dataSetId", dataSetId, "type", "关系")).getData();
        for (Label label : labelOfConcept) {
            if ("公文".equals(label.getName())) {
                gong_wen = label;
            } else if ("机构/部门".equals(label.getName())) {
                ji_gou = label;
            } else if ("人员".equals(label.getName())) {
                ren_yuan = label;
            }
        }
        for (Label label : labelOfRelation) {
            if (relation3.equals(label.getName())) {
                ti_ji_ji_gou = label;
            } else if (relation4.equals(label.getName())) {
                ti_ji_ren_yuan = label;
            }
        }
//        List<org.irm.lab.repository.entity.Resource> resourceList = resourceFeign.listByCondition(Map.of("knowledgeMatchConfirm", true, "workTaskId", workTaskId)).getData();

        for (org.irm.lab.repository.entity.Resource resource : List.of(prepareResource)) {
            if (resource.getTopNodeId() == null) continue;
            NodeEntity topNode = nodeEntityRepository.findById(resource.getTopNodeId());
            if (topNode == null) continue;
            log.info("当前资源名称：【{}】", resource.getName());
            JSONObject params = JSONUtil.createObj();
            params.putOpt("resourceId", resource.getId()).putOpt("page", resource.getPages());
            List<DocumentUnitVO> documentUnitVOS = iDocumentUnitService.currentPageList(params);
            if (ObjectUtil.isNotEmpty(documentUnitVOS)) {
                DocumentUnitVO documentUnitVO = documentUnitVOS.get(documentUnitVOS.size() - 1);
                String content = documentUnitVO.getContent();
                int contentLength = content.length();
                if (ObjectUtil.isNotEmpty(content) && content.contains("发送")) {
                    // 确保字符串长度大于500
                    if (contentLength >= 500) {
                        // 从字符串末尾向前数500个字符开始截取
                        content = content.substring(contentLength - 500);
                    } else {
                        // 如果字符串长度小于或等于500，则返回整个字符串
                        content = content.substring(0, contentLength);
                    }
                    content = content.substring(content.lastIndexOf("发送"));
                    try {
                        JSONObject entries = paseAlgo(postAlgo(content, predicate1, predicate2));
                        Object data = entries.get("data");
                        if (ObjectUtil.isNotEmpty(data)) {
                            buildNodeAndRelation(JSONUtil.parseObj(data), documentUnitVO.getId(), resource.getId(), topNode, predicate1, predicate2);
                        }
                    } catch (Exception e) {
                        log.error("算法异常：【{}】", e.getMessage());
                        log.error("异常资源ID【{}】，名称：【{}】", resource.getId(), resource.getName());
                    }
                }
                log.info(String.valueOf(documentUnitVOS.size()));
            }
        }
    }


    void genEntityOfDepartmentWork(String workId, String predicate1, String predicate2) {
        //可自行选择需要处理的任务
        WorkTask workTask = workTaskRepository.findById(workId);
        String dataSetId = workTask.getProcess().getDataSetId();
        List<Label> labelOfConcept = labelFeign.list(Map.of("dataSetId", dataSetId, "type", "概念")).getData();
        List<Label> labelOfRelation = labelFeign.list(Map.of("dataSetId", dataSetId, "type", "关系")).getData();
        for (Label label : labelOfConcept) {
            if ("公文".equals(label.getName())) {
                gong_wen = label;
            } else if ("机构/部门".equals(label.getName())) {
                ji_gou = label;
            } else if ("人员".equals(label.getName())) {
                ren_yuan = label;
            }
        }
        for (Label label : labelOfRelation) {
            if (relation3.equals(label.getName())) {
                ti_ji_ji_gou = label;
            } else if (relation4.equals(label.getName())) {
                ti_ji_ren_yuan = label;
            }
        }
        List<org.irm.lab.repository.entity.Resource> resourceList = resourceFeign.listByCondition(Map.of("knowledgeMatchConfirm", true, "workTaskId", workId)).getData();

        for (org.irm.lab.repository.entity.Resource resource : resourceList) {
            if (resource.getTopNodeId() == null) continue;
            NodeEntity topNode = nodeEntityRepository.findById(resource.getTopNodeId());
            if (topNode == null) continue;
            log.info("当前资源名称：【{}】", resource.getName());
            JSONObject params = JSONUtil.createObj();
            params.putOpt("resourceId", resource.getId()).putOpt("page", resource.getPages());
            List<DocumentUnitVO> documentUnitVOS = iDocumentUnitService.currentPageList(params);
            if (ObjectUtil.isNotEmpty(documentUnitVOS)) {
                DocumentUnitVO documentUnitVO = documentUnitVOS.get(documentUnitVOS.size() - 1);
                String content = documentUnitVO.getContent();
                int contentLength = content.length();
                if (ObjectUtil.isNotEmpty(content) && content.contains("发送")) {
                    // 确保字符串长度大于500
                    if (contentLength >= 500) {
                        // 从字符串末尾向前数500个字符开始截取
                        content = content.substring(contentLength - 500);
                    } else {
                        // 如果字符串长度小于或等于500，则返回整个字符串
                        content = content.substring(0, contentLength);
                    }
                    content = content.substring(content.lastIndexOf("发送"));
                    try {
                        JSONObject entries = paseAlgo(postAlgo(content, predicate1, predicate2));
                        Object data = entries.get("data");
                        if (ObjectUtil.isNotEmpty(data)) {
                            buildNodeAndRelation(JSONUtil.parseObj(data), documentUnitVO.getId(), resource.getId(), topNode, predicate1, predicate2);
                        }
                    } catch (Exception e) {
                        log.error("算法异常：【{}】", e.getMessage());
                        log.error("异常资源ID【{}】，名称：【{}】", resource.getId(), resource.getName());
                    }
                }
                log.info(String.valueOf(documentUnitVOS.size()));
            }
        }
    }


    /**
     * 构建节点和节点与top节点的关系
     */
    void buildNodeAndRelation(JSONObject entries, String unitID, String docId, NodeEntity topNode, String predicate1, String predicate2) {
        if (entries.containsKey(predicate1)) {
            JSONArray jsonArray = entries.getJSONArray(predicate1);
            List<NodeEntity> node = createNode(jsonArray.toList(String.class), ji_gou, topNode.getModelId(), unitID, docId);
            creatRelation(topNode, node, ti_ji_ji_gou, docId);
        }
        if (entries.containsKey(predicate2)) {
            JSONArray jsonArray = entries.getJSONArray(predicate2);
            List<NodeEntity> node = createNode(jsonArray.toList(String.class), ren_yuan, topNode.getModelId(), unitID, docId);
            creatRelation(topNode, node, ti_ji_ren_yuan, docId);
        }

    }

    /**
     * 构建节点与top节点的关系
     */
    void creatRelation(NodeEntity topNode, List<NodeEntity> nodeEntities, Label label, String docId) {
        KnowledgeRelation knowledgeRelation = knowledgeRelationRepository.findById(label.getSourceId());
        for (NodeEntity nodeEntity : nodeEntities) {
            NodeRelation forwardRelation = new NodeRelation();
            forwardRelation.setPositive(true)
                    .setPredicateName(knowledgeRelation.getForwardPre())
                    .setPredicateId(knowledgeRelation.getId())
                    .setStart(topNode)
                    .setEnd(nodeEntity);
            forwardRelation.getDocIds().add(docId);
            nodeRelationRepository.save(forwardRelation);
            log.info("创建正向关系：name：【{}】,id:【{}】", forwardRelation.getPredicateName(), forwardRelation.getId());
//            if (!knowledgeRelation.getIsSymmetric()) {
//                NodeRelation inverseRelation = new NodeRelation();
//                inverseRelation.setPositive(false).setPredicateName(knowledgeRelation.getInversePre())
//                        .setPredicateId(knowledgeRelation.getId())
//                        .setStart(nodeEntity)
//                        .setEnd(topNode);
//                inverseRelation.getDocIds().add(docId);
//                nodeRelationRepository.save(inverseRelation);
//                log.info("创建反向关系：name：【{}】,id:【{}】", inverseRelation.getPredicateName(), inverseRelation.getId());
//            }
        }

    }

    /**
     * 构建节点
     */
    List<NodeEntity> createNode(List<String> names, Label label, String modelId, String unitID, String docId) {
        List<NodeEntity> nodeEntities = new ArrayList<>();
        for (String name : names) {
            if ("人员".equals(label.getName()) && name.length() > 4) {
                log.info("跳过人员节点【{}】", name);
                continue;
            }
            if (name.contains("领导") || name.contains("总助总师") || name.contains("副总师") || name.contains("华能") || "公司".equals(name)) {
                log.info("跳过错误节点【{}】", name);
                continue;
            }
            org.neo4j.ogm.cypher.Filters filters = new org.neo4j.ogm.cypher.Filters(new Filter("entityName", org.neo4j.ogm.cypher.ComparisonOperator.EQUALS, name));
            filters.and(new Filter("conceptId", ComparisonOperator.EQUALS, label.getSourceId()));
            List<NodeEntity> nodeEntityList = nodeEntityRepository.findByCondition(filters);

            if (CollectionUtil.isNotEmpty(nodeEntityList)) {
                //只更新一个节点
                NodeEntity oneEntity = nodeEntityList.get(0);
                oneEntity.getDocIds().add(docId);

                Map<String, Object> properties = oneEntity.getProperties();
                properties.put(CompositeConstant.prefix + "script", "1");
                oneEntity.setProperties(properties);

                oneEntity.getDocumentUnitIds().add(unitID);
                nodeEntities.add(nodeEntityRepository.save(oneEntity));
                log.info("更新节点：name：【{}】,id:【{}】", name, oneEntity.getId());
            } else {
                NodeEntity nodeEntity = new NodeEntity();
                nodeEntity.getDocIds().add(docId);
                nodeEntity.getDocumentUnitIds().add(unitID);

                Map<String, Object> properties = nodeEntity.getProperties();
                properties.put(CompositeConstant.prefix + "script", "1");
                nodeEntity.setProperties(properties);

                nodeEntity.setEntityName(name).setConceptId(label.getSourceId()).setTopNodeEntity(false).setModelId(modelId);
                nodeEntities.add(nodeEntityRepository.save(nodeEntity));
                log.info("新增节点：name：【{}】,id:【{}】", name, nodeEntity.getId());
            }
        }
        return nodeEntities;
    }

    /**
     * 解析算法返回结果
     */
    JSONObject paseAlgo(String content) {
        //处理算法返回结果
        JSONObject result = JSONUtil.createObj();
        if (!JSONUtil.isTypeJSON(content)) return JSONUtil.createObj();
        JSONObject entries = JSONUtil.parseObj(content);
        //设置机构/部门、人员
//        JSONObject data = entries.getJSONObject("data");
//        if (data.containsKey(relation1)) {
//            result.putOpt(relation1, entries.getJSONArray(relation1));
//        } else if (data.containsKey(relation2)) {
//            result.putOpt(relation2, entries.getJSONArray(relation2));
//        }
        return entries;
    }


    String postAlgo(String content, String predicate1, String predicate2) {
        JSONObject obj = JSONUtil.createObj();
        obj.putOpt("text", content);
        JSONArray predicates = JSONUtil.createArray();
//        predicates.add(relation1);
//        predicates.add(relation2);
        predicates.add(predicate1);
        if (ObjectUtil.isNotEmpty(predicate2)) {
            predicates.add(predicate2);
        }
        obj.putOpt("predicates", predicates.toString());
        String url = "http://10.244.64.60:8080/api/kg/schema/infer/ie";
        log.info("提供算法参数：{}", obj);
        String post = HttpUtil.post(url, obj.toString());
        log.info("算法返回结果：{}", post);
        return post;
    }
}
