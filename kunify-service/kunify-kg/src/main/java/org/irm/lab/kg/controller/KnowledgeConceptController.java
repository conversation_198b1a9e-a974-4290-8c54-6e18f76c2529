package org.irm.lab.kg.controller;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.irm.lab.common.annotation.MyLog;
import org.irm.lab.common.api.R;
import org.irm.lab.common.constant.LogConstant;
import org.irm.lab.common.utils.Func;
import org.irm.lab.kg.entity.KnowledgeConcept;
import org.irm.lab.kg.entity.KnowledgeProperty;
import org.irm.lab.kg.feign.KnowledgeConceptFeign;
import org.irm.lab.kg.service.IKnowledgeConceptService;
import org.irm.lab.kg.vo.KnowledgeConceptVO;
import org.irm.lab.kg.vo.ReferenceMapVO;
import org.irm.lab.kg.vo.echarts.EchartsVO;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;
import java.util.Set;

@RestController
@RequestMapping("/knowledge-concept")
@Api(value = "知识概念")
public class KnowledgeConceptController implements KnowledgeConceptFeign {

    @Resource
    private IKnowledgeConceptService knowledgeConceptService;


    @ApiOperation(value = "树形式")
    @GetMapping("/tree-list")
    public R<List<KnowledgeConceptVO>> listTree() {
        return R.data(knowledgeConceptService.listTree());
    }

    @ApiOperation(value = "概念列表")
    @GetMapping("/list")
    public R<List<KnowledgeConcept>> list() {
        return R.data(knowledgeConceptService.list());
    }

    @MyLog(menu = LogConstant.MENU_VIEW, dataType = LogConstant.DATA_CONCEPT, operation = LogConstant.OPERATION_ADD_OR_ALTER)
    @ApiOperation(value = "概念增改")
    @PostMapping("/save")
    public R<KnowledgeConcept> save(@Validated @RequestBody KnowledgeConcept knowledgeConcept) {
        return R.data(knowledgeConceptService.save(knowledgeConcept));
    }

    /**
     * 获取当前概念可用属性
     *
     * @param conceptId 关系Id
     * @return 可用属性列表
     */
    @ApiOperation(value = "获取当前关系可用属性", hidden = true)
    @GetMapping("/list-available-property")
    public R<List<KnowledgeProperty>> listAvailableProperty(@RequestParam String conceptId) {
        return R.data(knowledgeConceptService.listAvailableProperty(conceptId));
    }


    /**
     * 概念引用属性
     *
     * @param id  概念Id
     * @param ids 属性Id列表
     * @return {@link KnowledgeConcept}
     */
    @MyLog(menu = LogConstant.CONFIGURE_THE_HUB, dataType = LogConstant.DATA_CONCEPT, operation = LogConstant.OPERATION_CITE)
    @ApiOperation(value = "概念绑定属性")
    @PostMapping("/bind")
    public R<KnowledgeConcept> bindProp(@RequestParam String id, @RequestBody String ids) {
        return R.data(knowledgeConceptService.bind(id, Func.objToStrList(ids)));
    }


    /**
     * 取消概念属性引用
     *
     * @param id  概念Id
     * @param ids 属性Id
     */
    @MyLog(menu = LogConstant.CONFIGURE_THE_HUB, dataType = LogConstant.DATA_CONCEPT, operation = LogConstant.OPERATION_DEREFERENCE)
    @ApiOperation(value = "取消概念属性引用")
    @PostMapping("/remove-concept-property")
    public R<Void> removeProperty(@RequestParam String id, @RequestBody String ids) {
        knowledgeConceptService.removeRelationProperty(id, Func.objToStrList(ids));
        return R.success();
    }


    @ApiOperation(value = "概念已被模型绑定的属性", hidden = true)
    @GetMapping("/already-bind-prop")
    public R<List<String>> alreadyBindModelProperty(@RequestParam String conceptId) {
        return R.data(knowledgeConceptService.alreadyBindModelProperty(conceptId));
    }

    @ApiOperation(value = "概念详情")
    @GetMapping("/info-vo")
    public R<KnowledgeConceptVO> infoVO(@RequestParam String id) {
        return R.data(knowledgeConceptService.infoVO(id));
    }

    @ApiOperation(value = "概念详情")
    @GetMapping("/info")
    public R<KnowledgeConcept> info(@RequestParam String id) {
        return R.data(knowledgeConceptService.info(id));
    }

    @ApiOperation(value = "概念详情-图结构")
    @GetMapping("/info-map")
    public R<EchartsVO> infoMap(String id) {
        return R.data(knowledgeConceptService.infoMap(id));
    }

    @ApiOperation(value = "模型概念列表")
    @GetMapping("/concept-by-model")
    public R<List<ReferenceMapVO>> conceptListByModelId(@RequestParam String modelId) {
        return R.data(knowledgeConceptService.conceptListByModelId(modelId));
    }

    @MyLog(menu = LogConstant.MENU_VIEW, dataType = LogConstant.DATA_CONCEPT, operation = LogConstant.OPERATION_REMOVE)
    @ApiOperation(value = "概念删除")
    @PostMapping(value = "/remove")
    public R<Void> remove(@RequestBody String ids) {
        knowledgeConceptService.remove(Func.objToStrList(ids));
        return R.success();
    }

    /**
     * 根据Id列表获取多个概念
     *
     * @param ids id列表
     * @return {@link KnowledgeConcept}
     */
    @ApiOperation(value = "根据ids查询", hidden = true)
    @GetMapping("/find-by-ids")
    public R<List<KnowledgeConcept>> findByIds(@RequestParam List<String> ids) {
        return R.data(knowledgeConceptService.findByIds(ids));
    }

    /**
     * 查询当前概念下的所有子概念id
     *
     * @param parentConceptId 当前概念id
     * @return ids(不包含当前id)
     */
    @ApiOperation(value = "查询当前概念下的所有子概念id", hidden = true)
    @GetMapping("/get-child-concept-ids")
    public R<Set<String>> getAllChildConceptIds(@RequestParam String parentConceptId, @RequestParam Set<String> conceptIds) {
        return R.data(knowledgeConceptService.getAllChildConceptIds(parentConceptId, conceptIds));
    }

    /**
     * 通过概念名称获取概念对象
     *
     * @param name 概念名称
     * @return {@link KnowledgeConcept}
     */
    @GetMapping("/find-by-concept-name")
    public R<KnowledgeConcept> findByConceptName(@RequestParam String name){
        return R.data(knowledgeConceptService.findByConceptName(name));
    }

    @GetMapping("/find-all-delete")
    public R<List<KnowledgeConcept>> findAllDelete() {
        return R.data(knowledgeConceptService.findAllDelete());
    }
}
