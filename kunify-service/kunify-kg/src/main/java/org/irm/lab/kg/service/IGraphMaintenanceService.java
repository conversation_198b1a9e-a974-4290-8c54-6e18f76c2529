package org.irm.lab.kg.service;

import org.irm.lab.common.support.MyPage;
import org.irm.lab.kg.dto.NodeRelationDTO;
import org.irm.lab.kg.entity.KnowledgeProperty;
import org.irm.lab.kg.entity.neo4j.node.NodeEntity;
import org.irm.lab.kg.entity.neo4j.node.NodeRelation;
import org.irm.lab.kg.vo.PropMessageVO;
import org.irm.lab.kg.vo.echarts.EchartsVO;
import org.irm.lab.kg.vo.node.NodeEntityVO;
import org.irm.lab.kg.vo.node.NodeRelationVO;

import java.util.List;
import java.util.Map;

public interface IGraphMaintenanceService {

    /**
     * 根据概念Id获取当前概念下的所有实例
     *
     * @param conceptId 概念Id
     * @param modelId   模型Id
     * @param showCount 节点数量
     * @return {@link  EchartsVO}
     */
    EchartsVO echartsNodesByConcept(String conceptId, String modelId, String showCount);

    /**
     * 新增实例
     *
     * @param nodeEntity {@link NodeEntity}
     * @return {@link NodeEntity}
     */
    NodeEntity saveNode(NodeEntity nodeEntity);

    /**
     * 删除实例
     *
     * @param ids 实例Id
     */
    void nodeRemove(List<String> ids);

    /**
     * 图谱维护可视化 ===> 获取指定节点拓展关系和节点（深度为1）
     *
     * @param nodeId    实例id
     * @param modelId   模型id 若为主模型 值传 0
     * @param showCount 展示数量
     * @return {@link EchartsVO}
     */
    EchartsVO echartsNodesWithRelation(String nodeId, String modelId, String showCount);

    /**
     * 实例详情
     *
     * @param id 实例id
     * @return NodeEntityVO
     */
    NodeEntityVO nodeVOInfo(String id);


    /**
     * 获取该模型下的指定概念的属性列表
     *
     * @param modelId   模型Id
     * @param conceptId 概念Id
     * @return {@link KnowledgeProperty}
     */
    List<KnowledgeProperty> listPropertyByModelId(String modelId, String conceptId);

    /**
     * 获取该模型下的指定关系的属性列表
     *
     * @param modelId    模型Id
     * @param relationId 关系Id
     * @return {@link KnowledgeProperty}
     */
    List<KnowledgeProperty> listRelationProperty(String modelId, String relationId);

    /**
     * 新增或修改实例的属性
     *
     * @param propMessageVO {@link PropMessageVO}
     */
    void updateNodeProperties(PropMessageVO propMessageVO);

    /**
     * 新增或修改关系的属性
     *
     * @param propMessageVO {@link PropMessageVO}
     */
    void updateRelationProperties(PropMessageVO propMessageVO);

    /**
     * 关系删除
     *
     * @param ids 关系Id
     */
    void relationRemove(List<String> ids);

    /**
     * 删除实例属性
     *
     * @param propMessageVO {@link PropMessageVO}
     */
    void removeNodeProperty(PropMessageVO propMessageVO);

    /**
     * 删除关系属性
     *
     * @param propMessageVO {@link PropMessageVO}
     */
    void removeRelationProperty(PropMessageVO propMessageVO);

    /**
     * 根据概念和关系获取可用实例
     *
     * @param conceptId 概念Id
     * @param nodeId    实例Id
     * @param modelId   实例Id
     * @return {@link NodeEntity}
     */
    List<NodeEntity> listNodeRelation(String conceptId, String nodeId, String modelId);

    MyPage<NodeEntity> pageNodeRelation(String conceptId, String nodeId, String modelId, Integer page, Integer size, String entityName);


    MyPage<NodeEntity> nodePage(Map<String, Object> queryParam, int page, int size, int depth);


    List<NodeEntity> nodeList(String conceptId, String modelId);

    void saveRelation(NodeRelationDTO relationDTO);

    NodeEntity nodeInfo(String id);


    NodeRelation relationInfo(String id, int depth);

    NodeRelationVO relationVOInfo(String id);


    List<NodeEntity> getNodeListByResourceId(String resourceId);


    List<NodeEntity> getFilter(Map<String, Object> queryParam);

    EchartsVO documentVisual(String classify, String showCount, String name);

    Long countByFilter(Map<String, Object> queryParam);

}
