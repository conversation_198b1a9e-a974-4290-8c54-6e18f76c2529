package org.irm.lab.kg.service;

import cn.hutool.json.JSONArray;
import org.irm.lab.kg.entity.processing.CacheLabelData;
import org.irm.lab.kg.entity.processing.CacheTripleLabelData;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/5/8 10:19
 * @description 规则解析树结构业务接口
 */
public interface IRuleParsingGraphService {


    /**
     * 规则解析 ===> 获取标签数据树
     *
     * @param resourceId 资源Id
     * @param dataSetId  数据集Id
     * @return 标签数据
     */
    JSONArray relevanceShow(String resourceId, String dataSetId);


    /**
     * 获取该资源下的所有标签数据
     *
     * @param resourceId 资源Id
     * @return {@link CacheLabelData}
     */
    List<CacheLabelData> listCacheLabelData(String resourceId);


    /**
     * 新增或修改缓存标签数据实例
     *
     * @param cacheLabelData {@link CacheLabelData}
     */
    void saveOrUpdateLabelData(CacheLabelData cacheLabelData);

    /**
     * 删除实例/实例的属性
     *
     * @param cacheLabelData 缓存标签数据实例
     */
    void removeLabelData(CacheLabelData cacheLabelData);

    /**
     * 新增/修改关系三元组
     *
     * @param cacheTripleLabelData {@link CacheTripleLabelData}
     */
    void saveOrUpdateTripleLabelData(List<CacheTripleLabelData> cacheTripleLabelData);


    /**
     * 删除关系三元组/属性
     *
     * @param cacheTripleLabelData 关系三元组
     */
    void removeTripleLabelData(CacheTripleLabelData cacheTripleLabelData);

    /**
     * 规则解析确认  ===> 规则解析结果确认
     *
     * @param resourceId 资源Id
     */
    void parsingConfirm(String resourceId);


    /**
     * 重新进行规则解析
     *
     * @param resourceId 资源Id
     */
    void reRuleParsing(String resourceId);


}
