package org.irm.lab.kg.service;

import cn.hutool.json.JSONArray;
import org.irm.lab.kg.entity.processing.LabelData;
import org.irm.lab.kg.entity.processing.TripleLabelData;

import java.util.List;

/**
 * 标签的类图形展示
 *
 * <AUTHOR>
 * @date 2023/3/13 18:39
 */
public interface ILabelGraphService {


    /**
     * 知识匹配 ===> 获取标签数据树
     *
     * @param resourceId 资源Id
     * @param dataSetId  数据集Id
     * @return 标签数据
     */
    JSONArray relevanceShow(String resourceId, String dataSetId);

    /**
     * 获取该资源下的所有标签数据
     *
     * @param resourceId 资源Id
     * @return {@link LabelData}
     */
    List<LabelData> listLabelData(String resourceId);

    /**
     * 新增关系三元组
     *
     * @param tripleLabelDataList {@link TripleLabelData}
     */
    void saveOrUpdateTripleLabelData(List<TripleLabelData> tripleLabelDataList);

    /**
     * 匹配确认
     *
     * @param resourceId 资源Id
     * @param dataSetId  数据集Id
     * @param modelId    模型Id
     */
    void matchConfirm(String resourceId, String dataSetId, String modelId);

    /**
     * 批量匹配确认
     *
     * @param resourceIds 资源Ids
     * @param dataSetId   数据集Id
     * @param modelId     模型Id
     */
    void matchConfirmMulti(List<String> resourceIds, String dataSetId, String modelId);

    /**
     * 重新匹配确认
     *
     * @param resourceId 资源Id
     * @param dataSetId  数据集Id
     * @param modelId    模型Id
     */
    void reMatchConfirm(String resourceId, String dataSetId, String modelId);


    /**
     * 新增或修改标签数据对象
     *
     * @param labelData {@link LabelData}
     */
    void saveOrUpdateLabelData(LabelData labelData);

    /**
     * 删除标签数据/标签数据属性
     *
     * @param labelData 标签数据
     */
    void removeLabelData(LabelData labelData);

    /**
     * 删除关系三元组/属性
     *
     * @param tripleLabelData 关系
     */
    void removeTripleLabelData(TripleLabelData tripleLabelData);
}
