package org.irm.lab.kg.service.annex;

import org.irm.lab.kg.entity.annex.processing.AnnexCacheLabelData;


import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2023/4/26 18:56
 * @description 附件缓存标签业务接口
 */
public interface IAnnexCacheLabelDataService {

    /**
     * 新增附件缓存标签
     *
     * @param annexCacheLabelData {@link AnnexCacheLabelData}
     * @return {@link AnnexCacheLabelData}
     */
    AnnexCacheLabelData save(AnnexCacheLabelData annexCacheLabelData);

    /**
     * 根据Id获取附件缓存标签
     *
     * @param id 附件缓存标签Id
     * @return {@link AnnexCacheLabelData}
     */
    AnnexCacheLabelData info(String id);

    /**
     * 根据附件Id、标签Id、缓存标签内容查询指定附件缓存标签
     *
     * @param annexId 附件ID
     * @param labelId 标签ID
     * @param text    缓存标签内容
     * @return {@link AnnexCacheLabelData}
     */
    AnnexCacheLabelData findAnnexCacheLabelDataByCondition(String annexId, String labelId, String text);

    /**
     * 根据附件Id、标签类型 删除标签数据缓存
     *
     * @param annexId 附件Id
     * @param type    标签类型
     */
    void removeByResourceIdAndType(String annexId, String type);

    /**
     * 条件查询附件缓存标签
     *
     * @param filterMap 条件
     * @return {@link AnnexCacheLabelData}
     */
    List<AnnexCacheLabelData> findByCondition(Map<String, Object> filterMap);

    /**
     * 根据附件Id获取该附件的顶级缓存标签
     *
     * @param annexId 附件Id
     * @return 顶级top缓存标签
     */
    AnnexCacheLabelData findTopLabel(String annexId);
}
