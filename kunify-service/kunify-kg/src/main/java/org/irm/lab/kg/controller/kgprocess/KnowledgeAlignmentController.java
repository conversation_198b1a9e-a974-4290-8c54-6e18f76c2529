package org.irm.lab.kg.controller.kgprocess;

import cn.hutool.core.convert.Convert;
import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.irm.lab.common.api.R;
import org.irm.lab.kg.dto.AlignmentDTO;
import org.irm.lab.kg.feign.AlignmentFeign;
import org.irm.lab.kg.service.IKnowledgeAlignmentService;
import org.irm.lab.kg.service.ILabelGraphService;
import org.irm.lab.kg.vo.echarts.EchartsVO;
import org.springframework.web.bind.annotation.*;

import java.io.BufferedReader;
import java.io.FileNotFoundException;
import java.io.FileReader;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2023/3/21 11:07
 * @description 知识对齐控制器
 */
@RestController
@RequestMapping("/knowledge-alignment")
@Api(value = "知识对齐控制器")
@RequiredArgsConstructor
public class KnowledgeAlignmentController implements AlignmentFeign {
    private final IKnowledgeAlignmentService iKnowledgeAlignmentService;
    private final ILabelGraphService iLabelGraphService;

    /**
     * 知识对齐树结构
     *
     * @param resourceId 资源Id
     * @return 知识对齐树
     */
    @ApiOperation(value = "知识对齐树结构")
    @GetMapping("/alignment-tree")
    public R<JSONArray> alignmentTree(@RequestParam String resourceId, @RequestParam String modelId) {
        return R.data(iKnowledgeAlignmentService.alignmentTree(resourceId, modelId));
    }

    @ApiOperation(value = "知识对齐树结构step1")
    @GetMapping("/alignment-tree-step1")
    public R<JSONArray> alignmentTreeStep1(@RequestParam String resourceId, @RequestParam String modelId) {
        return R.data(iKnowledgeAlignmentService.alignmentTreeStep1(resourceId, modelId));
    }

    @ApiOperation(value = "知识对齐树结构step2")
    @GetMapping("/alignment-tree-step2")
    public R<JSONObject> alignmentTreeStep2(@RequestParam String entityId, @RequestParam String conceptId, @RequestParam String entityName, @RequestParam(required = false) String modelId) {
        return R.data(iKnowledgeAlignmentService.alignmentTreeStep2(entityId, conceptId, entityName, modelId));
    }


    /**
     * 对齐
     */
    @ApiOperation(value = "知识对齐&&一键知识对齐")
    @PostMapping("/alignment")
    public R<String> alignment(@RequestBody Map<String, List<AlignmentDTO>> param) {
//        iKnowledgeAlignmentService.alignment(alignmentDTO);
        iKnowledgeAlignmentService.alignmentBatch(Convert.toList(AlignmentDTO.class, param.get("value")));
        return R.success();
    }

    /**
     * 一键知识对齐
     */
    @ApiOperation(value = "一键通过知识对齐")
    @PostMapping("/pass-alignment")
    public R<String> passAlignment(@RequestParam String resourceId) {
        iKnowledgeAlignmentService.passAlignment(resourceId);
        return R.success();
    }

    /**
     * 批量知识对齐
     */
    @ApiOperation(value = "批量通过知识对齐")
    @PostMapping("/pass-alignment-multi")
    public R<String> passAlignmentMulti(@RequestBody Map<String, Object> params) {
        List<String> resourceIds = JSONUtil.toList(JSONUtil.parseArray(params.get("resourceIds")), String.class);
        iKnowledgeAlignmentService.passAlignmentMulti(resourceIds);
        return R.success();
    }

    /**
     * 重新匹配确认
     *
     * @param resourceId 资源Id
     * @param dataSetId  数据集Id
     * @param modelId    模型Id
     */
    @ApiOperation(value = "重新匹配确认")
    @PostMapping("/re-match-confirm")
    public R<String> rematchConfirm(@RequestParam String resourceId, @RequestParam String dataSetId, @RequestParam String modelId) {
        iLabelGraphService.reMatchConfirm(resourceId, dataSetId, modelId);
        return R.success();
    }


    /**
     * 知识可视化
     *
     * @param resourceId 资源Id
     * @return {@link EchartsVO}
     */
    @ApiOperation(value = "知识可视化")
    @GetMapping("/visual")
    public R<EchartsVO> visual(@RequestParam String resourceId, @RequestParam(required = false) String name, @RequestParam(required = false) String showCount) {
        return R.data(iKnowledgeAlignmentService.visual(resourceId, name, showCount));
    }

    /**
     * 知识对齐回退
     *
     * @param param 资源Id
     */
    @ApiOperation(value = "知识对齐回退")
    @PostMapping("/rollback")
    public R<String> rollback(@RequestBody Map<String, Object> param) {
        iKnowledgeAlignmentService.rollback(Convert.toList(String.class, param.get("resourceId")));
        //【脚本】回退节点重复的文件
//        iKnowledgeAlignmentService.rollback(readFile());
        return R.success();
    }


    private static List<String> readFile() {
        String filePath = "/Users/<USER>/Desktop/rollback.txt";
        StringBuilder contentBuilder = new StringBuilder();

        try (BufferedReader br = new BufferedReader(new FileReader(filePath))) {
            String currentLine;
            while ((currentLine = br.readLine()) != null) {
                contentBuilder.append(currentLine);
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        return JSONUtil.parseArray(contentBuilder.toString()).toList(String.class)
                .stream().distinct().collect(Collectors.toList());

    }

}
