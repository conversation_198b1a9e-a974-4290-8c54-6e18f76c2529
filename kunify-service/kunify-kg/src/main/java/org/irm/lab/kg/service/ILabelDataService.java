package org.irm.lab.kg.service;

import org.irm.lab.kg.entity.processing.LabelData;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2023/3/1 15:05
 * @description 标签数据业务接口
 */
public interface ILabelDataService {
    /**
     * 根据条件查询标签数据
     *
     * @param filterMap 条件Map
     * @return {@link LabelData}
     */
    List<LabelData> findByCondition(Map<String, Object> filterMap);
}
