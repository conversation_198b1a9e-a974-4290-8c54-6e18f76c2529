package org.irm.lab.kg.algorithm.ruleMatching.biaozhangrongyu;

import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.log4j.Log4j2;
import org.apache.pdfbox.pdmodel.PDDocument;
import org.apache.pdfbox.text.PDFTextStripper;
import org.irm.lab.common.api.R;
import org.irm.lab.common.enums.AlgorithmType;
import org.irm.lab.common.exception.ServiceException;
import org.irm.lab.common.provider.MinioLinkProvider;
import org.irm.lab.common.utils.NetWorkFileUtil;
import org.irm.lab.kg.algorithm.IAlgorithmProcessor;
import org.irm.lab.kg.algorithm.ruleMatching.RuleParsingEntity;
import org.irm.lab.kg.algorithm.ruleMatching.RuleParsingRelation;
import org.irm.lab.kg.algorithm.ruleMatching.RuleParsingResult;
import org.irm.lab.kg.constant.LabelConstant;
import org.irm.lab.kg.constant.RuleMatchingConstant;
import org.irm.lab.kg.service.impl.kgprocess.ruleMatching.DocRuleParsing;
import org.irm.lab.repository.constant.DocumentResolvedStatus;
import org.irm.lab.repository.entity.Resource;
import org.irm.lab.repository.entity.ResourceAnnex;
import org.irm.lab.repository.feign.ResourceAnnexFeign;
import org.irm.lab.repository.feign.ResourceFeign;
import org.springframework.stereotype.Service;

import java.io.IOException;
import java.io.InputStream;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

@Log4j2
@Service
@RequiredArgsConstructor
public class BIAO_ZHANG_01 implements IAlgorithmProcessor {

    public static final List<String> startWords = new ArrayList<>(List.of("单位", "集体", "同志", "人", "党委", "职工"));

    public static final List<String> endWords = new ArrayList<>(List.of("荣誉称号", "称号", "，", "；"));

    private final ResourceFeign resourceFeign;
    private final ResourceAnnexFeign resourceAnnexFeign;
    private final MinioLinkProvider minioLinkProvider;

    @javax.annotation.Resource(name = "resourceDocRuleParsing")
    private DocRuleParsing docRuleParsing;

    @Override
    public AlgorithmType getCode() {
        return AlgorithmType.PDF_RULE_BIAO_ZHANG_01;
    }

    @Override
    public void process(Resource resource) {
        String processName = AlgorithmType.PDF_RULE_BIAO_ZHANG_01.getName();
        log.info(">>>>>>>>>>【主文件】【{}】【规则解析】开始>>>>>>>>>>", processName);
        try {
            resource.setRuleStatus(DocumentResolvedStatus.RESOLVING);
            resourceFeign.save(resource);
            /*// 获取规则解析结果
            JSONObject results = ruleProcess(resource);
            // 对结果转换为实体
            RuleParsingResult ruleParsingResult = JSONUtil.toBean(results, RuleParsingResult.class, true);
            // 存储处理后的实体
            List<RuleParsingEntity> entityList = new ArrayList<>();
            // 存储处理后的属性(实例的属性)
            List<RuleParsingEntity> propertyList = new ArrayList<>();
            // 存储处理后关系
            List<RuleParsingRelation> relationList = ruleParsingResult.getRelation();
            // 实例处理
            processEntity(ruleParsingResult.getResult(), entityList, propertyList);
            // 生成主文件缓存标签
            docRuleParsing.ruleParsingCacheLabelData(resource, entityList, propertyList, relationList);
            resource.setRuleStatus(DocumentResolvedStatus.RESOLVED);*/
            log.info("<<<<<<<<<<【主文件】【{}】【规则解析】结束<<<<<<<<<<", processName);
        } catch (Exception e) {
            resource.setRuleStatus(DocumentResolvedStatus.RESOLVED_FAILED);
            log.error("解析失败 {}", e.getMessage());
        } finally {
            resourceFeign.save(resource);
        }
    }

    @Override
    public void processEntity(List<RuleParsingEntity> entityList, List<RuleParsingEntity> newEntityList, List<RuleParsingEntity> propertyList) {
        for (RuleParsingEntity ruleParsingEntity : entityList) {
            if (StrUtil.equals(ruleParsingEntity.getType(), LabelConstant.CONCEPT)) newEntityList.add(ruleParsingEntity);
            else propertyList.add(ruleParsingEntity);
        }
    }

    /**
     * 规则解析
     *
     * @param resource
     * @return
     */
    public JSONObject ruleProcess(Resource resource) {
        JSONObject result = JSONUtil.createObj();
        //获取资源文件路径
        String pdfLink = minioLinkProvider.getMinioLinkIntranet(resource.getPdfAttachName());
        //若资源路径为空这集返回空结果
        if (pdfLink == null) return result;
        try (InputStream inputStream = NetWorkFileUtil.urlToInputStream(pdfLink);
             PDDocument document = PDDocument.load(inputStream)) {
            //提取文本信息，按段落切分
            String[] lines = text(document);
            //判断文档类型是否适合本规则
            checkType(lines);
            //抽取表彰荣誉和相关属性及表彰类型
            JSONObject content = title(lines);
            getAnnex(resource, content, lines);
            result = mergeResult(content);
            return result;
        } catch (Exception e) {
            e.printStackTrace();
        }
        return result;
    }


    /**
     * 按段落切分字符串
     */
    public String[] text(PDDocument document) throws IOException {
        // 创建 PDFTextStripper 对象
        PDFTextStripper stripper = new PDFTextStripper();

        // 获取 PDF 文档的文本内容
        String text = stripper.getText(document);
        if (StrUtil.contains(text, "附件：") || StrUtil.contains(text, "附件:"))
            // 将文本内容分割成段落
            return text.split("(?<=附件)[:：]");
        else {
            String[] strings = new String[1];
            strings[0] = text;
            return strings;
        }
    }


    /**
     * 判断文档类型是否适宜本规则
     */
    private void checkType(String[] lines) {
        boolean isCheck = true;
        String line = StrUtil.removeAll(lines[0], "\r\n").replaceAll("\\s+", "");
        if (StrUtil.contains(line, "的决定")) isCheck = false;
        if (StrUtil.contains(line, "的通报")) isCheck = false;
        if (StrUtil.contains(line, "名单")) isCheck = false;
        if (isCheck) throw new ServiceException("文档不适用于本规则！");
    }

    /**
     * 抽取表彰荣誉
     *
     * @param lines
     * @return
     */
    public JSONObject title(String[] lines) {
        JSONObject result = initJSONObject();
        JSONArray title = result.getJSONArray(RuleMatchingConstant.RESULT);
        JSONArray relations = result.getJSONArray(RuleMatchingConstant.RELATION);
        //获取荣誉名称、表彰类型
        getTitle(lines, result, title, relations);
        return result;
    }

    /**
     * 抽取附件
     */
    private void getAnnex(Resource resource, JSONObject content, String[] lines) {
        //先判断有无附件
        R<List<ResourceAnnex>> resourceAnnexListR = resourceAnnexFeign.listByResourceId(resource.getId());
        List<ResourceAnnex> resourceAnnexList = resourceAnnexListR.getData();
        if (!resourceAnnexListR.isSuccess() || ObjectUtil.isEmpty(resourceAnnexList)) {
            log.info("无需附件解析");
        } else {
            long index = 0;
            for (ResourceAnnex resourceAnnex : resourceAnnexList) {
                //抽取人员、部门、机构、职务
                if (StrUtil.contains(resourceAnnex.getName(), RuleMatchingConstant.PDF)
                        || StrUtil.contains(resourceAnnex.getName(), RuleMatchingConstant.DOC)
                        || StrUtil.contains(resourceAnnex.getName(), RuleMatchingConstant.DOCX)) {
                    if (!StrUtil.contains(resourceAnnex.getName(), RuleMatchingConstant.NAME_LIST)) {
                        resourceAnnex.setRuleStatus(DocumentResolvedStatus.RESOLVED);
                        resourceAnnexFeign.save(resourceAnnex);
                        continue;
                    }
                    index = index +1;
                    String pdfLink = minioLinkProvider.getMinioLinkIntranet(resourceAnnex.getPdfAttachName());
                    if (pdfLink == null) continue;
                    try (InputStream inputStream = NetWorkFileUtil.urlToInputStream(pdfLink);
                         PDDocument document = PDDocument.load(inputStream)) {
                        String[] text = extractContent(document);
                        boolean judgement = false;
//                        for (int i = 0; i < 3; i++) {
//                            if (text[i].contains(LabelConstant.ANNEX_LABEL)) {
//                                judgement = true;
//                                break;
//                            }
//                        }
//                        if (judgement)
                            getFromString(text, content);
                            //判断主文件中是否拥有附件名单
//                        else
                            document.close();
                    } catch (IOException e) {
                        e.printStackTrace();
                    }
                } else {
                    resourceAnnex.setRuleStatus(DocumentResolvedStatus.RESOLVED);
                    resourceAnnexFeign.save(resourceAnnex);
                }
            }
            if (0 == index) log.info("附件无可解析名单，无需附件解析！");

        }
    }

    /**
     * 从string中抽取
     */
    private void getFromString(String[] line, JSONObject content) {
        JSONArray title = content.getJSONArray(RuleMatchingConstant.RESULT);
        JSONArray relations = content.getJSONArray(RuleMatchingConstant.RELATION);

        //公文中的所有表彰称号
        List<String> titles = title.stream()
                .map(o -> JSONUtil.parseObj(o.toString()))
                .filter(map -> RuleMatchingConstant.COMMENDATION_TITLE.equals(map.get(RuleMatchingConstant.SCHEMA)))
                .map(map -> map.get("value").toString())
                .collect(Collectors.toList());
        //取出uuid
        List<String> uuids = title.stream().map(o -> JSONUtil.parseObj(o.toString()))
                .filter(map -> RuleMatchingConstant.COMMENDATION_TITLE.equals(map.get(RuleMatchingConstant.SCHEMA)))
                .map(map -> map.get(RuleMatchingConstant.UUID).toString())
                .collect(Collectors.toList());
        //只有一个表彰称号
        if (titles.size() == 1) {
            String join = String.join("", line).replaceAll("\\s+", "").replaceAll("—\\d+—", "");
            disposeOnce(join, title, relations, !titles.get(0).contains(RuleMatchingConstant.GROUP) && !titles.get(0).contains(RuleMatchingConstant.FIRM) && !titles.get(0).contains(RuleMatchingConstant.DEPARTMENT), uuids.get(0));
        } else if (titles.size() > 1) {
            ArrayList<Integer> index = new ArrayList<>();
            //在文本中把小标题拿出来
            for (int i = 0; i < line.length; i++) {
                String pattern = "[一二三四五六七八九十]+、[^\\n]*";
                Pattern regex = Pattern.compile(pattern);
                Matcher matcher = regex.matcher(line[i]);

                while (matcher.find()) {
                    String littleTitle = matcher.group();
                    String[] split = littleTitle.split("、");
                    if (split.length > 1){
                        String s = split[1].replaceAll("\\s+", "");
                        if (titles.stream().anyMatch(n -> n.contains(s))) {
                            index.add(i);
                        }
                    }
                }
            }
            index.add(line.length);
            Collections.sort(index);
            //根据索引将字符串们分成几个段落
            List<String> paragraph = new ArrayList<>();
            for (int i = 0; i < index.size(); i++) {
                if (index.get(i) == line.length) break;
                Integer start = index.get(i);
                Integer end = index.get(i + 1);
                String[] subArray = Arrays.copyOfRange(line, start, end);
                String string = String.join("", subArray).replaceAll("\\s+", "").replaceAll("—\\d+—", "");
                paragraph.add(string);
            }
            String[] result = paragraph.toArray(new String[0]);
//            String regex = String.join("|", titles); // 将列表中的分隔符用"|"连接成一个正则表达式
//            String[] result = line.split("(?<=(" + regex + "))");
            for (String s : result) {
                String[] split = s.split("1\\.");
                boolean isPerson = false;
                if (split.length > 1) {
                    if (!split[0].contains(RuleMatchingConstant.GROUP) && !split[0].contains(RuleMatchingConstant.FIRM) && !split[0].contains(RuleMatchingConstant.DEPARTMENT))
                        isPerson = true;
                }
                disposeOnce(s, title, relations, isPerson, null);
            }
        }
    }

    private void disposeOnce(String join, JSONArray title, JSONArray relations, boolean isPerson, String honorUUid) {
        //先处理括号
        String s = changeBracket(join);
        String[] split = s.split("\\d+\\.");
        //判断uuid是否为空，为空则为单个荣誉，否则为多个荣誉
        String uuid = null;
        if (StrUtil.isNotBlank(honorUUid)) uuid = honorUUid;
        if (split.length > 1) {
            //在多个荣誉中获取当前荣誉的uuid
            if (StrUtil.isBlank(honorUUid)) {
                String[] honorName = split[0].split("、");
                if (honorName.length >= 1) {
                    String honor = honorName[1];
                    String pattern = "（[一二三四五六七八九十]+）";
                    Pattern regex = Pattern.compile(pattern);
                    Matcher matcher = regex.matcher(honor);

                    if (matcher.find()) {
                        int startIndex = matcher.start();
                        honor = honor.substring(0, startIndex);
                    }
                    String finalHonor = honor;
                    List<String> uuids = title.stream().map(o -> JSONUtil.parseObj(o.toString()))
                            .filter(map -> map.get("value").toString().contains(finalHonor) && StrUtil.equals(map.get(RuleMatchingConstant.SCHEMA).toString(), RuleMatchingConstant.COMMENDATION_TITLE))
                            .map(map -> map.get(RuleMatchingConstant.UUID).toString())
                            .collect(Collectors.toList());
                    if (uuids.size() == 1) uuid = uuids.get(0);
                }
            }
            for (int i = 1; i < split.length; i++) {
                if (StrUtil.isNotBlank(split[i])) {
                    if (isPerson) {
                        //人员
                        if (split[i].contains("（")) {
                            Pattern pattern = Pattern.compile("(.*?)（(.*)）");
                            Matcher matcher = pattern.matcher(split[i]);
                            if (matcher.find()) {
                                String before = matcher.group(1).trim();
                                String inside = matcher.group(2).trim();
                                //增加概念
                                JSONObject person = newEntity(IdUtil.simpleUUID(), LabelConstant.CONCEPT, LabelConstant.PEOPLE, before);
                                title.add(person);
                                JSONObject position = newEntity(IdUtil.simpleUUID(), LabelConstant.CONCEPT, LabelConstant.POST, inside);
                                title.add(position);
                                //关系
                                relations.add(newRelation(LabelConstant.TAKE_OFFICE, person.getStr(RuleMatchingConstant.UUID), position.getStr(RuleMatchingConstant.UUID)));
                                relations.add(newRelation(LabelConstant.APPOINT, position.getStr(RuleMatchingConstant.UUID), person.getStr(RuleMatchingConstant.UUID)));
                                //人员与荣誉的关系
                                if (StrUtil.isNotBlank(uuid)) {
                                    relations.add(newRelation(LabelConstant.GRANTOR, uuid, person.getStr(RuleMatchingConstant.UUID)));
                                    relations.add(newRelation(LabelConstant.GIVE_THE_TITLE_OF, person.getStr(RuleMatchingConstant.UUID), uuid));
                                }
                            }
                        } else {
                            JSONObject person = newEntity(IdUtil.simpleUUID(), LabelConstant.CONCEPT, LabelConstant.PEOPLE, split[i]);
                            title.add(person);
                            if (StrUtil.isNotBlank(uuid)) {
                                relations.add(newRelation(LabelConstant.GRANTOR, uuid, person.getStr(RuleMatchingConstant.UUID)));
                                relations.add(newRelation(LabelConstant.GIVE_THE_TITLE_OF, person.getStr(RuleMatchingConstant.UUID), uuid));
                            }
                        }
                    } else {
                        JSONObject position = newEntity(IdUtil.simpleUUID(), LabelConstant.CONCEPT, LabelConstant.DEPARTMENT, split[i]);
                        title.add(position);
                        relations.add(newRelation(LabelConstant.GRANT_DEPARTMENT, uuid, position.getStr(RuleMatchingConstant.UUID)));
                        relations.add(newRelation(LabelConstant.GIVE_THE_TITLE_OF, position.getStr(RuleMatchingConstant.UUID), uuid));
                    }
                }
            }
        }
    }

    private String changeBracket(String s) {
        if (s.contains("(")) {
            s = s.replaceAll("\\(", "（").replaceAll("\\)", "）");
        }
        if (s.contains(":")) {
            s = s.replaceAll(":", "：");
        }
        if (s.contains(",")) {
            s = s.replaceAll(",", "，");
        }
        if (s.contains(";")) {
            s = s.replaceAll(";", "；");
        }
        return s;
    }

    /**
     * 获取表彰称号
     *
     * @param lines
     * @param result
     * @param title
     * @param relations
     */
    private void getTitle(String[] lines, JSONObject result, JSONArray title, JSONArray relations) {
        for (String s : lines) {
            String line;
            line = changeBracket(s);
            line = StrUtil.removeAll(line, "\r\n").replaceAll("\\s+", "");
            //以"单位", "集体", "同志"为开头，以"荣誉称号", "称号"为结尾
            Pattern compile = Pattern.compile("(?:" + String.join("|", startWords) + ").*?(?:" + String.join("|", endWords) + ")");
            String str = getStr(line);
            if (StrUtil.isBlank(str)) continue;
            Matcher matcher = compile.matcher(str);
            JSONObject honoraryTitleEntries = null;
            while (matcher.find()) {
                //待处理文本
                String pendingStr = matcher.group();
                pendingStr = pendingStr.replaceAll("^(" + String.join("|", startWords) + ")\\s*", "")
                        .replaceAll("\\s*(荣誉)?称号$", "").replaceAll("\\p{P}+[\\p{P}‘’“”'\"]*", "");
                //荣誉称号
                String honoraryTitle = pendingStr.replaceAll("\\s+", "");
                JSONObject honoraryTypeEntries = null;
                String year = "";
                if (StrUtil.contains(honoraryTitle, RuleMatchingConstant.YEAR)) {
                    //截取年度
                    honoraryTitle = honoraryTitle.replaceAll("“", "").replaceAll("”", "");
                    String[] split = honoraryTitle.split("(?<=年度)");
                    honoraryTypeEntries = newEntity(IdUtil.simpleUUID(), LabelConstant.CONCEPT, RuleMatchingConstant.COMMENDATION_TYPE, split[1].replaceAll("\\s+", ""));
                    title.add(honoraryTypeEntries);
                    year = split[0].replaceAll("\\s+", "");
                } else {
                    Pattern pattern = Pattern.compile("\\d{4}年度?");
                    Matcher yearMatcher = pattern.matcher(line.replaceAll("\\s+", ""));
                    if (yearMatcher.find()) {
                        String pendingYearStr = yearMatcher.group();
                        year = pendingYearStr;
                        honoraryTitle = honoraryTitle.replaceAll("“", "").replaceAll("”", "");
                        honoraryTypeEntries = newEntity(IdUtil.simpleUUID(), LabelConstant.CONCEPT, RuleMatchingConstant.COMMENDATION_TYPE, honoraryTitle);
                        title.add(honoraryTypeEntries);
                        honoraryTitle = pendingYearStr + honoraryTitle;
                    }
                }
                //生成实体
                honoraryTitleEntries = newEntity(IdUtil.simpleUUID(), LabelConstant.CONCEPT, RuleMatchingConstant.COMMENDATION_TITLE, honoraryTitle);
                title.add(honoraryTitleEntries);
                //连接主节点
                //relations.add(newRelation(LabelConstant.MENTION_TITLE, "0", honoraryTitleEntries.getStr(RuleMatchingConstant.UUID)));

                //新增关系
                try {
                    assert honoraryTypeEntries != null;
                    relations.add(newRelation(RuleMatchingConstant.PART_COMMENDATION_TYPE, honoraryTitleEntries.getStr(RuleMatchingConstant.UUID), honoraryTypeEntries.getStr(RuleMatchingConstant.UUID)));
                } catch (AssertionError e) {
                    log.info("未抽取到表彰类型");
                }
                //新增属性-表彰年度
                JSONObject yearEntity = null;
                if (StrUtil.isNotBlank(year)) {
                    yearEntity = newEntity(IdUtil.simpleUUID(), LabelConstant.PROPERTY, RuleMatchingConstant.COMMENDATION_YEAR, year);
                    title.add(yearEntity);
                }
                try {
                    assert yearEntity != null;
                    relations.add(newRelation(LabelConstant.PROPERTY, honoraryTitleEntries.getStr(RuleMatchingConstant.UUID), yearEntity.getStr(RuleMatchingConstant.UUID)));
                } catch (AssertionError e) {
                    log.info("未抽取到表彰年度");
                }

                //新增属性-面向群体
                String group = isRelation(line);
                if (StrUtil.isNotBlank(group)) {
                    JSONObject groupOriented = newEntity(IdUtil.simpleUUID(), LabelConstant.PROPERTY, RuleMatchingConstant.GROUP_ORIENTED, group);
                    title.add(groupOriented);
                    try {
                        relations.add(newRelation(LabelConstant.PROPERTY, honoraryTitleEntries.getStr(RuleMatchingConstant.UUID), groupOriented.getStr(RuleMatchingConstant.UUID)));
                    } catch (AssertionError e) {
                        log.info("未抽取到面向群体");
                    }
                }
            }
            result.putOpt(RuleMatchingConstant.RESULT, title);
            result.putOpt(RuleMatchingConstant.RELATION, relations);
        }
    }

    private String getStr(String line) {
        String startChar1 = "决定，授予";
        String startChar2 = "分别授予";
        String startChar3 = "决定授予";
        String endChar = "。";
        String result = null;
        if (line.contains(startChar1)) {
            int startIndex = line.indexOf(startChar1) + 1;
            String subStr = line.substring(startIndex);
            int endIndex = subStr.indexOf(endChar);
            result = subStr.substring(0, endIndex);
        } else if (line.contains(startChar2)) {
            int startIndex = line.indexOf(startChar2) + 1;
            String subStr = line.substring(startIndex);
            int endIndex = subStr.indexOf(endChar);
            result = subStr.substring(0, endIndex);
        }else if (line.contains(startChar3)){
            int startIndex = line.indexOf(startChar3) + 1;
            String subStr = line.substring(startIndex);
            int endIndex = subStr.indexOf(endChar);
            result = subStr.substring(0, endIndex);
        }
        return result;
    }

    /**
     * 以冒号截取面向群体
     *
     * @param line
     * @return
     */
    private String isRelation(String line) {
        line = StrUtil.trim(line);
        String startChar = "号";
        String endChar = "：";
        int startIndex = line.indexOf(startChar) + 1;
        String subStr = line.substring(startIndex);
        int endIndex = subStr.indexOf(endChar);
        String result = subStr.substring(0, endIndex);
        if (result.contains("的决定"))
            result = result.split("的决定")[1];
        else if (result.contains("的通报"))
            result = result.split("的通报")[1];
        return result;
    }
}
