package org.irm.lab.kg.controller.kgprocess;

import cn.hutool.json.JSONArray;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.irm.lab.common.annotation.MyLog;
import org.irm.lab.common.api.R;
import org.irm.lab.common.constant.LogConstant;
import org.irm.lab.kg.entity.processing.CacheLabelData;
import org.irm.lab.kg.entity.processing.CacheTripleLabelData;
import org.irm.lab.kg.service.ICacheLabelDataService;
import org.irm.lab.kg.service.IRuleParsingGraphService;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2023/5/8 10:12
 * @description 规则解析控制器
 */
@RestController
@RequestMapping("/rule-parsing")
@RequiredArgsConstructor
public class RuleParsingController {
    private final IRuleParsingGraphService iRuleParsingGraphService;
    private final ICacheLabelDataService iCacheLabelDataService;

    /**
     * 规则解析 ===> 获取标签数据树
     *
     * @param resourceId 资源Id
     * @param dataSetId  数据集Id
     * @return 标签数据
     */
    @ApiOperation(value = "获取标签数据树")
    @GetMapping("/relevance")
    public R<JSONArray> relevance(@RequestParam String resourceId, @RequestParam String dataSetId) {
        return R.data(iRuleParsingGraphService.relevanceShow(resourceId, dataSetId));
    }


    /**
     * 获取该资源下的所有缓存标签数据
     *
     * @param resourceId 资源Id
     * @return {@link CacheLabelData}
     */
    @ApiOperation(value = "获取该资源下的所有缓存标签数据", hidden = true)
    @GetMapping("/list-label-data")
    public R<List<CacheLabelData>> listLabelData(@RequestParam String resourceId) {
        return R.data(iRuleParsingGraphService.listCacheLabelData(resourceId));
    }


    /**
     * 获取指定概念下的所有实例标签
     *
     * @param labelId 标签Id（概念Id）
     * @return {@link CacheLabelData}
     */
    @ApiOperation(value = "获取指定概念下的所有实例标签", hidden = true)
    @GetMapping("/list-label-data-condition")
    public R<List<CacheLabelData>> listLabelDataCondition(@RequestParam String resourceId, @RequestParam String labelId) {
        return R.data(iCacheLabelDataService.findByCondition(Map.of("resourceId", resourceId, "labelId", labelId, "type", "规则解析")));
    }


    /**
     * 新增或修改标签数据对象
     *
     * @param cacheLabelData {@link CacheLabelData}
     */
    @MyLog(menu = LogConstant.MENU_KNOWLEDGE, dataType = LogConstant.DATA_ENTITY_LABEL, operation = LogConstant.OPERATION_ALTER)
    @ApiOperation(value = "新增或修改标签数据对象", hidden = true)
    @PostMapping("/save-label-data")
    public R<String> saveOrUpdateLabelData(@RequestBody CacheLabelData cacheLabelData) {
        iRuleParsingGraphService.saveOrUpdateLabelData(cacheLabelData);
        return R.success();
    }

    /**
     * 删除实例/实例的属性
     *
     * @param cacheLabelData 缓存标签数据实例
     */
    @ApiOperation(value = "删除实例/实例的属性", hidden = true)
    @PostMapping("/remove-label-data")
    public R<String> removeLabelData(@RequestBody CacheLabelData cacheLabelData) {
        iRuleParsingGraphService.removeLabelData(cacheLabelData);
        return R.success();
    }


    /**
     * 新增/修改关系三元组
     *
     * @param cacheTripleLabelData {@link CacheTripleLabelData}
     */

    @ApiOperation(value = "新增/修改关系三元组", hidden = true)
    @PostMapping("/save-triple-label-data")
    public R<String> saveOrUpdateTripleLabelData(@RequestBody List<CacheTripleLabelData> cacheTripleLabelData) {
        iRuleParsingGraphService.saveOrUpdateTripleLabelData(cacheTripleLabelData);
        return R.success();
    }

    /**
     * 删除关系三元组/属性
     *
     * @param cacheTripleLabelData 关系三元组
     */
    @ApiOperation(value = "删除关系三元组/属性", hidden = true)
    @PostMapping("/remove-triple-label-data")
    public R<String> removeTripleLabelData(@RequestBody CacheTripleLabelData cacheTripleLabelData) {
        iRuleParsingGraphService.removeTripleLabelData(cacheTripleLabelData);
        return R.success();
    }


    /**
     * 解析确认 ===> 规则解析结果确认
     *
     * @param resourceId 资源Id
     */
    @MyLog(menu = LogConstant.MENU_KNOWLEDGE, dataType = LogConstant.DATA_FILE, operation = LogConstant.OPERATION_RULE_ANALYSIS_TRUE)
    @ApiOperation(value = "解析确认")
    @PostMapping("/parsing-confirm")
    public R<String> parsingConfirm(@RequestParam String resourceId) {
        iRuleParsingGraphService.parsingConfirm(resourceId);
        return R.success();
    }

    /**
     * 重新解析  ===> 重新进行规则解析
     *
     * @param resourceId 资源Id
     */
    @MyLog(menu = LogConstant.MENU_KNOWLEDGE, dataType = LogConstant.DATA_FILE, operation = LogConstant.OPERATION_RULE_REANALYSIS)
    @ApiOperation(value = "重新解析")
    @PostMapping("/re-parsing")
    public R<String> reMatch(@RequestParam String resourceId) {
        iRuleParsingGraphService.reRuleParsing(resourceId);
        return R.success();
    }


}
