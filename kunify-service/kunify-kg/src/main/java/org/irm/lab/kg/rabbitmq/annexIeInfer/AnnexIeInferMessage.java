package org.irm.lab.kg.rabbitmq.annexIeInfer;

import java.io.Serializable;

import lombok.Data;

/**
 */
@Data
public class AnnexIeInferMessage implements Serializable {



    public static final String QUEUE = "QUEUE_ANNEX_IE_INFER_LOADED";
    public static final String EXCHANGE = "EXCHANGE_ANNEX_IE_INFER_LOADED";
    public static final String ROUTING_KEY = "ROUTING_KEY_ANNEX_IE_INFER_LOADED";


    /**
     * 资源id
     */
    private String annexId;

    /**
     * 类型
     */
    private String type;

    /**
     * 当前用户信息
     */
    private String user;

    private String schemas;

    /**
     * 语料
     */
    private String text;


    /**
     * 租户
     */
    private String tenant;

    /**
     * 语料id
     */
    private String corpusId;

    /**
     * 是否是当前资源的最后一条语料
     */
    private Boolean isEnd;

}
