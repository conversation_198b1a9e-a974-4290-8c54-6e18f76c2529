package org.irm.lab.kg.algorithm.ruleMatching;

import cn.hutool.core.io.FileUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONUtil;
import com.aspose.pdf.*;
import lombok.extern.log4j.Log4j2;
import org.irm.lab.common.utils.NetWorkFileUtil;
import org.irm.lab.kg.utils.PDFUtils;

import java.io.File;
import java.io.InputStream;
import java.util.ArrayList;
import java.util.List;
import java.util.Locale;
import java.util.Set;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * <AUTHOR>
 * @date 2023/5/6 15:07
 */
@Log4j2
public class PDFExtract_Mini {
    private static final Set<String> END_PUNCTUATION = Set.of(":","。","：");


    public static void main(String[] args){
        String inputPath1="C:\\Users\\<USER>\\Desktop\\华能处理数据\\1_中共中国华能集团有限公司党组会议纪要2021年第1期.pdf";
        String inputPath2="C:\\Users\\<USER>\\Desktop\\华能处理数据\\1_中共中国华能集团有限公司党组会议纪要2021年第1期.pdf";
        String inputPath3="F:\\demoQ\\word\\华能\\华能材料\\数据\\会议纪要\\文件\\63b53d2caf93b\\A0001-WS2020-Y-BGS-0009_中国华能集团有限公司2020年第9次党组会\\1_中国华能集团有限公司2020年党组会会议纪要第9期.pdf";
        String inputPath4 = "F:\\demoQ\\word\\华能\\华能材料\\数据\\会议纪要\\文件\\63b53a5fbcbf0\\A0001-WS2021-Y-BGS-0022_中国华能集团有限公司2021年第22次党组会\\3_2021年第22次党组会会议纪要和审签单.pdf";
        String inputPath5 = "C:\\Users\\<USER>\\Desktop\\华能处理数据\\1_正文：电化学储能技术研讨会议纪要.pdf";
        String inputPath6 = "F:\\demoQ\\word\\华能\\华能材料\\数据\\会议纪要\\文件\\63b53a5fbcbf0\\A0001-WS2021-Y-BGS-0046_中国华能集团有限公司2021年第46次党组会\\10_议题06：审议关于成立华能采购科技公司的汇报及建议.pdf";
        String ima1 = "F:\\demoQ\\word\\document\\WXGC\\e30af1b6-9dd2-4317-a37d-659d3d3de16d.pdf";
        JSONArray resolve = resolve(new File(inputPath1));
        for (Object o : resolve) {
            JSONArray array = JSONUtil.parseArray(o);
            for (Object o1 : array) {
                log.info(o1);
            }
        }
    }

    public static JSONArray resolve(String url){
        Locale.setDefault(new Locale("zh-cn"));
        try(InputStream inputStream = NetWorkFileUtil.urlToInputStream(url)){
            Document document = new Document(inputStream);
            int pageSize = document.getPages().size();
            PageCollection pageCollection = document.getPages();
            List<Page> pages = new ArrayList<>();
            for (int page = 1; page <= pageSize; page++) {
                pages.add(pageCollection.get_Item(page));
            }
            return nr(pages, 1);
        }catch (Exception e){
            e.printStackTrace();
        }
       return JSONUtil.createArray();

    }
    public static JSONArray resolve(File file){
        Locale.setDefault(new Locale("zh-cn"));
        Document document = new Document(FileUtil.getInputStream(file));
        int pageSize = document.getPages().size();
        PageCollection pageCollection = document.getPages();
        List<Page> pages = new ArrayList<>();
        for (int page = 1; page <= pageSize; page++) {
            pages.add(pageCollection.get_Item(page));
        }
        return nr(pages, 1);
    }

    public static JSONArray nr(List<Page> pages,int pageStart){
        JSONArray result = JSONUtil.createArray();
        //合并下一页的内容
        boolean isMergeNextPage = false;
        for (int page = 0; page < pages.size(); page++) {
            JSONArray pageContents = JSONUtil.createArray();
            boolean isMergeIng = false;
            StringBuilder tempContent = null;
            Page pagePDF = pages.get(page);
            double height = pagePDF.getArtBox().getHeight();
            log.info("高度：【{}】",height);
            // 处理段落
            ParagraphAbsorber absorber = new ParagraphAbsorber();
            absorber.visit(pagePDF);
            TableAbsorber tableAbsorber = new TableAbsorber();
            //不进行识别的区域
            List<Rectangle> tableRecList = new ArrayList<>();
            //页眉
            tableRecList.add(new Rectangle(10, 770, 560, 900));
            //页尾
            tableRecList.add(new Rectangle(10, 10, 560, 85));
            System.out.println(pageStart + page);
            try {
                tableAbsorber.visit(pagePDF);
            } catch (Exception e) {
                e.printStackTrace();
            }
            for (PageMarkup markup : absorber.getPageMarkups()) {
                //当前页,大部分情况下，markup的size一直都是1
                for (MarkupSection section : markup.getSections()) {
                    //遍历行,大部分情况下，section的size一直都是1
                    for (MarkupParagraph paragraph : section.getParagraphs()) {
                        Rectangle rec = PDFUtils.getParaRec(paragraph);
                        if (PDFUtils.inRectangle(rec, tableRecList)) {
                            continue;
                        }
                        String text = paragraph.getText();
                        log.info("段落:【{}】", text);
                        if (!isMergeIng){
                            if (isSeparateRow(rec)) {
                                if(isMergeNextPage){
                                    Object o = result.get(result.size()-1);
                                    JSONArray array = JSONUtil.parseArray(o);
                                    array.put(array.size()-1, array.get(array.size()-1) +text);
                                    result.put(result.size()-1,array);
                                    isMergeNextPage = false;
                                }
                            }else {
                                tempContent = new StringBuilder(text);
                                isMergeIng = true;
                            }
                        }else {
                            if (isSeparateRow(rec) || END_PUNCTUATION.contains( StrUtil.subSufByLength( text,1) )){
                                isMergeIng = false;
                                if(isMergeNextPage){
                                    Object o = result.get(result.size()-1);
                                    JSONArray array = JSONUtil.parseArray(o);
                                    array.put(array.size()-1, array.get(array.size()-1) +tempContent.append(text).toString());
                                    result.put(result.size()-1,array);
                                    isMergeNextPage = false;
                                }
                                tempContent = null;
                            }else {
                                tempContent.append(text);
                            }
                        }
                    }
                }
            }
            System.out.println("-----------"+tempContent);
            if (isMergeIng){
                pageContents.add(tempContent);
                isMergeNextPage = true;
            }
            result.add(pageContents);
        }
        return result;
    }

    private static boolean isPageNum(String text){
        String regex100 = "(?<=^[-,一])\\s?[0-9]{1,3}\\s?(?=[-,一])";
        Pattern pattern100 = Pattern.compile(regex100);
        Matcher matcher100 = pattern100.matcher(text);
        return matcher100.find();
    }

    /**
     * 通过最右侧横坐标来判断是否是单独的一行
     * @param rec 矩阵
     * @return true：是单独的一行
     */
    private static Boolean isSeparateRow(Rectangle rec){
        return rec.getURX() <= 450;
    }
}
