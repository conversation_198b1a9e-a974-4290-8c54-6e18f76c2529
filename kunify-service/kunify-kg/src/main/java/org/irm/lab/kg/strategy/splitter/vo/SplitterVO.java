package org.irm.lab.kg.strategy.splitter.vo;

import java.util.List;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR> <br/>
 * @date 2024/6/3 <br/>
 * @Copyright 博客：https://eliauku.gitee.io/  ||  per aspera and astra <br/>
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class SplitterVO {

    private List<ContentItem> content;
    private List<Object> table; // 根据实际需要调整类型
    private List<Footer> footer;

    // getters and setters
    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    public static class ContentItem {
        private int index;
        private int pageIndex;
        private String text;
        private String lvl;
        private String cmptitle;
        private Rect rect;

        // getters and setters
    }
    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    public static class Rect {
        private double left;
        private double right;
        private double top;
        private double bottom;

        // getters and setters
    }

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    public static class Footer {
        private int index;
        private int pageIndex;
        private String text;

        // getters and setters
    }

}
