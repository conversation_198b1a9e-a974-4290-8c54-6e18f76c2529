package org.irm.lab.kg.service.impl;

import cn.hutool.core.convert.Convert;
import cn.hutool.core.lang.Opt;
import cn.hutool.core.util.ObjectUtil;
import com.mongodb.client.model.Filters;
import lombok.RequiredArgsConstructor;
import lombok.Synchronized;
import lombok.extern.slf4j.Slf4j;
import org.irm.lab.common.api.R;
import org.irm.lab.common.constant.ModelConceptConst;
import org.irm.lab.common.constant.ModelRelationConst;
import org.irm.lab.common.exception.ServiceException;
import org.irm.lab.common.support.Condition;
import org.irm.lab.config.entity.Label;
import org.irm.lab.config.entity.Process;
import org.irm.lab.config.entity.ProcessNode;
import org.irm.lab.config.entity.WorkTask;
import org.irm.lab.config.feign.WorkTaskFeign;
import org.irm.lab.config.repository.LabelRepository;
import org.irm.lab.kg.entity.KnowledgeConcept;
import org.irm.lab.kg.entity.KnowledgeRelation;
import org.irm.lab.kg.entity.neo4j.node.NodeEntity;
import org.irm.lab.kg.entity.neo4j.node.NodeRelation;
import org.irm.lab.kg.entity.processing.LabelData;
import org.irm.lab.kg.entity.processing.LabelPropertyVO;
import org.irm.lab.kg.entity.processing.TripleLabelData;
import org.irm.lab.kg.repository.KnowledgeRelationRepository;
import org.irm.lab.kg.repository.neo4j.node.NodeEntityRepository;
import org.irm.lab.kg.repository.neo4j.node.NodeRelationRepository;
import org.irm.lab.kg.repository.processing.LabelDataRepository;
import org.irm.lab.kg.repository.processing.TripleLabelDataRepository;
import org.irm.lab.kg.service.*;
import org.irm.lab.kg.service.annex.IAnnexLabelGraphService;
import org.irm.lab.repository.constant.KnowledgeAlignmentStatus;
import org.irm.lab.repository.entity.Resource;
import org.irm.lab.repository.entity.ResourceAnnex;
import org.irm.lab.repository.feign.ResourceAnnexFeign;
import org.irm.lab.repository.feign.ResourceFeign;
import org.jetbrains.annotations.NotNull;
import org.neo4j.ogm.cypher.ComparisonOperator;
import org.neo4j.ogm.cypher.Filter;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.w3c.dom.Node;

import java.util.*;
import java.util.concurrent.atomic.AtomicReference;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2023/5/12 17:29
 * @description 资源匹配确认异步任务
 */
@Async
@Slf4j
@Service
@RequiredArgsConstructor
public class ResourceAsyncLabelGraphServiceImpl implements IResourceAsyncLabelGraphService {

    private final LabelDataRepository labelDataRepository;
    private final TripleLabelDataRepository tripleLabelDataRepository;
    private final LabelRepository labelRepository;
    private final NodeEntityRepository nodeEntityRepository;
    private final NodeRelationRepository nodeRelationRepository;
    private final ResourceFeign resourceFeign;
    private final KnowledgeRelationRepository knowledgeRelationRepository;
    private final ResourceAnnexFeign resourceAnnexFeign;
    private final IAnnexLabelGraphService iAnnexLabelGraphService;
    private final WorkTaskFeign workTaskFeign;
    private final IKnowledgeAlignmentService iKnowledgeAlignmentService;
    private final IKnowledgeConceptService conceptService;
    private final IKnowledgeRelationService relationService;
    private final IKgResourceService kgResourceService;

    /**
     * 匹配确认
     *
     * @param resource  资源对象
     * @param dataSetId 数据集ID
     * @param modelId   模型Id
     */
    @Override
    public void matchConfirm(Resource resource, String dataSetId, String modelId) {
        try {
            // 修改资源状态为匹配确认 为 true
            resource.setKnowledgeMatchConfirm(true);
            // 修改资源状态为 “匹配确认中”
            resource.setAlignmentStatus(KnowledgeAlignmentStatus.MATCH_CONFIRMING);
            resourceFeign.save(resource);
            // 判断是否开启自动对齐
            Boolean autoAlignment = isAutoAlignment(resource);
            // 生成实例
            Map<String, NodeEntity> entityMap = generateNodeEntity(modelId, resource, autoAlignment);
            // 建立所有实例间的关系
            generateRelation(resource, entityMap, dataSetId);
            // 修改资源为 “待对齐”
            resource.setAlignmentStatus(KnowledgeAlignmentStatus.UN_ALIGNMENT);
            resourceFeign.save(resource);
            // 完成主文件匹配确认后，开启附件匹配确认
            R<List<ResourceAnnex>> resourceAnnexListR = resourceAnnexFeign.listByResourceId(resource.getId());
            List<ResourceAnnex> resourceAnnexList = resourceAnnexListR.getData();
            if (ObjectUtil.isNotEmpty(resourceAnnexList)) {
                for (ResourceAnnex resourceAnnex : resourceAnnexList) {
                    iAnnexLabelGraphService.matchConfirm(resourceAnnex, dataSetId, modelId, autoAlignment);
                }
            }
            // 如果开启自动对齐
            if (autoAlignment) {
                // 如果不存在附件，则直接进行自动对齐
                if (ObjectUtil.isEmpty(resourceAnnexList)) {
                    // 自动实体对齐
                    iKnowledgeAlignmentService.passAlignment(resource.getId());
                }
            }

            // extra：将表彰荣誉节点（不论是主文件还是附件生成的）与根节点关联
//            String classify = kgResourceService.getClassifyByResourceOrAnnexId(resource.getId());
//            if ("奖章荣誉".equals(classify)) {
//                log.info("======extra：设置公文的默认关联：start======");
//                //拿概念
//                KnowledgeConcept bzryConcept = conceptService.findByConceptName(ModelConceptConst.CONCEPT_BIAOZHANG_RONGYU);
//                log.info("表彰荣誉概念已拿到：{}", bzryConcept.getId());
//                KnowledgeConcept gwConcept = conceptService.findByConceptName(ModelConceptConst.CONCEPT_GONGWEN);
//                log.info("公文概念已拿到：{}", gwConcept.getId());
//                KnowledgeConcept ryConcept = conceptService.findByConceptName(ModelConceptConst.CONCEPT_RENYUAN);
//                log.info("人员概念已拿到：{}", ryConcept.getId());
//
//                //拿边
//                AtomicReference<KnowledgeRelation> tjbzryRelation = new AtomicReference<>();
//                relationService.getForwardRelationByConceptId(gwConcept.getId())
//                        .stream().filter(relation -> ModelRelationConst.RELATION_TIJI_BIAOZHANG_RONGYU.equals(relation.getName()))
//                        .findFirst().ifPresent(tjbzryRelation::set);
//                log.info("公文提及表彰荣誉关系已拿到：{}", tjbzryRelation.get().getId());
//
//                AtomicReference<KnowledgeRelation> tjryRelation = new AtomicReference<>();
//                relationService.getForwardRelationByConceptId(gwConcept.getId())
//                        .stream().filter(relation -> ModelRelationConst.RELATION_TIJI_RENYUAN.equals(relation.getName()))
//                        .findFirst().ifPresent(tjryRelation::set);
//                log.info("公文提及人员关系已拿到：{}", tjryRelation.get().getId());
//
//
//                // 获取当前文档下的公文实例
//                org.neo4j.ogm.cypher.Filters gwFilters = new org.neo4j.ogm.cypher.Filters();
//                gwFilters.add(new Filter("conceptId", ComparisonOperator.EQUALS, gwConcept.getId()));
//                List<NodeEntity> gwEntity = nodeEntityRepository.findByCondition(gwFilters)
//                        .stream().filter(entity -> entity.getDocIds().contains(resource.getId())).collect(Collectors.toList());
//                log.info("当前文档{}下的公文实例个数：{}", resource.getId(), gwEntity.size());
//
//                // 获取当前文档下的所有的表彰荣誉实例
//                org.neo4j.ogm.cypher.Filters allBzrqFilters = new org.neo4j.ogm.cypher.Filters();
//                allBzrqFilters.add(new Filter("conceptId", ComparisonOperator.EQUALS, bzryConcept.getId()));
//                List<NodeEntity> allBzryEntity = nodeEntityRepository.findByCondition(allBzrqFilters, 1)
//                        .stream()
//                        .filter(entity -> entity.getDocIds().contains(resource.getId()))
//                        .collect(Collectors.toList());
//
//                // 获取当前文档下所有的人员实例
//                org.neo4j.ogm.cypher.Filters allRyFilters = new org.neo4j.ogm.cypher.Filters();
//                allRyFilters.add(new Filter("conceptId", ComparisonOperator.EQUALS, ryConcept.getId()));
//                List<NodeEntity> allRyEntity = nodeEntityRepository.findByCondition(allRyFilters, 1)
//                        .stream()
//                        .filter(entity -> entity.getDocIds().contains(resource.getId()))
//                        .collect(Collectors.toList());
//                log.info("当前文档{}下的所有的表彰荣誉实例个数：{}", resource.getId(), allBzryEntity.size());
//                log.info("当前文档{}下的所有的人员实例个数：{}", resource.getId(), allRyEntity.size());
//
//
//                Map<String, NodeEntity> distinctBzryEntity = duplicateNodeEntity(allBzryEntity);
//                Map<String, NodeEntity> distinctRyEntity = duplicateNodeEntity(allRyEntity);
//                log.info("当前文档{}下的去重表彰荣誉实例个数：{}", resource.getId(), distinctBzryEntity.size());
//                log.info("当前文档{}下的去重人员实例个数：{}", resource.getId(), distinctRyEntity.size());
//
////             打印日志
//                distinctBzryEntity.forEach((key, value) -> {
//                    log.info("=======表彰荣誉实体验证========");
//                    log.info("合并后的【实体】：{}", value.getEntityName());
//                    log.info("合并后的【数据属性】：{}", value.getProperties());
//                    log.info("合并后的【来源文档】：{}", value.getDocIds());
//                    log.info("合并后的【来源附件】：{}", value.getAnnexIds());
//                    log.info("合并后的【来源语料】：{}", value.getDocumentUnitIds());
//                    log.info("=======表彰荣誉实体验证========");
//                });
//
//                distinctRyEntity.forEach((key, value) -> {
//                    log.info("=======人员实体验证========");
//                    log.info("合并后的【实体】：{}", value.getEntityName());
//                    log.info("合并后的【数据属性】：{}", value.getProperties());
//                    log.info("合并后的【来源文档】：{}", value.getDocIds());
//                    log.info("合并后的【来源附件】：{}", value.getAnnexIds());
//                    log.info("合并后的【来源语料】：{}", value.getDocumentUnitIds());
//                    log.info("=======人员实体验证========");
//                });
//
//
//                if (!gwEntity.isEmpty() && !distinctBzryEntity.isEmpty() && tjbzryRelation.get() != null) {
//                    log.info("=====设置公文提及表彰荣誉：start=====");
//                    distinctBzryEntity.values().forEach(entity -> {
//                        log.info("开始添加提及表彰荣誉关系：【{}】---【{}】--->【{}】", gwEntity.get(0).getEntityName(), tjbzryRelation.get().getName(), entity.getEntityName());
//                        //添加【提及表彰荣誉】边
//                        nodeRelationRepository.save(
//                                new NodeRelation(
//                                        ModelRelationConst.RELATION_TIJI_BIAOZHANG_RONGYU,
//                                        tjbzryRelation.get().getId(),
//                                        entity.getDocIds(),
//                                        entity.getAnnexIds(),
//                                        new HashMap<>(),
//                                        gwEntity.get(0),
//                                        entity,
//                                        null,
//                                        true
//                                )
//                        );
//                    });
//                    log.info("=====设置公文提及表彰荣誉：complete=====");
//                }
//
//                if (!gwEntity.isEmpty() && !distinctRyEntity.isEmpty() && tjryRelation.get() != null) {
//                    log.info("=====设置公文提及人员：start=====");
//                    distinctRyEntity.values().forEach(entity -> {
//                        log.info("开始添加提及人员关系：【{}】---【{}】--->【{}】", gwEntity.get(0).getEntityName(), tjryRelation.get().getName(), entity.getEntityName());
//                        //添加【提及人员】边
//                        nodeRelationRepository.save(
//                                new NodeRelation(
//                                        ModelRelationConst.RELATION_TIJI_RENYUAN,
//                                        tjryRelation.get().getId(),
//                                        entity.getDocIds(),
//                                        entity.getAnnexIds(),
//                                        new HashMap<>(),
//                                        gwEntity.get(0),
//                                        entity,
//                                        null,
//                                        true
//                                )
//                        );
//                    });
//                    log.info("=====设置公文提及人员：complete=====");
//                }
//
//                log.info("======extra：设置公文的默认关联：complete======");
//            }
        } catch (Exception e) {
            // 匹配确认失败
            resource.setAlignmentStatus(KnowledgeAlignmentStatus.MATCH_CONFIRM_FAILED);
            resourceFeign.save(resource);
            // 打印错误信息
            log.error(e.getMessage());
            e.printStackTrace();
        }
    }

    private Map<String, NodeEntity> duplicateNodeEntity(List<NodeEntity> nodeEntities) {
        HashMap<String, NodeEntity> result = new HashMap<>();
        for (NodeEntity nodeEntity : nodeEntities) {
            if (result.containsKey(nodeEntity.getEntityName())) {
                //若存在重复，将现有entity和原有entity合并
                NodeEntity originEntity = alignmentEntity(result.get(nodeEntity.getEntityName()), nodeEntity);
                log.info("实体合并：合并完成【{}】", originEntity.getEntityName());
            } else {
                result.put(nodeEntity.getEntityName(), nodeEntity);
            }
        }
        return result;
    }

    //target合并到origin中
    private NodeEntity alignmentEntity(NodeEntity origin, NodeEntity target) {
        //合并数据属性
        origin.getProperties().putAll(target.getProperties());
        //合并来源文档
        origin.getDocIds().addAll(target.getDocIds());
        origin.getAnnexIds().addAll(target.getAnnexIds());
        origin.getDocumentUnitIds().addAll(target.getDocumentUnitIds());

        //合并from边
        Set<NodeRelation> targetFromEntity = target.getFromEntity();
        if (targetFromEntity != null) {
            targetFromEntity.forEach(relation -> {
                relation.setEnd(origin);
                Opt.ofNullable(origin.getFromEntity()).ifPresentOrElse((fromEntity) -> {
                    fromEntity.add(relation);
                }, () -> {
                    origin.setFromEntity(new HashSet<>(Collections.singletonList(relation)));
                });
                origin.getFromEntity().add(relation);
            });
        }

        //合并to边
        Set<NodeRelation> targetToEntity = target.getToEntity();
        if (targetToEntity != null) {
            targetToEntity.forEach(relation -> {
                relation.setStart(origin);
                Opt.ofNullable(origin.getToEntity()).ifPresentOrElse((toEntity) -> {
                    toEntity.add(relation);
                }, () -> {
                    origin.setToEntity(new HashSet<>(Collections.singletonList(relation)));
                });
                origin.getToEntity().add(relation);
            });
        }

        //删除origin中的原有【提及表彰荣誉】【提及人员】边
        if (origin.getFromEntity() != null) {
            origin.setFromEntity(
                    origin.getFromEntity().stream()
                            .filter(relation -> !ModelRelationConst.RELATION_TIJI_RENYUAN.equals(relation.getPredicateName())
                                    && !ModelRelationConst.RELATION_TIJI_BIAOZHANG_RONGYU.equals(relation.getPredicateName()
                            )).collect(Collectors.toSet()));
        }

        //删除origin中的原有【来源公文】【提及人员】边
        if (origin.getToEntity() != null) {
            origin.setToEntity(
                    origin.getToEntity().stream()
                            .filter(relation -> !ModelRelationConst.RELATION_TIJI_RENYUAN.equals(relation.getPredicateName())
                                    && !ModelRelationConst.REVERSE_RELATION_LAI_YUAN_GONG_WEN.equals(relation.getPredicateName()
                            )).collect(Collectors.toSet()));
        }

        //更新origin，删除target
        nodeEntityRepository.save(origin);
        nodeEntityRepository.deleteById(target.getId());

        return origin;
    }

    /**
     * 判断是否自动知识对齐
     *
     * @param resource 资源对象
     */
    private Boolean isAutoAlignment(Resource resource) {
        // 判断是否开启自动知识对齐
        WorkTask workTask = workTaskFeign.info(resource.getWorkTaskId()).getData();
        Process process = workTask.getProcess();
        for (ProcessNode kgProcessNode : process.getKgProcessNodes()) {
            if ("实体对齐环节".equals(kgProcessNode.getStageName())) {
                return kgProcessNode.getIsKnowledgeAlignment();
            }
        }
        return false;
    }

    /**
     * 生成缓存实例
     *
     * @param modelId       模型Id
     * @param resource      资源对象
     * @param autoAlignment 是否自动对齐
     */
    private Map<String, NodeEntity> generateNodeEntity(String modelId, Resource resource, boolean autoAlignment) {
        log.info(">>>>>>>>>>>>>>>>>>>>【主文件】开始生成实例>>>>>>>>>>>>>>>>>>>>");
        // 获取该附件的所有标签数据
        List<LabelData> LabelDataList = labelDataRepository.findByCondition(Filters.and(Filters.eq("resourceId", resource.getId()), Filters.eq("topLabel", false)));
        if (ObjectUtil.isEmpty(LabelDataList)) {
            log.info("该资源不存在标签，无需生成知识图谱!");
            return null;
        }
        // 生成主文件的顶级公文node实例
        Map<String, NodeEntity> entityMap = generateResourceTopNodeEntity(modelId, resource);
        // 再生成其它节点
        generateOtherEntity(modelId, resource, LabelDataList, entityMap, autoAlignment);
        log.info("<<<<<<<<<<<<<<<<<<<<【主文件】实例生成完毕<<<<<<<<<<<<<<<<<<<<");
        return entityMap;
    }

    /**
     * 生成主文件的顶级公文标签
     *
     * @param modelId  模型ID
     * @param resource 资源对象
     * @return labelDataId value：对应实例
     */
    @NotNull
    private Map<String, NodeEntity> generateResourceTopNodeEntity(String modelId, Resource resource) {
        // key: labelDataId value：对应实例
        Map<String, NodeEntity> entityMap = new HashMap<>();
        // 先获取顶级标签数据，并生成实例
        List<LabelData> topLabelDataList = labelDataRepository.findByCondition(Filters.and(Filters.eq("resourceId", resource.getId()), Filters.eq("topLabel", true)));
        LabelData topLabelData;
        // 如果存在顶级节点
        if (ObjectUtil.isNotEmpty(topLabelDataList)) {
            topLabelData = topLabelDataList.get(0);
            // 创建实例对象
            NodeEntity nodeEntity = new NodeEntity();
            nodeEntity.setEntityName(topLabelData.getContent());
            // 获取标签数据对应的标签对象
            Label label = labelRepository.findById(topLabelData.getLabelId());
            // 设置概念Id（根据labelId查询label对象，并获取该label的sourceId）
            nodeEntity.setConceptId(Optional.ofNullable(label.getSourceId()).orElse("附件"));
            // 设置通用的实例属性
            setNodeEntity(modelId, resource, topLabelData, nodeEntity);
            // 设置顶级节点标识
            nodeEntity.setTopNodeEntity(true);
            // 生成 顶级缓存实例
            NodeEntity topNodeEntity = nodeEntityRepository.save(nodeEntity);
            // 设置资源的顶级节点Id
            resource.setTopNodeId(topNodeEntity.getId());
            // 更新资源状态
            resourceFeign.save(resource);
            log.info("【主文件】生成顶级节点 ===> {}", nodeEntity.getEntityName());
            // 存储顶级实例
            entityMap.put(topLabelData.getId(), nodeEntity);
        }
        return entityMap;
    }


    /**
     * 生成剩余实例节点
     *
     * @param modelId       模型Id
     * @param resource      资源对象
     * @param LabelDataList 标签集合
     * @param entityMap     key：标签Id value：实例对象
     * @param autoAlignment 是否自动对齐
     */
    private void generateOtherEntity(String modelId, Resource resource, List<LabelData> LabelDataList, Map<String, NodeEntity> entityMap, boolean autoAlignment) {
        try {
            // 生成neo4j实例
            for1:
            for (LabelData labelData : LabelDataList) {
                // 获取标签数据对应的标签对象
                Label label = labelRepository.findById(labelData.getLabelId());
                if (label == null) continue;
                // 获取概念Id
                String conceptId = Opt.ofNullable(label.getSourceId()).orElse("");
                // 如果开启了自动对齐，则在生成实例前，查询是否已经存在相同实例
                if (autoAlignment) {
                    org.neo4j.ogm.cypher.Filters filters = new org.neo4j.ogm.cypher.Filters(new Filter("entityName", ComparisonOperator.EQUALS, labelData.getContent()));
                    filters.and(new Filter("conceptId", ComparisonOperator.EQUALS, conceptId));
                    List<NodeEntity> nodeEntityList = nodeEntityRepository.findByCondition(filters);
                    // 存在疑似相同实例，在判断属性，如果属性也相同则就是相同实例，属性不同，则是不同实例
                    if (ObjectUtil.isNotEmpty(nodeEntityList)) {
                        for (NodeEntity nodeEntity : nodeEntityList) {
                            // 判断属性
                            Map<String, Object> properties = nodeEntity.getProperties();
                            List<LabelPropertyVO> labelProperties = labelData.getLabelProperties();
                            // 如果存在属性
                            if (ObjectUtil.isNotEmpty(properties) && ObjectUtil.isNotEmpty(labelProperties)) {
                                // 遍历标签的属性
                                for (LabelPropertyVO labelProperty : labelProperties) {
                                    // 判断是否存在相同属性
                                    Object value = properties.get("prop_" + labelProperty.getIdentifier());
                                    if (value != null) {
                                        try {
                                            String valueStr = Convert.toStr(value);
                                            String propertyValue = labelProperty.getValue();
                                            if (ObjectUtil.equal(valueStr, propertyValue)) {
                                                log.info("【主文件】【{}】当前实例已存在，跳过该实例生成!", labelData.getContent());
                                                nodeEntity.getDocIds().add(labelData.getResourceId());
                                                entityMap.put(labelData.getId(), nodeEntity);
                                                continue for1;
                                            }
                                        } catch (Exception ignored) {
                                        }
                                    }
                                }
                            }
                            // 如果都不存在属性
                            if (ObjectUtil.isEmpty(properties) && ObjectUtil.isEmpty(labelProperties)) {
                                nodeEntity.getDocIds().add(labelData.getResourceId());
                                log.info("【主文件】【{}】当前实例已存在，跳过该实例生成!", labelData.getContent());
                                entityMap.put(labelData.getId(), nodeEntity);
                                continue for1;
                            }
                        }

                    }
                }
                // 创建缓存实例
                NodeEntity nodeEntity = new NodeEntity();
                // 设置实例名称
                nodeEntity.setEntityName(labelData.getContent());
                // 设置概念Id（根据labelId查询label对象，并获取该label的sourceId）
                nodeEntity.setConceptId(conceptId);
                // 设置通用的实例属性
                setNodeEntity(modelId, resource, labelData, nodeEntity);
                log.info("【主文件】生成实例 ===> {}", nodeEntity.getEntityName());
                // 新增实例
                nodeEntity = nodeEntityRepository.save(nodeEntity);
                // 存储其它实例
                entityMap.put(labelData.getId(), nodeEntity);
            }
        } catch (ServiceException ignored) {
        }
    }


    /**
     * 为实例节点设置通用属性
     *
     * @param modelId    模型Id
     * @param resource   资源
     * @param labelData  标签数据
     * @param nodeEntity 实例
     */
    private void setNodeEntity(String modelId, Resource resource, LabelData labelData, NodeEntity nodeEntity) {
        nodeEntity.setModelId(modelId);
        nodeEntity.getDocIds().add(resource.getId());
        nodeEntity.getDocumentUnitIds().addAll(labelData.getDocumentUnitIds());
        nodeEntity.setLabelDataId(labelData.getId());
        // 属性Map
        Map<String, Object> properties = Opt.ofNullable(nodeEntity.getProperties()).orElse(new HashMap<>());
        // 设置属性
        for (LabelPropertyVO labelProperty : labelData.getLabelProperties()) {
            // 根据属性标签Id，获取属性标签对象
            Label propertyLabel = Opt.ofNullable(labelRepository.findById(labelProperty.getLabelPropertyId())).orElseThrow(ServiceException::new, "该标签不存在");
            Object value = labelProperty.getValue();
            Date dateValue = labelProperty.getDateValue();
            // 设置 key：属性名 value：属性值
            properties.put("prop_" + propertyLabel.getIdentifier(), Opt.ofNullable(value).orElse(dateValue));
        }
        nodeEntity.setProperties(properties);
    }


    /**
     * 生成关系
     */
    private void generateRelation(Resource resource, Map<String, NodeEntity> entityMap, String dataSetId) {
        log.info(">>>>>>>>>>>>>>>>>>>>【主文件】开始建立关系>>>>>>>>>>>>>>>>>>>>");
        // 获取该资源下的所有关系三元组对象
        List<TripleLabelData> tripleLabelDataList = Opt.ofNullable(tripleLabelDataRepository.findByCondition(Filters.eq("resourceId", resource.getId())))
                .orElseThrow(ServiceException::new, "不存在指定关系!");
        // 遍历关系三元组
        final Map<String, String> lableData = new HashMap<>();
        for (TripleLabelData tripleLabelData : tripleLabelDataList) {
            System.out.println("<<<<<<<<<<<<<<<<匹配确认关系三元组信息>>>>>>>>>>>>>>>>>>>>" + tripleLabelData);
            if (tripleLabelData.getStartLabelDataId() == null || tripleLabelData.getEndLabelDataId() == null) continue;
            // 获取关系主语的缓存节点实例
            NodeEntity fromEntity = entityMap.get(tripleLabelData.getStartLabelDataId());
            if (fromEntity == null) continue;
            // 获取关系宾语的缓存节点实例
            NodeEntity toEntity = entityMap.get(tripleLabelData.getEndLabelDataId());
            if (toEntity == null) continue;
            // 根据relationId，获取该谓词标签 对应的关系库中的 正向和反向谓词
//            Bson filter = Filters.eq(new ObjectId());
//            Bson filter1 = Filters.eq("dataSetId", dataSetId);
            if (ObjectUtil.isEmpty(tripleLabelData.getRelationLabelId())) continue;
            List<Label> relationLabel = labelRepository.findByCondition(Condition.getFilter(Map.of("id", tripleLabelData.getRelationLabelId(), "dataSetId", dataSetId), Label.class));
//            Label relationLabel = labelRepository.findOne(Filters.and(filter, filter1));
//            Label relationLabel = labelRepository.findById(tripleLabelData.getRelationLabelId());
            if (relationLabel.size() == 0) continue;
            // 获取对应关系库的关系Id
            KnowledgeRelation knowledgeRelation = knowledgeRelationRepository.findById(relationLabel.get(0).getSourceId());
            if (knowledgeRelation == null) continue;

            /*// 去重
            String s = lableData.getOrDefault(tripleLabelData.getRelationLabelId(), "");
            final String startLabelId = tripleLabelData.getStartLabelId();
            final String endLabelId = tripleLabelData.getEndLabelId();
            if(ObjectUtil.isEmpty(s)) {
                lableData.put(tripleLabelData.getRelationLabelId(), startLabelId + endLabelId);
            } else {
                if(ObjectUtil.equals(s, startLabelId + endLabelId)) {
                    continue;
                }
            }*/

            // 生成正向关系和逆向关系
            NodeRelation forwardRelation = new NodeRelation();
            forwardRelation.setPositive(true);
            // 谓词名称
            forwardRelation.setPredicateName(knowledgeRelation.getForwardPre());
            // 生成关系
            generateNodeRelation(resource, tripleLabelData, fromEntity, toEntity, knowledgeRelation, forwardRelation);
            // 非对称性关系，生成反向关系
            if (!knowledgeRelation.getIsSymmetric()) {
                // 反向关系
                NodeRelation inverseRelation = new NodeRelation();
                inverseRelation.setPositive(false);
                inverseRelation.setPredicateName(knowledgeRelation.getInversePre());
                // 生成关系
                generateNodeRelation(resource, tripleLabelData, toEntity, fromEntity, knowledgeRelation, inverseRelation);
            }
        }
        log.info("<<<<<<<<<<<<<<<<<<<<【主文件】关系建立完毕<<<<<<<<<<<<<<<<<<<<");
    }

    /**
     * 设置实例节点
     *
     * @param resource          资源对象
     * @param tripleLabelData   关系三元组
     * @param fromEntity        主语
     * @param toEntity          宾语
     * @param knowledgeRelation 关系库对象
     * @param relation          关系对象
     */
    private void generateNodeRelation(Resource resource, TripleLabelData tripleLabelData, NodeEntity fromEntity, NodeEntity toEntity, KnowledgeRelation knowledgeRelation, NodeRelation relation) {
        // 谓词Id
        relation.setPredicateId(knowledgeRelation.getId());
        // 来源公文
        relation.getDocIds().add(resource.getId());
        Map<String, Object> forwardRelationProperties = Opt.ofNullable(relation.getProperties()).orElse(new HashMap<>());
        // 设置属性
        for (LabelPropertyVO relationProperty : tripleLabelData.getRelationProperties()) {
            // 获取属性名称
            Label propertyLabel = Opt.ofNullable(labelRepository.findById(relationProperty.getLabelPropertyId())).get();
            if(propertyLabel == null) continue;
            // 添加属性
            Object value = relationProperty.getValue();
            Date dateValue = relationProperty.getDateValue();
            forwardRelationProperties.put("prop_" + propertyLabel.getIdentifier(), Opt.ofNullable(value).orElse(dateValue));
        }
        relation.setProperties(forwardRelationProperties);
        // 设置正向关系的起点和终点
        relation.setStart(fromEntity);
        relation.setEnd(toEntity);
        nodeRelationRepository.save(relation);
    }

}