package org.irm.lab.kg.algorithm.ruleMatching.lingdaopifu;

import static org.bouncycastle.asn1.x500.style.RFC4519Style.name;

import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.log4j.Log4j2;
import org.dom4j.Document;
import org.dom4j.DocumentException;
import org.dom4j.Element;
import org.dom4j.io.SAXReader;
import org.dromara.streamquery.stream.core.stream.Steam;
import org.irm.lab.common.api.R;
import org.irm.lab.common.enums.AlgorithmType;
import org.irm.lab.kg.algorithm.IAlgorithmProcessor;
import org.irm.lab.kg.algorithm.ruleMatching.RuleParsingEntity;
import org.irm.lab.kg.algorithm.ruleMatching.RuleParsingRelation;
import org.irm.lab.kg.algorithm.ruleMatching.RuleParsingResult;
import org.irm.lab.kg.constant.LabelConstant;
import org.irm.lab.kg.constant.RuleMatchingConstant;
import org.irm.lab.kg.entity.processing.process.ProcessRecords;
import org.irm.lab.kg.repository.processing.process.ProcessRecordsRepository;
import org.irm.lab.kg.service.impl.kgprocess.ruleMatching.DocRuleParsing;
import org.irm.lab.repository.constant.DocumentResolvedStatus;
import org.irm.lab.repository.entity.Resource;
import org.irm.lab.repository.entity.ResourceAnnex;
import org.irm.lab.repository.feign.ResourceAnnexFeign;
import org.irm.lab.resource.entity.Attach;
import org.irm.lab.resource.feign.AttachFeign;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

import com.mongodb.client.model.Filters;

/**
 * <AUTHOR>
 * @date 2023/5/4 16:29
 */
@Log4j2
@Service
@RequiredArgsConstructor
public class BAN_LI_DAN implements IAlgorithmProcessor {

    public static final Set<String> leadershipSet = new HashSet<>(List.of("温枢刚", "邓建玲", "王森", "樊启祥", "王益华", "王文宗", "王利民", "李向良", "张涛"));

    private final ResourceAnnexFeign resourceAnnexFeign;
    private final AttachFeign attachFeign;
    @javax.annotation.Resource(name = "annexDocRuleParsing")
    private DocRuleParsing docRuleParsing;
    private final ProcessRecordsRepository processRecordsRepository;

    @Override
    public AlgorithmType getCode() {
        return AlgorithmType.BAN_LI_DAN;
    }


    @Override
    public void process(Resource resource) {
        // 获取该资源的全部附件
        R<List<ResourceAnnex>> resourceAnnexListR = resourceAnnexFeign.listByResourceId(resource.getId());
        List<ResourceAnnex> resourceAnnexList = resourceAnnexListR.getData();
        if (!resourceAnnexListR.isSuccess() || ObjectUtil.isEmpty(resourceAnnexList)) {
            log.info("该资源不存在附件，无需附件规则解析!");
            return;
        }
        // 调用附件解析
        for (ResourceAnnex resourceAnnex : resourceAnnexList) {
            /*if (DocumentResolvedStatus.RESOLVED.equals(resourceAnnex.getRuleStatus())) {
                log.info("该附件已解析!");
                continue;
            }*/
            final List<ProcessRecords> processRecords = processRecordsRepository.findByCondition(Filters.eq("resourceId", resourceAnnex.getResourceId()));
            if (ObjectUtil.isNotEmpty(processRecords)) {
                log.info("文件存在批示信息，开始删除批示信息，批示个数是「{}」", processRecords.size());
                processRecordsRepository.deleteManyByIds(Steam.of(processRecords).map(ProcessRecords::getId).toList());
            }
            final List<ProcessRecords> processRecordsList = processPlus(resourceAnnex.getPrimaryFileId(), resource.getId());
            processRecordsRepository.saveAll(processRecordsList);
            annexProcess(resourceAnnex);
        }

    }

    /**
     * 附件解析（办理单）
     *
     * @param resourceAnnex 附件
     */
    @Override
    public void annexProcess(ResourceAnnex resourceAnnex) {
        //获取附件attach
        Attach attach = attachFeign.info(resourceAnnex.getPrimaryFileId()).getData();
        // 只处理xml类型办理单文件
        if (attach != null && !"xml".equals(attach.getFileExtension())) return;
        log.info(">>>>>>>>>>【附件】【办理单】规则解开启>>>>>>>>>>");
        try {
            resourceAnnex.setRuleStatus(DocumentResolvedStatus.RESOLVING);
            resourceAnnexFeign.save(resourceAnnex);
            // 调用办理单规则解析
            JSONObject results = processPlus(attach.getLink());
            log.info("【附件】【办理单】规则解析结果:{}", results.toString());
            // 结果转换
            RuleParsingResult ruleParsingResult = JSONUtil.toBean(results, RuleParsingResult.class, true);
            log.info("【附件】【办理单】规则解析结果:{}", ruleParsingResult.toString());
            // 获取关系
            List<RuleParsingRelation> relationList = ruleParsingResult.getRelation();
            // 对办理单类型的文件的关系进行处理
            relationList = relationList.stream().peek(relation -> {
                if ("0".equals(relation.getStart())) {
                    relation.setStart("1");
                } else if ("0".equals(relation.getEnd())) {
                    relation.setEnd("1");
                }
            }).collect(Collectors.toList());
            // 实例处理
            List<RuleParsingEntity> entityList = ruleParsingResult.getResult().stream().filter(ruleParsingEntity -> "概念".equals(ruleParsingEntity.getType())).collect(Collectors.toList());
            List<RuleParsingEntity> propertyList = ruleParsingResult.getResult().stream().filter(ruleParsingEntity -> "属性".equals(ruleParsingEntity.getType())).collect(Collectors.toList());
            // 规则解析生成标签
            docRuleParsing.ruleParsingCacheLabelData(resourceAnnex, entityList, propertyList, relationList);
            resourceAnnex.setRuleStatus(DocumentResolvedStatus.RESOLVED);
            log.info("<<<<<<<<<<【附件】【办理单】规则解结束<<<<<<<<<<");
        } catch (Exception e) {
            resourceAnnex.setRuleStatus(DocumentResolvedStatus.RESOLVED_FAILED);
            log.error("解析失败 {}", e.getMessage());
        } finally {
            resourceAnnexFeign.save(resourceAnnex);
        }
    }

    public JSONObject processPlus(String link) {


        Document doc = null;
        try {
            SAXReader reader = new SAXReader();
            doc = reader.read(link);
        } catch (DocumentException e) {
            e.printStackTrace();
        }
        JSONObject result = initJSONObject();
        JSONArray persons = result.getJSONArray(RuleMatchingConstant.RESULT);
        JSONArray relations = result.getJSONArray(RuleMatchingConstant.RELATION);
        assert doc != null;

        Element root = doc.getRootElement();

        // 检查根元素或子元素的名称，以判断结构
        if ("办理过程字段内容".equals(root.getName())) {
            // 处理带有ItemInfo作为子元素的结构
            log.info("存在办理过程字段内容");
            processItemInfo(root.element("ItemInfo"), persons, relations);
        } else {
            // 如果根元素就是ItemInfo，直接处理
            log.info("不存在办理过程字段内容");
            processItemInfo(root, persons, relations);
        }
        return result;
        /*Element root2 = elements.get(0);
        List<Element> elements1 = root2.elements();*/




    }

    private  void processItemInfo(Element itemInfo, JSONArray persons, JSONArray relations) {
        List<Element> items = itemInfo.elements();
        for (Element item : items) {

            processItem(item, persons, relations);

        }
    }

    private  void processItem(Element item, JSONArray persons, JSONArray relations) {
        List<Element> children = item.elements();
        if (!children.isEmpty() && children.get(0).getName().startsWith("Field")) {
            // 假设是第一种格式，每个子元素都是Field
            // 处理当前节点办理人员
            final String personId = IdUtil.simpleUUID();
            final JSONObject attribute = JSONUtil.createObj();

            boolean foundCurrentNode = false;  // 标志位，用于检查是否找到当前节点
            String currentNodeHandler = null;  // 用于存储当前节点办理人员
            for (Element field : children) {
                String name = field.attributeValue("Name");
                String value = field.attributeValue("Value");

                // 检查当前字段是否为“当前节点”，如果是，则设置标志位
                if (ObjectUtil.equals(name, "当前节点")) {
                    foundCurrentNode = true;  // 将标志位设为 true，表示已找到当前节点
                } else if (foundCurrentNode && ObjectUtil.equals(name, "办理人员") && !ObjectUtil.isEmpty(value)) {
                    currentNodeHandler = value;  // 如果找到了当前节点并且下一个办理人员字段不为空，则记录办理人员
                    foundCurrentNode = false;  // 重置标志位
                }

                // 其他逻辑处理，如获取办理意见、办理序号等
                if (ObjectUtil.equals(name, RuleMatchingConstant.HANDLING_ADVICE)) {
                    attribute.putOpt(RuleMatchingConstant.HANDLING_ADVICE, ObjectUtil.isEmpty(value) ? RuleMatchingConstant.NO_CHANCE : value);
                }

                // 处理完办理人员后，如果是当前节点的办理人员，进行相应的处理
                if (currentNodeHandler != null) {
                    persons.add(newEntity(personId, LabelConstant.CONCEPT, LabelConstant.PEOPLE, currentNodeHandler));

                    final String uuid = IdUtil.simpleUUID();
                    if (leadershipSet.contains(currentNodeHandler)) {
                        persons.add(newEntity(uuid, LabelConstant.PROPERTY, RuleMatchingConstant.PEOPLE_TYPE, RuleMatchingConstant.HIGH_LEADER));
                    } else {
                        persons.add(newEntity(uuid, LabelConstant.PROPERTY, RuleMatchingConstant.PEOPLE_TYPE, RuleMatchingConstant.ORDINARY_PERSONNEL));
                    }
                    relations.add(newRelation(LabelConstant.PROPERTY, personId, uuid));
                    currentNodeHandler = null;  // 重置当前节点办理人员变量
                }
                // 获取办理意见
                if (ObjectUtil.equals(name, RuleMatchingConstant.HANDLING_ADVICE)) {

                    if (StrUtil.isNotBlank(value)) {
                        final String s = IdUtil.simpleUUID();
                        // 保存办理意见概念
                        persons.add(newEntity(s, LabelConstant.CONCEPT, LabelConstant.INSTRUCTION, value));

                        relations.add(newRelation(RuleMatchingConstant.LEADER_INSTRUCTION, personId, s));
                    }
                    attribute.putOpt(RuleMatchingConstant.HANDLING_ADVICE, ObjectUtil.isEmpty(value) ? RuleMatchingConstant.NO_CHANCE : value);
                }
                // 获取办理序号
                if (ObjectUtil.equals(name, RuleMatchingConstant.SERIAL_NUMBER_NEW)) {
                    attribute.putOpt(RuleMatchingConstant.SERIAL_NUMBER, value);
                }
                // 获取发送时间
                if(ObjectUtil.equals(name, RuleMatchingConstant.SEND_TIME)){
                    attribute.putOpt(RuleMatchingConstant.START_TIME, value);
                }
                // 获取结束时间
                if(ObjectUtil.equals(name, RuleMatchingConstant.COMPLETION_TIME)){
                    attribute.putOpt(RuleMatchingConstant.FINISH_TIME, value);
                }
            }

// 添加其他关系类型
            relations.add(newRelation(RuleMatchingConstant.TRANSACTION_FILE, personId, RuleMatchingConstant.ZERO).putOpt(RuleMatchingConstant.ATTRIBUTE, attribute));
            relations.add(newRelation(RuleMatchingConstant.TRANSACTION_PEOPLE, RuleMatchingConstant.ZERO, personId));
        } else {

            // 处理当前节点办理人员
            final String personId = IdUtil.simpleUUID();
            final JSONObject attribute = JSONUtil.createObj();
            // 假设是第二种格式，直接是具体的字段
            for (Element field : children) {

                if (ObjectUtil.equals(field.getName(), RuleMatchingConstant.CURRENT_NODE_HANDLER)) {

                    final String name = field.getText();
                    persons.add(newEntity(personId, LabelConstant.CONCEPT, LabelConstant.PEOPLE, name));

                    final String uuid = IdUtil.simpleUUID();
                    if (leadershipSet.contains(name)) {
                        persons.add(newEntity(uuid, LabelConstant.PROPERTY, RuleMatchingConstant.PEOPLE_TYPE, RuleMatchingConstant.HIGH_LEADER));
                    } else {
                        persons.add(newEntity(uuid, LabelConstant.PROPERTY, RuleMatchingConstant.PEOPLE_TYPE, RuleMatchingConstant.ORDINARY_PERSONNEL));
                    }
                    relations.add(newRelation(LabelConstant.PROPERTY, personId, uuid));

                }


                // 获取办理意见
                if (ObjectUtil.equals(field.getName(), RuleMatchingConstant.HANDLING_ADVICE)) {

                    final String nineValue = field.getText();
                    attribute.putOpt(RuleMatchingConstant.HANDLING_ADVICE, ObjectUtil.isEmpty(nineValue) ? RuleMatchingConstant.NO_CHANCE : nineValue);
                }
                // 获取办理序号
                if (ObjectUtil.equals(field.getName(), RuleMatchingConstant.SERIAL_NUMBER_NEW)) {
                    attribute.putOpt(RuleMatchingConstant.SERIAL_NUMBER, field.getText());
                }
                // 获取发送时间
                if(ObjectUtil.equals(field.getName(), RuleMatchingConstant.SEND_TIME)){
                    attribute.putOpt(RuleMatchingConstant.START_TIME, field.getText());
                }
                // 获取结束时间
                if(ObjectUtil.equals(field.getName(), RuleMatchingConstant.COMPLETION_TIME)){
                    attribute.putOpt(RuleMatchingConstant.FINISH_TIME, field.getText());
                }
                // 获取办理意见
                if(ObjectUtil.equals(field.getName(), RuleMatchingConstant.HANDLING_ADVICE)){

                    if (StrUtil.isNotBlank(field.getText())) {
                        final String s = IdUtil.simpleUUID();
                        // 保存办理意见概念
                        persons.add(newEntity(s, LabelConstant.CONCEPT, LabelConstant.INSTRUCTION, field.getText()));

                        relations.add(newRelation(RuleMatchingConstant.LEADER_INSTRUCTION, personId, s));
                    }

                    attribute.putOpt(RuleMatchingConstant.HANDLING_ADVICE, ObjectUtil.isEmpty(field.getText()) ? RuleMatchingConstant.NO_CHANCE : field.getText());
                }

            }
            relations.add(newRelation(RuleMatchingConstant.TRANSACTION_FILE, personId, RuleMatchingConstant.ZERO).putOpt(RuleMatchingConstant.ATTRIBUTE, attribute));
            relations.add(newRelation(RuleMatchingConstant.TRANSACTION_PEOPLE, RuleMatchingConstant.ZERO, personId));
        }
    }


    public List<ProcessRecords> processPlus(String link, String resourceId) {


        org.dom4j.Document doc = null;
        try {
            SAXReader reader = new SAXReader();
            doc = reader.read(link);
        } catch ( org.dom4j.DocumentException e) {
            e.printStackTrace();
        }


        assert doc != null;

        Element root = doc.getRootElement();

        List<ProcessRecords> processRecordsByResource;

        // 检查根元素或子元素的名称，以判断结构
        if ("办理过程字段内容".equals(root.getName())) {
            // 处理带有ItemInfo作为子元素的结构
            log.info("存在办理过程字段内容");
            processRecordsByResource = processItemInfo(root.element("ItemInfo"),resourceId);
        } else {
            // 如果根元素就是ItemInfo，直接处理
            log.info("不存在办理过程字段内容");
            processRecordsByResource = processItemInfo(root,resourceId);
        }
        return processRecordsByResource;
        /*Element root2 = elements.get(0);
        List<Element> elements1 = root2.elements();*/




    }

    private  List<ProcessRecords>  processItemInfo(Element itemInfo,String resourceId) {
        List<Element> items = itemInfo.elements();
        List<ProcessRecords> processRecordsList = new ArrayList<>();
        for (Element item : items) {

            final ProcessRecords processRecords = processItem(item, resourceId);
            processRecordsList.add(processRecords);
        }
        return processRecordsList;
    }

    private  ProcessRecords processItem(Element item,  String resourceId) {
        final ProcessRecords processRecords = new ProcessRecords();
        processRecords.setResourceId(resourceId);

        List<Element> children = item.elements();
        if (!children.isEmpty() && children.get(0).getName().startsWith("Field")) {
            // 假设是第一种格式，每个子元素都是Field
            // 处理当前节点办理人员

            final JSONObject attribute = JSONUtil.createObj();

            boolean foundCurrentNode = false;  // 标志位，用于检查是否找到当前节点
            String currentNodeHandler = null;  // 用于存储当前节点办理人员
            for (Element field : children) {
                String name = field.attributeValue("Name");
                String value = field.attributeValue("Value");

                // 检查当前字段是否为“当前节点”，如果是，则设置标志位
                if (ObjectUtil.equals(name, "当前节点")) {
                    foundCurrentNode = true;  // 将标志位设为 true，表示已找到当前节点
                } else if (foundCurrentNode && ObjectUtil.equals(name, "办理人员") && !ObjectUtil.isEmpty(value)) {
                    currentNodeHandler = value;  // 如果找到了当前节点并且下一个办理人员字段不为空，则记录办理人员
                    processRecords.setHandler(value);
                    foundCurrentNode = false;  // 重置标志位
                }

                // 其他逻辑处理，如获取办理意见
                if (ObjectUtil.equals(name, RuleMatchingConstant.HANDLING_ADVICE)) {
                    processRecords.setContent(ObjectUtil.isEmpty(value) ? RuleMatchingConstant.NO_CHANCE : value);
                    attribute.putOpt(RuleMatchingConstant.HANDLING_ADVICE, ObjectUtil.isEmpty(value) ? RuleMatchingConstant.NO_CHANCE : value);
                }

                // 处理完办理人员后，如果是当前节点的办理人员，进行相应的处理
                if (currentNodeHandler != null) {

                    currentNodeHandler = null;  // 重置当前节点办理人员变量
                }

                // 获取办理序号
                if (ObjectUtil.equals(name, RuleMatchingConstant.SERIAL_NUMBER_NEW)) {
                    processRecords.setSort(value);
                    attribute.putOpt(RuleMatchingConstant.SERIAL_NUMBER, value);
                }
                // 获取发送时间
                if(ObjectUtil.equals(name, RuleMatchingConstant.SEND_TIME)){
                    processRecords.setSendTime(value);
                    attribute.putOpt(RuleMatchingConstant.START_TIME, value);
                }
                // 获取结束时间
                if(ObjectUtil.equals(name, RuleMatchingConstant.COMPLETION_TIME)){
                    processRecords.setFinishTime(value);
                    attribute.putOpt(RuleMatchingConstant.FINISH_TIME, value);
                }
            }

        } else {

            // 假设是第二种格式，直接是具体的字段
            for (Element field : children) {

                if (ObjectUtil.equals(field.getName(), RuleMatchingConstant.CURRENT_NODE_HANDLER)) {

                    final String name = field.getText();
                    processRecords.setHandler(name);

                }


                // 获取办理意见
                if (ObjectUtil.equals(field.getName(), RuleMatchingConstant.HANDLING_ADVICE)) {

                    final String nineValue = field.getText();
                    processRecords.setContent(ObjectUtil.isEmpty(nineValue) ? RuleMatchingConstant.NO_CHANCE : nineValue);
                }
                // 获取办理序号
                if (ObjectUtil.equals(field.getName(), RuleMatchingConstant.SERIAL_NUMBER_NEW)) {
                    processRecords.setSort(field.getText());
                }
                // 获取发送时间
                if(ObjectUtil.equals(field.getName(), RuleMatchingConstant.SEND_TIME)){
                    processRecords.setSendTime(field.getText());
                }
                // 获取结束时间
                if(ObjectUtil.equals(field.getName(), RuleMatchingConstant.COMPLETION_TIME)){
                    processRecords.setFinishTime(field.getText());
                }


            }

        }
        return processRecords;
    }

}
