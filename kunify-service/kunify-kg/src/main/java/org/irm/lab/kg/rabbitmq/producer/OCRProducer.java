package org.irm.lab.kg.rabbitmq.producer;

import org.irm.lab.kg.algorithm.DocumentImage;


import org.irm.lab.kg.message.OCRMessage;


import org.irm.lab.repository.entity.Resource;

import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import lombok.RequiredArgsConstructor;

/**
     
 * @date 2024/2/27 <br/>
      
 */
@Component
@RequiredArgsConstructor
public class OCRProducer {
    private final RabbitTemplate rabbitTemplate;

    @Value("${spring.application.name}")
    private String KEY_SELF;

    /**
     * 发送消息
     */
    public void sendMessage(String user, Resource resourceAnnex, DocumentImage annexDocumentImage, Integer page, Boolean isEnd){
        OCRMessage documentParsingMessage = new OCRMessage();
        documentParsingMessage.setUser(user);
        documentParsingMessage.setResource(resourceAnnex);
        documentParsingMessage.setDocumentImage(annexDocumentImage);
        documentParsingMessage.setPage(page);
        documentParsingMessage.setIsEnd(isEnd);
        // 发送消息
        rabbitTemplate.convertAndSend(OCRMessage.EXCHANGE,OCRMessage.ROUTING_KEY+KEY_SELF,documentParsingMessage);
    }
}
