package org.irm.lab.kg.kgprocess.strategy;

import org.irm.lab.repository.entity.Resource;


/**
 * <AUTHOR>
 * @date 2023/4/25 18:23
 * @description 加工能力
 */

public interface ProcessingStrategy {
    /**
     * 主文件加工
     *
     * @param resource 资源对象
     */
    void process(Resource resource);

    /**
     * 附件加工
     *
     * @param resource 资源对象
     */
    default void annexProcess(Resource resource){
    }

    String getCode();

    String getName();
}
