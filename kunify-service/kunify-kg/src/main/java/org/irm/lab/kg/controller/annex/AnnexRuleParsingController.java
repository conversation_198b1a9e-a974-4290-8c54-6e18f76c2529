package org.irm.lab.kg.controller.annex;

import cn.hutool.json.JSONArray;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.irm.lab.common.annotation.MyLog;
import org.irm.lab.common.api.R;
import org.irm.lab.common.constant.LogConstant;
import org.irm.lab.kg.entity.annex.processing.AnnexCacheLabelData;
import org.irm.lab.kg.entity.annex.processing.AnnexCacheTripleLabelData;
import org.irm.lab.kg.entity.processing.CacheLabelData;
import org.irm.lab.kg.service.annex.IAnnexCacheLabelDataService;
import org.irm.lab.kg.service.annex.IAnnexRuleParsingGraphService;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2023/5/9 14:35
 * @description 附件规则解析控制器
 */
@RestController
@RequestMapping("/annex-rule-parsing")
@RequiredArgsConstructor
public class AnnexRuleParsingController {
    private final IAnnexRuleParsingGraphService iAnnexRuleParsingGraphService;
    private final IAnnexCacheLabelDataService iAnnexCacheLabelDataService;

    /**
     * 附件规则解析 ===> 获取标签数据树
     *
     * @param annexId   附件Id
     * @param dataSetId 数据集Id
     * @return 标签数据
     */
    @ApiOperation(value = "获取标签数据树")
    @GetMapping("/relevance")
    public R<JSONArray> relevance(@RequestParam String annexId, @RequestParam String dataSetId) {
        return R.data(iAnnexRuleParsingGraphService.relevanceShow(annexId, dataSetId));
    }

    /**
     * 获取该附件下的所有缓存标签数据
     *
     * @param annexId 附件Id
     * @return {@link AnnexCacheLabelData}
     */
    @ApiOperation(value = "获取该附件下的所有缓存标签数据", hidden = true)
    @GetMapping("/list-label-data")
    public R<List<AnnexCacheLabelData>> listLabelData(@RequestParam String annexId) {
        return R.data(iAnnexRuleParsingGraphService.listCacheLabelData(annexId));
    }


    /**
     * 获取指定概念下的所有实例标签
     *
     * @param annexId 附件Id
     * @param labelId 标签Id（概念Id）
     * @return {@link CacheLabelData}
     */
    @ApiOperation(value = "获取指定概念下的所有实例标签", hidden = true)
    @GetMapping("/list-label-data-condition")
    public R<List<AnnexCacheLabelData>> listLabelDataCondition(@RequestParam String annexId, @RequestParam String labelId) {
        return R.data(iAnnexCacheLabelDataService.findByCondition(Map.of("annexId", annexId, "labelId", labelId, "type", "规则解析")));
    }


    /**
     * 新增或修改标签数据对象
     *
     * @param annexCacheLabelData {@link AnnexCacheLabelData}
     */
    @ApiOperation(value = "新增或修改标签数据对象", hidden = true)
    @PostMapping("/save-label-data")
    public R<String> saveOrUpdateLabelData(@RequestBody AnnexCacheLabelData annexCacheLabelData) {
        iAnnexRuleParsingGraphService.saveOrUpdateLabelData(annexCacheLabelData);
        return R.success();
    }

    /**
     * 删除实例/实例的属性
     *
     * @param annexCacheLabelData 附件缓存标签数据实例
     */
    @ApiOperation(value = "删除实例/实例的属性", hidden = true)
    @PostMapping("/remove-label-data")
    public R<String> removeLabelData(@RequestBody AnnexCacheLabelData annexCacheLabelData) {
        iAnnexRuleParsingGraphService.removeLabelData(annexCacheLabelData);
        return R.success();
    }


    /**
     * 新增/修改关系三元组
     *
     * @param annexCacheTripleLabelData {@link AnnexCacheTripleLabelData}
     */
    @ApiOperation(value = "新增/修改关系三元组", hidden = true)
    @PostMapping("/save-triple-label-data")
    public R<String> saveOrUpdateTripleLabelData(@RequestBody List<AnnexCacheTripleLabelData> annexCacheTripleLabelData) {
        iAnnexRuleParsingGraphService.saveOrUpdateTripleLabelData(annexCacheTripleLabelData);
        return R.success();
    }

    /**
     * 删除关系三元组/属性
     *
     * @param annexCacheTripleLabelData 附件关系三元组
     */
    @ApiOperation(value = "删除关系三元组/属性", hidden = true)
    @PostMapping("/remove-triple-label-data")
    public R<String> removeTripleLabelData(@RequestBody AnnexCacheTripleLabelData annexCacheTripleLabelData) {
        iAnnexRuleParsingGraphService.removeTripleLabelData(annexCacheTripleLabelData);
        return R.success();
    }


    /**
     * 解析确认 ===> 规则解析结果确认
     *
     * @param annexId 附件Id
     */
    @MyLog(menu = LogConstant.MENU_KNOWLEDGE, dataType = LogConstant.DATA_ANNEX, operation = LogConstant.OPERATION_RULE_ANALYSIS_TRUE)
    @ApiOperation(value = "解析确认")
    @PostMapping("/parsing-confirm")
    public R<String> parsingConfirm(@RequestParam String annexId) {
        iAnnexRuleParsingGraphService.parsingConfirm(annexId);
        return R.success();
    }

    /**
     * 重新解析  ===> 附件重新进行规则解析
     *
     * @param annexId 附件Id
     */
    @MyLog(menu = LogConstant.MENU_KNOWLEDGE, dataType = LogConstant.DATA_ANNEX, operation = LogConstant.OPERATION_RULE_REANALYSIS)
    @ApiOperation(value = "重新解析")
    @PostMapping("/re-parsing")
    public R<String> reMatch(@RequestParam String annexId) {
        iAnnexRuleParsingGraphService.reRuleParsing(annexId);
        return R.success();
    }


}
