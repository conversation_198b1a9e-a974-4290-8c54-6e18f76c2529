package org.irm.lab.kg.mongoService;

import org.irm.lab.kg.entity.KnowledgeConcept;

import com.anwen.mongo.service.IService;

/**
 * <AUTHOR> <br/>
 * @date 2024/4/10 <br/>
 * @Copyright 博客：https://eliauku.gitee.io/  ||  per aspera and astra <br/>
 */
public interface KnowledgeConceptService extends IService<KnowledgeConcept> {

    /**
     * 根据id查询数据是否存在
     */
    boolean existById(String id);

}
