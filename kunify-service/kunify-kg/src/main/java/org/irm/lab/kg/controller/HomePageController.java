package org.irm.lab.kg.controller;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.irm.lab.common.api.R;
import org.irm.lab.kg.service.IHomePageService;
import org.irm.lab.kg.vo.ResourceCountVO;
import org.irm.lab.kg.vo.node.LineChartVO;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

@RestController
@RequestMapping("/home-page")
@Api(value = "首页")
public class HomePageController {

    @Resource
    private IHomePageService homePageService;

    @ApiOperation(value = "数量展示", hidden = true)
    @GetMapping("/count-show")
    public R<List<ResourceCountVO>> countShow() {
        return R.data(homePageService.countShow());
    }

    @ApiOperation(value = "折线图展示", hidden = true)
    @GetMapping("/line-chart")
    public R<List<LineChartVO>> getLineChart() {
        return R.data(homePageService.getLineChart());
    }
}
