package org.irm.lab.kg.service.annex;

/**
 * <AUTHOR>
 * @date 2023/5/8 15:28
 * @description 附件文档解析业务接口
 */
public interface IAnnexDocumentParsingService {

    /**
     * 重新进行附件文档解析
     *
     * @param resourceAnnexId 附件Id
     */
    void reParsing(String resourceAnnexId);

    /**
     * 附件文档解析结果确认
     *
     * @param resourceAnnexId 附件Id
     */
    void confirm(String resourceAnnexId);

    /**
     * 使用OCR重新进行附件文档解析
     *
     * @param resourceAnnexId 附件Id
     */
    void reParsingWithOCR(String resourceAnnexId);
}
