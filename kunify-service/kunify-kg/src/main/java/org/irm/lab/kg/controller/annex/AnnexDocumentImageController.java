package org.irm.lab.kg.controller.annex;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.irm.lab.common.api.R;
import org.irm.lab.common.utils.ThreadLocalUtil;
import org.irm.lab.kg.rabbitmq.producer.EsSyncProducer;
import org.irm.lab.kg.service.annex.IAnnexDocumentImageService;
import org.irm.lab.repository.entity.Resource;
import org.irm.lab.repository.repository.ResourceRepository;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

@RestController
@RequestMapping("/annex-document-image")
@RequiredArgsConstructor
@Api(value = "附件图片")
public class AnnexDocumentImageController {

    private final IAnnexDocumentImageService iAnnexDocumentImageService;
    private final EsSyncProducer esSyncProducer;

    private final ResourceRepository resourceRepository;
    @PostMapping("/test")
    public void test(String id) {
        final Resource byId = resourceRepository.findById(id);


        esSyncProducer.sendMessage(ThreadLocalUtil.get("user"),byId,null, "asdasdad", "主文件");

    }

    /**
     * 根据附件id和页码，获取指定页码的图片
     *
     * @param annexId 附件id
     * @param page    页码
     * @return 图片地址
     */
    @ApiOperation(value = "获取当前图片路径", hidden = true)
    @GetMapping("/get-page-picture")
    public R<String> getPagePicture(@RequestParam String annexId, @RequestParam Integer page) {
        return R.data(iAnnexDocumentImageService.getPagePicture(annexId, page));
    }


    /**
     * 根据附件id获取当前附件的所有图片预览地址
     *
     * @param annexId 附件Id
     * @return 图片预览地址列表
     */
    @ApiOperation(value = "获取当前文件图片", hidden = true)
    @GetMapping("/get-annex-image")
    public R<List<String>> getResourceImage(@RequestParam String annexId) {
        return R.data(iAnnexDocumentImageService.getResourceImage(annexId));
    }
}
