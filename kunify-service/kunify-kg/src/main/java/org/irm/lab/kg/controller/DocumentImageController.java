package org.irm.lab.kg.controller;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.irm.lab.common.api.R;
import org.irm.lab.kg.service.IDocumentImageService;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

@RestController
@RequestMapping("/document-image")
@Api(value = "文档图片")
public class DocumentImageController {

    @Resource
    private IDocumentImageService documentImageService;

    @ApiOperation(value = "获取当前图片路径", hidden = true)
    @GetMapping("/get-page-picture")
    public R<String> getPagePicture(@RequestParam String resourceId, @RequestParam String page) {
        return R.data(documentImageService.getPagePicture(resourceId, page));
    }

    @ApiOperation(value = "获取当前文件图片", hidden = true)
    @GetMapping("/get-resource-image")
    public R<List<String>> getResourceImage(@RequestParam String resourceId) {
        return R.data(documentImageService.getResourceImage(resourceId));
    }
}
