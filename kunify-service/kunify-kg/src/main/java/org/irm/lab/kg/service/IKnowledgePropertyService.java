package org.irm.lab.kg.service;

import org.bson.conversions.Bson;
import org.irm.lab.common.support.MyPage;
import org.irm.lab.kg.entity.KnowledgeProperty;
import org.irm.lab.kg.vo.KnowledgePropertyVO;

import java.util.List;
import java.util.Map;

public interface IKnowledgePropertyService {

    MyPage<KnowledgeProperty> page(Map<String, Object> pageMap, int page, int size);

    List<KnowledgeProperty> list();

    KnowledgePropertyVO infoVO(String id);

    KnowledgeProperty save(KnowledgeProperty knowledgeProperty);

    void remove(List<String> ids);

    List<KnowledgeProperty> listByIds(List<String> ids);

    List<KnowledgeProperty> findByBson(Bson bson);

    /**
     * 根据Id查询属性
     *
     * @param id 属性Id
     * @return {@link KnowledgeProperty}
     */
    KnowledgeProperty info(String id);

    KnowledgeProperty findByIdentifier(String identifier);
}
