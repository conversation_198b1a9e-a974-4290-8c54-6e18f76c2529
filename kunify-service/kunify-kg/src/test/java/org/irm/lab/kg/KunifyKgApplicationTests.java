//package org.irm.lab.kg;
//
//import cn.hutool.core.collection.CollectionUtil;
//import cn.hutool.core.util.ObjectUtil;
//import cn.hutool.core.util.StrUtil;
//import cn.hutool.http.HttpUtil;
//import cn.hutool.json.JSONArray;
//import cn.hutool.json.JSONObject;
//import cn.hutool.json.JSONUtil;
//import kotlin.Triple;
//import lombok.Data;
//import lombok.experimental.Accessors;
//import lombok.extern.log4j.Log4j2;
//import org.apache.poi.ss.usermodel.*;
//import org.apache.poi.xssf.usermodel.XSSFWorkbook;
//import org.irm.lab.common.api.R;
//import org.irm.lab.common.constant.AuthConstant;
//import org.irm.lab.common.enums.AlgorithmType;
//import org.irm.lab.common.utils.ThreadLocalUtil;
//import org.irm.lab.config.entity.Label;
//import org.irm.lab.config.entity.WorkTask;
//import org.irm.lab.config.feign.LabelFeign;
//import org.irm.lab.config.repository.LabelRepository;
//import org.irm.lab.config.repository.WorkTaskRepository;
//import org.irm.lab.kg.algorithm.AlgorithmProcessFactory;
//import org.irm.lab.kg.algorithm.DocumentUnit;
//import org.irm.lab.kg.algorithm.PDFPreciseProcessor;
//import org.irm.lab.kg.entity.KnowledgeRelation;
//import org.irm.lab.kg.entity.neo4j.node.NodeEntity;
//import org.irm.lab.kg.entity.neo4j.node.NodeRelation;
//import org.irm.lab.kg.rabbitmq.message.IeGenMessage;
//import org.irm.lab.kg.rabbitmq.producer.IeGenProducer;
//import org.irm.lab.kg.repository.DocumentUnitRepository;
//import org.irm.lab.kg.repository.KnowledgeRelationRepository;
//import org.irm.lab.kg.repository.neo4j.node.NodeEntityRepository;
//import org.irm.lab.kg.repository.neo4j.node.NodeRelationRepository;
//import org.irm.lab.kg.service.IDocumentUnitService;
//import org.irm.lab.kg.service.IOfficeConvertService;
//import org.irm.lab.kg.service.impl.DocumentUnitServiceImpl;
//import org.irm.lab.kg.vo.DocumentUnitVO;
//import org.irm.lab.repository.feign.ResourceFeign;
//import org.junit.jupiter.api.Test;
//import org.neo4j.ogm.cypher.ComparisonOperator;
//import org.neo4j.ogm.cypher.Filter;
//import org.springframework.boot.test.context.SpringBootTest;
//import org.springframework.test.context.ActiveProfiles;
//
//import javax.annotation.Resource;
//import java.io.*;
//import java.net.HttpURLConnection;
//import java.net.URL;
//import java.text.Collator;
//import java.text.ParseException;
//import java.text.SimpleDateFormat;
//import java.time.LocalDate;
//import java.util.*;
//import java.util.stream.Collectors;
//import java.util.stream.Stream;
//
//@Log4j2
//@SpringBootTest(classes = KunifyKgApplication.class)
//@ActiveProfiles("dc")
//class KunifyKgApplicationTests {
//    @Resource
//    private AlgorithmProcessFactory algorithmProcessFactory;
//    @Resource(name = "pdfToPicServiceImpl")
//    private IOfficeConvertService officeConvertService;
//    @Resource
//    private IeGenProducer ieGenProducer;
//    @Resource
//    private PDFPreciseProcessor pdfPreciseProcessor;
//    @Resource
//    private ResourceFeign resourceFeign;
//    @Resource
//    private IDocumentUnitService iDocumentUnitService;
//    @Resource
//    private LabelRepository labelRepository;
//    @Resource
//    private WorkTaskRepository workTaskRepository;
//    @Resource
//    private LabelFeign labelFeign;
//    @Resource
//    private NodeEntityRepository nodeEntityRepository;
//    @Resource
//    private KnowledgeRelationRepository knowledgeRelationRepository;
//    @Resource
//    private NodeRelationRepository nodeRelationRepository;
//
//
//    public static final String DOCCANO_PATH = "/Users/<USER>/Desktop/华能/数据/doccano.jsonl";
//
//    public static final String DOCCANO_EXT_PATH = "/Users/<USER>/Desktop/华能/数据/doccano_ext.jsonl";
//
//    public static final String TRAIN_PATH = "/Users/<USER>/Desktop/华能/数据/train.json";
//    public static final String relation1 = "提及机构/部门";
//    public static final String relation2 = "提及人员";
//    public static Label gong_wen = new Label();
//    public static Label ji_gou = new Label();
//    public static Label ren_yuan = new Label();
//    public static Label ti_ji_ji_gou = new Label();
//    public static Label ti_ji_ren_yuan = new Label();
//
//    @Test
//    void genEntityOfDepartment() {
//        ThreadLocalUtil.set("user", JSONUtil.createObj().putOpt(AuthConstant.TENANT_ID, "028639").toString());
//        //可自行选择需要处理的任务
//        String taskId = "66b96f8c08ad2b1966a0188b";
//        WorkTask workTask = workTaskRepository.findById(taskId);
//        String dataSetId = workTask.getProcess().getDataSetId();
//        List<Label> labelOfConcept = labelFeign.list(Map.of("dataSetId", dataSetId, "type", "概念")).getData();
//        List<Label> labelOfRelation = labelFeign.list(Map.of("dataSetId", dataSetId, "type", "关系")).getData();
//        for (Label label : labelOfConcept) {
//            if ("公文".equals(label.getName())) {
//                gong_wen = label;
//            } else if ("机构/部门".equals(label.getName())) {
//                ji_gou = label;
//            } else if ("人员".equals(label.getName())) {
//                ren_yuan = label;
//            }
//        }
//        for (Label label : labelOfRelation) {
//            if (relation1.equals(label.getName())) {
//                ti_ji_ji_gou = label;
//            } else if (relation2.equals(label.getName())) {
//                ti_ji_ren_yuan = label;
//            }
//        }
////        String cypher = "MATCH (n:ENTITY) WHERE ANY(x IN n.docIds WHERE x = $resourceId ) and n.topNodeEntity=true return n";
//
////        List<NodeEntity> conceptId = nodeEntityRepository.findByCondition(Map.of("conceptId", gong_wen.getSourceId()));
////        log.info(gong_wen.getName(), ji_gou.getName(), ren_yuan.getName(), ti_ji_ren_yuan.getName(), ti_ji_ji_gou.getName());
//        List<org.irm.lab.repository.entity.Resource> resourceList = resourceFeign.listByCondition(Map.of("knowledgeMatchConfirm", true, "workTaskId", taskId)).getData();
//        for (org.irm.lab.repository.entity.Resource resource : resourceList) {
////            List<NodeEntity> resourceTopLabelList = nodeEntityRepository.findEntityByCypher(cypher, Map.of("resourceId", resource.getId()));
//            if (resource.getTopNodeId() == null) continue;
//            NodeEntity topNode = nodeEntityRepository.findById(resource.getTopNodeId());
//            if (topNode == null) continue;
//            log.info("当前资源名称：【{}】", resource.getName());
//            JSONObject params = JSONUtil.createObj();
//            params.putOpt("resourceId", resource.getId()).putOpt("page", resource.getPages());
//            List<DocumentUnitVO> documentUnitVOS = iDocumentUnitService.currentPageList(params);
//            if (ObjectUtil.isNotEmpty(documentUnitVOS)) {
//                DocumentUnitVO documentUnitVO = documentUnitVOS.get(documentUnitVOS.size() - 1);
//                String content = documentUnitVO.getContent();
//                int contentLength = content.length();
//
//                // 确保字符串长度大于500
//                if (contentLength >= 500) {
//                    // 从字符串末尾向前数500个字符开始截取
//                    content = content.substring(contentLength - 500);
//                } else {
//                    // 如果字符串长度小于或等于500，则返回整个字符串
//                    content = content.substring(0, contentLength);
//                }
//
//                JSONObject entries = paseAlgo(postAlgo(content));
//                Object data = entries.get("data");
//                if (ObjectUtil.isNotEmpty(data)) {
//                    buildNodeAndRelation(JSONUtil.parseObj(data), documentUnitVO.getId(), resource.getId(), topNode);
//                }
//            }
//            log.info(documentUnitVOS.size());
//        }
//    }
//
//    /**
//     * 构建节点和节点与top节点的关系
//     */
//    void buildNodeAndRelation(JSONObject entries, String unitID, String docId, NodeEntity topNode) {
//        if (entries.containsKey(relation1)) {
//            JSONArray jsonArray = entries.getJSONArray(relation1);
//            List<NodeEntity> node = createNode(jsonArray.toList(String.class), ji_gou, topNode.getModelId(), unitID, docId);
//            creatRelation(topNode, node, ti_ji_ji_gou, docId);
//        }
//        if (entries.containsKey(relation2)) {
//            JSONArray jsonArray = entries.getJSONArray(relation2);
//            List<NodeEntity> node = createNode(jsonArray.toList(String.class), ren_yuan, topNode.getModelId(), unitID, docId);
//            creatRelation(topNode, node, ti_ji_ren_yuan, docId);
//        }
//
//    }
//
//    /**
//     * 构建节点与top节点的关系
//     */
//    void creatRelation(NodeEntity topNode, List<NodeEntity> nodeEntities, Label label, String docId) {
//        KnowledgeRelation knowledgeRelation = knowledgeRelationRepository.findById(label.getSourceId());
//        for (NodeEntity nodeEntity : nodeEntities) {
//            NodeRelation forwardRelation = new NodeRelation();
//            forwardRelation.setPositive(true)
//                    .setPredicateName(knowledgeRelation.getForwardPre())
//                    .setPredicateId(knowledgeRelation.getId())
//                    .setStart(topNode)
//                    .setEnd(nodeEntity);
//            forwardRelation.getDocIds().add(docId);
//            nodeRelationRepository.save(forwardRelation);
//            log.info("创建正向关系：name：【{}】,id:【{}】", forwardRelation.getPredicateName(), forwardRelation.getId());
//            if (!knowledgeRelation.getIsSymmetric()){
//                NodeRelation inverseRelation = new NodeRelation();
//                inverseRelation.setPositive(false).setPredicateName(knowledgeRelation.getInversePre())
//                        .setPredicateId(knowledgeRelation.getId())
//                        .setStart(nodeEntity)
//                        .setEnd(topNode);
//                inverseRelation.getDocIds().add(docId);
//                nodeRelationRepository.save(inverseRelation);
//                log.info("创建反向关系：name：【{}】,id:【{}】", inverseRelation.getPredicateName(), inverseRelation.getId());
//            }
//        }
//
//    }
//
//    /**
//     * 构建节点
//     */
//    List<NodeEntity> createNode(List<String> names, Label label, String modelId, String unitID, String docId) {
//        List<NodeEntity> nodeEntities = new ArrayList<>();
//        for (String name : names) {
//            org.neo4j.ogm.cypher.Filters filters = new org.neo4j.ogm.cypher.Filters(new Filter("entityName", org.neo4j.ogm.cypher.ComparisonOperator.EQUALS, name));
//            filters.and(new Filter("conceptId", ComparisonOperator.EQUALS, label.getSourceId()));
//            List<NodeEntity> nodeEntityList = nodeEntityRepository.findByCondition(filters);
//            if (CollectionUtil.isNotEmpty(nodeEntityList)) {
//                for (NodeEntity nodeEntity : nodeEntityList) {
//                    nodeEntity.getDocIds().add(docId);
//                    nodeEntity.getDocumentUnitIds().add(unitID);
//                    nodeEntities.add(nodeEntityRepository.save(nodeEntity));
//                    log.info("更新节点：name：【{}】,id:【{}】", name, nodeEntity.getId());
//                }
//            } else {
//                NodeEntity nodeEntity = new NodeEntity();
//                nodeEntity.getDocIds().add(docId);
//                nodeEntity.getDocumentUnitIds().add(unitID);
//                nodeEntity.setEntityName(name).setConceptId(label.getSourceId()).setTopNodeEntity(false).setModelId(modelId);
//                nodeEntities.add(nodeEntityRepository.save(nodeEntity));
//                log.info("新增节点：name：【{}】,id:【{}】", name, nodeEntity.getId());
//            }
//        }
//        return nodeEntities;
//    }
//
//    /**
//     * 解析算法返回结果
//     */
//    JSONObject paseAlgo(String content) {
//        //处理算法返回结果
//        JSONObject result = JSONUtil.createObj();
//        if (!JSONUtil.isTypeJSON(content)) return JSONUtil.createObj();
//        JSONObject entries = JSONUtil.parseObj(content);
//        //设置机构/部门、人员
//        JSONObject data = entries.getJSONObject("data");
//        if (data.containsKey(relation1)) {
//            result.putOpt(relation1, entries.getJSONArray(relation1));
//        } else if (data.containsKey(relation2)) {
//            result.putOpt(relation2, entries.getJSONArray(relation2));
//        }
//        return result;
//    }
//
//    @Test
//    void postAlgo() {
//        String content = "华能国际电力股份有限公司颁发了天天打架不上班奖品";
//        log.info(postAlgo(content));
//    }
//
//    /**
//     * 请求算法
//     */
//    String postAlgo(String content) {
//        JSONObject obj = JSONUtil.createObj();
//        obj.putOpt("text", content);
//        JSONArray predicates = JSONUtil.createArray();
//        predicates.add(relation1);
//        predicates.add(relation2);
//        obj.putOpt("predicates", predicates);
//        String url = "http://120.244.125.71:17000/kg/schema/infer/ie";
//        return HttpUtil.post(url, obj.toString());
//    }
//
//    public static void main(String[] args) throws ParseException {
//        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
//        String format = sdf.format(new Date());
//        System.out.println("format = " + format);
//    }
//
//
////    public static void main(String[] args) throws IOException {
////        // 创建一个新的Excel工作簿
////        Workbook workbook = new XSSFWorkbook();
////
////        // 创建一个工作表
////        Sheet sheet = workbook.createSheet("规章制度表");
////
////        // 创建表头行
////        Row headerRow = sheet.createRow(0);
////
////        // 创建单元格样式
////        CellStyle headerCellStyle = workbook.createCellStyle();
////        headerCellStyle.setFillPattern(FillPatternType.SOLID_FOREGROUND);
////        headerCellStyle.setAlignment(HorizontalAlignment.CENTER);
////
////        // 创建表头单元格并设置值
////        Cell cell = headerRow.createCell(0);
////        cell.setCellValue("题名");
////        cell.setCellStyle(headerCellStyle);
////
////        cell = headerRow.createCell(1);
////        cell.setCellValue("文号");
////        cell.setCellStyle(headerCellStyle);
////
////        // 设置列的宽度
////        sheet.setColumnWidth(0, 10000);
////        sheet.setColumnWidth(1, 5000);
////
////        String line;
////        int rowNum = 1;
////        try (BufferedReader bufferedReader = new BufferedReader(new FileReader("/Users/<USER>/Desktop/gzzd_failed.txt"))) {
////            while ((line = bufferedReader.readLine()) != null) {
////                String[] split = line.split("《=====》");
////                String tiMing = split[0];
////                String wenHao = split[1];
////                // 创建新行并写入数据
////                Row dataRow = sheet.createRow(rowNum++);
////                dataRow.createCell(0).setCellValue(tiMing);
////                dataRow.createCell(1).setCellValue(wenHao);
////            }
////        } catch (Exception e) {
////            e.printStackTrace();
////        }
////
////        // 将Excel文件写入磁盘
////        try (FileOutputStream outputStream = new FileOutputStream("/Users/<USER>/Desktop/规章制度.xlsx")) {
////            workbook.write(outputStream);
////        } catch (IOException e) {
////            e.printStackTrace();
////        }
////
////        // 关闭工作簿
////        try {
////            workbook.close();
////        } catch (IOException e) {
////            e.printStackTrace();
////        }
////
////    }
//
//
////    public static void main(String[] args) throws IOException {
////        //获取doccano的每一行
////        BufferedReader doccanoReader = new BufferedReader(new FileReader(DOCCANO_PATH));
////        BufferedWriter doccanoExtWriter = new BufferedWriter(new FileWriter(DOCCANO_EXT_PATH, true));
////
////        String line;
////        while ((line = doccanoReader.readLine()) != null) {
////            DoccanoBean doccanoBean = JSONUtil.parseObj(line).toBean(DoccanoBean.class);
////            String text = doccanoBean.getText();
////            List<Entity> entityList = doccanoBean.getEntities();
////            entityList.forEach(entity -> {
////                String prefix = text.substring(0, entity.getStart_offset());
////                String entityText = text.substring(entity.getStart_offset(), entity.getEnd_offset());
////
////                //1、判断prefix有多少空格(start和end都减去prefix的空格数)
////                Integer prefixBlankCount = getBlankCount(prefix);
////                entity.setStart_offset(entity.getStart_offset() - prefixBlankCount);
////                entity.setEnd_offset(entity.getEnd_offset() - prefixBlankCount);
////
////                //2、判断entityText有多少空格（end减去entityText的空格数）
////                Integer entityTextBlankCount = getBlankCount(entityText);
////                entity.setEnd_offset(entity.getEnd_offset() - entityTextBlankCount);
////
////            });
////            doccanoBean.setText(StrUtil.cleanBlank(doccanoBean.getText()));
////            System.out.println("text = " + doccanoBean.getText());
////            entityList.forEach(entity -> {
////                System.out.println("entity = " + doccanoBean.getText().substring(entity.getStart_offset(), entity.getEnd_offset()));
////            });
//////            if (doccanoBean.getText().length() < 512) {
//////                doccanoExtWriter.write(JSONUtil.toJsonStr(doccanoBean) + "\n");
//////                doccanoExtWriter.flush();
//////            }
////        }
////    }
//
//
//    private static Integer getBlankCount(String str) {
//        //求字符串中的空格数
//        int count = 0;
//        for (int i = 0; i < str.length(); i++) {
//            char p = str.charAt(i);
//            if (p == ' ') {
//                count++;
//            }
//        }
//        return count;
//    }
//
//
////    public static void main(String[] args) throws IOException {
////        //获取doccano的每一行
////        BufferedReader doccanoReader = new BufferedReader(new FileReader(DOCCANO_PATH));
////        String line;
////        while ((line = doccanoReader.readLine()) != null) {
////            //构建转化后的schema
////            DoccanoBean doccanoBean = JSONUtil.parseObj(line).toBean(DoccanoBean.class);
////            //复制一份entity信息，key为entity的id，value为entity
////            Map<Integer, Entity> copyEntities = doccanoBean.getEntities().stream()
////                    .collect(Collectors.toMap(Entity::getId, each -> each, (value1, value2) -> value1));
////            //生成指定格式的训练集
////            convert2TrainDataSet(buildTriple(doccanoBean, copyEntities));
////
////        }
////    }
//
//    private static void convert2TrainDataSet(List<Triple> triples) throws IOException {
//        BufferedWriter trainWriter = new BufferedWriter(new FileWriter(TRAIN_PATH, true));
//
//        JSONObject initTrainJsonQuery = new JSONObject();
//        JSONArray initTrainJsonResult = new JSONArray();
//        StringBuilder queryBuilder = new StringBuilder();
//        StringBuilder resultBuilder = new StringBuilder();
//
//
//        triples.forEach(triple -> {
//            //多个triple时，表示text一样，识别的关系和实体不一样，需要合并为一条语料
//            initTrainJsonQuery(triple, initTrainJsonQuery);
//            initTrainJsonResult(triple, initTrainJsonResult);
//        });
//
//        if (!triples.isEmpty()) {
//            //triples不为空，再写入文件
//            JSONObject resultLine = new JSONObject();
//            queryBuilder.append("根据schema:")
//                    .append(initTrainJsonQuery)
//                    .append(",对以下文本进行知识图谱三元组抽取, 并以JSON格式输出:")
//                    .append(triples.get(0).getText());
//            resultBuilder.append(initTrainJsonResult);
//            resultLine.putOpt("query", queryBuilder).putOpt("result", resultBuilder);
//
//            //写入文件
//            try {
//                trainWriter.write(resultLine + "\n");
//                trainWriter.flush();
//            } catch (IOException e) {
//                throw new RuntimeException(e);
//            }
//        }
//        initTrainJsonQuery.clear();
//        initTrainJsonResult.clear();
//    }
//
//
//    private static void initTrainJsonQuery(Triple triple, JSONObject jsonQuery) {
//        Entity odd = triple.getOdd();
//        if (odd == null) {
//            jsonQuery.putOpt(triple.getStart().getLabel(), List.of(triple.getRelation().getType()));
//        } else {
//            jsonQuery.putOpt(odd.getLabel(), new ArrayList<>());
//        }
//    }
//
//    private static void initTrainJsonResult(Triple triple, JSONArray jsonResult) {
//        Entity odd = triple.getOdd();
//        Entity start = triple.getStart();
//        Relation relation = triple.getRelation();
//        Entity end = triple.getEnd();
//        if (odd == null) {
//            jsonResult.add(
//                    new JSONObject()
//                            .putOpt(start.getLabel(), start.getText())
//                            .putOpt("data", new JSONObject().putOpt(relation.getType(), end.getText()))
//            );
//        } else {
//            jsonResult.add(
//                    new JSONObject()
//                            .putOpt(odd.getLabel(), odd.getText())
//            );
//            //TODO 没有边的情况？
//        }
//    }
//
//    private static ArrayList<Triple> buildTriple(DoccanoBean doccanoBean, Map<Integer, Entity> entities) {
//        //构建三元组triples
//        ArrayList<Triple> triples = new ArrayList<>();
//        doccanoBean.getRelations().forEach(relation -> {
//            doccanoBean.getEntities().stream().filter(fromEntity -> fromEntity.getId().equals(relation.getFrom_id()))
//                    .findFirst()
//                    .ifPresent(fromEntity -> {
//                        doccanoBean.getEntities().stream()
//                                .filter(toEntity -> toEntity.getId().equals(relation.getTo_id()))
//                                .findFirst().ifPresent(toEntity -> {
//                                    triples.add(
//                                            new Triple().setStart(fromEntity)
//                                                    .setEnd(toEntity)
//                                                    .setRelation(relation)
//                                                    .setText(doccanoBean.getText())
//                                    );
//                                    //从原来的entity集合中，删除已经被加入triple的entity
//                                    entities.remove(fromEntity.getId());
//                                    entities.remove(toEntity.getId());
//                                });
//                    });
//        });
//
//        entities.forEach((key, value) -> triples.add(
//                new Triple()
//                        .setOdd(value).setText(doccanoBean.getText())
//        ));
//
//        //修正entity中的text
//        triples.forEach(triple -> {
//            Entity odd = triple.getOdd();
//            if (odd == null) {
//                Entity start = triple.getStart();
//                Entity end = triple.getEnd();
//                start.setText(getTextByPosition(start, triple.getText()));
//
//                end.setText(getTextByPosition(end, triple.getText()));
//            } else {
//                odd.setText(getTextByPosition(odd, triple.getText()));
//            }
//        });
//        return triples;
//    }
//
//    private static String getTextByPosition(Entity entity, String text) {
//        if (ObjectUtil.isNotEmpty(text)) {
//            return text.substring(entity.getStart_offset(),
//                    entity.getEnd_offset());
//        }
//        return "";
//    }
//
//    @Data
//    static class DoccanoBean {
//        Integer id;
//        String text;
//        List<Entity> entities = new ArrayList<>();
//        List<Relation> relations = new ArrayList<>();
//    }
//
//    @Data
//    static class Entity {
//        Integer id;
//        String label;
//        Integer start_offset;
//        Integer end_offset;
//        String text;
//    }
//
//    @Data
//    static class Relation {
//        Integer id;
//        Integer from_id;
//        Integer to_id;
//        String type;
//    }
//
//    //单宾语Triple
//    @Data
//    @Accessors(chain = true)
//    static class Triple {
//        String text;
//        Entity start;
//        Entity end;
//        Relation relation;
//
//        Entity odd;
//    }
//
//    @Test
//    void unit() {
//        try {
//
//            String directoryPath = "C:\\Users\\<USER>\\Desktop\\四类文件语料导出\\制度文件\\信息化管理制度\\";
//            File directory = new File(directoryPath);
//
//            if (directory.isDirectory()) {
//                File[] subDirectories = directory.listFiles(File::isDirectory);
//
//                for (File subDirectory : subDirectories) {
//                    File[] files = subDirectory.listFiles();
//                    if (files != null && files.length > 0) {
//                        for (File file : files) {
//                            String fileName = file.getName();
//                            //                            if (!fileName.startsWith("1_中"))continue;
//                            fileName = fileName.replace(fileName.substring(fileName.lastIndexOf(".")), "");
//                            List<String> strings;
//                            try {
//                                strings = pdfPreciseProcessor.generateUnitTxt(file);
//                            } catch (Exception e) {
//                                continue;
//                            }
//                            if (ObjectUtil.isEmpty(strings)) continue;
//                            String filePath = "C:\\Users\\<USER>\\Desktop\\四类文件语料导出\\制度文件\\信息化管理制度\\" + fileName + ".txt";
//                            BufferedWriter writer = new BufferedWriter(new FileWriter(filePath));
//                            for (String line : strings) {
//                                if (line.contains("普通商密")) continue;
//                                writer.write(line);
//                                writer.newLine();
//                            }
//                            writer.flush();
//                            writer.close();
//                            System.out.println("解析成功" + filePath);
//                        }
//                    }
//                }
//            }
//
//
//        } catch (Exception e) {
//            e.printStackTrace();
//        }
//    }
//
//    @Test
//    void contextLoads() {
//        //		org.irm.lab.repository.entity.Resource resource = resourceFeign.info("63f47b0c1008d47737b7a113").getData();
//        org.irm.lab.repository.entity.Resource resource1 = new org.irm.lab.repository.entity.Resource();
//        resource1.setId("63f47b0c1008d47737b7a113");
//        resource1.setPdfAttachName("upload/2023-02-21/9_议题05：审议关于制订《加强子企业董事会建设方案》《落实子企业董事会职权工作方案》有关情况的汇报及建议.pdf");
//        algorithmProcessFactory.algorithmApply(AlgorithmType.PDF_PRECISE_STRIPPER).process(resource1);
//    }
//
//    @Test
//    void test1() {
//        org.irm.lab.repository.entity.Resource resource1 = new org.irm.lab.repository.entity.Resource();
//        resource1.setId("6414406cbb6adab8579e9b6e");
//        resource1.setPdfAttachName("upload/2023-02-21/9_议题05：审议关于制订《加强子企业董事会建设方案》《落实子企业董事会职权工作方案》有关情况的汇报及建议.pdf");
//        officeConvertService.pdfToPic(resource1);
//    }
//
//    @Test
//    void test2() {
//        IeGenMessage ieGenMessage = new IeGenMessage();
//        ieGenMessage.setTenantId("028639");
//        ieGenMessage.setSchema("[{\"人员\":[\"任职\",\"教学\",\"参与\",\"性别\",\"民族\"]},\"职称\",\"学科\",\"时间\",\"附件\",{\"事件\":[\"发生时间\",\"发生地点\",\"发文时间\",\"测试\"]},\"机构\",\"文号\",{\"发文机关\":[\"发文时间\"]},\"文种\",\"金额\",\"其他文件\",\"部门\",\"学院\",\"职务\",\"地点\",\"抄送\",\"抄报\",\"性别\"]");
//        ieGenMessage.setUserId("123");
//        String content = "我校大学外语教学部退休教师严子石同志因病于2013年10月31日去世，终年82岁。现就其去世后有关抚恤问题提出如下处理意见：一、根据安徽省人事厅、财政厅、民政厅、劳动和社会保障厅皖人发〔2008〕45号文件规定，同意发给其遗属一次性抚恤费，按其病故前二十个月全额基本工资（职务工资365.00元，津贴156.43元，提高退休费1005.00）计发，合计30528.60元。二、根据安徽省财政厅财社〔2001〕1252号文件规定，2000.00元丧葬费发给其遗属包干使用。人 事 处2014年1月15日抄送：离退休工作处，财务处，档案馆，校医院，医保办，大学外语教学部";
//        ieGenMessage.setContent(Map.of("1", content));
//        ieGenProducer.ieGen(ieGenMessage);
//    }
//
//    @Test
//    File downloadFile(String url, String filePath, String fildName, String method) {
//        //创建不同的文件夹目录
//        File file = new File(filePath);
//        //判断文件夹是否存在
//        if (!file.exists()) {
//            //如果文件夹不存在，则创建新的的文件夹
//            file.mkdirs();
//        }
//        FileOutputStream fileOut = null;
//        HttpURLConnection conn = null;
//        InputStream inputStream = null;
//        try {
//            // 建立链接
//            URL httpUrl = new URL(url);
//            conn = (HttpURLConnection) httpUrl.openConnection();
//            //以Post方式提交表单，默认get方式
//            conn.setRequestMethod(method);
//            conn.setDoInput(true);
//            conn.setDoOutput(true);
//            // post方式不能使用缓存
//            conn.setUseCaches(false);
//            //连接指定的资源
//            conn.connect();
//            //获取网络输入流
//            inputStream = conn.getInputStream();
//            BufferedInputStream bis = new BufferedInputStream(inputStream);
//            //判断文件的保存路径后面是否以/结尾
//            if (!filePath.endsWith("/")) {
//                filePath += "/";
//            }
//            //文件留下在的文件的名称（包含文件后缀）
//            //写入到文件（注意文件保存路径的后面一定要加上文件的名称）
//            fileOut = new FileOutputStream(filePath + fildName);
//            BufferedOutputStream bos = new BufferedOutputStream(fileOut);
//
//            byte[] buf = new byte[4096];
//            int length = bis.read(buf);
//            //保存文件
//            while (length != -1) {
//                bos.write(buf, 0, length);
//                length = bis.read(buf);
//            }
//            bos.close();
//            bis.close();
//            conn.disconnect();
//        } catch (Exception e) {
//            e.printStackTrace();
//            System.out.println("抛出异常！！");
//        }
//
//        return file;
//    }
//
//    @Test
//    void download() {
//        //下载路径
//        String Url = "http://192.168.50.66:9000/kunify-hn-028639/upload/2023-04-18/a65f2444-84bb-4d79-a237-0576db4319f3.pdf";   //文件URL地址
//        //文件名称 = 初始文件夹 + \\ + 资源名称
//        String filePath = "E:\\new";      //保存目录  （初始文件夹）
//        String fileName = "\\" + "正文-关于吴雷、郭西和职务任免的通知";     //为下载的文件命名 （资源名称）
//        downloadFile(Url, filePath + fileName, "5.pdf", "GET");
//
//    }
//
//    @Test
//    void testt() {
//        // 党组会议纪要
//        org.irm.lab.repository.entity.Resource resource1 = new org.irm.lab.repository.entity.Resource();
//        resource1.setId("648b2674b29a9355d404d1e6");
//        resource1.setPdfAttachName("upload/2023-06-15/9e84fd58-e535-4ef9-9cbf-c9991043bf8d.pdf");
//        // 电光学储能
//        org.irm.lab.repository.entity.Resource resource2 = new org.irm.lab.repository.entity.Resource();
//        resource2.setId("648865d54551df2474d6384b");
//        resource2.setPdfAttachName("upload/2023-06-13/f550fd52-ab7d-4e08-a961-fdf54a7de0bf.pdf");
//        algorithmProcessFactory.algorithmApply(AlgorithmType.PDF_RULE_JI_YAO_01).process(resource1);
//    }
//
//    @Test
//    void jmTest() {
//        //		org.irm.lab.repository.entity.Resource resource = resourceFeign.info("63f47b0c1008d47737b7a113").getData();
//        org.irm.lab.repository.entity.Resource resource1 = new org.irm.lab.repository.entity.Resource();
//        resource1.setId("648c3d0331c22229a0cf8f4d");
//        resource1.setPdfAttachName("upload/2023-06-16/111.pdf");
//        algorithmProcessFactory.algorithmApply(AlgorithmType.PDF_RULE_BIAO_ZHANG_01).process(resource1);
//    }
//
//}