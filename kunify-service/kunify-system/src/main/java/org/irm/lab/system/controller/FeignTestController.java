package org.irm.lab.system.controller;

import lombok.extern.slf4j.Slf4j;
import org.irm.lab.common.api.R;
import org.irm.lab.common.utils.ThreadLocalUtil;
import org.irm.lab.user.provider.UserProviderFeign;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.util.Enumeration;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * Feign头传播测试控制器
 * 
 * 用于测试和验证Feign客户端请求头传播是否正常工作
 */
@Slf4j
@RestController
@RequestMapping("/feign-test")
public class FeignTestController {

    @Resource
    private UserProviderFeign userProviderFeign;

    /**
     * 测试Feign头传播功能
     * 
     * @return 测试结果
     */

    
    /**
     * 获取当前请求的所有头信息
     * 
     * @return 头信息Map
     */
    private Map<String, String> getCurrentRequestHeaders() {
        Map<String, String> headers = new HashMap<>();
        
        ServletRequestAttributes attributes = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
        if (attributes != null) {
            HttpServletRequest request = attributes.getRequest();
            Enumeration<String> headerNames = request.getHeaderNames();
            
            while (headerNames.hasMoreElements()) {
                String headerName = headerNames.nextElement();
                String headerValue = request.getHeader(headerName);
                headers.put(headerName, headerValue);
            }
        }
        
        return headers;
    }
    
    /**
     * 简单的健康检查接口
     * 
     * @return 健康状态
     */
    @GetMapping("/health")
    public R<String> health() {
        return R.success("Feign测试控制器运行正常");
    }
}
