package org.irm.lab.system.service.impl;

import cn.hutool.core.convert.Convert;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import java.util.concurrent.atomic.AtomicReference;
import org.bson.conversions.Bson;
import org.irm.lab.common.constant.AuthConstant;
import org.irm.lab.common.constant.FiledNameConst;
import org.irm.lab.common.exception.ServiceException;
import org.irm.lab.common.support.Condition;
import org.irm.lab.common.utils.ThreadLocalUtil;
import org.irm.lab.system.entity.Menu;
import org.irm.lab.system.repository.MenuRepository;
import org.irm.lab.system.service.IMenuService;
import org.irm.lab.user.constant.ExceptionMessage;
import org.irm.lab.user.entity.User;
import org.irm.lab.user.feign.RoleFeign;
import org.irm.lab.user.provider.UserProviderFeign;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;
import lombok.extern.slf4j.Slf4j;

/**
 * 菜单服务实现类
 *
 * 关于Feign客户端请求头传播的说明：
 * 本服务中的Feign调用（如userProviderFeign.getRoleNames()）能够正确传播请求头，
 * 这是因为系统中配置了FeignAuthRequestInterceptor，它会自动将原始HTTP请求的所有头信息
 * （包括"user"头）传播到Feign客户端调用中。
 *
 * <AUTHOR>
 * @date 2022/12/30 15:28
 */
@Slf4j
@Service
public class MenuServiceImpl implements IMenuService {
    @Resource
    private MenuRepository menuRepository;
    @Resource
    private RoleFeign roleFeign;
    @Resource
    private UserProviderFeign userProviderFeign;

    public void remove(List<String> ids) {
        List<String> toDelete = new ArrayList<>();
        List<Menu> menus = menuRepository.findById(ids);
        for (Menu menu : menus) {
            toDelete.add(menu.getId());
            getChildren(menu, new ArrayList<>()).forEach(child -> {
                if (!toDelete.contains(child.getId())) toDelete.add(child.getId());
            });
        }
        roleFeign.pullMenuId(ids);
        menuRepository.deleteByIdFake(toDelete);
    }

    private List<Menu> getChildren(Menu parent, List<Menu> result) {
        List<Menu> menus = this.findByParentId(parent.getId());
        if (ObjectUtil.isNotEmpty(menus)) {
            for (Menu menu : menus) {
                result.addAll(getChildren(menu, new ArrayList<>()));
            }
        }
        result.addAll(menus);
        return result;
    }

    public List<Menu> findByParentId(String parentId) {
        JSONObject obj = JSONUtil.createObj().putOpt("parentId", parentId);
        return this.findAll(obj);
    }

    public List<Menu> findAll(JSONObject obj) {
        return menuRepository.findByCondition(Condition.getFilter(obj, Menu.class));
    }

    @Override
    public List<Menu> currentMenuList() {
        log.debug("开始获取当前用户菜单列表");
        List<Menu> menuList;

        // 获取当前用户信息 - 优先从Header获取，fallback到ThreadLocal
        String currentUserId = getCurrentUserId();
        log.debug("当前用户ID: {}", currentUserId);

        // 通过Feign调用获取角色信息 - 由于FeignAuthRequestInterceptor的存在，header会自动传播
        List<String> roleNames = userProviderFeign.getRoleNames().getData();
        log.debug("用户角色列表: {}", roleNames);

        // 根据角色权限获取菜单列表
        if (roleNames.contains("ADMIN")) {
            log.debug("用户具有ADMIN角色，获取所有菜单");
            menuList = menuRepository.findAll();
        } else {
            log.debug("用户为普通用户，根据权限获取菜单");
            // 通过Feign调用获取用户菜单权限 - header同样会自动传播
            menuList = menuRepository.findById(userProviderFeign.getMenuIds().getData());
        }

        log.debug("获取到菜单数量: {}", menuList.size());

        // 按排序字段排序并返回
        return menuList.stream()
                .sorted(Comparator.comparing(Menu::getSort))
                .collect(Collectors.toList());
    }

    /**
     * 获取当前用户ID - 优先从Header获取，fallback到ThreadLocal
     * 这个方法提取了用户ID获取逻辑，使代码更清晰
     *
     * @return 当前用户ID
     * @throws ServiceException 当无法获取用户信息时抛出异常
     */
    private String getCurrentUserId() {
        // 尝试从HTTP请求Header中获取用户信息
        ServletRequestAttributes attributes = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
        if (attributes != null) {
            String userHeader = attributes.getRequest().getHeader("user");
            if (ObjectUtil.isNotEmpty(userHeader)) {
                try {
                    log.debug("从HTTP Header中获取用户信息");
                    JSONObject userJsonObject = JSONUtil.parseObj(userHeader);
                    String userId = userJsonObject.getStr(AuthConstant.USER_ID);
                    log.debug("从Header解析得到用户ID: {}", userId);
                    return userId;
                } catch (Exception e) {
                    log.warn("Header中的用户信息解析失败，尝试从ThreadLocal获取: {}", e.getMessage());
                    // Header解析失败，继续尝试ThreadLocal
                }
            } else {
                log.debug("HTTP Header中没有用户信息，尝试从ThreadLocal获取");
            }
        } else {
            log.debug("无法获取ServletRequestAttributes，尝试从ThreadLocal获取用户信息");
        }

        // Fallback: 从ThreadLocal中获取用户信息
        String userFromThreadLocal = ThreadLocalUtil.get("user");
        if (ObjectUtil.isNotEmpty(userFromThreadLocal)) {
            try {
                log.debug("从ThreadLocal中获取用户信息");
                JSONObject userJsonObject = JSONUtil.parseObj(userFromThreadLocal);
                String userId = userJsonObject.getStr(AuthConstant.USER_ID);
                log.debug("从ThreadLocal解析得到用户ID: {}", userId);
                return userId;
            } catch (Exception e) {
                log.error("ThreadLocal中的用户信息格式错误: {}", e.getMessage());
                throw new ServiceException("ThreadLocal中的用户信息格式错误: " + e.getMessage());
            }
        }

        // 无法获取用户信息
        log.error("无法获取当前用户信息，Header和ThreadLocal中都没有有效的用户数据");
        throw new ServiceException(ExceptionMessage.CURRENT_USER_ERROR);
    }

    @Override
    public List<Menu> currentVisibleMenu() {
        List<Menu> menus = currentMenuList();
        return menus.stream().filter(menu ->
                ObjectUtil.isNotEmpty(menu.getIsVisible()) && !menu.getIsVisible() && "菜单".equals(menu.getMenuType())
        ).collect(Collectors.toList());
    }

    @Override
    public List<Menu> getMenuByTitle(String name) {
        Bson filter = Condition.getFilter(Map.of(FiledNameConst.TITLE, name), Menu.class);
        return menuRepository.findByCondition(filter).stream().map(menu -> {
            if (!"0".equals(menu.getParentId())) {
                Menu parentMenu = menuRepository.findById(menu.getParentId());
                menu.setPath(parentMenu.getPath() + "/" + menu.getPath());
            }
            return menu;
        }).collect(Collectors.toList());
    }

    /**
     * 获取当前用户的快捷跳转菜单
     *
     * @return List<Menu>
     */
    @Override
    public List<Menu> currentJumpMenu() {
        User user = userProviderFeign.getCurrentUser().getData();
        List<Menu> menuList = user.getJumpMenu();
        if (ObjectUtil.isNotEmpty(menuList)) {
            return menuList;
        } else {
            List<Menu> menus = currentVisibleMenu();
            menus = ObjectUtil.isEmpty(menus) ? new ArrayList<>() : menus;
            menus = menus.size() > 4 ? menus.subList(0, 4) : menus;
            return menus;
        }
    }
}
