package org.irm.lab.system.service.impl;

import cn.hutool.core.convert.Convert;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import java.util.concurrent.atomic.AtomicReference;
import org.bson.conversions.Bson;
import org.irm.lab.common.constant.AuthConstant;
import org.irm.lab.common.constant.FiledNameConst;
import org.irm.lab.common.exception.ServiceException;
import org.irm.lab.common.support.Condition;
import org.irm.lab.common.utils.ThreadLocalUtil;
import org.irm.lab.system.entity.Menu;
import org.irm.lab.system.repository.MenuRepository;
import org.irm.lab.system.service.IMenuService;
import org.irm.lab.user.constant.ExceptionMessage;
import org.irm.lab.user.entity.User;
import org.irm.lab.user.feign.RoleFeign;
import org.irm.lab.user.provider.UserProviderFeign;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

/**
 * <AUTHOR>
 * @date 2022/12/30 15:28
 */
@Service
public class MenuServiceImpl implements IMenuService {
    @Resource
    private MenuRepository menuRepository;
    @Resource
    private RoleFeign roleFeign;
    @Resource
    private UserProviderFeign userProviderFeign;

    public void remove(List<String> ids) {
        List<String> toDelete = new ArrayList<>();
        List<Menu> menus = menuRepository.findById(ids);
        for (Menu menu : menus) {
            toDelete.add(menu.getId());
            getChildren(menu, new ArrayList<>()).forEach(child -> {
                if (!toDelete.contains(child.getId())) toDelete.add(child.getId());
            });
        }
        roleFeign.pullMenuId(ids);
        menuRepository.deleteByIdFake(toDelete);
    }

    private List<Menu> getChildren(Menu parent, List<Menu> result) {
        List<Menu> menus = this.findByParentId(parent.getId());
        if (ObjectUtil.isNotEmpty(menus)) {
            for (Menu menu : menus) {
                result.addAll(getChildren(menu, new ArrayList<>()));
            }
        }
        result.addAll(menus);
        return result;
    }

    public List<Menu> findByParentId(String parentId) {
        JSONObject obj = JSONUtil.createObj().putOpt("parentId", parentId);
        return this.findAll(obj);
    }

    public List<Menu> findAll(JSONObject obj) {
        return menuRepository.findByCondition(Condition.getFilter(obj, Menu.class));
    }

    @Override
    public List<Menu> currentMenuList() {
        List<Menu> menuList;
        AtomicReference<User> userAtomicReference = new AtomicReference<>();
        //从Header中获取用户信息
        Optional.ofNullable((ServletRequestAttributes) RequestContextHolder.getRequestAttributes())
                .ifPresentOrElse((servletRequestAttributes) -> {
                            JSONObject userJsonObject = JSONUtil.parseObj(servletRequestAttributes.getRequest().getHeader("user"));
                        }, () -> {
                            //从threadLocal中获取用户信息
                            if (ObjectUtil.isNotEmpty(ThreadLocalUtil.get("user"))) {
                                String userId = new JSONObject(ThreadLocalUtil.get("user")).getStr(AuthConstant.USER_ID);
                            } else {
                                throw new ServiceException(ExceptionMessage.CURRENT_USER_ERROR);
                            }
                        }
                );
        User user = userAtomicReference.get();
        List<String> roleNames = userProviderFeign.getRoleNames().getData();
        if (roleNames.contains("ADMIN")) {
            menuList = menuRepository.findAll();
        } else {
            menuList = menuRepository.findById(userProviderFeign.getMenuIds().getData());
        }
        return menuList.stream().sorted(Comparator.comparing(Menu::getSort)).collect(Collectors.toList());
    }

    @Override
    public List<Menu> currentVisibleMenu() {
        List<Menu> menus = currentMenuList();
        return menus.stream().filter(menu ->
                ObjectUtil.isNotEmpty(menu.getIsVisible()) && !menu.getIsVisible() && "菜单".equals(menu.getMenuType())
        ).collect(Collectors.toList());
    }

    @Override
    public List<Menu> getMenuByTitle(String name) {
        Bson filter = Condition.getFilter(Map.of(FiledNameConst.TITLE, name), Menu.class);
        return menuRepository.findByCondition(filter).stream().map(menu -> {
            if (!"0".equals(menu.getParentId())) {
                Menu parentMenu = menuRepository.findById(menu.getParentId());
                menu.setPath(parentMenu.getPath() + "/" + menu.getPath());
            }
            return menu;
        }).collect(Collectors.toList());
    }

    /**
     * 获取当前用户的快捷跳转菜单
     *
     * @return List<Menu>
     */
    @Override
    public List<Menu> currentJumpMenu() {
        User user = userProviderFeign.getCurrentUser().getData();
        List<Menu> menuList = user.getJumpMenu();
        if (ObjectUtil.isNotEmpty(menuList)) {
            return menuList;
        } else {
            List<Menu> menus = currentVisibleMenu();
            menus = ObjectUtil.isEmpty(menus) ? new ArrayList<>() : menus;
            menus = menus.size() > 4 ? menus.subList(0, 4) : menus;
            return menus;
        }
    }
}
