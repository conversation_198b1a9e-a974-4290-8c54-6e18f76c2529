package org.irm.lab.user.controller;

import cn.hutool.core.util.ObjectUtil;
import lombok.extern.slf4j.Slf4j;
import org.irm.lab.common.api.R;
import org.irm.lab.common.constant.PermissionConstant;
import org.irm.lab.common.exception.SecureException;
import org.irm.lab.user.constant.IHnCodeConstant;
import org.irm.lab.user.dto.HnCodeDTO;
import org.irm.lab.user.entity.Role;
import org.irm.lab.user.entity.User;
import org.irm.lab.user.service.IRoleService;
import org.irm.lab.user.service.IUserService;
import org.irm.lab.user.vo.UserVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.CrossOrigin;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;

@Slf4j
@Controller
@CrossOrigin
@RequestMapping("/ihn")
public class IHnCodeController {
    @Autowired
    private IUserService userService;

    @Resource
    private IRoleService roleService;


//    /*
//     * 扫码后执行的方法，获取扫码人的信息
//     * */
//    @GetMapping("/callback")
//    public R callback(String code, String state) {
//        try {
//            if (ObjectUtil.isEmpty(code)) return R.failed("取消登陆");
//            log.info("授权回调域：code:{},state:{}", code, state);
//            HnCodeDTO hnCodeDTO = userService.hnUserId(code);
////        HnCodeDTO hnCodeDTO = new HnCodeDTO().setUserId("HZ30000010")
////                .setUserInfo(new HnCodeDTO.UserInfo()
////                        .setName("电子公文库-戴辰").setMobile("13935633506").setEmail("").setGender("1"));
//            //先判断表中有无该用户，若没有，再获取用户信息进行注册
//            List<UserVO> hnUsers =
//                    userService.findByCondition(Map.of(IHnCodeConstant.FIELD_USER_ID, hnCodeDTO.getUserId()));
//
//            User hnUser;
//            if (hnUsers.isEmpty()) {
//                //没有当前用户，获取用户信息，并注册
//                HnCodeDTO.UserInfo userInfo = userService.hnUserInfo(hnCodeDTO.getUserId()).getUserInfo();
////            HnCodeDTO.UserInfo userInfo = hnCodeDTO.getUserInfo();
//                Role adminRole = roleService.findOneByNameAndTenantId(IHnCodeConstant.DEFAULT_ROLE_NAME, IHnCodeConstant.DEFAULT_TENANT_ID);
//                hnUser = userService.save(new User()
//                        .setName(userInfo.getName())
//                        .setUsername(userInfo.getMobile())
//                        .setUserId(hnCodeDTO.getUserId())
//                        .setEmail(userInfo.getEmail())
//                        .setPassword(BCrypt.hashpw(IHnCodeConstant.DEFAULT_PASSWORD, BCrypt.gensalt()))
//                        .setTenantId(IHnCodeConstant.DEFAULT_TENANT_ID)
//                        .setAvatar(userInfo.getAvatar())
//                        .setRoleIds(List.of(adminRole.getId())));
//
//                log.info("二维码登陆：新增用户入库【{}】", hnUser);
//            } else {
//                hnUser = hnUsers.get(0);
//                log.info("二维码登陆：已有当前用户【{}】", hnUser);
//            }
//
//            //TODO 默认参数
//            return authFeign.getAccessToken(
//                    Map.of("grant_type", "password",
//                            "password", IHnCodeConstant.DEFAULT_PASSWORD,
//                            "tenantId", IHnCodeConstant.DEFAULT_TENANT_ID,
//                            "client_secret", IHnCodeConstant.DEFAULT_CLIENT_SECRET,
//                            "client_id", IHnCodeConstant.DEFAULT_CLIENT_ID,
//                            "account", hnUser.getEmail(),
//                            "username", hnUser.getUsername())
//            );
//        } catch (Exception e) {
//            e.printStackTrace();
//            return R.failed(e.getMessage());
//        }
//    }


    /*
     * 扫码后执行的方法，获取扫码人的信息
     * */
    @GetMapping("/callback")
    public String callback(String code, String state) {
        try {
            if (ObjectUtil.isEmpty(code)) return "redirect:https://edocarchive.chng.com.cn/#/codelogin";
            log.info("授权回调域：code:{},state:{}", code, state);
            HnCodeDTO hnCodeDTO = userService.hnUserId(code);
            //先判断表中有无该用户，若没有，再获取用户信息进行注册
//            List<UserVO> hnUsers =
//                    userService.findByCondition(Map.of(IHnCodeConstant.FIELD_USER_ID, hnCodeDTO.getUserId()));

            //工号=用户名
            List<UserVO> hnUsers =
                    userService.findByCondition(Map.of("username", hnCodeDTO.getUserId()));


            User hnUser;
            if (hnUsers.isEmpty()) {
//                log.info("开始创建ihn+用户");
//                //没有当前用户，获取用户信息，并注册
//                HnCodeDTO.UserInfo userInfo = userService.hnUserInfo(hnCodeDTO.getUserId()).getUserInfo();
//                Role adminRole = roleService.findOneByNameAndTenantId(IHnCodeConstant.DEFAULT_ROLE_NAME, IHnCodeConstant.DEFAULT_ROLE_TENANT_ID);
//                hnUser = userService.save(new User()
//                        .setName(userInfo.getName())
//                        .setUsername(userInfo.getUserid())
//                        .setUserId(hnCodeDTO.getUserId())
//                        .setEmail(userInfo.getEmail())
//                        .setPassword(IHnCodeConstant.DEFAULT_PASSWORD)
//                        .setTenantId(IHnCodeConstant.DEFAULT_USER_TENANT_ID)
//                        .setAvatar(userInfo.getAvatar())
//                        .setGround(PermissionConstant.USER_GROUND_FRONT)
//                        .setRoleIds(List.of(adminRole.getId())));
//
//                log.info("二维码登陆：新增用户入库【{}】", hnUser);
                throw new SecureException("暂无权限登录系统");
            } else {
                hnUser = hnUsers.get(0);
                log.info("二维码登陆：已有当前用户【{}】", hnUser);
            }
            return "redirect:https://edocarchive.chng.com.cn/#/codelogin?userid=" + hnUser.getUsername();
        } catch (Exception e) {
            e.printStackTrace();
            return "redirect:https://edocarchive.chng.com.cn/#/codelogin";
        }
    }


    @GetMapping("/getCode")
    public R<HnCodeDTO> hnGetCode() {
        return R.data(userService.hnGetCode());
    }


}
