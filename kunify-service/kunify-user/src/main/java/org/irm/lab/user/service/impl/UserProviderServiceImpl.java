package org.irm.lab.user.service.impl;

import cn.hutool.core.convert.Convert;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.mongodb.client.model.Filters;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import org.irm.lab.common.api.R;
import org.irm.lab.common.constant.AuthConstant;
import org.irm.lab.common.exception.ServiceException;
import org.irm.lab.common.provider.MinioLinkProvider;
import org.irm.lab.common.utils.ThreadLocalUtil;
import org.irm.lab.resource.entity.Attach;
import org.irm.lab.resource.feign.AttachFeign;
import org.irm.lab.user.config.BuiltinUserConfig;
import org.irm.lab.user.constant.AdminConstant;
import org.irm.lab.user.constant.ExceptionMessage;
import org.irm.lab.user.entity.Department;
import org.irm.lab.user.entity.Role;
import org.irm.lab.user.entity.Unit;
import org.irm.lab.user.entity.User;
import org.irm.lab.user.repository.DepartmentRepository;
import org.irm.lab.user.repository.RoleRepository;
import org.irm.lab.user.repository.UnitRepository;
import org.irm.lab.user.repository.UserRepository;
import org.irm.lab.user.service.IUserProviderService;
import org.springframework.stereotype.Service;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.atomic.AtomicReference;

/**
 * UserProvider
 *
 * <AUTHOR>
 * @date 2023/10/7 9:52
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class UserProviderServiceImpl implements IUserProviderService {

    @Resource
    private UserRepository userRepository;
    @Resource
    private RoleRepository roleRepository;
    @Resource
    private UnitRepository unitRepository;
    @Resource
    private DepartmentRepository departmentRepository;

    private final MinioLinkProvider minioLinkProvider;
    private final AttachFeign attachFeign;
    /**
     * 获取当前登录用户
     *
     * @return {@link User}
     */
    @Override
    public User getCurrentUser() {
        log.info("=== UserProviderServiceImpl.getCurrentUser() 开始获取当前用户 ===");

        AtomicReference<User> userAtomicReference = new AtomicReference<>();

        // 调试：检查当前请求上下文
        debugCurrentRequestContext();

        //从Header中获取用户信息
        Optional.ofNullable((ServletRequestAttributes) RequestContextHolder.getRequestAttributes())
                .ifPresentOrElse((servletRequestAttributes) -> {
                    final String userHeader = servletRequestAttributes.getRequest().getHeader("user");
                    log.info("从HTTP请求头获取user信息: {}", userHeader != null ? "存在" : "不存在");

                    if (ObjectUtil.isEmpty(userHeader)) {
                        log.info("HTTP请求头中没有user信息，尝试从ThreadLocal获取");
                        String userFromThreadLocal = ThreadLocalUtil.get("user");
                        if (ObjectUtil.isNotEmpty(userFromThreadLocal)) {
                            log.info("从ThreadLocal获取到用户信息: {}", userFromThreadLocal);
                            String userId = new JSONObject(userFromThreadLocal).getStr(AuthConstant.USER_ID);
                            userAtomicReference.set(userRepository.findById(userId));
                        } else {
                            log.error("ThreadLocal中也没有用户信息");
                            throw new ServiceException(ExceptionMessage.CURRENT_USER_ERROR);
                        }
                    } else {
                        log.info("从HTTP请求头解析用户信息: {}", userHeader);
                        JSONObject userJsonObject = JSONUtil.parseObj(userHeader);
                        String userId = Convert.toStr(userJsonObject.get(AuthConstant.USER_ID));
                        log.info("解析得到用户ID: {}", userId);
                        userAtomicReference.set(userRepository.findById(userId));
                    }
                }, () -> {
                    log.info("无法获取ServletRequestAttributes，尝试从ThreadLocal获取用户信息");
                    //从threadLocal中获取用户信息
                    String userFromThreadLocal = ThreadLocalUtil.get("user");
                    if (ObjectUtil.isNotEmpty(userFromThreadLocal)) {
                        log.info("从ThreadLocal获取到用户信息: {}", userFromThreadLocal);
                        String userId = new JSONObject(userFromThreadLocal).getStr(AuthConstant.USER_ID);
                        userAtomicReference.set(userRepository.findById(userId));
                    } else {
                        log.error("ThreadLocal中没有用户信息");
                        throw new ServiceException(ExceptionMessage.CURRENT_USER_ERROR);
                    }
                });

        User user = userAtomicReference.get();
        if (user != null) {
            log.info("成功获取到用户: ID={}, Name={}", user.getId(), user.getName());
        } else {
            log.error("用户对象为null");
            throw new ServiceException("获取用户信息失败");
        }
        R<Attach> info = null;
        try {
            info = attachFeign.info(user.getAvatar());

        }catch (Exception e) {
            log.error("获取头像失败{}",e.getMessage());
            return user;
        }

        final Attach data = info.getData();
        if (ObjectUtil.isEmpty(data)) {
            user.setAvatar(user.getAvatar());
        }else {
            user.setAvatar(minioLinkProvider.getMinioLink(data.getOssObjectName()));

        }
        log.info("当前登录用户 ===> {}",user);
        return user;
    }

    /**
     * 获取当前用户单位
     *
     * @return 部门
     */
    @Override
    public Unit getCurrentUnit() {
        String unitId = getCurrentUser().getUnitId();
        if (ObjectUtil.isEmpty(unitId)) return null;
        return unitRepository.findById(unitId);
    }

    /**
     * 获取当前用户部门
     *
     * @return 部门
     */
    @Override
    public Department getCurrentDepartment() {
        String departmentId = getCurrentUser().getDepartmentId();
        if (ObjectUtil.isEmpty(departmentId)) return null;
        return departmentRepository.findById(departmentId);
    }

    /**
     * 获取当前用户的角色列表
     *
     * @return {@link Role}
     */
    @Override
    public List<Role> getCurrentRoles() {
        return roleRepository.findById(getCurrentUser().getRoleIds());
    }

    /**
     * 根据角色Id列表获取角色列表
     *
     * @param roleIds 角色Id列表
     * @return 角色列表
     */
    private List<Role> getRoles(List<String> roleIds) {
        return roleRepository.findById(roleIds);
    }

    /**
     * 判断当前登录的用户是否为管理员
     *
     * @return Boolean
     */
    @Override
    public Boolean isAdmin() {
        User user = getCurrentUser();
        if (user == null) throw new ServiceException("没有当前用户");
        List<Role> roles = getRoles(user.getRoleIds());
        return roles.stream().anyMatch(m -> BuiltinUserConfig.roleName.equals(m.getName()));
    }

    /**
     * 判断指定用户是否为管理员
     *
     * @return Boolean
     */
    @Override
    public Boolean isAdmin(String userId) {
        User user = userRepository.findById(userId);
        if (user == null) throw new ServiceException("没有当前用户");
        List<Role> roles = getRoles(user.getRoleIds());
        return roles.stream().anyMatch(m -> BuiltinUserConfig.roleName.equals(m.getName()));
    }

    /**
     * 判断当前用户是否为管理员
     *
     * @return 是：返回当前用户 否：返回null
     */
    @Override
    public User isAdminAndGetUser() {
        User user = getCurrentUser();
        if (ObjectUtil.isEmpty(user)) {
            return null;
        }
        List<Role> roles = getRoles(user.getRoleIds());
        return roles.stream().anyMatch(m -> BuiltinUserConfig.roleName.equals(m.getName())) ? null : user;
    }

    /**
     * 获取当前用户的角色名称列表
     *
     * @return 角色名称列表
     */
    @Override
    public List<String> getRoleNames() {
        List<String> roleNames = new ArrayList<>();
        getCurrentRoles().forEach(role -> roleNames.add(role.getName()));
        return roleNames;
    }

    /**
     * 获取当前用户所有的菜单权限的id
     *
     * @return 菜单id列表
     */
    @Override
    public Set<String> getMenuIds() {
        Set<String> menuIds = new HashSet<>();
        getCurrentRoles().forEach(role -> menuIds.addAll(role.getMenuIds()));
        return menuIds;
    }

    /**
     * 获取当前用户所属租户Id
     *
     * @return 当前租户ID
     */
    @Override
    public String getTenantId() {
        return getCurrentUser().getTenantId();
    }

    /**
     * 获取超级管理员用户
     *
     * @return {@link User}
     */
    @Override
    public User getAdmin() {
        // 获取超级管理员角色对象
        List<Role> roleList = roleRepository.findByCondition(Filters.and(Filters.eq("name", BuiltinUserConfig.roleName), Filters.eq("roleAlias", AdminConstant.ROLE_ALIAS)));
        if (ObjectUtil.isNotEmpty(roleList)) {
            Role adminRole = roleList.get(0);
            // 获取当前租户 角色为超级管理员的 用户
            List<User> admin = userRepository.findByCondition(Filters.and(Filters.eq("roleIds", adminRole.getId()), Filters.eq("tenantId", getTenantId())));
            return ObjectUtil.isNotEmpty(admin) ? admin.get(0) : null;
        }
        return null;
    }

    /**
     * 调试当前请求上下文信息
     * 用于排查Feign头传播问题
     */
    private void debugCurrentRequestContext() {
        ServletRequestAttributes attributes = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
        if (attributes != null) {
            log.info("=== 当前请求上下文调试信息 ===");

            // 检查user头
            String userHeader = attributes.getRequest().getHeader("user");
            if (userHeader != null) {
                log.info("✅ 请求头中存在user信息");
                log.info("user头长度: {} 字符", userHeader.length());
                log.info("user头完整内容: [{}]", userHeader);

                // 检查JSON是否完整
                try {
                    JSONObject testJson = JSONUtil.parseObj(userHeader);
                    log.info("✅ user头JSON解析成功，包含字段: {}", testJson.keySet());
                    if (testJson.containsKey(AuthConstant.USER_ID)) {
                        log.info("✅ 包含user_id字段: {}", testJson.get(AuthConstant.USER_ID));
                    } else {
                        log.error("❌ 缺少user_id字段！");
                    }
                } catch (Exception e) {
                    log.error("❌ user头JSON解析失败: {}", e.getMessage());
                }
            } else {
                log.warn("❌ 请求头中不存在user信息");
            }

            // 打印所有请求头（用于调试）
            log.debug("所有请求头信息:");
            java.util.Enumeration<String> headerNames = attributes.getRequest().getHeaderNames();
            int headerCount = 0;
            while (headerNames.hasMoreElements()) {
                String headerName = headerNames.nextElement();
                String headerValue = attributes.getRequest().getHeader(headerName);
                log.debug("  {}: {}", headerName, headerValue);
                headerCount++;
            }
            log.info("总共接收到 {} 个请求头", headerCount);

            // 检查请求URL
            String requestURL = attributes.getRequest().getRequestURL().toString();
            log.info("当前请求URL: {}", requestURL);

        } else {
            log.warn("❌ 无法获取ServletRequestAttributes");
        }

        // 检查ThreadLocal
        String userFromThreadLocal = ThreadLocalUtil.get("user");
        if (userFromThreadLocal != null) {
            log.info("✅ ThreadLocal中存在用户信息: {}", userFromThreadLocal);
        } else {
            log.warn("❌ ThreadLocal中不存在用户信息");
        }

        log.info("=== 请求上下文调试信息结束 ===");
    }
}
