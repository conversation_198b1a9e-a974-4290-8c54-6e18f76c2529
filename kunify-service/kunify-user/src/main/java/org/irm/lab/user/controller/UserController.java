package org.irm.lab.user.controller;

import cn.hutool.core.convert.Convert;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.mongodb.client.model.Filters;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.irm.lab.common.annotation.MyLog;
import org.irm.lab.common.api.R;
import org.irm.lab.common.constant.AuthConstant;
import org.irm.lab.common.constant.LogConstant;
import org.irm.lab.common.constant.MessageConstant;
import org.irm.lab.common.constant.PermissionConstant;
import org.irm.lab.common.exception.SecureException;
import org.irm.lab.common.exception.ServiceException;
import org.irm.lab.common.pojo.BaseStatus;
import org.irm.lab.common.support.Condition;
import org.irm.lab.common.support.MyPage;
import org.irm.lab.common.utils.Func;
import org.irm.lab.common.utils.RedisUtil;
import org.irm.lab.common.utils.ThreadLocalUtil;
import org.irm.lab.log.entity.LogEntity;
import org.irm.lab.log.feign.LogFeign;
import org.irm.lab.log.utils.IPUtil;
import org.irm.lab.user.config.BuiltinUserConfig;
import org.irm.lab.user.config.LockedUserProperties;
import org.irm.lab.user.constant.IHnCodeConstant;
import org.irm.lab.user.dto.UserDTO;
import org.irm.lab.user.entity.Tag;
import org.irm.lab.user.entity.User;
import org.irm.lab.user.feign.AuthFeign;
import org.irm.lab.user.feign.UserFeign;
import org.irm.lab.user.granter.ITokenGranter;
import org.irm.lab.user.granter.TokenGranterBuilder;
import org.irm.lab.user.granter.TokenParameter;
import org.irm.lab.user.repository.UserRepository;
import org.irm.lab.user.service.IUserProviderService;
import org.irm.lab.user.service.IUserService;
import org.irm.lab.user.vo.UserVO;
import org.irm.lab.user.wrapper.UserWrapper;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.crypto.Cipher;
import javax.crypto.KeyGenerator;
import javax.crypto.SecretKey;
import javax.crypto.spec.SecretKeySpec;
import javax.servlet.http.HttpServletRequest;
import java.io.UnsupportedEncodingException;
import java.nio.charset.StandardCharsets;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;


import static org.irm.lab.user.constant.IPConstant.*;

import com.mongodb.client.model.Filters;

@RestController
@RequestMapping(value = "/user")
@Api(value = "用户")
@Slf4j
public class UserController implements UserFeign {

    @Resource
    private IUserService userService;
    @Resource
    private AuthFeign authFeign;
    @Resource
    private IUserProviderService iUserProviderService;
    @Resource
    private RedisUtil redisUtil;
    @Resource
    private HttpServletRequest request;
    @Resource
    private UserRepository userRepository;
    @Resource
    private LockedUserProperties lockedUserProperties;

    @Value("${ihn_code.default_password}")
    private String defaultPassword;

    @Value("${ihn_code.default_user_tenant_id}")
    private String defaultUserTenantId;

    @ApiOperation(value = "用户分页")
    @GetMapping(value = "/page")
    public R<MyPage<UserVO>> page(@RequestParam Map<String, Object> user) {
        Integer page = Convert.toInt(user.getOrDefault("page", 0));
        Integer size = Convert.toInt(user.getOrDefault("size", 10));
        return R.data(userService.page(user, page, size));
    }

    @MyLog(menu = LogConstant.MENU_SYSTEM, dataType = LogConstant.DATA_USER, operation = LogConstant.OPERATION_ADD)
    @ApiOperation(value = "用户增改")
    @PostMapping(value = "/save")
    public R<User> save(@RequestBody User user) {
        return R.data(userService.save(user));
    }

    @MyLog(menu = LogConstant.MENU_SYSTEM, dataType = LogConstant.DATA_USER, operation = LogConstant.OPERATION_REMOVE)
    @ApiOperation(value = "用户删除")
    @PostMapping(value = "/remove")
    public R<Void> remove(@RequestBody String ids) {
        userService.remove(Func.objToStrList(ids));
        return R.success();
    }

    @MyLog(menu = LogConstant.MENU_SYSTEM, dataType = LogConstant.DATA_USER_PASSWORD, operation = LogConstant.OPERATION_RESET)
    @ApiOperation(value = "用户密码重置", hidden = true)
    @PostMapping(value = "/reset-password")
    public R<User> resetPassword(@RequestBody Map<String, String> params) {
        return R.data(userService.resetPassword(params));
    }

    @MyLog(menu = LogConstant.MENU_SYSTEM, dataType = LogConstant.DATA_USER_PASSWORD, operation = LogConstant.OPERATION_ALTER)
    @ApiOperation(value = "用户密码修改", hidden = true)
    @PostMapping(value = "/update-password")
    public R<User> updatePassword(@RequestParam Map<String, Object> params) {
        String userId = Convert.toStr(params.getOrDefault("userId", null));
        String oldPassword = Convert.toStr(params.getOrDefault("oldPassword", null));
        String newPassword = Convert.toStr(params.getOrDefault("newPassword", null));
        return R.data(userService.updatePassword(userId, oldPassword, newPassword));
    }

    @ApiOperation(value = "当前租户下的所有用户", hidden = true)
    @GetMapping(value = "/list-by-tenant")
    public R<List<User>> listByTenantId() {
        return R.data(userService.listByTenantId());
    }

    @ApiOperation(value = "所有用户", hidden = true)
    @GetMapping(value = "/list-all")
    public R<List<User>> listAll() {
        return R.data(userService.listAll());
    }

    @MyLog(menu = LogConstant.MENU_SYSTEM, dataType = LogConstant.DATA_USER_PHOTO, operation = LogConstant.OPERATION_ALTER)
    @ApiOperation(value = "用户头像修改", hidden = true)
    @PostMapping(value = "/update-photo")
    public R<Void> update(@RequestParam MultipartFile file, @RequestParam String userId) {
        userService.updatePhoto(file, userId);
        return R.success();
    }

    @MyLog(menu = LogConstant.MENU_SYSTEM, dataType = LogConstant.DATA_USER_STATUS, operation = LogConstant.OPERATION_ALTER)
    @ApiOperation(value = "用户状态修改", hidden = true)
    @PostMapping(value = "/update-status")
    public R<Void> updateStatus(@RequestBody Map<String, Object> params) {
        String status = Convert.toStr(params.get("status"), BaseStatus.OK);
        String id = Convert.toStr(params.get("id"));
        userService.updateStatus(status, id);
        return R.success();
    }

    @ApiOperation(value = "单个用户")
    @GetMapping(value = "/info")
    public R<User> info(@RequestParam String id) {
        return R.data(userService.info(id));
    }

    @ApiOperation(value = "多个用户", hidden = true)
    @GetMapping(value = "/multiple-info")
    public R<List<User>> info(@RequestParam List<String> ids) {
        return R.data(userService.info(ids));
    }

    @ApiOperation(value = "根据邮箱和租户查询用户", hidden = true)
    @GetMapping("/user-by-email-and-tenant")
    public R<User> userByEmailAndTenantId(@RequestParam String email, @RequestParam String tenantId) {
        return R.data(userService.findOneByEmailAndTenantId(email, tenantId));
    }

    @ApiOperation(value = "根据用户名和租户查询用户", hidden = true)
    @GetMapping("/user-by-username-and-tenant")
    public R<User> userByUsernameAndTenantId(@RequestParam String username, @RequestParam String tenantId) {
        return R.data(userService.findOneByUsernameAndTenantId(username, tenantId));
    }

    @ApiOperation(value = "获取当前用户信息", hidden = true)
    @GetMapping("/current-user")
    public R<UserVO> getCurrentUser() {
        return R.data(UserWrapper.build().entityVO(iUserProviderService.getCurrentUser()));
    }

    @MyLog(menu = LogConstant.MENU_SYSTEM, dataType = LogConstant.DATA_FAST_BUTTON, operation = LogConstant.OPERATION_ALTER)
    @ApiOperation(value = "配置快捷按钮", hidden = true)
    @PostMapping("/save-jump-menu")
    public R<User> saveJumpMenu(@RequestParam String userId, @RequestBody String menuIds) {
        return R.data(userService.saveJumpMenu(userId, Func.objToStrList(menuIds)));
    }

    @ApiOperation(value = "获取用户IP", hidden = true)
    @PostMapping("/save-ip")
    public R saveIp(HttpServletRequest request) {
        R<UserVO> currentUser = getCurrentUser();
        UserVO user = currentUser.getData();
        if (StrUtil.isNotBlank(request.getHeader(request_header))) {
            redisUtil.set(redis_ip_key + user.getId(), request.getHeader(request_header));
        } else
            redisUtil.set(redis_ip_key + user.getId(), request.getHeader(default_ip));
        return R.success();
    }

    @MyLog(menu = LogConstant.MENU_SYSTEM, dataType = "", operation = LogConstant.OPERATION_LOGIN, logRank = LogConstant.RANK_LOGIN)
    @ApiOperation(value = "用户登录", hidden = true)
    @PostMapping(value = "/login")
    public R login(@RequestParam String grantType, @RequestParam(required = false) String refreshToken, @RequestParam String account, @RequestParam String password,
                   @RequestParam String tenantId, @RequestParam String username, @RequestParam(required = false) String ground, @RequestParam(required = false) String uuid, @RequestParam(required = false) String code) throws UnsupportedEncodingException {

        log.info("grantType:{}", grantType);
        log.info("refreshToken:{}", refreshToken);
        log.info("account:{}", account);
        log.info("password:{}", password);
        log.info("tenantId:{}", tenantId);
        log.info("username:{}", username);
        log.info("ground:{}", ground);

        String userType = Convert.toStr(request.getHeader(AuthConstant.USER_TYPE_HEADER_KEY), AuthConstant.DEFAULT_USER_TYPE);
        String client_header = Convert.toStr(request.getHeader(AuthConstant.CLIENT_AUTH_HEADER_KEY), "Basic ay11bmlmeS1hcHA6UlVDTEFCIzIwMjI=");
        String[] tokens = extractClientInfo(client_header);
        assert tokens.length == 2;
        String clientId = tokens[0];
        String clientSecret = tokens[1];

        System.out.println(clientId);
        System.out.println(clientSecret);

        // 获取内置用户，为了做审计
        User innerSysUser = userRepository.findOneNotExist(Filters.and(Filters.eq("username", BuiltinUserConfig.builtInUser)));
        if (innerSysUser == null) {
            throw new ServiceException("内置用户为空，系统无法初始化");
        }

        String loginErrorMark = AuthConstant.MARK_PREFIX + username;
        String loginErrorLocked = AuthConstant.LOCKED_PREFIX + username;

        // 校验客户端信息
        if (validateClient(clientId, clientSecret)) {
            throw new SecureException(MessageConstant.CLIENT_AUTH_FAILUIRE);
        }

        // 账号锁定校验

        User user = userRepository.findOneNotExist(Filters.and(Filters.eq("username", username), Filters.eq("tenantId", tenantId)));
        if (user != null && BaseStatus.BLOCK.equals(user.getStatus())) {
            // lock-key还存在
            if (redisUtil.hasKey(loginErrorLocked)) {
                throw new ServiceException("账号已被锁定," + lockedUserProperties.getUnlockTime() + "分钟后自动解锁");
            } else {
                user.setStatus(BaseStatus.OK);
                // 添加审计

                ThreadLocalUtil.set("user", JSONUtil.createObj().putOpt(AuthConstant.USER_ID, innerSysUser.getId()).toString());
                save(user);
                ThreadLocalUtil.remove();
            }
        }

        //验证码校验
        //validateCaptcha(code, uuid);

        TokenParameter tokenParameter = new TokenParameter();
        tokenParameter.getArgs().set("account", account)
                .set("password", password)
                .set("grantType", grantType)
                .set("refreshToken", refreshToken)
                .set("userType", userType)
                .set("client_id", clientId)
                .set("username", username)
                .set("tenantId", tenantId);
        ITokenGranter granter = TokenGranterBuilder.getGranter(grantType);
        UserDTO userDTO;
        try {
            userDTO = granter.grant(tokenParameter);
        } catch (Exception e) {
            e.printStackTrace();
            // 添加审计
            ThreadLocalUtil.set("user", JSONUtil.createObj().putOpt(AuthConstant.USER_ID, innerSysUser.getId()).toString());
            lockValidate(user, loginErrorMark, loginErrorLocked);
            ThreadLocalUtil.remove();
            throw new ServiceException("当前用户不存在，或密码错误!");
        }
        if (userDTO == null || userDTO.getId() == null) {
            // 添加审计
            ThreadLocalUtil.set("user", JSONUtil.createObj().putOpt(AuthConstant.USER_ID, innerSysUser.getId()).toString());
            lockValidate(user, loginErrorMark, loginErrorLocked);
            ThreadLocalUtil.remove();
            return R.failed(MessageConstant.USERNAME_PASSWORD_ERROR);
        }
        //判断当前用户是否为后台管理员
        if (!"front".equals(ground) && !"back".equals(userDTO.getGround())) {
            throw new ServiceException(MessageConstant.ACCESS_FORBIDDEN_BACK_SYSTEM);
        }
        // 获取远端token
        Map<String, String> params = new HashMap<>();
        params.put("client_id", clientId);
        params.put("client_secret", clientSecret);
        params.put("grant_type", grantType);
        params.put("username", username);
        params.put("account", account);
        params.put("password", password);
        params.put("tenantId", userDTO.getTenantId());

        // 登陆成功，清除登陆失败痕迹与锁定key
        redisUtil.del(loginErrorMark);
        redisUtil.del(loginErrorLocked);
        return authFeign.getAccessToken(params);
    }

    private void lockValidate(User user, String loginErrorMark, String loginErrorLocked) {
        if (user != null) {
            int errorCount = 1;
            Object existErrorCount = redisUtil.get(loginErrorMark);
            if (ObjectUtil.isNotEmpty(existErrorCount)) errorCount = (Integer.parseInt(existErrorCount.toString()) + 1);
            if (errorCount >= lockedUserProperties.getErrorCount()) {
                // 锁定账号
                user.setStatus(BaseStatus.BLOCK);
                save(user);
                // 添加登陆锁定key，移除登陆失败痕迹
                redisUtil.set(loginErrorLocked, 1, lockedUserProperties.getUnlockTime(), TimeUnit.MINUTES);
                redisUtil.del(loginErrorMark);
                log.warn("用户{}，登陆失败{}次，自动锁定账号{}分钟", user.getUsername(), errorCount, lockedUserProperties.getUnlockTime());
            } else {
                // 添加登陆失败痕迹
                redisUtil.set(loginErrorMark, errorCount, lockedUserProperties.getErrorTime(), TimeUnit.MINUTES);
                log.warn("用户{}，登陆失败{}次", user.getUsername(), errorCount);
            }

        }
    }

    @SneakyThrows
    public void validateCaptcha(String code, String uuid) {
        String verifyKey = "captcha_codes:" + uuid;
        String captcha = redisUtil.get(verifyKey).toString();
        redisUtil.del(verifyKey);
        if (captcha == null) {
            // 验证码为空逻辑方法
            throw new ServiceException("请输入验证码！");
        }
        if (!code.equalsIgnoreCase(captcha)) {
            // 验证码错误逻辑方法
            throw new ServiceException("验证码错误！");
        }
    }


    /**
     * 获取clientid和secret的信息头
     *
     * @param header
     * @return
     */
    @SneakyThrows
    public static String[] extractClientInfo(String header) throws UnsupportedEncodingException {
        header = header.replace(AuthConstant.BASIC_HEADER_PREFIX_EXT, AuthConstant.BASIC_HEADER_PREFIX);
        if (!header.startsWith(AuthConstant.BASIC_HEADER_PREFIX)) {
            throw new ServiceException("No client information in request header");
        }

        byte[] base64Token = header.substring(6).getBytes(StandardCharsets.UTF_8);

        byte[] decoded;
        try {
            decoded = Base64.getDecoder().decode(base64Token);
        } catch (IllegalArgumentException var7) {
            throw new ServiceException("Failed to decode basic authentication token");
        }

        String token = new String(decoded, StandardCharsets.UTF_8);
        int index = token.indexOf(StrUtil.COLON);
        if (index == -1) {
            throw new ServiceException("Invalid basic authentication token");
        } else {
            return new String[]{token.substring(0, index), token.substring(index + 1)};
        }
    }

    /**
     * 校验客户端信息
     *
     * @param clientId
     * @param clientSecret
     * @return
     */
    private boolean validateClient(String clientId, String clientSecret) {
        if (StrUtil.equalsAnyIgnoreCase(AuthConstant.ADMIN_CLIENT_ID, clientId) && StrUtil.equalsAnyIgnoreCase(AuthConstant.ADMIN_CLIENT_SECRET, clientSecret))
            return false;
        return !StrUtil.equalsAnyIgnoreCase(AuthConstant.PORTAL_CLIENT_ID, clientId) || !StrUtil.equalsAnyIgnoreCase(AuthConstant.PORTAL_CLIENT_SECRET, clientSecret);
    }

    /**
     * 获取当前租户下的所有用户
     *
     * @return {@link User}
     */
    @GetMapping("/current-tenantId-list")
    public R<List<User>> currentTenantIdList() {
        return R.data(encryptedName(userService.currentTenantIdList()));
    }

    private List<User> encryptedName(List<User> users) {
        return users.stream().peek(user -> user.setName(cn.hutool.core.codec.Base64.encode(user.getName()))).collect(Collectors.toList());
    }

    /**
     * 新增/修改用户标签
     *
     * @param tag {@link Tag}
     */
    @PostMapping("/save-update-tag")
    public R<Void> saveOrUpdateTag(@RequestBody Tag tag) {
        userService.saveOrUpdateTag(tag);
        return R.success();
    }

    /**
     * 删除标签
     *
     * @param tag {@link Tag}
     */
    @PostMapping("/remove-tag")
    public R<Void> removeTag(@RequestBody Tag tag) {
        userService.removeTag(tag);
        return R.success();
    }

    @Resource
    private LogFeign logFeign;


    public static String decrypt(String encryptedData, String key) throws Exception {
        SecretKeySpec secretKey = new SecretKeySpec(key.getBytes(), "AES");
        Cipher cipher = Cipher.getInstance("AES");
        cipher.init(Cipher.DECRYPT_MODE, secretKey);
        byte[] originalData = cipher.doFinal(Base64.getDecoder().decode(encryptedData));
        return new String(originalData);
    }

    public static String encrypt(String data, String key) throws Exception {
        SecretKeySpec secretKey = new SecretKeySpec(key.getBytes(), "AES");
        Cipher cipher = Cipher.getInstance("AES");
        cipher.init(Cipher.ENCRYPT_MODE, secretKey);
        byte[] encryptedData = cipher.doFinal(data.getBytes());
        return Base64.getEncoder().encodeToString(encryptedData);
    }

    public static SecretKey generateKey() throws Exception {
        KeyGenerator keyGen = KeyGenerator.getInstance("AES");
        keyGen.init(128); // 选择密钥长度：128、192或256位
        return keyGen.generateKey();
    }



    // 判断是否登陆成功，并颁发token
    @GetMapping("/oauth-login")
    public R oAuthLogin(@RequestParam() String userid) throws UnsupportedEncodingException {
//        List<UserVO> hnUsers =
//                userService.findByCondition(Map.of(IHnCodeConstant.FIELD_USER_ID, userid));

        try {
            if (StrUtil.isBlank(userid)) {
                throw new ServiceException("非法参数");
            }
            userid = decrypt(userid, "h8eVLtcW/oj7U++tV7iPLw==");
        } catch (Exception e) {
            throw new ServiceException("非法参数");
        }

        List<UserVO> hnUsers =
                userService.findByCondition(Map.of("username", userid));
        if (hnUsers.isEmpty())
            return R.failed("登陆失败");
        else {

            final R password = login("password", null, hnUsers.get(0).getUsername(), defaultPassword,
                    defaultUserTenantId, hnUsers.get(0).getUsername(), PermissionConstant.USER_GROUND_FRONT, null, null);
            if (password.getCode() == 200) {
                final LogEntity logEntity = new LogEntity();
                logEntity.setUserId(hnUsers.get(0).getId());
                logEntity.setSystem(LogConstant.SYSTEM_FRONT);
                logEntity.setRank(LogConstant.RANK_LOGIN);
                logEntity.setIp(IPUtil.getIpAddr(request));
                logEntity.setContext(LogConstant.STATUS_LONGIN_SUCCESS);
                logEntity.setStatus(LogConstant.STATUS_LONGIN_SUCCESS);
                logEntity.setOperation(LogConstant.RANK_LOGIN);
                logFeign.save(logEntity);
                return password;
            } else {
                final LogEntity logEntity = new LogEntity();
                logEntity.setUserId(hnUsers.get(0).getId());
                logEntity.setSystem(LogConstant.SYSTEM_FRONT);
                logEntity.setRank(LogConstant.RANK_LOGIN);
                logEntity.setIp(IPUtil.getIpAddr(request));
                logEntity.setContext(LogConstant.STATUS_LOGIN_ERROR);
                logEntity.setStatus(LogConstant.STATUS_LOGIN_ERROR);
                logEntity.setOperation(LogConstant.RANK_LOGIN);
                logFeign.save(logEntity);
                return password;
            }

        }
//        return login("password", null, "admin", "PAhzdt!@#",
//                "028639", "admin", null, null, null);
    }

    public User getLoginUser(Object parameterValue) {
        String userName = parameterValue.toString();
        List<User> users = new ArrayList<>();
        try {
            users = userRepository.findByCondition(Filters.eq("username", userName));
        } catch (Exception e) {
            e.printStackTrace();
        }
        return ObjectUtil.isEmpty(users) ? null : users.get(0);
    }
}
