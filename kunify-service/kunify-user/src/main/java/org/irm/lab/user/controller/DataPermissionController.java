package org.irm.lab.user.controller;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.irm.lab.common.annotation.MyLog;
import org.irm.lab.common.api.R;
import org.irm.lab.common.constant.LogConstant;
import org.irm.lab.common.entity.ApiMessage;
import org.irm.lab.common.utils.Func;
import org.irm.lab.user.entity.DataPermission;
import org.irm.lab.user.entity.Permission;
import org.irm.lab.user.enums.PermissionEnum;
import org.irm.lab.user.feign.DataPermissionFeign;
import org.irm.lab.user.service.IDataPermissionService;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;

@RestController
@RequestMapping(value = "/data-permission")
@Api(value = "数据权限")
public class DataPermissionController implements DataPermissionFeign {

    @Resource
    private IDataPermissionService dataPermissionService;

    @MyLog(menu = LogConstant.MENU_SYSTEM, dataType = LogConstant.DATA_INTERFACE_AUTH, operation = LogConstant.OPERATION_ADD_OR_ALTER)
    @ApiOperation(value = "新增或修改", notes = "新增或修改权限")
    @PostMapping(value = "/save")
    public R<DataPermission> save(@RequestBody DataPermission dataPermission) {
        return R.data(dataPermissionService.save(dataPermission.getParentId(), dataPermission));
    }

    @ApiOperation(value = "获取权限列表")
    @GetMapping(value = "/list")
    public R<List<DataPermission>> list(@RequestParam Map<String, Object> query) {
        return R.data(dataPermissionService.list(query));
    }

    @MyLog(menu = LogConstant.MENU_SYSTEM, dataType = LogConstant.DATA_INTERFACE_AUTH, operation = LogConstant.OPERATION_REMOVE)
    @ApiOperation(value = "权限删除")
    @PostMapping(value = "/remove")
    public R<Void> remove(@RequestBody String identifiers) {
        dataPermissionService.remove(Func.objToStrList(identifiers));
        return R.success();
    }

    @ApiOperation(value = "获取所有数据权限", hidden = true)
    @GetMapping(value = "/getParam")
    public R<List<ApiMessage>> getParam() {
        return R.data(dataPermissionService.getParam());
    }

    @ApiOperation(value = "根据权限id集合获取所有子数据权限", hidden = true)
    @GetMapping(value = "/get-child-path-by-list")
    public R<List<String>> getChildPathByList(List<Permission> permissionList) {
        return R.data(dataPermissionService.getChildPathByList(permissionList));
    }

    @ApiOperation(value = "获取所有子数据权限", hidden = true)
    @GetMapping(value = "/get-all-path")
    public R<List<String>> getAllPath() {
        return R.data(dataPermissionService.getChildPath());
    }

    @ApiOperation(value = "数据权限详情")
    @GetMapping(value = "/info")
    public R<DataPermission> info(String id) {
        return R.data(dataPermissionService.info(id));
    }

    @ApiOperation(value = "权限分类获取")
    @GetMapping("/get-permission")
    public R<List<String>> getPermissionList(@RequestParam String type){
        return R.data(PermissionEnum.permissionList(type));
    }
}
