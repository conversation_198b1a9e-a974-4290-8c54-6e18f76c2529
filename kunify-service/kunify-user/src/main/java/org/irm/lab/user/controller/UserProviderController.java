package org.irm.lab.user.controller;


import lombok.RequiredArgsConstructor;
import org.irm.lab.common.api.R;
import org.irm.lab.user.entity.Department;
import org.irm.lab.user.entity.Role;
import org.irm.lab.user.entity.Unit;
import org.irm.lab.user.entity.User;
import org.irm.lab.user.provider.UserProviderFeign;
import org.irm.lab.user.service.IUserProviderService;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;


import java.util.List;
import java.util.Set;

/**
 * UserProvider
 *
 * <AUTHOR>
 * @date 2023/10/7 9:50
 */
@RestController
@RequestMapping("/user-provider")
@RequiredArgsConstructor
public class UserProviderController implements UserProviderFeign {

    private final IUserProviderService iUserProviderService;

    /**
     * 获取当前登录用户
     *
     * @return {@link User}
     */
    @GetMapping("/get-current-user")
    public R<User> getCurrentUser() {
        return R.data(iUserProviderService.getCurrentUser());
    }

    /**
     * 获取当前用户单位
     *
     * @return 单位
     */
    @GetMapping("/get-current-unit")
    public R<Unit> getCurrentUnit() {
        return R.data(iUserProviderService.getCurrentUnit());
    }

    /**
     * 获取当前用户部门
     *
     * @return 部门
     */
    @GetMapping("/get-current-department")
    public R<Department> getCurrentDepartment() {
        return R.data(iUserProviderService.getCurrentDepartment());
    }

    /**
     * 获取当前用户的角色列表
     *
     * @return {@link Role}
     */
    @GetMapping("/get-current-roles")
    public R<List<Role>> getCurrentRole() {
        return R.data(iUserProviderService.getCurrentRoles());
    }

    /**
     * 判断当前登录的用户是否为管理员
     *
     * @return Boolean
     */
    @GetMapping("/current-is-admin")
    public R<Boolean> isAdmin(){
        return R.data(iUserProviderService.isAdmin());
    }

    /**
     * 判断指定用户是否为管理员
     *
     * @return Boolean
     */
    @GetMapping("/is-admin")
    public R<Boolean> isAdmin(@RequestParam String userId){
        return R.data(iUserProviderService.isAdmin(userId));
    }

    /**
     * 判断当前用户是否为管理员
     *
     * @return 是：返回当前用户 否：返回null
     */
    @GetMapping("/is-admin-get-user")
    public R<User> isAdminAndGetUser(){
        return R.data(iUserProviderService.isAdminAndGetUser());
    }

    /**
     * 获取当前用户的角色名称列表
     *
     * @return 角色名称列表
     */
    @GetMapping("/get-role-names")
    public R<List<String>> getRoleNames(){
        return R.data(iUserProviderService.getRoleNames());
    }

    /**
     * 获取当前用户所有的菜单权限的id
     *
     * @return 菜单id列表
     */
    @GetMapping("/get-menu-ids")
    public R<Set<String>> getMenuIds() {
        return R.data(iUserProviderService.getMenuIds());
    }

    /**
     * 获取当前用户所属租户Id
     *
     * @return 当前租户ID
     */
    @GetMapping("/get-tenant-id")
    public R<String> getTenantId(){
        return R.data(iUserProviderService.getTenantId());
    }

    /**
     * 获取超级管理员用户
     *
     * @return {@link User}
     */
    @GetMapping("/get-admin")
    public R<User> getAdmin(){
        return R.data(iUserProviderService.getAdmin());
    }
}
