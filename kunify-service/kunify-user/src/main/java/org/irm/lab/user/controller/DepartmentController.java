package org.irm.lab.user.controller;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.irm.lab.common.annotation.MyLog;
import org.irm.lab.common.api.R;
import org.irm.lab.common.constant.LogConstant;
import org.irm.lab.common.utils.Func;
import org.irm.lab.user.entity.Department;
import org.irm.lab.user.feign.DepartmentFeign;
import org.irm.lab.user.service.IDepartmentService;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

@RestController
@RequestMapping(value = "/department")
@Api(value = "部门")
public class DepartmentController implements DepartmentFeign {

    @Resource
    private IDepartmentService departmentService;

    @ApiOperation(value = "部门列表")
    @GetMapping(value = "/list")
    public R<List<Department>> list(){
        return R.data(departmentService.list());
    }

    @ApiOperation(value = "单位部门列表")
    @GetMapping(value = "/list-by-unitId")
    public R<List<Department>> listByUnitId(@RequestParam String unitId){
        return R.data(departmentService.listByUnitId(unitId));
    }

    @MyLog(menu = LogConstant.MENU_SYSTEM, dataType = LogConstant.DATA_DEPARTMENT, operation = LogConstant.OPERATION_ADD)
    @ApiOperation(value = "部门增改")
    @PostMapping(value = "/save")
    public R<Department> save(@RequestBody Department department) {
        return R.data(departmentService.save(department));
    }

    @MyLog(menu = LogConstant.MENU_SYSTEM, dataType = LogConstant.DATA_DEPARTMENT, operation = LogConstant.OPERATION_REMOVE)
    @ApiOperation(value = "部门删除")
    @PostMapping(value = "/remove")
    public R<Void> remove(@RequestBody String ids) {
        departmentService.remove(Func.objToStrList(ids));
        return R.success();
    }

    @ApiOperation(value = "获取未被引用的部门")
    @GetMapping("/available-department")
    public R<List<Department>> availableDepartment(@RequestParam String unitId) {
        return R.data(departmentService.availableDepartment(unitId));
    }

    @ApiOperation(value = "单个部门")
    @GetMapping(value = "/info")
    public R<Department> info(@RequestParam String id) {
        return R.data(departmentService.info(id));
    }
}
