package org.irm.lab.user.controller;

import lombok.extern.slf4j.Slf4j;
import org.irm.lab.common.api.R;
import org.irm.lab.common.utils.ThreadLocalUtil;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.servlet.http.HttpServletRequest;
import java.util.Enumeration;
import java.util.HashMap;
import java.util.Map;

/**
 * Feign头传播测试控制器 - kunify-user服务
 * 
 * 用于验证kunify-user服务是否能正确接收到从其他服务传播过来的请求头
 */
@Slf4j
@RestController
@RequestMapping("/user-provider")
public class FeignHeaderTestController {

    /**
     * 测试接收请求头的情况
     * 
     * @return 接收到的头信息
     */

    
    /**
     * 获取当前请求的所有头信息
     * 
     * @return 头信息Map
     */
    private Map<String, String> getCurrentRequestHeaders() {
        Map<String, String> headers = new HashMap<>();
        
        ServletRequestAttributes attributes = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
        if (attributes != null) {
            HttpServletRequest request = attributes.getRequest();
            Enumeration<String> headerNames = request.getHeaderNames();
            
            while (headerNames.hasMoreElements()) {
                String headerName = headerNames.nextElement();
                String headerValue = request.getHeader(headerName);
                headers.put(headerName, headerValue);
            }
        }
        
        return headers;
    }
    
    /**
     * 获取当前请求URL
     * 
     * @return 请求URL
     */
    private String getCurrentRequestURL() {
        ServletRequestAttributes attributes = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
        if (attributes != null) {
            return attributes.getRequest().getRequestURL().toString();
        }
        return "无法获取请求URL";
    }
    
    /**
     * 简单的健康检查接口
     * 
     * @return 健康状态
     */
    @GetMapping("/health")
    public R<String> health() {
        return R.success("kunify-user服务 - Feign头测试控制器运行正常");
    }
}
