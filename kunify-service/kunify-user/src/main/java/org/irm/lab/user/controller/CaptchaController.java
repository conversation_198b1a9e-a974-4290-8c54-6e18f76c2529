package org.irm.lab.user.controller;


import cn.hutool.core.codec.Base64;
import cn.hutool.json.JSONObject;
import com.google.code.kaptcha.Producer;
import io.swagger.annotations.Api;
import org.irm.lab.common.api.R;
import org.irm.lab.common.utils.RedisUtil;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.util.FastByteArrayOutputStream;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.imageio.ImageIO;
import java.awt.image.BufferedImage;
import java.io.IOException;
import java.util.UUID;
import java.util.concurrent.TimeUnit;

@RestController
@RequestMapping(value = "/captcha")
@Api(value = "验证码")
public class CaptchaController {
    @Resource
    private Producer captchaProducer;
    @Resource
    private RedisTemplate<String, String> redisTemplate;
    @Resource
    private RedisUtil redisUtil;

    @GetMapping("/captchaImage")
    public R<JSONObject> getCode() throws IOException {
        // AjaxResult 本质是一个 HashMap, 调用put方法向内放置内容
        JSONObject result = new JSONObject();
        String uuid = UUID.randomUUID().toString();
        String redisKey = "captcha_codes:" + uuid;
        String captchaText = captchaProducer.createText();
        BufferedImage image = captchaProducer.createImage(captchaText);
        redisTemplate.opsForValue().set(redisKey, captchaText, 3, TimeUnit.MINUTES);
        boolean set = redisUtil.set(redisKey, captchaText, 180);
        if (set){
            FastByteArrayOutputStream os = new FastByteArrayOutputStream();
            ImageIO.write(image, "jpg", os);
            result.putOpt("uuid", uuid);
            result.putOpt("img", Base64.encode(os.toByteArray()));
            return R.data(result);
        }
        return R.failed();
    }
}

