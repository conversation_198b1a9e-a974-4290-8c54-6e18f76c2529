package org.irm.lab.user.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.bean.copier.CopyOptions;
import cn.hutool.core.collection.IterUtil;
import cn.hutool.core.io.FileUtil;
import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.crypto.SmUtil;
import cn.hutool.crypto.digest.BCrypt;
import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.mongodb.client.model.Filters;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.bson.Document;
import org.bson.conversions.Bson;
import org.irm.lab.common.constant.AbstractBaseEntityFieldConstant;
import org.irm.lab.common.constant.AuthConstant;
import org.irm.lab.common.constant.ExceptionMessageConst;
import org.irm.lab.common.constant.FiledNameConst;
import org.irm.lab.common.exception.ServiceException;
import org.irm.lab.common.pojo.BaseStatus;
import org.irm.lab.common.support.Condition;
import org.irm.lab.common.support.MyPage;
import org.irm.lab.common.utils.RedisUtil;
import org.irm.lab.resource.entity.Attach;
import org.irm.lab.resource.feign.IOssEndPoint;
import org.irm.lab.system.entity.Menu;
import org.irm.lab.system.feign.MenuFeign;
import org.irm.lab.user.config.BuiltinUserConfig;
import org.irm.lab.user.config.HnCodeConfig;
import org.irm.lab.user.config.LockedUserProperties;
import org.irm.lab.user.constant.ExceptionMessage;
import org.irm.lab.user.constant.IHnCodeConstant;
import org.irm.lab.user.dto.HnCodeDTO;
import org.irm.lab.user.entity.Role;
import org.irm.lab.user.entity.Tag;
import org.irm.lab.user.entity.User;
import org.irm.lab.user.remote.RemoteHnCodeStatement;
import org.irm.lab.user.repository.UserRepository;
import org.irm.lab.user.service.IRoleService;
import org.irm.lab.user.service.IUserProviderService;
import org.irm.lab.user.service.IUserService;
import org.irm.lab.user.vo.UserVO;
import org.irm.lab.user.wrapper.UserWrapper;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.net.URLEncoder;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

@Service
@Slf4j
@RequiredArgsConstructor
public class UserServiceImpl implements IUserService {

    private final UserRepository userRepository;
    private final IRoleService roleService;
    private final MenuFeign menuFeign;
    private final IOssEndPoint ossEndPoint;
    private final IUserProviderService iUserProviderService;
    private final HttpServletRequest request;
    @Resource
    private final RemoteHnCodeStatement hnCodeStatement;
    private final RedisTemplate<String, Object> redisTemplate;
    @Resource
    private RedisUtil redisUtil;
    @Resource
    private LockedUserProperties lockedUserProperties;


    //@PostConstruct
    public User initUser() {
        String tenantId = "028639";
        String name = BuiltinUserConfig.roleName;
        Role role = roleService.findOneByNameAndTenantId(name, tenantId);
        if (role == null) {
            role = new Role();
            role.setName(BuiltinUserConfig.roleName);
            role.setDescription("管理员");
            role.setRoleAlias("管理员");
            role.setTenantId("028639");
            roleService.save(role);
        }

        String password = BCrypt.hashpw("PAhzdt!@#", BCrypt.gensalt());
        Map<String, Object> query = new HashMap<>();
        query.put("email", "<EMAIL>");
        query.put("tenantId", tenantId);
        List<User> users = IterUtil.toList(userRepository.findByCondition(Condition.getFilter(query, User.class)));
        if (ObjectUtil.isEmpty(users)) {
            User user = new User();
            user.setName("超级管理员");
            user.setUsername(BuiltinUserConfig.builtInUser);
            user.setPassword(password);
            user.setEmail("<EMAIL>");
            user.setCreateTime(new Date());
            user.setStatus(BaseStatus.OK);
            user.setRoleIds(List.of(role.getId()));
            user.setTenantId(tenantId);
            userRepository.save(user);
            return user;
        }
        return users.get(0);
    }

    /**
     * 二维码登陆：获取accesstoken
     *
     * @return
     */
    @Override
    public HnCodeDTO hnAccessToken() {
        HnCodeDTO hnCodeDTO = new HnCodeDTO();
        //获取access_token
        if (Boolean.TRUE.equals(redisTemplate.hasKey(IHnCodeConstant.FIELD_ACCESS_TOKEN)))
            return hnCodeDTO.setAccessToken(mappingRedis2Bean());
        String accessTokenUrl = HnCodeConfig.HN_ACCESS_TOKEN_URL +
                "?corpid=%s" +
                "&corpsecret=%s";

        accessTokenUrl = String.format(accessTokenUrl,
                HnCodeConfig.HN_APP_ID,
                HnCodeConfig.HN_APP_SECRET);
        log.info("二维码登陆：获取accesstoken的URL为【{}】", accessTokenUrl);

        hnCodeDTO = hnCodeDTO.wrapperAccessToken(hnCodeStatement.hnAccessToken(accessTokenUrl));

        //存入redis
        redisTemplate.opsForHash().putAll(IHnCodeConstant.FIELD_ACCESS_TOKEN, mappingBean2Redis(hnCodeDTO.getAccessToken()));
        redisTemplate.expire(IHnCodeConstant.FIELD_ACCESS_TOKEN,
                IHnCodeConstant.FIELD_ACCESS_TOKEN_EXPIRE, TimeUnit.SECONDS);
        return hnCodeDTO;
    }

    private HnCodeDTO.AccessToken mappingRedis2Bean() {
        return BeanUtil.mapToBean(redisTemplate.opsForHash().entries(IHnCodeConstant.FIELD_ACCESS_TOKEN),
                HnCodeDTO.AccessToken.class,
                false,
                CopyOptions.create());
    }

    private Map<String, Object> mappingBean2Redis(Object obj) {
        return BeanUtil.beanToMap(
                obj, false, false
        );
    }

    /**
     * 二维码登陆：获取二维码url
     *
     * @return
     */
    @Override
    public HnCodeDTO hnGetCode() {
        try {
            //生成ihn+二维码，只需要访问提供的固定url，带上我们的参数值即可
            String baseUrl = HnCodeConfig.HN_CODE_URL +
                    "?appid=%s" +
                    "&agentid=%s" +
                    "&redirect_uri=%s" +
                    "&state=%s";
            //回调地址需要用URLEncoder加密
            String redirectUrl = URLEncoder.encode(HnCodeConfig.HN_REDIRECT_URL, "UTF-8");

            //拼接hn提供的url参数
            String codeUrl = String.format(
                    baseUrl,
                    HnCodeConfig.HN_APP_ID,
                    HnCodeConfig.HN_AGENT_ID,
                    redirectUrl,
                    HnCodeConfig.HN_STATE
            );
            log.info("二维码登陆：调取iHN+二维码URL为【{}】", codeUrl);
            return new HnCodeDTO().setCodeUrl(codeUrl);
        } catch (Exception e) {
            log.error("二维码登陆：获取ihn+二维码失败！");
            throw new ServiceException("获取ihn+二维码失败！");
        }

    }

    @Override
    public HnCodeDTO hnUserId(String code) {
        //获取用户信息
        HnCodeDTO hnCodeDTO = new HnCodeDTO();
        String userIdUrl = HnCodeConfig.HN_USER_ID_URL +
                "?access_token=%s" +
                "&code=%s";

        userIdUrl = String.format(userIdUrl,
                hnAccessToken().getAccessToken().getAccess_token(),
                code);

        log.info("二维码登陆：获取UserID接口url为【{}】", userIdUrl);
        return hnCodeDTO.wrapperUserId(hnCodeStatement.hnUserId(userIdUrl));
    }

    @Override
    public HnCodeDTO hnUserInfo(String userId) {
        //获取用户信息
        HnCodeDTO hnCodeDTO = new HnCodeDTO();
        String userInfoUrl = HnCodeConfig.HN_USER_INFO_URL +
                "?access_token=%s" +
                "&userid=%s";

        userInfoUrl = String.format(userInfoUrl,
                hnAccessToken().getAccessToken().getAccess_token(),
                userId);

        log.info("二维码登陆：获取UserInfo接口url为【{}】", userInfoUrl);
        return hnCodeDTO.wrapperUserInfo(hnCodeStatement.hnUserInfo(userInfoUrl));
    }


    /**
     * 用户条件查询
     */
    @Override
    public List<UserVO> findByCondition(Map<String, Object> param) {
        List<User> users = userRepository.findByCondition(Condition.getFilter(param, User.class));
        return UserWrapper.build().listVO(users);
    }

    /**
     * 用户分页
     *
     * @param param 查询条件
     * @param page  页
     * @param size  条
     * @return
     */
    @Override
    public MyPage<UserVO> page(Map<String, Object> param, Integer page, Integer size) {
        String builtInUser = BuiltinUserConfig.builtInUser;
        System.out.println(builtInUser);
        MyPage<User> users = userRepository.findPageByConditionAndSorted(
                Condition.getFilter(param, User.class), Filters.eq(AbstractBaseEntityFieldConstant.CREATE_TIME, -1), page, size);
        List<UserVO> userVOS = UserWrapper.build().listVO(users.getContent());
        return new MyPage<>(page, size, users.getTotalElements(), userVOS);
    }

    @Override
    public User save(User userDTO) {
        User user;   //添加或修改的user
        Map<String, Object> userMap = new HashMap<>();
        userMap.put("username", userDTO.getUsername());
        if (ObjectUtil.isEmpty(userDTO.getId())) {
            if (ObjectUtil.isNotEmpty(userRepository.findByCondition(Condition.getFilter(userMap, User.class)))) {
                throw new ServiceException(ExceptionMessage.USERNAME_EXIST);
            }
            user = BeanUtil.copyProperties(userDTO, User.class, "id");
            user.setPassword(BCrypt.hashpw(userDTO.getPassword(), BCrypt.gensalt()));
            user.setPasswordSM3(SmUtil.sm3(userDTO.getPassword()));
            //扫码登陆，无法获取tenantId
            if (ObjectUtil.isEmpty(user.getTenantId()))
                user.setTenantId(iUserProviderService.getCurrentUser().getTenantId());
            user.setRoleIds(userDTO.getRoleIds());
        } else {
            user = userRepository.findById(userDTO.getId());
            if (user.getName().equals("超级管理员"))
                userDTO.setName("超级管理员");
            List<User> users = userRepository.findByCondition(Condition.getFilter(userMap, User.class)).stream().
                    filter(user1 -> !user1.getId().equals(userDTO.getId())).collect(Collectors.toList());
            if (ObjectUtil.isNotEmpty(users)) throw new ServiceException(ExceptionMessage.USERNAME_EXIST);

            BeanUtil.copyProperties(userDTO, user, "password");
            user.setRoleIds(userDTO.getRoleIds());
        }
        // 用户标签
        List<Tag> tagList = user.getTag();
        for (Tag tag : tagList) {
            if (tag.getIdentifier() == null) {
                tag.setIdentifier(IdUtil.simpleUUID());
            }
        }

        String loginErrorLocked = AuthConstant.LOCKED_PREFIX + user.getUsername();
        String loginErrorMark = AuthConstant.MARK_PREFIX + user.getUsername();
        // 若修改用户状态为BLOCK，添加redisKey，自动锁定
        if (BaseStatus.BLOCK.equals(user.getStatus()) && !(redisUtil.hasKey(loginErrorLocked))) {
            // 添加登陆锁定key
            redisUtil.set(loginErrorLocked, 1, lockedUserProperties.getUnlockTime(), TimeUnit.MINUTES);
        }
        if (BaseStatus.OK.equals(user.getStatus())) {
            // 启动用户后，删除key
            redisUtil.del(loginErrorLocked, loginErrorMark);
        }
        return info(userRepository.save(user));
    }

    /**
     * 用户删除
     *
     * @param ids 用户ids
     */
    @Override
    public void remove(List<String> ids) {
        List<String> collect = userRepository.findById(ids).stream().map(User::getName).collect(Collectors.toList());
        if (collect.contains("超级管理员"))
            throw new ServiceException(ExceptionMessage.CAN_NOT_OPERATION_ADMIN);
        userRepository.deleteByIdFake(ids);
    }

    /**
     * 密码重置
     *
     * @param params
     * @return
     */
    @Override
    public User resetPassword(Map<String, String> params) {
        String password = MapUtil.get(params, "password", String.class);
        String userId = MapUtil.get(params, "userId", String.class);
        if (StrUtil.isNotBlank(userId) && StrUtil.isNotBlank(password)) {
            User user = userRepository.findById(userId);
            user.setPassword(BCrypt.hashpw(password, BCrypt.gensalt()));
            user.setPasswordSM3(SmUtil.sm3(password));
            return userRepository.findById(userRepository.save(user));
        } else throw new ServiceException("传参错误，请联系管理员！！！");
    }

    /**
     * 密码修改
     *
     * @param userId      用户id
     * @param old 原密码
     * @param newPwd 新密码
     * @return
     */
    @Override
    public User updatePassword(String userId,String old, String newPwd) {
        if (ObjectUtil.isEmpty(userId) || ObjectUtil.isEmpty(old) || ObjectUtil.isEmpty(newPwd))
            throw new ServiceException(ExceptionMessage.MESSAGE_IS_WRONG);
        User user = userRepository.findById(userId);

        if (!BCrypt.checkpw(old, user.getPassword())) {
            throw new ServiceException(ExceptionMessage.OLD_PASSWORD_ERROR);
        }
        user.setPassword(BCrypt.hashpw(newPwd, BCrypt.gensalt()));
        user.setPasswordSM3(SmUtil.sm3(newPwd));
        return userRepository.findById(userRepository.save(user));
    }

    /**
     * 头像上传
     *
     * @param file   头像
     * @param userId 用户id
     * @return
     */
    @Override
    public void updatePhoto(MultipartFile file, String userId) {
        User user = userRepository.findById(userId);
        if (file != null && !file.isEmpty()) {
            String suffix = FileUtil.getSuffix(file.getOriginalFilename()).toLowerCase();
            if (!ObjectUtil.contains(List.of("png", "jpg", "jpeg", "gif"), suffix))
                throw new ServiceException(ExceptionMessage.AVATAR_FORMAT_ERROR);
            Attach attach = ossEndPoint.putFile(file).getData();
            user.setAvatar(attach.getId());
        }
        userRepository.save(user);
    }


    /**
     * 用户状态修改
     *
     * @param status 状态
     * @param userId 用户id
     * @return
     */
    @Override
    public void updateStatus(String status, String userId) {
        User user = userRepository.findById(userId);
        user.setStatus(status);
        userRepository.save(user);
    }

    @Override
    public List<User> getUsersByRoleId(String roleId) {
        Bson bson = Filters.in("roleIds", roleId);
        return userRepository.findByCondition(bson);
    }

    @Override
    public User info(String id) {
        return userRepository.findById(id);
    }

    @Override
    public List<User> info(List<String> ids) {
        return userRepository.findById(ids);
    }

    public List<String> pullRoleIds(List<String> roleIds) {
        Map<String, List<String>> update = new HashMap<>(1);
        update.put("roleIds", roleIds);
        return userRepository.pullCollectDistinct("", update);
    }

    /**
     * 根据邮箱和租户id查询用户
     *
     * @param email    邮箱
     * @param tenantId 租户id
     * @return
     */
    @Override
    public User findOneByEmailAndTenantId(String email, String tenantId) {
        HashMap<String, Object> query = new HashMap<>();
        query.put(FiledNameConst.EMAIL, email);
        query.put(FiledNameConst.TENANT_ID, tenantId);
        return userRepository.findOne(Condition.getFilter(query, User.class));
    }

    /**
     * 根据用户名和租户id查询用户信息
     *
     * @param username 用户名
     * @param tenantId 租户id
     * @return
     */
    @Override
    public User findOneByUsernameAndTenantId(String username, String tenantId) {
        HashMap<String, Object> query = new HashMap<>();
        query.put(FiledNameConst.USER_NAME, username);
        query.put(FiledNameConst.TENANT_ID, tenantId);
        return userRepository.findOne(Condition.getFilter(query, User.class));
    }

    /**
     * 查看当前租户下的所有用户
     *
     * @return 用户集合
     */
    @Override
    public List<User> listByTenantId() {
        HashMap<String, Object> query = new HashMap<>();
        query.put(FiledNameConst.TENANT_ID, iUserProviderService.getTenantId());
        return userRepository.findByCondition(Condition.getFilter(query, User.class));
    }

    @Override
    public List<User> listAll() {
        return userRepository.findAll();
    }

    /**
     * 添加快捷接口
     *
     * @param userId  用户id
     * @param menuIds 菜单ids
     * @return 用户
     */
    @Override
    public User saveJumpMenu(String userId, List<String> menuIds) {
        if (menuIds.size() > 4)
            throw new ServiceException(ExceptionMessageConst.CONFIG_MENU_MAX_FOUR);
        User user = info(userId);
        List<Menu> menuList = menuIds.stream().map(menuId -> {
            Menu menu = menuFeign.info(menuId).getData();
            return setPath(menu);
        }).collect(Collectors.toList());
        user.setJumpMenu(menuList);
        return info(userRepository.save(user));
    }

    public Menu setPath(Menu menu) {
        if (!menu.getParentId().equals("0")) {
            Menu parentMenu = setPath(menuFeign.info(menu.getParentId()).getData());
            menu.setPath(parentMenu.getPath() + "/" + menu.getPath());
        }
        return menu;
    }

    /**
     * 递归查询用户操作的按钮和菜单
     *
     * @param menus  带单
     * @param result
     * @return
     */
    public Map<String, List<String>> recurGetButton(List<JSONObject> menus, Map<String, List<String>> result) {
        List<String> button = new ArrayList<>();
        for (JSONObject menu : menus) {
            JSONArray childMenuArray = menu.getJSONArray("children");
            Object menuType = menu.get("menuType");
            if (childMenuArray != null) {
                List<JSONObject> childMenuList = new ArrayList<>();
                for (Object o : childMenuArray) {
                    childMenuList.add(JSONUtil.parseObj(String.valueOf(o)));
                }
                recurGetButton(childMenuList, result);
            } else if (menuType.equals("菜单")) {
                result.put(menu.get("title").toString(), button);
            } else if (menuType.equals("按钮")) {
                button.add(menu.get("perms").toString());
                String parentId = menu.get("parentId").toString();
                result.put(menuFeign.info(parentId).getData().getTitle(), button);
            }
        }
        return result;
    }

    /**
     * 获取当前租户下的所有用户
     *
     * @return {@link User}
     */
    @Override
    public List<User> currentTenantIdList() {
        String userStr = request.getHeader("user");
        JSONObject userObject = JSONUtil.parseObj(userStr);
        // 获取租户Id
        String tenantId = userObject.getStr("tenant_id");
        String userId = userObject.getStr("user_id");
        // 获取指定租户下的所有用户
        List<User> userList = userRepository.findByCondition(Filters.eq("tenantId", tenantId));
        // 移除当前用户
        userList.removeIf(user -> user.getId().equals(userId));
        return userList.stream().map(user -> {
            User newUser = new User();
            newUser.setId(user.getId());
            newUser.setName(user.getName());
            return newUser;
        }).collect(Collectors.toList());
    }


    /**
     * 新增/修改用户标签
     *
     * @param tag {@link Tag}
     */
    @Override
    public void saveOrUpdateTag(Tag tag) {
        User currentUser = iUserProviderService.getCurrentUser();
        String userId = currentUser.getId();
        String identifier = tag.getIdentifier();
        if (identifier != null) {
            // 修改标签
            userRepository.pullDistinct(userId, Map.of("tag", new Document("identifier", identifier)));
            userRepository.pushDistinct(userId, Map.of("tag", new Document(BeanUtil.beanToMap(tag, false, true))));
            return;
        }
        // 新增标签
        tag.setIdentifier(IdUtil.simpleUUID());
        userRepository.pushDistinct(userId, Map.of("tag", new Document(BeanUtil.beanToMap(tag, false, true))));
    }

    /**
     * 删除标签
     *
     * @param tag {@link Tag}
     */
    @Override
    public void removeTag(Tag tag) {
        User currentUser = iUserProviderService.getCurrentUser();
        String userId = currentUser.getId();
        userRepository.pullDistinct(userId, Map.of("tag", new Document("identifier", tag.getIdentifier())));
    }
}
