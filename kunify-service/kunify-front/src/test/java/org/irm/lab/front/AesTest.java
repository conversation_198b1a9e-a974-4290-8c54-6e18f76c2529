package org.irm.lab.front;

import org.irm.lab.front.mapper.DocumentEsMapper;
import org.jasypt.encryption.StringEncryptor;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.boot.test.context.SpringBootTest;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @date 2023/4/19 21:53
 * @description EasyES 手动创建索引
 */
@SpringBootTest
public class AesTest {

    @Qualifier("jasyptStringEncryptor")
    @Autowired
    private StringEncryptor stringEncryptor;

    @Test
    public void aesTest() {
        String text = "PsOsGgN0rfXfIPbz";

        System.out.println("stringEncryptor.encrypt(text) = " + stringEncryptor.encrypt(text));
    }
}
