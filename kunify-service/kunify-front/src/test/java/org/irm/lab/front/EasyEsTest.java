package org.irm.lab.front;

import org.irm.lab.front.mapper.DocumentEsMapper;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @date 2023/4/19 21:53
 * @description EasyES 手动创建索引
 */
@SpringBootTest
public class EasyEsTest {

    @Resource
    private DocumentEsMapper documentEsMapper;
    @Test
    public void createESIndex(){
        /**
         * 测试创建索引 根据实体类字段及其注解配置创建索引 大多数场景适用,最为简单,但灵活性稍差
         * 创建的索引与自动挡-运动模式一样,但触发方式为手动调用 区别是自动挡模式下索引创建及更新随spring容器启动时自动执行
         */
        documentEsMapper.createIndex("hn_document_es_028641");
    }
}
