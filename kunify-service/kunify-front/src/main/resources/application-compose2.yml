spring:
  cloud:
    nacos:
      discovery:
        server-addr: ***********:8848

  redis:
    database: 5
    port: 6379
    host: ***********
    password: kunify@411

  rabbitmq:
    host: *********** # RabbitMQ 服务的地址
    port: 5672 # RabbitMQ 服务的端口
    username: root # RabbitMQ 服务的账号
    password: 123456 # RabbitMQ 服务的密码

mongodb:
  host: ***********
  port: 27017
  database: kunify-hn
  username: root
  password: mongo2022
  authenticationDatabase: admin

neo4j:
  url: bolt://***********:27687
  username: neo4j
  password: 123456

easy-es:
  enable: true #默认为true,若为false则认为不启用本框架
  address: ***********:39200
  username: elastic #若无 则可省略此行配置
  password: 123456 #若无 则可省略此行配置

ai:
  sdkHost: ***********:1000
  sdkSecretKey: kc2HMKE9sTFgew0
  sdkApikey: VW3H6uDjgr
  qaHost: ***********:1000
  qaDataSet: 1083451845453545472

img-constant:
  internet-domain: http://***********:9000
  domain: http://**************:9000
  bucket: /kunify-hn-

qa:
  dataset:
    all-id: 1783409423943733250
    bzry-id: 1783409342967508994
    hyjy-id: 1787673080802955266
    gzzd-id: 1787424937336684545
    ldps-id: 1783409270379823106

logging:
  level:
    root: INFO
  file:
    name: /data/kunify-front.log