package org.irm.lab.front.controller;

import org.irm.lab.common.api.R;
import org.irm.lab.front.service.ResourceStatisticsService;
import org.irm.lab.front.vo.ResourceStatisticsVO;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

@RestController
@RequestMapping("/resource/statistics")
public class ResourceStatisticsController {
    @Resource
    private ResourceStatisticsService resourceStatisticsService;
    @PostMapping("/query")
    public R<ResourceStatisticsVO> resourceStatistics(){
        return R.data(resourceStatisticsService.queryStatistics());
    }
}
