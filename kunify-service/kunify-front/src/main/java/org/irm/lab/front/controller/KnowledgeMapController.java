package org.irm.lab.front.controller;

import cn.hutool.core.convert.Convert;
import cn.hutool.core.util.ObjectUtil;
import io.swagger.annotations.ApiModelProperty;
import lombok.RequiredArgsConstructor;
import org.irm.lab.common.api.R;
import org.irm.lab.front.dto.FilterMetadataDTO;
import org.irm.lab.front.service.IKnowledgeMapService;
import org.irm.lab.kg.entity.KnowledgeConcept;
import org.irm.lab.kg.feign.GraphMaintenanceFeign;
import org.irm.lab.kg.feign.KnowledgeConceptFeign;
import org.irm.lab.kg.vo.echarts.EchartsVO;
import org.springframework.web.bind.annotation.*;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2023/4/21 17:09
 * @description 知识地图控制器
 */
@RestController
@RequestMapping("/knowledge-map")
@RequiredArgsConstructor
public class KnowledgeMapController {
    private final GraphMaintenanceFeign graphMaintenanceFeign;
    private final KnowledgeConceptFeign knowledgeConceptFeign;
    private final IKnowledgeMapService knowledgeMapService;

    /**
     * 分类知识可视化
     *
     * @param classify 公文分类
     * @return EchartsVO
     */
    @ApiModelProperty(value = "分类知识可视化", hidden = true)
    @GetMapping("/document-visual")
    public R<EchartsVO> documentVisual(@RequestParam String classify, @RequestParam String showCount, @RequestParam String name) {
        showCount = ObjectUtil.isEmpty(showCount) ? "" : showCount;
        return R.data(knowledgeMapService.documentVisualPlus(classify, showCount, name));
    }

    /**
     * 获取所有概念类
     *
     * @return 概念列表
     */
    @ApiModelProperty(value = "获取所有概念类", hidden = true)
    @GetMapping("/knowledge-concept")
    public R<List<KnowledgeConcept>> knowledgeConcept() {
        List<KnowledgeConcept> conceptList = knowledgeConceptFeign.list().getData();
        conceptList = ObjectUtil.isEmpty(conceptList) ? new ArrayList<>() : conceptList.stream().filter(knowledgeConcept -> !"0".equals(knowledgeConcept.getParentId())).collect(Collectors.toList());
        return R.data(conceptList);
    }

    /**
     * 概念知识可视化
     *
     * @param map 查询条件
     * @return EchartsVO
     */
    @ApiModelProperty(value = "概念知识可视化", hidden = true)
    @GetMapping("/concept-visual")
    public R<EchartsVO> conceptVisual(@RequestParam Map<String, Object> map) {
        String conceptId = Convert.toStr(map.getOrDefault("conceptId", ""));
        String showCount = Convert.toStr(map.getOrDefault("showCount", ""));
        String name = Convert.toStr(map.getOrDefault("name", ""));
        return R.data(knowledgeMapService.conceptVisual(conceptId, showCount, name));
    }

    /**
     * 查询与当前节点关联的实例和关系
     *
     * @return 概念列表
     */
    @ApiModelProperty(value = "查询与当前节点关联的实例和关系", hidden = true)
    @GetMapping("/node-relation-echarts")
    public R<EchartsVO> echartsNodesWithRelation(@RequestParam String nodeId, @RequestParam String showCount) {
        return R.data(knowledgeMapService.echartsNodesWithRelation(nodeId,showCount));
    }

    /**
     * 实例关联树形图
     *
     * @param nodeId 实例id
     * @return 实例树形图
     */
    @ApiModelProperty(value = "实例关联树形图", hidden = true)
    @GetMapping("/node-group-by-concept")
    public R<EchartsVO> treeNodesWithRelation(@RequestParam String nodeId) {
        return R.data(knowledgeMapService.nodeGroupByConcept(nodeId));
    }

    @ApiModelProperty(value = "模糊查询对应属性下的实例名称", hidden = true)
    @GetMapping("/list-by-concept-and-name")
    public R<List<String>> NodesByConceptAndName(@RequestParam String conceptId, @RequestParam String name) {
        return R.data(knowledgeMapService.NodesByConceptAndName(conceptId, name));
    }

    @ApiModelProperty(value = "根据实例名称查询相关实例", hidden = true)
    @PostMapping("/echarts-by-entity-name")
    public R<EchartsVO> getEchartsByEntityName(@RequestBody List<FilterMetadataDTO> filterMetadataDTOS) {
        return R.data(knowledgeMapService.getEchartsByEntityName(filterMetadataDTOS));
    }
}
