package org.irm.lab.front.controller;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.irm.lab.common.api.R;
import org.irm.lab.common.constant.LogConstant;
import org.irm.lab.common.utils.ThreadLocalUtil;
import org.irm.lab.front.dto.AIMiddle.OcrInferResultDTO;
import org.irm.lab.front.dto.DraftQueryDTO;
import org.irm.lab.front.dto.RagQueryDTO;
import org.irm.lab.front.service.AIService;
import org.irm.lab.log.entity.LogEntity;
import org.irm.lab.log.feign.LogFeign;
import org.irm.lab.log.utils.IPUtil;
import org.irm.lab.user.entity.User;
import org.irm.lab.user.provider.UserProviderFeign;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.servlet.mvc.method.annotation.SseEmitter;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpSession;
import javax.validation.Valid;
import java.text.MessageFormat;
import java.util.Date;
import java.util.Random;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ConcurrentLinkedQueue;

/**
 * AI接口
 *
 * @date 2024/2/1 <br/>
 */
@Slf4j
@RestController
@RequiredArgsConstructor
public class AIController {


    private final AIService aiService;

    private final long timeout = Long.MAX_VALUE;
    private final HttpServletRequest httpServletRequest;
    private final UserProviderFeign userProviderFeign;
    private final LogFeign logFeign;



    /**
     * 智能问答
     *
     * @param parameter 问答所需参数
     * @return 返回sse
     */
    @PostMapping(value = "/outline", produces = MediaType.TEXT_EVENT_STREAM_VALUE)
    public SseEmitter intelligentQA(@RequestBody @Valid RagQueryDTO parameter) {
        log.info("outline开始执行");
        return aiService.intelligentQA(parameter);
//        return intelligentQAPlus(parameter);
//        return simulateQAResult(parameter);
    }

    /**
     * 大纲生成
     */
    @PostMapping(value = "/outlineGeneration")
    public SseEmitter outlineGeneration(@RequestBody DraftQueryDTO parameter) {

        return aiService.outlineGeneration(parameter);
    }

    /**
     * 正文生成
     */
    @PostMapping(value = "/textGeneration")
    public SseEmitter textGeneration(@RequestBody DraftQueryDTO parameter) {

        return aiService.textGeneration(parameter);

    }

    @PostMapping(value = "/ocrInfer")
    public OcrInferResultDTO ocrInfer(@RequestParam MultipartFile file, @RequestParam(required = false) String tenant,
                                      @RequestParam(required = false, defaultValue = "1") String drawBox,
                                      @RequestParam(required = false, defaultValue = "dg") String model) {

        return aiService.ocrInfer(file, tenant, drawBox, model);
    }


    private SseEmitter simulateQAResult(RagQueryDTO parameter) {
        SseEmitter emitter = new SseEmitter();
        ConcurrentLinkedQueue<String> successMessages = new ConcurrentLinkedQueue<>();
        ConcurrentLinkedQueue<String> errorMessages = new ConcurrentLinkedQueue<>();
        // 从请求头获取用户信息并存储在ThreadLocal中
        String user = httpServletRequest.getHeader("user");
        ThreadLocalUtil.set("user", user);
        // 避免前端轮训调用，5s间隔内的请求不予记录日志
        emitter.onCompletion(() -> {
            log.info("开始记录问答日志");
            writeLog(successMessages.toString(), parameter.getDataSetType());

        });
        // 模拟发送一些事件数据
        new Thread(() -> {
            successMessages.clear();
            errorMessages.clear();
            try {
                for (int i = 0; i < 5; i++) {
                    String msg = null;
                    if (i < 4) {
                        msg = "{" +
                                "success: false," +
                                "source: {" +
                                "id: \"1\"," +
                                "name: \"fdsf\"" +
                                "}," +
                                "answer: \"fdjklasfjkdlsajfkldjkslfjkldsjalfkjdsakl\"" +
                                "}";
                    } else {
                        msg = "{" +
                                "success: true," +
                                "source: {" +
                                "id: \"1\"," +
                                "name: \"fdsf\"" +
                                "}" +
                                "answer: \"fdjklasfjkdlsajfkldjkslfjkldsjalfkjdsakl\"" +
                                "}";
                        successMessages.add(msg);
                    }

                    emitter.send(msg, MediaType.TEXT_EVENT_STREAM);


//                    Thread.sleep(1000); // 模拟延迟
                }
                emitter.complete();
                // 注册onCompletion回调


                // 注册onError回调
                emitter.onError((e) -> {
                    System.out.println("errorMessages = " + errorMessages);
                });

                // 注册onTimeout回调
                emitter.onTimeout(() -> {
                    System.out.println("timeout = " + timeout);
                });
            } catch (Exception e) {
                log.error("error");
                emitter.completeWithError(e);
                errorMessages.add(e.getMessage());
            }
        }).start();

        return emitter;
    }


    public SseEmitter intelligentQAPlus(RagQueryDTO ragQueryDTO) {
        SseEmitter emitter = new SseEmitter(6 * 100000L);  // 设置超时时间
        int sid = 1;
        String user = httpServletRequest.getHeader("user");
        ThreadLocalUtil.set("user", user);
        String userStr = ThreadLocalUtil.get("user");
        // 定义固定的响应内容
        String content = "模拟SseEMitter返回的内容1，模拟SseEMitter返回的内容1，模拟SseEMitter返回的内容1，模拟SseEMitter返回的内容1，模拟SseEMitter返回的内容1，模拟SseEMitter返回的内容1，模拟SseEMitter返回的内容1，模拟SseEMitter返回的内容1，模拟SseEMitter返回的内容1，模拟SseEMitter返回的内容1";  // 要输出的字符串
        String thinkStr = "模拟Think返回的内容1，模拟Think返回的内容1，模拟Think返回的内容1，模拟Think返回的内容1，模拟Think返回的内容1，模拟Think返回的内容1，模拟Think返回的内容1，模拟Think返回的内容1，模拟Think返回的内容1，模拟Think返回的内容1，模拟Think返回的内容1，模拟Think返回的内容1，模拟Think返回的内容1，模拟Think返回的内容1，模拟Think返回的内容1";

        Random random = new Random();  // 用于生成随机数
        ConcurrentLinkedQueue<String> successMessages = new ConcurrentLinkedQueue<>();
        emitter.onCompletion(() -> {

            log.info("开始记录问答日志");
            // 返回日志id
            String id = aiService.writeLog(successMessages, ragQueryDTO, userStr);
            log.info("---------日志id{}---------------", id);
        });
//
//        if (!isRepeatRe   quest()) {
//            }
        String contentStr = "{\"answer\":\"模拟SseEMitter返回的内容1\"}";
        successMessages.add(contentStr);
        CompletableFuture.runAsync(() -> {
//            String userStr = ThreadLocalUtil.get("user");
            int currentLength = 0;  // 当前已发送的内容长度
            int currentThinkLength = 0;
            try {
                log.info("<<<<<<<<<<<<<<<<<<模拟固定内容开始执行问答>>>>>>>>>>>>>");

                while (currentThinkLength < thinkStr.length()) {
                    // 生成一个随机的输出长度，确保不会超过剩余字符数
                    int incrementThink = random.nextInt(5) + 1;  // 随机生成1到5的字符数
                    int thinkStrNewLength = Math.min(currentThinkLength + incrementThink, thinkStr.length());
                    // 判断是否是最后一次输出
                    boolean isLastThink = thinkStrNewLength == thinkStr.length();
                    // 截取新的字符串部分
                    String partialContentThink = thinkStr.substring(0, thinkStrNewLength);
                    String fixedResponseThink = "{\"reasoning\": " + (!isLastThink) + ", \"reasoning_text\": \"" + partialContentThink + "\" ,\"success\": " + false + ", \"source\": \"[{\\\"sid\\\": " + sid + ", \\\"name\\\": \\\"模拟文件\\\"}]\", \"answer\": \"" + "" + "\"}";

                    // 发送响应内容给前端
                    emitter.send(fixedResponseThink, MediaType.TEXT_EVENT_STREAM);

                    // 更新已输出的字符长度
                    currentThinkLength = thinkStrNewLength;

                    if (currentThinkLength == thinkStr.length()) {
                        // 持续发送响应直到所有字符输出完毕
                        while (currentLength < content.length()) {
                            // 每次发送前暂停1.5秒

                            // 生成一个随机的输出长度，确保不会超过剩余字符数
                            int increment = random.nextInt(5) + 1;  // 随机生成1到5的字符数
                            int newLength = Math.min(currentLength + increment, content.length());

                            // 判断是否是最后一次输出
                            boolean isLast = newLength == content.length();

                            // 截取新的字符串部分
                            String partialContent = content.substring(0, newLength);
                            String fixedResponse = "{\"reasoning\": " + false + ", \"reasoning_text\": \"" + "" + "\" ,\"success\": " + isLast + ", \"source\": \"[{\\\"sid\\\": " + sid + ", \\\"name\\\": \\\"模拟文件\\\"}]\", \"answer\": \"" + partialContent + "\"}";

                            // 发送响应内容给前端
                            emitter.send(fixedResponse, MediaType.TEXT_EVENT_STREAM);

                            // 更新已输出的字符长度
                            currentLength = newLength;
                        }
                    }


                }

                log.info("---模拟结束---");

                emitter.complete();
//                emitter.onCompletion(() -> {
//
//                    log.info("开始记录问答日志");
//                    // 返回日志id
//                    String id = aiService.writeLog(successMessages, ragQueryDTO, userStr);
//                    log.info("---------日志id{}---------------",id);
//                });
            } catch (Exception e) {
                emitter.completeWithError(e);
            }
        }).whenComplete((result, throwable) -> {
            if (throwable != null) {
                emitter.completeWithError(throwable);
            } else {
                emitter.complete();
            }
        });

        return emitter;
    }


    private boolean isRepeatRequest() {
        //TODO 单实例下生效
        HttpSession session = httpServletRequest.getSession();
        Date lastRequestTime = (Date) session.getAttribute("lastRequestTime");
        Date currentTime = new Date();

        // 更新最后请求时间
        session.setAttribute("lastRequestTime", currentTime);

        // 避免前端轮训调用，5s间隔内的请求不予记录日志
        // 如果两次请求时间小于最小间隔，则不记录日志或返回错误
        return lastRequestTime != null && (currentTime.getTime() - lastRequestTime.getTime()) < 5000L;
    }


    private String writeLog(String qa, String aws) {
        LogEntity logEntity = new LogEntity();
        R<User> userR = userProviderFeign.getCurrentUser();
        User user = userR.getData();
        logEntity.setIp(IPUtil.getIpAddr(httpServletRequest));
        logEntity.setMenu(LogConstant.MENU_QUESTION_SMART);
        logEntity.setDataType(LogConstant.DATA_QA_QUESTION);
        logEntity.setOperation(LogConstant.OPERATION_LIFT);
        logEntity.setSystem(LogConstant.SYSTEM_FRONT);
        logEntity.setRank(LogConstant.RANK_OPERATION);
        logEntity.setUserId(user.getId());

        String template = logEntity.getOperation() + "了1个" + logEntity.getDataType() + "【{0}】，得到结果：【{1}】";
        logEntity.setContext(MessageFormat.format(template, qa, aws));

        // 从请求头获取用户信息并存储在ThreadLocal中
        R<LogEntity> save = logFeign.save(logEntity);
        return save.getData().getId();

    }
}
