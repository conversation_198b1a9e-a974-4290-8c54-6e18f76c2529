package org.irm.lab.front.service.impl;

import cn.easyes.core.conditions.select.LambdaEsQueryWrapper;
import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.lang.Opt;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import cn.hutool.log.StaticLog;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.lucene.search.join.ScoreMode;
import org.bson.conversions.Bson;
import org.bson.types.ObjectId;
import org.dromara.streamquery.stream.core.stream.Steam;
import org.elasticsearch.action.search.SearchResponse;
import org.elasticsearch.index.query.NestedQueryBuilder;
import org.elasticsearch.index.query.QueryBuilders;
import org.elasticsearch.search.aggregations.AggregationBuilders;
import org.elasticsearch.search.aggregations.Aggregations;
import org.elasticsearch.search.aggregations.bucket.nested.Nested;
import org.elasticsearch.search.aggregations.bucket.nested.NestedAggregationBuilder;
import org.elasticsearch.search.aggregations.bucket.terms.Terms;
import org.elasticsearch.search.builder.SearchSourceBuilder;
import org.elasticsearch.search.sort.FieldSortBuilder;
import org.elasticsearch.search.sort.NestedSortBuilder;
import org.irm.lab.common.api.R;
import org.irm.lab.common.config.resolve.EsIndexResolver;
import org.irm.lab.common.constant.ModelConceptConst;
import org.irm.lab.common.constant.ModelPropertyConst;
import org.irm.lab.common.constant.ModelRelationConst;
import org.irm.lab.common.exception.ServiceException;
import org.irm.lab.common.provider.TenantProvider;
import org.irm.lab.common.support.MyPage;
import org.irm.lab.common.utils.RedisUtil;
import org.irm.lab.config.repository.SortConfigRepository;
import org.irm.lab.front.comparator.MeetingMinutesComparator;
import org.irm.lab.front.constant.RedisPrefixConstant;
import org.irm.lab.front.dto.MeetingKnowledgeQueryDTO;
import org.irm.lab.front.mapper.DocumentEsMapper;
import org.irm.lab.front.model.Document;
import org.irm.lab.front.service.IMeetingMinutesService;
import org.irm.lab.front.service.IResourceMessageService;
import org.irm.lab.front.task.MyTrigger;
import org.irm.lab.front.vo.MeetingDataCountVO;
import org.irm.lab.front.vo.TypeCountVO;
import org.irm.lab.front.vo.analyse.meeting.*;
import org.irm.lab.kg.entity.KnowledgeConcept;
import org.irm.lab.kg.entity.KnowledgeProperty;
import org.irm.lab.kg.entity.KnowledgeRelation;
import org.irm.lab.kg.entity.neo4j.node.NodeEntity;
import org.irm.lab.kg.entity.neo4j.node.NodeRelation;
import org.irm.lab.kg.enums.MeetingTypeEnum;
import org.irm.lab.kg.feign.KnowledgeConceptFeign;
import org.irm.lab.kg.repository.KnowledgeConceptRepository;
import org.irm.lab.kg.repository.KnowledgePropertyRepository;
import org.irm.lab.kg.repository.KnowledgeRelationRepository;
import org.irm.lab.kg.repository.neo4j.node.NodeEntityRepository;
import org.irm.lab.kg.repository.neo4j.node.NodeRelationRepository;
import org.irm.lab.kg.vo.echarts.EchartsNode;
import org.irm.lab.kg.vo.echarts.EchartsRelation;
import org.irm.lab.kg.vo.echarts.EchartsVO;
import org.irm.lab.repository.entity.Resource;
import org.irm.lab.repository.entity.ResourceAnnex;
import org.irm.lab.repository.feign.ResourceAnnexFeign;
import org.irm.lab.repository.feign.ResourceFeign;
import org.irm.lab.repository.repository.ResourceRepository;
import org.irm.lab.repository.vo.MetadataVO;
import org.irm.lab.user.provider.UserProviderFeign;
import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;
import org.neo4j.ogm.cypher.ComparisonOperator;
import org.neo4j.ogm.cypher.Filter;
import org.neo4j.ogm.cypher.Filters;
import org.springframework.stereotype.Service;

import javax.servlet.http.HttpServletRequest;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2023/6/25 9:32
 * @description 辅助分析-会议纪要业务实现类
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class MeetingMinutesServiceImpl implements IMeetingMinutesService {

    private final KnowledgeConceptFeign knowledgeConceptFeign;
    private final KnowledgeRelationRepository knowledgeRelationRepository;
    private final NodeEntityRepository nodeEntityRepository;
    private final NodeRelationRepository nodeRelationRepository;
    private final KnowledgePropertyRepository knowledgePropertyRepository;
    private final KnowledgeConceptRepository knowledgeConceptRepository;
    private final ResourceFeign resourceFeign;
    private final ResourceAnnexFeign resourceAnnexFeign;
    private final TenantProvider tenantProvider;
    private final RedisUtil redisUtil;
    private final MyTrigger trigger;
    private final HttpServletRequest request;
    private final UserProviderFeign userProviderFeign;
    private static final String HOT_ISSUES = "HOT_ISSUES_";
    private final IResourceMessageService resourceMessageService;
    private final EsIndexResolver esIndexResolver;
    private final DocumentEsMapper documentEsMapper;
    private final ResourceRepository resourceRepository;
    private final SortConfigRepository sortConfigRepository;


    /**
     * 新增访问数量
     */
    @Override
    public void visitMeetingMinutes() {
        Object o = redisUtil.get(RedisPrefixConstant.VISITS_USER_PRE + tenantProvider.getTenantId());
        if (o != null) {
            Long count = Convert.toLong(o);
            redisUtil.set(RedisPrefixConstant.VISITS_USER_PRE + tenantProvider.getTenantId(), ++count);
            return;
        }
        redisUtil.set(RedisPrefixConstant.VISITS_USER_PRE + tenantProvider.getTenantId(), 1);
    }

    /**
     * 获取时间区间内的es文档数
     */
    public long countByDate(String name, String start, String end) {
        System.out.println("开始时间" + start);
        System.out.println("结束时间" + end);
        // 创建一个新的 LambdaEsQueryWrapper 实例
        LambdaEsQueryWrapper<org.irm.lab.front.model.Document> queryWrapper = new LambdaEsQueryWrapper<>();
        queryWrapper.index(esIndexResolver.resolveEsIndexByTenant());
        if (name.equals("createTime")) {
            // 添加嵌套查询条件
            queryWrapper.ge("createTime", start).le("createTime", end);
        } else {
            // 添加嵌套查询条件
            queryWrapper.nested("metadata", nestedWrapper -> {
                nestedWrapper.eq("metadata.name", name)
                        .exists("metadata.dateValue").ge("metadata.dateValue", start).le("metadata.dateValue", end)
                ; // 检查dateValue元数据是否存在

            }).nested("metadata", nestedWrapper -> {
                nestedWrapper.eq("metadata.name", "公文分类").in("metadata.value", List.of("会议纪要"));
            });

        }


        return documentEsMapper.selectCount(queryWrapper);
    }

    /**
     * 收录数据统计
     * <p>
     * 总收录会议  当年收录会议数量    议题数量        累计服务用户
     *
     * @return {@link MeetingDataCountVO}
     */
    @Override
    public MeetingDataCountVO dataCount() {
        MeetingDataCountVO meetingDataCountVO = new MeetingDataCountVO();
        R<KnowledgeConcept> knowledgeConceptR = knowledgeConceptFeign.findByConceptName(ModelConceptConst.CONCEPT_HUIYI);
        KnowledgeConcept knowledgeConcept = knowledgeConceptR.getData();
        if (ObjectUtil.isEmpty(knowledgeConcept)) throw new ServiceException("不存在会议概念!");
        // 1、统计总收录会议数量
        String conceptId = knowledgeConcept.getId();
        Long totalMeetingCount = nodeEntityRepository.countByCondition(new Filters(new Filter("conceptId", ComparisonOperator.EQUALS, conceptId)));
//        meetingDataCountVO.setMeetingCount(this.countByMeeting());
        meetingDataCountVO.setMeetingCount(totalMeetingCount);
        // 2、统计当年收录的会议数量
        LocalDate currentDate = LocalDate.now();
        int currentYear = currentDate.getYear();
        // 获取当前年份的最小时间（1月1日的 00:00:00）
        LocalDateTime minDate = LocalDateTime.of(currentYear, 1, 1, 0, 0, 0);
        // 获取当前年份的最大时间（12月31日的 23:59:59.999999999）
        LocalDateTime maxDate = LocalDateTime.of(currentYear, 12, 31, 23, 59, 59, 999999999);
        long count = this.countByDate("印发日期", minDate.format(DateTimeFormatter.ISO_DATE_TIME), maxDate.format(DateTimeFormatter.ISO_DATE_TIME));
        meetingDataCountVO.setCurrentYearMeetingCount(count);
        // 3、统计议题总数
        R<KnowledgeConcept> knowledgeConceptIssueR = knowledgeConceptFeign.findByConceptName(ModelConceptConst.CONCEPT_HUIYI_YITI);
        KnowledgeConcept knowledgeConceptIssue = knowledgeConceptIssueR.getData();
        if (ObjectUtil.isEmpty(knowledgeConceptIssue)) throw new ServiceException("不存在会议议题概念!");
        Long issuesCount = nodeEntityRepository.countByCondition(new Filters(new Filter("conceptId", ComparisonOperator.EQUALS, knowledgeConceptIssue.getId())));
        meetingDataCountVO.setIssuesCount(issuesCount);
        // 4、三重一大
        String meetingIdentifier = getMeetingIdentifier(ModelPropertyConst.PROPERTY_SANZHONG_YIDA);
        Long conceptCount = nodeEntityRepository.countByCondition(new Filters(new Filter(meetingIdentifier, ComparisonOperator.EQUALS, "是")));
        meetingDataCountVO.setConceptCount(conceptCount);
        // 5、服务用户数量
        meetingDataCountVO.setVisitsCount(Convert.toLong(redisUtil.get(RedisPrefixConstant.VISITS_USER_PRE + tenantProvider.getTenantId())));
        return meetingDataCountVO;
    }

    private long countByMeeting() {
        // 创建一个新的 LambdaEsQueryWrapper 实例
        LambdaEsQueryWrapper<org.irm.lab.front.model.Document> queryWrapper = new LambdaEsQueryWrapper<>();
        queryWrapper.index(esIndexResolver.resolveEsIndexByTenant());
        // 添加嵌套查询条件
        queryWrapper.nested("metadata", nestedWrapper -> {
            nestedWrapper.eq("metadata.name", "公文分类").in("metadata.value", List.of("会议纪要"));
        });


        return documentEsMapper.selectCount(queryWrapper);
    }

    /**
     * 最新会议
     *
     * @return {@link NodeEntity}
     */
    public List<NodeEntity> newMeeting() {
        KnowledgeConcept concept = getKnowledgeConcept(ModelConceptConst.CONCEPT_HUIYI);
        String cypher = "MATCH (n:ENTITY) where n.conceptId=$conceptId RETURN n order by n.createTime DESC LIMIT $size";
        return nodeEntityRepository.findEntityByCypher(cypher, Map.of("conceptId", concept.getId(), "size", 5));
    }

    /**
     * 会议查询
     *
     * @param type 会议类型
     * @return 会议实例
     */
    public MyPage<NodeEntityVO> meetingQuery(Integer page, Integer size, String type) {
        // 第一部分代码
        long startTime1 = System.nanoTime();
//        Bson in;
//        Bson groupFilters = com.mongodb.client.model.Filters.eq("metadata.name", "分组");
//        if ("other".equals(type)) {
//            in = com.mongodb.client.model.Filters.exists("metadata.value", false);
//        } else {
//            in = com.mongodb.client.model.Filters.in("metadata.value", type);
//        }
//        Bson knowledgeMatchConfirm = com.mongodb.client.model.Filters.eq("knowledgeMatchConfirm", true);
//        Bson bson = com.mongodb.client.model.Filters.and(groupFilters, in, knowledgeMatchConfirm);
        Set<String> ids = queryIdByMetadataInEs(List.of(type), "分组");
//        List<Resource> resultResource = resourceRepository.findByCondition(bson);
        long end = System.nanoTime();
        log.info("分组查询es耗时：【{}】", (end - startTime1));
        KnowledgeConcept meetingConcept = getKnowledgeConcept(ModelConceptConst.CONCEPT_HUIYI);
//        Set<String> ids = new HashSet();
//        for (Resource resource : resultResource) {
//            ids.add(resource.getId());
//        }
        startTime1 = System.nanoTime();
        MyPage<NodeEntity> pageByConditionAndSort = nodeEntityRepository.findPageByConditionAndSort(meetingConcept.getId(), ids, page, size);
        end = System.nanoTime();
        log.info("分页分组查询neo4j耗时：【{}】", (end - startTime1));
        startTime1 = System.nanoTime();
        List<NodeEntityVO> nodeEntityVOS = returnNodeEntityVo(pageByConditionAndSort.getContent());
        end = System.nanoTime();
        log.info("转换content耗时：【{}】", (end - startTime1));
        return new MyPage<>(pageByConditionAndSort.getPage(), pageByConditionAndSort.getSize(), pageByConditionAndSort.getTotalElements(), nodeEntityVOS);
    }

    private List<NodeEntityVO> returnNodeEntityVo(List<NodeEntity> content) {
        ArrayList<NodeEntityVO> voResult = new ArrayList<>();
        for (NodeEntity nodeEntity : content) {
            Map<String, String> map = obtainResourceMap(nodeEntity.getDocIds());
            NodeEntityVO nodeEntityVO = new NodeEntityVO();
            BeanUtil.copyProperties(nodeEntity, nodeEntityVO, "originResource");
            nodeEntityVO.setOriginResource(map);
            voResult.add(nodeEntityVO);
        }
        return voResult;
    }

    /**
     * 根据某个元数据排序
     */
    public List<Resource> getMeatDateLikeOrderBy(String name, List<String> ids) {

        // 创建一个新的 LambdaEsQueryWrapper 实例
        LambdaEsQueryWrapper<Document> queryWrapper = new LambdaEsQueryWrapper<>();
        queryWrapper.index(esIndexResolver.resolveEsIndexByTenant());

        // 添加嵌套查询条件
        queryWrapper.nested("metadata", nestedWrapper -> {
            nestedWrapper.eq("metadata.name", name)
                    .in(org.irm.lab.front.model.Document::getId, ids)
                    .exists("metadata.dateValue"); // 检查dateValue元数据是否存在

        });

        FieldSortBuilder fieldSortBuilder = new FieldSortBuilder("metadata.dateValue")
                .order(org.elasticsearch.search.sort.SortOrder.DESC)
                .setNestedSort(new NestedSortBuilder("metadata"));
        queryWrapper.sort(fieldSortBuilder);
        final List<org.irm.lab.front.model.Document> xx = documentEsMapper.selectList(queryWrapper);
        final List<String> collect1 = xx.stream().map(org.irm.lab.front.model.Document::getId).collect(Collectors.toList());

        List<ObjectId> objectIdList = collect1.stream()
                .map(ObjectId::new)
                .collect(Collectors.toList());

        Bson bson1 = com.mongodb.client.model.Filters.in("_id", objectIdList);
        List<Resource> byCondition = resourceRepository.findByCondition(bson1);
        byCondition.sort(Comparator.comparingInt(resource -> collect1.indexOf(resource.getId())));
        return byCondition;

    }

    public Set<String> getMeatDate(Map<String, List<String>> params) {

        // 创建一个新的 LambdaEsQueryWrapper 实例
        final LambdaEsQueryWrapper<org.irm.lab.front.model.Document> queryWrapper = new LambdaEsQueryWrapper<>();
        queryWrapper.index(esIndexResolver.resolveEsIndexByTenant());
        // 这里演示了如何添加嵌套查询条件，具体方法取决于 Easy-Es 的版本和功能
        for (Map.Entry<String, List<String>> param : params.entrySet()) {
            queryWrapper.nested("metadata", nestedWrapper -> nestedWrapper
                    .eq("metadata.name", param.getKey())
                    .in("metadata.value", param.getValue()));
        }


        // 执行查询
        final List<org.irm.lab.front.model.Document> xx = documentEsMapper.selectList(queryWrapper);
        return xx.stream().map(Document::getId).collect(Collectors.toSet());
//        List<ObjectId> objectIdList = collect1.stream()
//                .map(ObjectId::new)
//                .collect(Collectors.toList());
//
//        Bson bson1 = com.mongodb.client.model.Filters.in("_id", objectIdList);
//        return resourceRepository.findByCondition(bson1);

    }

    /**
     * 知识查询
     *
     * @param meetingKnowledgeQueryDTO 查询条件
     * @return {@link MeetingVO}
     */
    @Override
    public MyPage<MeetingVO> knowledgeQueryMeeting(MeetingKnowledgeQueryDTO meetingKnowledgeQueryDTO) {
        long totalStart = System.nanoTime();
        Integer page = meetingKnowledgeQueryDTO.getPage();
        Integer size = meetingKnowledgeQueryDTO.getSize();
        page = ObjectUtil.isEmpty(page) ? 1 : page;
        size = ObjectUtil.isEmpty(size) ? 10 : size;
        // 获取会议和人员的概念对象
        KnowledgeConcept meetingConcept = getKnowledgeConcept(ModelConceptConst.CONCEPT_HUIYI);
        KnowledgeConcept peopleConcept = getKnowledgeConcept(ModelConceptConst.CONCEPT_RENYUAN);
        final String type = meetingKnowledgeQueryDTO.getType();
        // 全部会议类型
        long startTime1 = System.nanoTime();
        MyPage<NodeEntity> pageByConceptAndNameSort = null;
        long end = System.nanoTime();
        if (!type.equals("全部")) {
            HashMap<String, List<String>> params = new HashMap<>();
            params.put("公文分类", List.of("会议纪要"));
            params.put("分组", List.of(type));
            //当前分组下边的id
            final Set<String> list = getMeatDate(params);
            end = System.nanoTime();
//            log.info("根据分组查询会议纪要文件并确定存在于Mongodb中，耗时【{}】", end - startTime1);
//            StaticLog.info("当前分组下边的id个数:{}", list.size());
//            StaticLog.info("会议节点的id为：「{}」", meetingConcept.getId());

            pageByConceptAndNameSort = nodeEntityRepository.findPageByConceptAndNameSort(meetingConcept.getId(), meetingKnowledgeQueryDTO.getValue(), list, page, size);
        } else {
            pageByConceptAndNameSort = nodeEntityRepository.findPageByConceptSort(meetingConcept.getId(), meetingKnowledgeQueryDTO.getValue(), page, size);

        }
        startTime1 = System.nanoTime();
        // 转化结果
        List<MeetingVO> collect = pageByConceptAndNameSort.getContent()
                .stream()
                .map(nodeEntity -> generateMeetingVO(meetingConcept, peopleConcept, nodeEntity, type))
                .collect(Collectors.toList());
        end = System.nanoTime();
//        log.info("转化结果总耗时【{}】", end - startTime1);
//        StaticLog.info("参数传递 size:{},根据查询条件获取数量:{},根据generateMeetingVO返回数量:{},总数Total:{}", size, pageByConceptAndNameSort.getContent().size(), collect.size(), pageByConceptAndNameSort.getTotalElements());
        long endTime = System.nanoTime();
//        log.info("接口总耗时【{}】", endTime - totalStart);
        return new MyPage<>(pageByConceptAndNameSort.getPage(), pageByConceptAndNameSort.getSize(), pageByConceptAndNameSort.getTotalElements(), collect);
    }

    public Set<String> queryIdByMetadataInEs(List<String> value, String key) {
        if (ObjectUtil.isEmpty(value)) return new HashSet<>();

        // 创建一个新的 LambdaEsQueryWrapper 实例
        final LambdaEsQueryWrapper<org.irm.lab.front.model.Document> queryWrapper = new LambdaEsQueryWrapper<>();
        queryWrapper.index(esIndexResolver.resolveEsIndexByTenant());

        // 对于每个分类，添加一个嵌套查询条件
        for (String classify : value) {
            // 这里演示了如何添加嵌套查询条件，具体方法取决于 Easy-Es 的版本和功能
            queryWrapper.nested("metadata", nestedWrapper -> nestedWrapper
                    .eq("metadata.name", key)
                    .in("metadata.value", classify));
        }

        // 执行查询
        final List<org.irm.lab.front.model.Document> xx = documentEsMapper.selectList(queryWrapper);
        return xx.stream().map(Document::getId).collect(Collectors.toSet());
//        List<ObjectId> objectIdList = collect1.stream()
//                .map(ObjectId::new)
//                .collect(Collectors.toList());
//
//        Bson bson1 = com.mongodb.client.model.Filters.in("_id", objectIdList);
//        final List<Resource> resourceList = resourceRepository.findByCondition(bson1);
//        return resourceList.stream().map(Resource::getId).collect(Collectors.toSet());
    }

    /**
     * 生成会议VO对象
     *
     * @param meetingConcept 会议概念
     * @param peopleConcept  人员概念
     * @param nodeEntity     实例对象
     * @return {@link MeetingVO}
     */
    @NotNull
    private MeetingVO generateMeetingVO(KnowledgeConcept meetingConcept, KnowledgeConcept peopleConcept, NodeEntity nodeEntity, String type) {

        Map<String, Object> properties = nodeEntity.getProperties();
        // 转化实例
        MeetingVO meetingVO = new MeetingVO();
        meetingVO.setId(nodeEntity.getId());
        String docId = nodeEntity.getDocIds().isEmpty() ? null : nodeEntity.getDocIds().iterator().next();
        if (nodeEntity.getDocIds().isEmpty()) {
            StaticLog.error("nodeEntity节点name:[{}]的docId为空", nodeEntity.getEntityName());
        }
        meetingVO.setResourceId(docId);
        long startTime1 = System.nanoTime();
        Map<String, String> reousrceMap = obtainResourceMap(nodeEntity.getDocIds());
        long end = System.nanoTime();
        log.info("resourceMap:{},耗时【{}】", reousrceMap, end - startTime1);
        meetingVO.setOriginResource(reousrceMap);
        meetingVO.setName(nodeEntity.getEntityName());

        // 设置数据属性
        if (type == null || type.equals("全部")) {
            startTime1 = System.nanoTime();
            //其他处理逻辑
            final String resourceId = meetingVO.getResourceId();
            final Resource resource = resourceRepository.findById(resourceId);
            if (ObjectUtil.isNotEmpty(resource)) {
                final List<String> fenzu = getResourceMetadataValue(resource, "分组");
                if (ObjectUtil.isNotEmpty(fenzu)) {
                    meetingVO.setType(fenzu.get(0));
                }
            }
            end = System.nanoTime();
            StaticLog.info("查询节点【{}】的分组耗时【{}】", nodeEntity.getEntityName(), end - startTime1);
        } else {
            meetingVO.setType(type);
        }
        meetingVO.setTime(Convert.toStr(properties.get(getMeetingIdentifier(ModelPropertyConst.PROPERTY_HUIYI_SHIJIAN))));
        meetingVO.setMeetingNumber(Convert.toStr(properties.get(getMeetingIdentifier(ModelPropertyConst.PROPERTY_HUIYI_QISHU))));
//        // 设置对象属性
//        if (ObjectUtil.isNotNull(docId)){
//            List<NodeEntity> entityList = getMeetingMember(meetingConcept, peopleConcept, nodeEntity, docId);
//            meetingVO.setParticipant(entityList.stream().map(NodeEntity::getEntityName).sorted(new MeetingMinutesComparator(sortConfigRepository)).distinct().collect(Collectors.toList()));
//        }
        // 设置对象属性，不限制docId（为啥要限制docId？）
        startTime1 = System.nanoTime();
        List<NodeEntity> entityList = getMeetingMember(meetingConcept, peopleConcept, nodeEntity);
        meetingVO.setParticipant(entityList.stream().map(NodeEntity::getEntityName).sorted(new MeetingMinutesComparator(sortConfigRepository)).distinct().collect(Collectors.toList()));
        end = System.nanoTime();
        StaticLog.info("查询参会人员耗时【{}】", end - startTime1);
        return meetingVO;
    }

    public List<String> getResourceMetadataValue(Resource resource, String metadataName) {
        //获取公文分类，用于区分查询内容
        List<MetadataVO> divisiveMetadataVO = resource.getMetadata().stream().filter(metadataVO -> metadataVO.getName().equals(metadataName)).collect(Collectors.toList());
        List<String> metadataValue = new ArrayList<>();
        divisiveMetadataVO.forEach(metadataVO -> {
            if (ObjectUtil.isNotEmpty(metadataVO.getValue()))
                metadataValue.addAll(metadataVO.getValue());
        });
        return metadataValue;
    }

    /**
     * 知识查询  会议参与人查询
     *
     * @param meetingKnowledgeQueryDTO 查询条件
     * @return {@link MeetingVO}
     */
    @Override
    public MyPage<AttendeesVO> knowledgeQueryAttendees(MeetingKnowledgeQueryDTO meetingKnowledgeQueryDTO) {
        Integer page = meetingKnowledgeQueryDTO.getPage();
        Integer size = meetingKnowledgeQueryDTO.getSize();
        page = ObjectUtil.isEmpty(page) ? 1 : page;
        size = ObjectUtil.isEmpty(size) ? 10 : size;
        KnowledgeConcept meetingConcept = getKnowledgeConcept(ModelConceptConst.CONCEPT_HUIYI);
        KnowledgeConcept peopleConcept = getKnowledgeConcept(ModelConceptConst.CONCEPT_RENYUAN);
        long start = System.nanoTime();
        // 直接根据人名查询
        String cypher = "MATCH (n:ENTITY) where n.entityName  CONTAINS $entityName and n.conceptId=$conceptId return n order by size(n.entityName) ASC";
        List<NodeEntity> entityNameList = nodeEntityRepository.findEntityByCypher(cypher, Map.of("conceptId", peopleConcept.getId(), "entityName", meetingKnowledgeQueryDTO.getValue()));
        List<NodeEntity> collect = entityNameList
                .stream()
                .skip((long) (page == 0 ? page : page - 1) * size)
                .limit(size)
                .collect(Collectors.toList());
        long end = System.nanoTime();
        log.info("根据人名查询所有人员节点耗时【{}】", (end - start) / 1000000000.0000);
        List<AttendeesVO> result = collect.stream()
                .map(nodeEntity -> getPeopleWithMeeting(meetingConcept, peopleConcept, nodeEntity))
//                .filter(attendeesVO -> attendeesVO.getTotal() > 0)
                .collect(Collectors.toList());
        // 转换结果
        return new MyPage<>(page, size, entityNameList.size(), result);
    }

//    /**
//     * 知识查询  会议参与人查询
//     *
//     * @param meetingKnowledgeQueryDTO 查询条件
//     * @return {@link MeetingVO}
//     */
//    public MyPage<AttendeesVO> knowledgeQueryAttendeesPlus(MeetingKnowledgeQueryDTO meetingKnowledgeQueryDTO) {
//        Integer page = meetingKnowledgeQueryDTO.getPage();
//        Integer size = meetingKnowledgeQueryDTO.getSize();
//        page = ObjectUtil.isEmpty(page) ? 1 : page;
//        size = ObjectUtil.isEmpty(size) ? 10 : size;
//        KnowledgeConcept meetingConcept = getKnowledgeConcept(ModelConceptConst.CONCEPT_HUIYI);
//        KnowledgeConcept peopleConcept = getKnowledgeConcept(ModelConceptConst.CONCEPT_RENYUAN);
//        long start = System.nanoTime();
//        // 直接根据人名查询
//        String cypher = "MATCH (n:ENTITY) where n.entityName  CONTAINS $entityName and n.conceptId=$conceptId return n order by size(n.entityName) ASC";
//        List<NodeEntity> entityNameList = nodeEntityRepository.findEntityByCypher(cypher, Map.of("conceptId", peopleConcept.getId(), "entityName", meetingKnowledgeQueryDTO.getValue()));
//        List<NodeEntity> collect = entityNameList
//                .stream()
//                .skip((long) (page == 0 ? page : page - 1) * size)
//                .limit(size)
//                .collect(Collectors.toList());
//        long end = System.nanoTime();
//        log.info("根据人名查询所有人员节点耗时【{}】", (end-start)/1000000000.0000);
//        // 转换结果
//        List<AttendeesVO> result = collect.stream()
//                .map(nodeEntity -> getPeopleWithMeetingPlus(meetingConcept, peopleConcept, nodeEntity.getEntityName()))
////                .filter(attendeesVO -> attendeesVO.getTotal() > 0)
//                .collect(Collectors.toList());
//
//        return new MyPage<>(page, size, entityNameList.size(), result);
//    }

    /**
     * 获取人员对应的会议
     *
     * @param meetingConcept 会议概念
     * @param peopleConcept  人员概念
     * @return {@link AttendeesVO}
     */
    @NotNull
    private AttendeesMeetingVO getPeopleWithMeetingPlus(KnowledgeConcept meetingConcept, KnowledgeConcept peopleConcept,  String name, Integer page ,Integer size) {
        AttendeesMeetingVO attendeesMeetingVO = new AttendeesMeetingVO();
        List<AttendeesInfoVO> attendeesInfoVOS = new ArrayList<>();
        // 人员 -> 会议
        String cypher2 = "MATCH (s:ENTITY)-[r:RELATION]->(e:ENTITY) where s.conceptId=$startConceptId and s.entityName=$startEntityName and r.predicateName = '参会' and e.conceptId=$endConceptId return s,r,e";
        long start = System.nanoTime();
        Set<NodeRelation> nodeRelationList = nodeRelationRepository.findRelationByCypherPlus(cypher2, Map.of("startConceptId", peopleConcept.getId(), "startEntityName", name, "endConceptId", meetingConcept.getId()));
        long endTime = System.nanoTime();
        log.info("查询关系耗时【{}】", (endTime - start) / 1000000000.0000);
        start = System.nanoTime();
        Set<NodeRelation> pageResult = nodeRelationList
                .stream()
                .skip((long) (page == 0 ? page : page - 1) * size)
                .limit(size)
                .collect(Collectors.toSet());
        AttendeesInfoVO attendeesInfoVO = new AttendeesInfoVO();
        List<MeetingVO> meetingVOS = new ArrayList<>();
        if (pageResult.size() != 0) {
            for (NodeRelation nodeRelation : pageResult) {
                attendeesInfoVO.setType(nodeRelation.getPredicateName());
                NodeEntity end = nodeRelation.getEnd();
                meetingVOS.add(generateMeetingVO(meetingConcept, peopleConcept, end, null));
            }
            attendeesInfoVO.setMeeting(meetingVOS);
            attendeesInfoVO.setCount((long) nodeRelationList.size());
            attendeesInfoVOS.add(attendeesInfoVO);

        } else {
            attendeesInfoVO.setType("参会");
            attendeesInfoVO.setMeeting(new ArrayList<>());
            attendeesInfoVO.setCount(0L);
            attendeesInfoVOS.add(attendeesInfoVO);
        }

        endTime = System.nanoTime();
        log.info("查询人员所参加的会议耗时【{}】",(endTime - start) / 1000000000.0000);
        attendeesMeetingVO.setAttendeesInfoList(attendeesInfoVOS);

        return attendeesMeetingVO;
    }

    /**
     * 获取人员对应的会议
     *
     * @param meetingConcept 会议概念
     * @param peopleConcept  人员概念
     * @return {@link AttendeesVO}
     */
    @NotNull
    public AttendeesVO getPeopleWithMeeting(KnowledgeConcept meetingConcept, KnowledgeConcept peopleConcept, NodeEntity nodeEntity) {
        long meetingSize = 0;
        AttendeesVO attendeesVO = new AttendeesVO();
        List<AttendeesInfoVO> attendeesInfoVOS = new ArrayList<>();
        // 人员 -> 会议
        String cypher2 = "MATCH (s:ENTITY)-[r:RELATION]->(e:ENTITY) where s.conceptId=$startConceptId and s.entityName=$startEntityName and r.predicateName = '参会' and e.conceptId=$endConceptId return s,r,e";
        long start = System.nanoTime();
        Set<NodeRelation> nodeRelationList = nodeRelationRepository.findRelationByCypherPlus(cypher2, Map.of("startConceptId", peopleConcept.getId(), "startEntityName", nodeEntity.getEntityName(), "endConceptId", meetingConcept.getId()));
        long endTime = System.nanoTime();
        log.info("查询关系耗时【{}】", (endTime - start) / 1000000000.0000);
        // 只有一个map都是参会
        Map<String, List<NodeRelation>> type = nodeRelationList.stream().collect(Collectors.groupingBy(NodeRelation::getPredicateName));
        start = System.nanoTime();
        if (ObjectUtil.isNotEmpty(type)) {
            for (Map.Entry<String, List<NodeRelation>> entry : type.entrySet()) {
                if (!entry.getKey().equals("参会")) continue;
                AttendeesInfoVO attendeesInfoVO = new AttendeesInfoVO();
                attendeesInfoVO.setType(entry.getKey());
                List<NodeRelation> value = entry.getValue();
                List<MeetingVO> meetingVOS = new ArrayList<>();
                for (NodeRelation nodeRelation : value) {
                    NodeEntity end = nodeRelation.getEnd();
                    if (ObjectUtil.isNotEmpty(end)) {
                        meetingSize++;
                    }

//                    meetingVOS.add(generateMeetingVO(meetingConcept, peopleConcept, end, null));
                }
                attendeesInfoVO.setMeeting(meetingVOS);
                attendeesInfoVO.setCount(meetingSize);
                attendeesInfoVOS.add(attendeesInfoVO);
            }
        } else {
            AttendeesInfoVO attendeesInfoVO = new AttendeesInfoVO();
            attendeesInfoVO.setType("参会");
            attendeesInfoVO.setMeeting(new ArrayList<>());
            attendeesInfoVO.setCount(0L);
            attendeesInfoVOS.add(attendeesInfoVO);
        }
        endTime = System.nanoTime();
        log.info("查询人员所参加的会议耗时【{}】", (endTime - start) / 1000000000.0000);
        attendeesVO.setId(nodeEntity.getId());
        attendeesVO.setAttendees(nodeEntity.getEntityName());
        attendeesVO.setAttendeesInfoList(attendeesInfoVOS);
        long total = 0;
        for (AttendeesInfoVO attendeesInfoVO : attendeesInfoVOS) {
            Long count = attendeesInfoVO.getCount();
            total += count;
        }
        attendeesVO.setTotal(total);
        return attendeesVO;

    }


    /**
     * 获取指定name的概念对象
     *
     * @param name 概念名称
     * @return {@link KnowledgeConcept}
     */
    @NotNull
    private KnowledgeConcept getKnowledgeConcept(String name) {
        R<KnowledgeConcept> meetingConceptR = knowledgeConceptFeign.findByConceptName(name);
        KnowledgeConcept meetingConcept = meetingConceptR.getData();
        if (!meetingConceptR.isSuccess() || meetingConcept == null) {
            throw new ServiceException(StrUtil.format("概念库：{}概念不存在！", name));
        }
        return meetingConcept;
    }


    /**
     * 获取会议类型的 Identifier 标识
     *
     * @return Identifier
     */
    @Nullable
    private String getMeetingIdentifier(String name) {
        String identifier = null;
        // 查询属性库，获取“会议类型”
        List<KnowledgeProperty> knowledgePropertyList = knowledgePropertyRepository.findByCondition(com.mongodb.client.model.Filters.eq("name", name));
        if (ObjectUtil.isNotEmpty(knowledgePropertyList)) {
            KnowledgeProperty knowledgeProperty = knowledgePropertyList.get(0);
            // 获取属性的唯一标识
            identifier = "prop_" + knowledgeProperty.getIdentifier();
        }
        return identifier;
    }

    /**
     * 知识查询  会议议题查询
     *
     * @param meetingKnowledgeQueryDTO 查询条件
     * @return {@link MeetingVO}
     */
    @Override
    public MyPage<IssuesVO> knowledgeQueryIssues(MeetingKnowledgeQueryDTO meetingKnowledgeQueryDTO) {
        Integer page = meetingKnowledgeQueryDTO.getPage();
        Integer size = meetingKnowledgeQueryDTO.getSize();
        page = ObjectUtil.isEmpty(page) ? 1 : page;
        size = ObjectUtil.isEmpty(size) ? 10 : size;
        Set<String> ids = new HashSet<String>();
        if (!meetingKnowledgeQueryDTO.getType().equals("全部")) {
            HashMap<String, List<String>> params = new HashMap<>();
            params.put("公文分类", List.of("会议纪要"));
            params.put("分组", List.of(meetingKnowledgeQueryDTO.getType()));
            ids = getMeatDate(params);
        }

        // 获取会议和人员的概念对象
        KnowledgeConcept meetingConcept = getKnowledgeConcept(ModelConceptConst.CONCEPT_HUIYI);
        log.info("查询出会议概念的id " + meetingConcept.getId());
        KnowledgeConcept meetingIssuesConcept = getKnowledgeConcept(ModelConceptConst.CONCEPT_HUIYI_YITI);
        log.info("查询出会议议题的id" + meetingIssuesConcept.getId());
        KnowledgeConcept peopleConcept = getKnowledgeConcept(ModelConceptConst.CONCEPT_RENYUAN);
        log.info("查询出人员概念的id" + peopleConcept.getId());
//        String timeIdentifier = getMeetingIdentifier(ModelPropertyConst.PROPERTY_HUIYI_SHIJIAN);//  会议时间 标识符
        // 传出会议议题
        long startTime = System.nanoTime();
        MyPage<NodeEntity> meetingIssues = nodeEntityRepository.findPageSort(meetingIssuesConcept.getId(), meetingKnowledgeQueryDTO.getValue(), page, size, ids, meetingConcept.getId()); // 模糊查询出会议纪要中、分组为所选分组且会议议题为value的议题
        long endTime = System.nanoTime();
        log.info("查询会议议题及关系耗时【{}】s", (endTime - startTime) / 1000000000.000);
        List<NodeEntity> content = meetingIssues.getContent();
//        log.info("meetingIssues.getContent().size:[{}]--meetingIssues.getTotalElements().size:[{}]", content.size(), meetingIssues.getTotalElements());
        List<IssuesVO> result = new ArrayList<>();
        content.forEach(nodeEntity -> {
            try {
                log.info("会议议题节点的内容" + nodeEntity.toString());
            } catch (Exception e) {
                e.printStackTrace();
                System.out.println("NodeEntityToString失败");
            }
            Set<NodeRelation> fromEntity = nodeEntity.getFromEntity();
            if (ObjectUtil.isNotEmpty(fromEntity)) {

                for (NodeRelation relation : fromEntity) {
                    // 获取指向该 会议议题的 主语
                    NodeEntity start = relation.getStart();
                    //  log.info("关系的发出节点是" + start.getEntityName());
                    // 如果是会议
                    if (meetingConcept.getId().equals(start.getConceptId())) {
                        IssuesVO issuesVO = getIssuesVO(meetingConcept, peopleConcept, nodeEntity, start, meetingKnowledgeQueryDTO);
                        result.add(issuesVO);
                        //log.info("会议议题及会议正确查询");
                        break;
                    } else {
                        // 进入此分支说明该会议议题没有指向他的关系，所以该会议议题不能显示，否则会议名称、参会人员将为空、会议跳转将报错
                        log.info("该会议议题不能显示，因为指向它的节点没有会议，会议名称、参会人员将为空、会议跳转将报错");
                        meetingIssues.setTotalElements(meetingIssues.getTotalElements() - 1);
                    }
                }
            } else {
                // 进入此分支说明该会议议题没有指向他的关系，所以该会议议题不能显示，否则会议名称、参会人员将为空、会议跳转将报错、
                meetingIssues.setTotalElements(meetingIssues.getTotalElements() - 1);
                log.info("会议议题： " + nodeEntity.getEntityName() + "没有指向它的关系");

            }
        });
        List<IssuesVO> collect = result.stream()
//                .skip((long) (page == 0 ? page : page - 1) * size)
                .limit(size)
                .sorted(Comparator.comparing(issuesVO -> issuesVO.getIssuesName().length()))
                .collect(Collectors.toList());
        log.info("根据查询条件meetingIssues 数量:[{}],进入forEach后 result 数量:[{}],排序分页后 collect 数量:[{}],最后总数getTotalElements 数量:[{}]", content.size(), result.size(), collect.size(), meetingIssues.getTotalElements());
        return new MyPage<>(page, size, meetingIssues.getTotalElements(), collect);
    }

    /**
     * 生成会议议题VO
     *
     * @param meetingConcept           会议概念
     * @param peopleConcept            人员概念
     * @param nodeEntity               会议议题
     * @param start                    会议节点
     * @param meetingKnowledgeQueryDTO 查询条件  包含分类、查询值、分页参数
     */
    @NotNull
    private IssuesVO getIssuesVO(KnowledgeConcept meetingConcept, KnowledgeConcept peopleConcept, NodeEntity nodeEntity, NodeEntity start, MeetingKnowledgeQueryDTO meetingKnowledgeQueryDTO) {
        IssuesVO issuesVO = new IssuesVO();
        issuesVO.setId(nodeEntity.getId());
        String value = nodeEntity.getEntityName();
        if (StrUtil.isNotBlank(meetingKnowledgeQueryDTO.getValue())) {
            value = nodeEntity.getEntityName().replace(meetingKnowledgeQueryDTO.getValue(), "<span style='color:red;'>" + meetingKnowledgeQueryDTO.getValue() + "</span>");
        }
        issuesVO.setIssuesName(value);
        issuesVO.setMeetingId(start.getId());
        issuesVO.setMeetingName(start.getEntityName());
//        issuesVO.setMeetingTime(Convert.toStr(properties.get(timeIdentifier)));

        Set<String> docIds = nodeEntity.getDocIds();
        // 避免报错
        String docId = docIds.iterator().hasNext() ? docIds.iterator().next() : null;
        issuesVO.setResourceId(docId);
        Map<String, String> resourceMap = obtainResourceMap(nodeEntity.getDocIds());
        issuesVO.setOriginResource(resourceMap);
//        // 设置会议参与人
//        List<NodeEntity> entityList = getMeetingMember(meetingConcept, peopleConcept, start, docId);

        // 设置会议参与人，不限制docId（为啥要限制docId？）
        List<NodeEntity> entityList = getMeetingMember(meetingConcept, peopleConcept, start);
        List<String> peoples = entityList.stream().map(NodeEntity::getEntityName).sorted(new MeetingMinutesComparator(sortConfigRepository)).distinct().collect(Collectors.toList());
        issuesVO.setParticipant(peoples.isEmpty() ? List.of("暂无人员") : peoples);
        return issuesVO;
    }

    /**
     * 获取指定会议实例的参与人
     *
     * @param meetingConcept 会议概念
     * @param peopleConcept  人员概念
     * @param nodeEntity     会议实例
     * @return {@link NodeEntity}
     */
    private List<NodeEntity> getMeetingMember(KnowledgeConcept meetingConcept, KnowledgeConcept peopleConcept, NodeEntity nodeEntity, String docId) {
        String cypher = "MATCH (s:ENTITY)-[r:RELATION]->(e:ENTITY) where s.conceptId=$startConceptId and s.id=$startId and e.conceptId=$endConceptId and $docId IN e.docIds  return e";
        return nodeEntityRepository.findEntityByCypher(cypher, Map.of("startConceptId", meetingConcept.getId(), "startId", nodeEntity.getId(), "endConceptId", peopleConcept.getId(), "docId", docId));
    }

    /**
     * 获取指定会议实例的参与人(不限制docId)
     *
     * @param meetingConcept 会议概念
     * @param peopleConcept  人员概念
     * @param nodeEntity     会议实例
     * @return {@link NodeEntity}
     */
    private List<NodeEntity> getMeetingMember(KnowledgeConcept meetingConcept, KnowledgeConcept peopleConcept, NodeEntity nodeEntity) {
        String cypher = "MATCH (s:ENTITY)-[r:RELATION]->(e:ENTITY) where s.conceptId=$startConceptId and s.id=$startId and e.conceptId=$endConceptId return e";
        return nodeEntityRepository.findEntityByCypher(cypher, Map.of("startConceptId", meetingConcept.getId(), "startId", nodeEntity.getId(), "endConceptId", peopleConcept.getId()));
    }

    /**
     * 会议详情页
     *
     * @param meetingId 会议Id
     * @return {@link MeetingInfoVO}
     */
    public MeetingInfoVO meetingInfo(String meetingId) {
        MeetingInfoVO meetingInfoVO = new MeetingInfoVO();
        // 查询当前会议实例对象
        NodeEntity meetingEntity = nodeEntityRepository.findById(meetingId);
        if (meetingEntity == null) {
            throw new ServiceException("当前会议不存在!");
        }

        // 会议材料
        Set<String> docIds = meetingEntity.getDocIds();
        if (ObjectUtil.isNotEmpty(docIds)) {
            String resourceId = docIds.iterator().next();
            meetingInfoVO.setResourceId(resourceId);
            R<List<ResourceAnnex>> resourceAnnexListR = resourceAnnexFeign.listByResourceId(resourceId);
            List<ResourceAnnex> resourceAnnexList = resourceAnnexListR.getData();
            meetingInfoVO.setResourceAnnexList(resourceAnnexList);
            // 设置会议的类型和属性
            initMeetingInfo(meetingEntity, meetingInfoVO, resourceId);
        }
        // 会议议题
        KnowledgeConcept meetingIssuesConcept = getKnowledgeConcept(ModelConceptConst.CONCEPT_HUIYI_YITI);
        String cypher = "MATCH (s:ENTITY)-[r:RELATION]->(e:ENTITY) where s.conceptId=$startConceptId and s.id=$startId  and e.conceptId=$endConceptId return e order by e.createTime desc skip $page limit $size ";
        List<NodeEntity> nodeEntityList = nodeEntityRepository
                .findEntityByCypher(cypher,
                        Map.of("startConceptId", meetingEntity.getConceptId(),
                                "startId", meetingEntity.getId(),
                                "endConceptId", meetingIssuesConcept.getId(),
                                "page", 1,
                                "size", 10));
        List<IssuesVO> collect = nodeEntityList.stream().map(nodeEntity -> {
            IssuesVO issuesVO = new IssuesVO();
            issuesVO.setId(nodeEntity.getId());
            issuesVO.setIssuesName(nodeEntity.getEntityName());
            issuesVO.setMeetingId(meetingEntity.getId());
            return issuesVO;
        }).collect(Collectors.toList());
        meetingInfoVO.setIssues(collect);

        return meetingInfoVO;
    }

    /**
     * 设置会议类型和属性
     *
     * @param meetingEntity 会议实例
     * @param meetingInfoVO 会议详情VO
     * @param resourceId
     */
    private void initMeetingInfo(NodeEntity meetingEntity, MeetingInfoVO meetingInfoVO, String resourceId) {
        String meetingTypeIdentifier = getMeetingIdentifier(ModelPropertyConst.PROPERTY_HUIYI_LEIXING);
        String meetingNumIdentifier = getMeetingIdentifier(ModelPropertyConst.PROPERTY_HUIYI_QISHU);
        String meetingTimeIdentifier = getMeetingIdentifier(ModelPropertyConst.PROPERTY_HUIYI_SHIJIAN);
        String meetingPlaceIdentifier = getMeetingIdentifier(ModelPropertyConst.PROPERTY_HUIYI_DIDIAN);
        KnowledgeConcept peopleConcept = getKnowledgeConcept(ModelConceptConst.CONCEPT_RENYUAN);
        // 设置会议Id
        meetingInfoVO.setId(meetingEntity.getId());
        // 获取会议的属性
        Map<String, Object> properties = meetingEntity.getProperties();
        if (ObjectUtil.isNotEmpty(properties)) {
            // ①会议类型
            meetingInfoVO.setMeetingType(Convert.toStr(properties.get(meetingTypeIdentifier)));
        }
        // 获取会议的所属公文
//        Set<String> docIds = meetingEntity.getDocIds();
//        String resourceId = docIds.iterator().next();
//        R<Resource> resourceR = resourceFeign.info(resourceId);
//        Resource resource = resourceR.getData();
//        if (!resourceR.isSuccess() || resource == null) {
//            throw new ServiceException("当前公文不存在!");
//        }
//        // 创建属性集合
        ArrayList<PropertyVO> propertyVOList = new ArrayList<>();
//        List<MetadataVO> metadata = resource.getMetadata();
//        for (MetadataVO metadataItem : metadata) {
        // ②会议摘要
//            if ("摘要".equals(metadataItem.getName())) {
//                PropertyVO propertyVO = new PropertyVO();
//                propertyVO.setName("会议摘要");
//                propertyVO.setValue(List.of(Opt.ofNullable(metadataItem.getValue() == null ? null : metadataItem.getValue().get(0)).orElse("暂无摘要")));
//                propertyVOList.add(propertyVO);
//            }
//        }
        PropertyVO propertyVO1 = new PropertyVO();
        if (properties.get(meetingNumIdentifier) != null) {
            propertyVO1.setName(ModelPropertyConst.PROPERTY_HUIYI_QISHU);
            propertyVO1.setValue(List.of(Convert.toStr(Opt.ofNullable(properties.get(meetingNumIdentifier)).orElse("暂无期数"))));
            propertyVOList.add(propertyVO1);
        }
        if (properties.get(meetingTimeIdentifier) != null) {
            PropertyVO propertyVO2 = new PropertyVO();
            propertyVO2.setName(ModelPropertyConst.PROPERTY_HUIYI_SHIJIAN);
            propertyVO2.setValue(List.of(Convert.toStr(Opt.ofNullable(properties.get(meetingTimeIdentifier)).orElse("暂无时间"))));
            propertyVOList.add(propertyVO2);
        }
        if (properties.get(meetingPlaceIdentifier) != null) {
            PropertyVO propertyVO3 = new PropertyVO();
            propertyVO3.setName(ModelPropertyConst.PROPERTY_HUIYI_DIDIAN);
            propertyVO3.setValue(List.of(Convert.toStr(Opt.ofNullable(properties.get(meetingPlaceIdentifier)).orElse("暂无地点"))));
            propertyVOList.add(propertyVO3);
        }

        // 设置主持人
//        setObjectProperty(peopleConcept, meetingEntity, propertyVOList, ModelRelationConst.RELATION_ZHUCHI_RENYUAN);
        // 设置记录人
//        setObjectProperty(peopleConcept, meetingEntity, propertyVOList, ModelRelationConst.RELATION_JILU_RENYUAN);
        // 设置参会人员（不限制docId，为啥要限制docId）
        setObjectProperty(peopleConcept, meetingEntity, propertyVOList, ModelRelationConst.RELATION_CANHUI_RENYUAN);
        // 设置列席人员
//        setObjectProperty(peopleConcept, meetingEntity, propertyVOList, ModelRelationConst.RELATION_LIEXI_RENYUAN);
        // 设置属性
        meetingInfoVO.setProperties(propertyVOList);
    }

    /**
     * 设置对象属性
     *
     * @param peopleConcept  人员概念对象
     * @param meetingEntity  会议实例
     * @param propertyVOList 属性集合
     * @param name           属性名称
     */
    private void setObjectProperty(KnowledgeConcept peopleConcept, NodeEntity meetingEntity, ArrayList<PropertyVO> propertyVOList, String name, String resourceId) {
        List<KnowledgeRelation> relationList2 = knowledgeRelationRepository.findByCondition(com.mongodb.client.model.Filters.eq("name", name));
        if (ObjectUtil.isNotEmpty(relationList2)) {
            KnowledgeRelation relation = relationList2.get(0);
            String cypher = "MATCH (s:ENTITY)-[r:RELATION]->(e:ENTITY) where s.conceptId=$startConceptId and s.id=$startId and r.predicateId=$predicatedId  and e.conceptId=$endConceptId and $docId IN e.docIds return e";
            List<NodeEntity> nodeEntityList = nodeEntityRepository.findEntityByCypher(cypher, Map.of("startConceptId", meetingEntity.getConceptId(), "startId", meetingEntity.getId(), "predicatedId", relation.getId(), "endConceptId", peopleConcept.getId(), "docId", resourceId));
            PropertyVO propertyVO = new PropertyVO();
            propertyVO.setName(name);
            propertyVO.setValue(nodeEntityList.stream().map(NodeEntity::getEntityName).sorted(new MeetingMinutesComparator(sortConfigRepository)).distinct().collect(Collectors.toList()));
            propertyVOList.add(propertyVO);
        }
    }

    /**
     * 设置对象属性（不限制docId）
     *
     * @param peopleConcept  人员概念对象
     * @param meetingEntity  会议实例
     * @param propertyVOList 属性集合
     * @param name           属性名称
     */
    private void setObjectProperty(KnowledgeConcept peopleConcept, NodeEntity meetingEntity, ArrayList<PropertyVO> propertyVOList, String name) {
        List<KnowledgeRelation> relationList2 = knowledgeRelationRepository.findByCondition(com.mongodb.client.model.Filters.eq("name", name));
        if (ObjectUtil.isNotEmpty(relationList2)) {
            KnowledgeRelation relation = relationList2.get(0);
            String cypher = "MATCH (s:ENTITY)-[r:RELATION]->(e:ENTITY) where s.conceptId=$startConceptId and s.id=$startId and r.predicateId=$predicatedId  and e.conceptId=$endConceptId  return e";
            List<NodeEntity> nodeEntityList = nodeEntityRepository.findEntityByCypher(cypher, Map.of("startConceptId", meetingEntity.getConceptId(), "startId", meetingEntity.getId(), "predicatedId", relation.getId(), "endConceptId", peopleConcept.getId()));
            PropertyVO propertyVO = new PropertyVO();
            propertyVO.setName(name);
            propertyVO.setValue(nodeEntityList.stream().map(NodeEntity::getEntityName).sorted(new MeetingMinutesComparator(sortConfigRepository)).distinct().collect(Collectors.toList()));
            propertyVOList.add(propertyVO);
        }
    }


    /**
     * 分页查询会议议题
     *
     * @param page      当前页码
     * @param size      每页条数
     * @param meetingId 会议Id
     * @return {@link IssuesVO}
     */
    @Override
    public MyPage<IssuesVO> issuesPage(Integer page, Integer size, String meetingId) {
        // 查询当前会议实例对象
        NodeEntity meetingEntity = nodeEntityRepository.findById(meetingId);
        // 会议议题 (审议)
        List<KnowledgeRelation> relationList = knowledgeRelationRepository.findByCondition(com.mongodb.client.model.Filters.eq("name", ModelRelationConst.RELATION_TIJI_HUIYI_YITI));
        KnowledgeConcept meetingIssuesConcept = getKnowledgeConcept(ModelConceptConst.CONCEPT_HUIYI_YITI);
        if (ObjectUtil.isNotEmpty(relationList)) {
            KnowledgeRelation relation = relationList.get(0);
            String cypher = "MATCH (s:ENTITY)-[r:RELATION]->(e:ENTITY) where s.conceptId=$startConceptId and s.id=$startId and r.predicateId=$predicatedId  and e.conceptId=$endConceptId return DISTINCT e order by e.createTime desc";
            List<NodeEntity> nodeEntityList = nodeEntityRepository
                    .findEntityByCypher(cypher,
                            Map.of("startConceptId", meetingEntity.getConceptId(),  // 会议概念id
                                    "startId", meetingEntity.getId(), //
                                    "predicatedId", relation.getId(),
                                    "endConceptId", meetingIssuesConcept.getId()));
            log.info("结果nodeEntityList数量为{}，startConceptId: {},startId: {},predicatedId: {},endConceptId: {}", nodeEntityList.size(), meetingEntity.getConceptId(), meetingEntity.getId(), relation.getId(), meetingIssuesConcept.getId());
            List<IssuesVO> result = nodeEntityList.stream()
                    .map(nodeEntity -> {
                        IssuesVO issuesVO = new IssuesVO();
                        issuesVO.setId(nodeEntity.getId());
                        issuesVO.setIssuesName(nodeEntity.getEntityName());
                        issuesVO.setMeetingId(meetingEntity.getId());
                        return issuesVO;
                    })
                    .skip((long) (page == 0 ? page : page - 1) * size)
                    .limit(size)
                    .collect(Collectors.toList());
            System.out.println("结果：" + result);
            if (ObjectUtil.isEmpty(result)) {
                System.out.println("result为空判断逻辑");
                final Set<String> docIds = meetingEntity.getDocIds();
                HashMap<String, String> strings = new HashMap<>();
                docIds.stream().forEach(docId -> {

                    final JSONArray objects = resourceMessageService.involveRecommend(docId);
                    final Map<String, String> strings1 = extractUniqueMeetingIssues(objects);
                    strings.putAll(strings1);
                });
                ArrayList<IssuesVO> issuesVOS = new ArrayList<>();
                for (Map.Entry<String, String> stringStringEntry : strings.entrySet()) {
                    IssuesVO issuesVO = new IssuesVO();
                    issuesVO.setIssuesName(stringStringEntry.getValue());
                    issuesVO.setId(stringStringEntry.getKey());
                    issuesVOS.add(issuesVO);
                }
                final List<IssuesVO> collect = issuesVOS.stream().skip((long) (page == 0 ? page : page - 1) * size)
                        .limit(size)
                        .collect(Collectors.toList());
                return new MyPage<>(page, size, strings.size(), collect);
            }
            return new MyPage<>(page, size, nodeEntityList.size(), result);
        }

        return new MyPage<>();
    }

    public Map<String, String> extractUniqueMeetingIssues(JSONArray jsonArray) {
        HashMap<String, String> result = new HashMap<>();

        for (int i = 0; i < jsonArray.size(); i++) {
            JSONObject item = jsonArray.getJSONObject(i);
            if (ModelConceptConst.CONCEPT_HUIYI_YITI.equals(item.getStr("key"))) {
                JSONArray issues = item.getJSONArray("value");
                for (int j = 0; j < issues.size(); j++) {
                    JSONObject issue = issues.getJSONObject(j);

                    result.put(issue.getStr("id"), issue.getStr("entityName"));
                }
            }
        }
        return result;
    }

    /**
     * 关联会议分页查询
     *
     * @return {@link MeetingVO}
     */
    @Override
    public MyPage<MeetingVO> relevancyMeetingPage(Integer page, Integer size, String meetingId) {
        // 获取会议实例
        List<MeetingVO> meetingVOS = getMeetingVOS(meetingId);
        List<MeetingVO> collect = meetingVOS.stream().skip((long) (page == 0 ? page : page - 1) * size).limit(size).collect(Collectors.toList());
        MyPage<MeetingVO> result = new MyPage<>();
        result.setPage(page);
        result.setSize(size);
        result.setTotalElements(meetingVOS.size());
        result.setContent(collect);
        return result;
    }

    /**
     * 获取当前会议相关联的其它会议列表
     *
     * @param meetingId 会议id
     * @return {@link MeetingVO}
     */
    @NotNull
    private List<MeetingVO> getMeetingVOS(String meetingId) {
        NodeEntity meetEntity = nodeEntityRepository.findById(meetingId);
        KnowledgeConcept peopleConcept = getKnowledgeConcept(ModelConceptConst.CONCEPT_RENYUAN);
        // 获取这个会议下的所有人员
        String cypher = "MATCH (s:ENTITY)-[r:RELATION]->(e:ENTITY) where s.id=$startId and e.conceptId=$endConceptId return e order by e.createTime desc";
        List<NodeEntity> entityList = nodeEntityRepository.findEntityByCypher(cypher, Map.of("startId", meetEntity.getId(), "endConceptId", peopleConcept.getId()));
        List<MeetingVO> meetingVOS = new ArrayList<>();
        for (NodeEntity nodeEntity : entityList) {
            // 获取人员相关的所有会议
            String cypher1 = "MATCH (s:ENTITY)-[r:RELATION]->(e:ENTITY) where s.entityName=$startEntityName and e.conceptId=$endConceptId return e order by e.createTime desc";
            List<NodeEntity> meetingList = nodeEntityRepository.findEntityByCypher(cypher1, Map.of("startEntityName", nodeEntity.getEntityName(), "endConceptId", meetEntity.getConceptId()));
            // 把会议实例转化为会议VO
            if (ObjectUtil.isNotEmpty(meetingList)) {
                List<MeetingVO> meetingVOList = meetingList.stream().map(meeting -> {
                    MeetingVO meetingVO = new MeetingVO();
                    meetingVO.setId(meeting.getId());
                    Set<String> docIds = meeting.getDocIds();
                    if (docIds.iterator().hasNext()) {
                        String resourceId = docIds.iterator().next();
                        meetingVO.setResourceId(resourceId);
                    }
                    meetingVO.setName(meeting.getEntityName());
                    Map<String, Object> properties = meeting.getProperties();
                    if (ObjectUtil.isNotEmpty(properties)) {
                        Object time = properties.get(getMeetingIdentifier(ModelPropertyConst.PROPERTY_HUIYI_SHIJIAN));
                        meetingVO.setTime(Convert.toStr(time));
                    }
                    return meetingVO;
                }).collect(Collectors.toList());
                meetingVOS.addAll(meetingVOList);
                meetingVOS = meetingVOS.stream().distinct().collect(Collectors.toList());
            }
        }
        meetingVOS.removeIf(meetingVO -> meetingVO.getId().equals(meetEntity.getId()));
        return meetingVOS;
    }

//    /**
//     * 关联议题分页查询 （慢接口）
//     *
//     * @param page      当前页码
//     * @param size      每页条数
//     * @param meetingId 会议Id
//     * @return {@link IssuesVO}
//     */
//    @Override
//    public MyPage<IssuesVO> relevancyIssuesPage(Integer page, Integer size, String meetingId) {
//        List<MeetingVO> meetingVOList = getMeetingVOS(meetingId);
//        // 获取所有关联会议的关联议题
//        KnowledgeConcept issuesConcept = getKnowledgeConcept(ModelConceptConst.CONCEPT_HUIYI_YITI);
//        // 获取所有关联会议的议题
//        List<IssuesVO> allIssuesList = new ArrayList<>();
//        for (MeetingVO meetingVO : meetingVOList) {
//            String cypher = "MATCH (s:ENTITY)-[r:RELATION]->(e:ENTITY) where s.id=$startId and e.conceptId=$endConceptId return e order by e.createTime desc";
//            List<NodeEntity> entityList = nodeEntityRepository.findEntityByCypher(cypher, Map.of("startId", meetingVO.getId(), "endConceptId", issuesConcept.getId()));
//            List<IssuesVO> collect = entityList.stream().map(nodeEntity -> {
//                IssuesVO issuesVO = new IssuesVO();
//                issuesVO.setId(nodeEntity.getId());
//                issuesVO.setMeetingId(meetingVO.getId());
//                issuesVO.setMeetingName(meetingVO.getName());
//                issuesVO.setIssuesName(nodeEntity.getEntityName());
//                issuesVO.setMeetingTime(meetingVO.getTime());
//                return issuesVO;
//            }).collect(Collectors.toList());
//            allIssuesList.addAll(collect);
//        }
//        if (ObjectUtil.isEmpty(allIssuesList)) {
//            final Set<String> docIds = meetingVOList.stream().map(MeetingVO::getResourceId).collect(Collectors.toSet());
//            final Set<String> strings = new HashSet<>();
//            docIds.stream().forEach(docId -> {
//
//                final JSONArray objects = resourceMessageService.involveRecommend(docId);
//                final Set<String> strings1 = extractUniqueMeetingIssues(objects);
//                strings.addAll(strings1);
//            });
//
//            final List<IssuesVO> collect = strings.stream().map(s -> {
//                        IssuesVO issuesVO = new IssuesVO();
//                        issuesVO.setIssuesName(s);
//                        return issuesVO;
//                    }).skip((long) (page == 0 ? page : page - 1) * size)
//                    .limit(size)
//                    .collect(Collectors.toList());
//            return new MyPage<>(page, size, collect.size(), collect);
//        }
//        List<IssuesVO> result = allIssuesList.stream().skip((long) (page == 0 ? page : page - 1) * size).limit(size).collect(Collectors.toList());
//
//        return new MyPage<>(page, size, allIssuesList.size(), result);
//    }


    /**
     * 关联议题分页查询 （重写逻辑）
     *
     * @param page      当前页码
     * @param size      每页条数
     * @param meetingId 会议Id
     * @return {@link IssuesVO}
     */
    @Override
    public MyPage<IssuesVO> relevancyIssuesPage2(Integer page, Integer size, String meetingId) {
        // 获取  当前会议==》人员==》其他会议==》会议议题
        String cypher = "MATCH (n:ENTITY)-[r:RELATION]->(p:ENTITY)-[rr:RELATION]->(nn:ENTITY)-[rrr:RELATION]->(nnn:ENTITY) where n.id=$currentNodeId and p.conceptId=$personId and nn.conceptId=$meetingId and nn.id<>$currentNodeId2 and nnn.conceptId=$issueId return DISTINCT nnn skip $skip limit $limit";

        // 获取所有关联会议的议题
        List<IssuesVO> allIssuesList = new ArrayList<>();

        // 概念id
        KnowledgeConcept personConcept = getKnowledgeConcept(ModelConceptConst.CONCEPT_RENYUAN);
        KnowledgeConcept meetingConcept = getKnowledgeConcept(ModelConceptConst.CONCEPT_HUIYI);
        KnowledgeConcept issuesConcept = getKnowledgeConcept(ModelConceptConst.CONCEPT_HUIYI_YITI);


        List<NodeEntity> entityList = nodeEntityRepository.findEntityByCypher(cypher, Map.of(
                "currentNodeId", meetingId,
                "personId", personConcept.getId(),
                "meetingId", meetingConcept.getId(),
                "currentNodeId2", meetingId,
                "issueId", issuesConcept.getId(),
                "skip", (long) (page == 0 ? page : page - 1) * size,
                "limit", size
        ));

        // 优化点（有时间看能否直接从上面cypher拿到会议node）
        entityList.forEach(nodeEntity -> {
            String cypherGetMeeting = "MATCH (n:ENTITY)-[r:RELATION]->(p:ENTITY) where p.id=$issueNodeId and n.conceptId=$meetingConceptId RETURN n LIMIT 1";
            nodeEntityRepository.findEntityByCypher(cypherGetMeeting, Map.of(
                            "issueNodeId", nodeEntity.getId(),
                            "meetingConceptId", meetingConcept.getId()
                    )).stream().findFirst()
                    .ifPresent(meetingNode -> {
                        IssuesVO issuesVO = new IssuesVO();
                        issuesVO.setId(nodeEntity.getId());
                        issuesVO.setMeetingId(meetingNode.getId());
                        issuesVO.setMeetingName(meetingNode.getEntityName());
                        issuesVO.setIssuesName(nodeEntity.getEntityName());

                        Map<String, Object> properties = meetingNode.getProperties();
                        if (ObjectUtil.isNotEmpty(properties)) {
                            Object time = properties.get(getMeetingIdentifier(ModelPropertyConst.PROPERTY_HUIYI_SHIJIAN));
                            issuesVO.setMeetingTime(Convert.toStr(time));
                        }

                        allIssuesList.add(issuesVO);
                    });
        });

        return new MyPage<>(page, size, allIssuesList.size(), allIssuesList);
    }


    /**
     * 热点议题
     *
     * @return {@link JSONObject}
     */
    @Override
    public List<JSONObject> hotIssues() {
        // 触发热点议题计算
        String user = request.getHeader("user");
        trigger.computeHotIssues(user);
        // 获取当前租户下的所有会议议题
        Map<Object, Object> hmget = redisUtil.hmget(HOT_ISSUES + userProviderFeign.getTenantId().getData());
        // 转化类型
        Map<String, Long> convertedMap = hmget.entrySet().stream()
                .collect(HashMap::new,
                        (m, entry) -> m.put(entry.getKey().toString(), Long.parseLong(entry.getValue().toString())),
                        HashMap::putAll);
        // 排序
        List<Map.Entry<String, Long>> entries = new ArrayList<>(convertedMap.entrySet());
        List<Map.Entry<String, Long>> sortedList = entries.stream()
                .sorted(Comparator.comparing(Map.Entry<String, Long>::getValue).reversed())
                .collect(Collectors.toList());
        // 统计
        ArrayList<JSONObject> resultList = new ArrayList<>();
        if (ObjectUtil.isNotEmpty(hmget)) {
            for (Map.Entry<String, Long> entry : sortedList) {
                String name = entry.getKey();
                if (ObjectUtil.isNotEmpty(name)) {
                    JSONObject object = new JSONObject();
                    object.putOpt("name", name);
                    object.putOpt("count", entry.getValue());
                    resultList.add(object);
                }
            }
        }
        return resultList;
    }

    public static final String graph = "会议图谱";

    /**
     * 会议图谱
     *
     * @return {@link EchartsVO}
     */
    @Override
    public EchartsVO graph() {
        JSONObject cacheResult = JSONUtil.parseObj(redisUtil.get(graph));
        if (ObjectUtil.isNotEmpty(cacheResult)) {
            return JSONUtil.toBean(cacheResult, EchartsVO.class);
        }
        EchartsVO echartsVO = new EchartsVO();
        Set<EchartsNode> nodeSet = new HashSet<>();
        Set<EchartsRelation> relationSet = new HashSet<>();
        // 获取会议的概念对象
        KnowledgeConcept meetingConcept = getKnowledgeConcept(ModelConceptConst.CONCEPT_HUIYI);
        List<NodeEntity> nodeEntityList = nodeEntityRepository.findByCondition(Map.of("conceptId", meetingConcept.getId()), 1).stream().limit(5).collect(Collectors.toList());
        HashMap<String, KnowledgeConcept> conceptMap = new HashMap<>();
        for (NodeEntity nodeEntity : nodeEntityList) {
            // 生成会议节点
            EchartsNode currentNode = new EchartsNode();
            currentNode.setId(nodeEntity.getId());
            currentNode.setConceptId(meetingConcept.getId());
            currentNode.setName(nodeEntity.getEntityName());
            currentNode.setType(meetingConcept.getName());
            nodeSet.add(currentNode);
            // 生成会议的下一级节点
            // 获取该实例指向的宾语
            Set<NodeRelation> toEntity = nodeEntity.getToEntity();
            // 如果存在宾语
            if (ObjectUtil.isNotEmpty(toEntity)) {
                for (NodeRelation nodeRelation : toEntity) {
                    // 创建实例
                    EchartsNode node = new EchartsNode();
                    NodeEntity end = nodeRelation.getEnd();
                    node.setId(end.getId());
                    node.setConceptId(end.getConceptId());
                    node.setName(end.getEntityName());
                    KnowledgeConcept knowledgeConcept = conceptMap.get(end.getConceptId());
                    if (knowledgeConcept == null) {
                        knowledgeConcept = knowledgeConceptRepository.findById(end.getConceptId());
                        conceptMap.put(end.getConceptId(), knowledgeConcept);
                    }
                    node.setType(knowledgeConcept.getName());
                    nodeSet.add(node);
                    // 生成关系
                    EchartsRelation relation = new EchartsRelation();
                    // 关系的Id
                    relation.setId(nodeRelation.getId());
                    // 关系的谓语Id
                    relation.setConceptId(nodeRelation.getPredicateId());
                    relation.setName(nodeRelation.getPredicateName());
                    relation.setSource(currentNode.getId());
                    relation.setTarget(node.getId());
                    relationSet.add(relation);
                }
            }
        }
        echartsVO.setNodes(nodeSet);
        echartsVO.setRelations(relationSet);
        redisUtil.set(graph, echartsVO);
        return echartsVO;
    }


    /**
     * 会议图谱(现有需求与原有逻辑冲突，先以缓存的方式提高速度)
     *
     * @return {@link EchartsVO}
     */
    @Override
    public void setGraph() {
        log.info("========更新会议图谱缓存：开始========");
        EchartsVO echartsVO = new EchartsVO();
        Set<EchartsNode> nodeSet = new HashSet<>();
        Set<EchartsRelation> relationSet = new HashSet<>();
        // 获取会议的概念对象
        KnowledgeConcept meetingConcept = getKnowledgeConcept(ModelConceptConst.CONCEPT_HUIYI);
        List<NodeEntity> nodeEntityList = nodeEntityRepository.findByCondition(Map.of("conceptId", meetingConcept.getId()), 1).stream().limit(5).collect(Collectors.toList());
        HashMap<String, KnowledgeConcept> conceptMap = new HashMap<>();
        for (NodeEntity nodeEntity : nodeEntityList) {
            // 生成会议节点
            EchartsNode currentNode = new EchartsNode();
            currentNode.setId(nodeEntity.getId());
            currentNode.setConceptId(meetingConcept.getId());
            currentNode.setName(nodeEntity.getEntityName());
            currentNode.setType(meetingConcept.getName());
            nodeSet.add(currentNode);
            // 生成会议的下一级节点
            // 获取该实例指向的宾语
            Set<NodeRelation> toEntity = nodeEntity.getToEntity();
            // 如果存在宾语
            if (ObjectUtil.isNotEmpty(toEntity)) {
                for (NodeRelation nodeRelation : toEntity) {
                    // 创建实例
                    EchartsNode node = new EchartsNode();
                    NodeEntity end = nodeRelation.getEnd();
                    node.setId(end.getId());
                    node.setConceptId(end.getConceptId());
                    node.setName(end.getEntityName());
                    KnowledgeConcept knowledgeConcept = conceptMap.get(end.getConceptId());
                    if (knowledgeConcept == null) {
                        knowledgeConcept = knowledgeConceptRepository.findById(end.getConceptId());
                        conceptMap.put(end.getConceptId(), knowledgeConcept);
                    }
                    node.setType(knowledgeConcept.getName());
                    nodeSet.add(node);
                    // 生成关系
                    EchartsRelation relation = new EchartsRelation();
                    // 关系的Id
                    relation.setId(nodeRelation.getId());
                    // 关系的谓语Id
                    relation.setConceptId(nodeRelation.getPredicateId());
                    relation.setName(nodeRelation.getPredicateName());
                    relation.setSource(currentNode.getId());
                    relation.setTarget(node.getId());
                    relationSet.add(relation);
                }
            }
        }
        echartsVO.setNodes(nodeSet);
        echartsVO.setRelations(relationSet);
        redisUtil.del(graph);
        redisUtil.set(graph, echartsVO);
        log.info("========更新会议图谱缓存：完成========");
    }


    /**
     * 会议分布
     */
    @Deprecated
    public List<TypeCountVO> distributionDeprecated() {
        List<TypeCountVO> typeCountVOList = new ArrayList<>();
        String meetingIdentifier = getMeetingIdentifier(ModelPropertyConst.PROPERTY_HUIYI_LEIXING);
        KnowledgeConcept meetingConcept = getKnowledgeConcept(ModelConceptConst.CONCEPT_HUIYI);
        Long totalMeetingCount = nodeEntityRepository.countByCondition(new Filters(new Filter("conceptId", ComparisonOperator.EQUALS, meetingConcept.getId())));

        Long total = 0L;
        for (MeetingTypeEnum value : MeetingTypeEnum.values()) {
            String name = value.getName();
            if ("全部".equals(name) || "其它会议".equals(name)) continue;
            Long count = nodeEntityRepository.countByCondition(new Filters(new Filter(meetingIdentifier, ComparisonOperator.EQUALS, value.getPropertyValue())));
            total += count;
            TypeCountVO typeCountVO = new TypeCountVO();
            typeCountVO.setName(name);
            typeCountVO.setValue(count);
            typeCountVOList.add(typeCountVO);
        }
        TypeCountVO typeCountVO = new TypeCountVO();
        typeCountVO.setName("其它会议");
        typeCountVO.setValue(totalMeetingCount - total);
        typeCountVOList.add(typeCountVO);
        return typeCountVOList;
    }

    public static final String distribution = "会议类型分布";

    @Override
    public List<TypeCountVO> distribution() {
        List<Object> cacheResult = redisUtil.lGet(distribution, 0, -1);
        if (ObjectUtil.isNotEmpty(cacheResult)) {
            return cacheResult.stream().flatMap(ele -> JSONUtil.parseArray(ele).stream()).map(ele -> JSONUtil.toBean(ele.toString(), TypeCountVO.class)).collect(Collectors.toList());
        }

        List<String> list = new ArrayList<>(List.of("党组会纪要", "总办会纪要", "董专会纪要", "董事会纪要", "专题会纪要", "党组中心组学习通报"));

        List<TypeCountVO> typeCountVOList = new ArrayList<>();


        Steam.of(list).forEach(data -> {

            final Integer i = countMeeting(data);

            TypeCountVO typeCountVO = new TypeCountVO();
            typeCountVO.setName(data);
            typeCountVO.setValue(Convert.toLong(i));
            typeCountVOList.add(typeCountVO);

        });
        redisUtil.lSet(distribution, typeCountVOList);
        return typeCountVOList;
    }

    @Override
    public void setDistribution() {
        log.info("========更新会议类型分布缓存：开始========");
        List<String> list = new ArrayList<>(List.of("党组会纪要", "总办会纪要", "董专会纪要", "董事会纪要", "专题会纪要", "党组中心组学习通报"));

        List<TypeCountVO> typeCountVOList = new ArrayList<>();


        Steam.of(list).forEach(data -> {

            final Integer i = countMeeting(data);

            TypeCountVO typeCountVO = new TypeCountVO();
            typeCountVO.setName(data);
            typeCountVO.setValue(Convert.toLong(i));
            typeCountVOList.add(typeCountVO);

        });
        redisUtil.del(distribution);
        redisUtil.lSet(distribution, typeCountVOList);
        log.info("========更新会议类型分布缓存：完成========");

    }


    private Integer countMeeting(String type) {

        Bson in;
        Bson groupFilters = com.mongodb.client.model.Filters.eq("metadata.name", "分组");

        in = com.mongodb.client.model.Filters.in("metadata.value", type);

        Bson knowledgeMatchConfirm = com.mongodb.client.model.Filters.eq("knowledgeMatchConfirm", true);
        Bson bson = com.mongodb.client.model.Filters.and(groupFilters, in, knowledgeMatchConfirm);
        final Set<String> collect = resourceRepository.findByCondition(bson).stream().map(Resource::getId).collect(Collectors.toSet());

        KnowledgeConcept meetingConcept = getKnowledgeConcept(ModelConceptConst.CONCEPT_HUIYI);

        return nodeEntityRepository.findCount(meetingConcept.getId(), collect);
    }

    public static final String yearDistribution = "会议年度分布";

    /**
     * 会议年度分布
     */
    @Override
    public List<JSONObject> yearDistribution() {
        List<Object> cacheResult = redisUtil.lGet(yearDistribution, 0, -1);
        if (ObjectUtil.isNotEmpty(cacheResult)) {
            return cacheResult.stream().flatMap(ele -> JSONUtil.parseArray(ele).stream()).map(JSONObject::new).collect(Collectors.toList());
        }

        KnowledgeConcept meetingConcept = getKnowledgeConcept(ModelConceptConst.CONCEPT_HUIYI);
        String meetingTypeIdentifier = getMeetingIdentifier(ModelPropertyConst.PROPERTY_HUIYI_LEIXING);
        List<JSONObject> result = new ArrayList<>();
        // 获取所有会议概念的实例
        List<NodeEntity> nodeEntityList = nodeEntityRepository.findByCondition(Map.of("conceptId", meetingConcept.getId()));
        String meetingTimeIdentifier = getMeetingIdentifier(ModelPropertyConst.PROPERTY_HUIYI_SHIJIAN);
        Map<Integer, Map<String, Long>> yearMap = new HashMap<>();
        Map<String, Long> meetingCount;
        // 获取所有会议
        for (NodeEntity nodeEntity : nodeEntityList) {
            Map<String, Object> properties = nodeEntity.getProperties();
            String time = Convert.toStr(properties.get(meetingTimeIdentifier));
            if (ObjectUtil.isNotEmpty(time)) {
                String[] split = time.split("年");
                // 获取当前会议的年度
                Integer year = Convert.toInt(split[0]);
                meetingCount = yearMap.get(year);
                if (ObjectUtil.isEmpty(meetingCount)) {
                    meetingCount = new HashMap<>();
                    // 初始化各会议数量
                    for (MeetingTypeEnum value : MeetingTypeEnum.values()) {
                        String name = value.getName();
                        if ("全部".equals(name)) continue;
                        meetingCount.put(name, 0L);
                    }
                }
                // 获取会议类型
                final String entityName = nodeEntity.getEntityName();
                String meetingName = MeetingTypeEnum.getMeetingTypeByEntityName(entityName);
                if (StrUtil.isBlank(meetingName)) meetingName = "其它会议";
                Long count = meetingCount.get(meetingName);
                if (count == 0L) {
                    meetingCount.put(meetingName, 1L);
                } else {
                    meetingCount.put(meetingName, ++count);
                }
                yearMap.put(year, meetingCount);


            }
        }
        // 统计次数
        for (Map.Entry<Integer, Map<String, Long>> entry : yearMap.entrySet()) {
            JSONObject jsonObject = new JSONObject();
            Integer year = entry.getKey();
            jsonObject.putOpt("year", year);
            Map<String, Long> typeMap = entry.getValue();
            List<JSONObject> list = new ArrayList<>();
            for (Map.Entry<String, Long> longEntry : typeMap.entrySet()) {
                JSONObject count = new JSONObject();
                count.putOpt("name", longEntry.getKey());
                count.putOpt("value", longEntry.getValue());
                list.add(count);
            }
            jsonObject.putOpt("count", list);
            result.add(jsonObject);
        }
        redisUtil.lSet(yearDistribution, result);
        return result;
    }


    /**
     * 会议年度分布
     */
    @Override
    public void setYearDistribution() {
        log.info("========更新会议年度分布缓存：开始========");
        KnowledgeConcept meetingConcept = getKnowledgeConcept(ModelConceptConst.CONCEPT_HUIYI);
        String meetingTypeIdentifier = getMeetingIdentifier(ModelPropertyConst.PROPERTY_HUIYI_LEIXING);
        List<JSONObject> result = new ArrayList<>();
        // 获取所有会议概念的实例
        List<NodeEntity> nodeEntityList = nodeEntityRepository.findByCondition(Map.of("conceptId", meetingConcept.getId()));
        String meetingTimeIdentifier = getMeetingIdentifier(ModelPropertyConst.PROPERTY_HUIYI_SHIJIAN);
        Map<Integer, Map<String, Long>> yearMap = new HashMap<>();
        Map<String, Long> meetingCount;
        // 获取所有会议
        for (NodeEntity nodeEntity : nodeEntityList) {
            Map<String, Object> properties = nodeEntity.getProperties();
            String time = Convert.toStr(properties.get(meetingTimeIdentifier));
            if (ObjectUtil.isNotEmpty(time)) {
                String[] split = time.split("年");
                // 获取当前会议的年度
                Integer year = Convert.toInt(split[0]);
                meetingCount = yearMap.get(year);
                if (ObjectUtil.isEmpty(meetingCount)) {
                    meetingCount = new HashMap<>();
                    // 初始化各会议数量
                    for (MeetingTypeEnum value : MeetingTypeEnum.values()) {
                        String name = value.getName();
                        if ("全部".equals(name)) continue;
                        meetingCount.put(name, 0L);
                    }
                }
                // 获取会议类型
                final String entityName = nodeEntity.getEntityName();
                String meetingName = MeetingTypeEnum.getMeetingTypeByEntityName(entityName);
                if (StrUtil.isBlank(meetingName)) meetingName = "其它会议";
                Long count = meetingCount.get(meetingName);
                if (count == 0L) {
                    meetingCount.put(meetingName, 1L);
                } else {
                    meetingCount.put(meetingName, ++count);
                }
                yearMap.put(year, meetingCount);


            }
        }
        // 统计次数
        for (Map.Entry<Integer, Map<String, Long>> entry : yearMap.entrySet()) {
            JSONObject jsonObject = new JSONObject();
            Integer year = entry.getKey();
            jsonObject.putOpt("year", year);
            Map<String, Long> typeMap = entry.getValue();
            List<JSONObject> list = new ArrayList<>();
            for (Map.Entry<String, Long> longEntry : typeMap.entrySet()) {
                JSONObject count = new JSONObject();
                count.putOpt("name", longEntry.getKey());
                count.putOpt("value", longEntry.getValue());
                list.add(count);
            }
            jsonObject.putOpt("count", list);
            result.add(jsonObject);
        }
        redisUtil.del(yearDistribution);
        redisUtil.lSet(yearDistribution, result);
        log.info("========更新会议年度分布缓存：完成========");
    }


    /**
     * 议题汇总
     *
     * @return 议题汇总树结构
     */
    @Override
    public JSONObject issuesSummary(String year, String type) {
        JSONObject cacheResult = JSONUtil.parseObj(redisUtil.get(year + "-" + type));
        if (ObjectUtil.isNotEmpty(cacheResult)) {
            return cacheResult;
        }

        final JSONObject entries = new JSONObject();

        KnowledgeConcept meetingConcept = getKnowledgeConcept(ModelConceptConst.CONCEPT_HUIYI);
        // 获取要查询的会议类型

        // 构建正则表达式，不匹配任何已知类型

        Bson in;
        Bson groupFilters = com.mongodb.client.model.Filters.eq("metadata.name", "分组");

        in = com.mongodb.client.model.Filters.in("metadata.value", type);

        Bson knowledgeMatchConfirm = com.mongodb.client.model.Filters.eq("knowledgeMatchConfirm", true);
        Bson bson = com.mongodb.client.model.Filters.and(groupFilters, in, knowledgeMatchConfirm);
        final Set<String> collect = resourceRepository.findByCondition(bson).stream().map(Resource::getId).collect(Collectors.toSet());

        // 对接华能1.6.0配置界面，处理现场遇到的附件排序新问题
        // 重写会议议题树状图接口

        final List<NodeEntity> byConditionAndSort = nodeEntityRepository.findData(meetingConcept.getId(), collect);

        //获取所有的docId
        final Set<String> docIds = byConditionAndSort.stream().map(NodeEntity::getDocIds).flatMap(Collection::stream).collect(Collectors.toSet());

        LambdaEsQueryWrapper<Document> queryWrapper = new LambdaEsQueryWrapper<>();

        queryWrapper.index(esIndexResolver.resolveEsIndexByTenant());
        queryWrapper.in(Document::getId, docIds);
        queryWrapper.nested("metadata", nestedWrapper -> nestedWrapper
                .eq("metadata.name", "年度")
                .in("metadata.value", year));


        // 执行查询
        List<org.irm.lab.front.model.Document> xx = documentEsMapper.selectList(queryWrapper);
        final List<String> collect1 = xx.stream().map(org.irm.lab.front.model.Document::getId).collect(Collectors.toList());

        List<ObjectId> objectIdList = collect1.stream()
                .map(ObjectId::new)
                .collect(Collectors.toList());

        Bson bson1 = com.mongodb.client.model.Filters.in("_id", objectIdList);
        final List<Resource> resourceList = resourceRepository.findByCondition(bson1);

        // 从 resourceList 中提取 ID
        Set<String> resourceIds = resourceList.stream()
                .map(Resource::getId)
                .collect(Collectors.toSet());
        JSONArray first = new JSONArray();


        // 过滤 byConditionAndSort 列表，保留包含 resourceIds 的 NodeEntity
        List<NodeEntity> nodeEntityPrimaryKeys = byConditionAndSort.stream()
                .filter(nodeEntity -> nodeEntity.getDocIds().stream().anyMatch(resourceIds::contains))
                .limit(3)
                .collect(Collectors.toList());

        for (NodeEntity data : nodeEntityPrimaryKeys) {

            final JSONObject jsonObject = new JSONObject();
            jsonObject.putOpt("name", data.getEntityName());
            JSONArray secound = new JSONArray();
            MyPage<IssuesVO> issuesVOMyPage = issuesPage(1, 10, data.getId());
            issuesVOMyPage.getContent().forEach(d -> {
                final JSONObject entries1 = new JSONObject();
                entries1.putOpt("name", d.getIssuesName());
                secound.add(entries1);
            });
            jsonObject.set("children", secound);

            first.add(jsonObject);
        }

        entries.set("children", first);

        entries.set("name", year);

        redisUtil.set(year + "-" + type, entries);
        return entries;

        /*JSONObject result = new JSONObject();
        // 1、一级 会议时间
        result.putOpt("name", year);
        KnowledgeConcept meetingConcept = getKnowledgeConcept(ModelConceptConst.CONCEPT_HUIYI);
        String meetingTypeIdentifier = getMeetingIdentifier(ModelPropertyConst.PROPERTY_HUIYI_LEIXING);
        String meetingTimeIdentifier = getMeetingIdentifier(ModelPropertyConst.PROPERTY_HUIYI_SHIJIAN);
        KnowledgeConcept issuesConcept = getKnowledgeConcept(ModelConceptConst.CONCEPT_HUIYI_YITI);
        // 会议类型
        type = MeetingTypeEnum.getPropertyValueByName(type);
        // 查询条件
        Filters filters = new Filters(new Filter("conceptId", ComparisonOperator.EQUALS, meetingConcept.getId()));
        filters.and(new Filter(meetingTimeIdentifier, ComparisonOperator.CONTAINING, year));
        if ("other".equals(type)) {
            filters.and(new Filter(meetingTypeIdentifier, ComparisonOperator.IS_NULL));
        } else if (!"all".equals(type)) {
            filters.and(new Filter(meetingTypeIdentifier, ComparisonOperator.EQUALS, type));
        }
        // 获取会议集合
        List<NodeEntity> nodeEntityList = nodeEntityRepository.findByCondition(filters);
        // 根据会议类型分组
        Map<String, List<NodeEntity>> nodeListMap = nodeEntityList.stream()
                .filter(Objects::nonNull)
                .collect(Collectors.groupingBy(nodeEntity -> {
                    Map<String, Object> properties = nodeEntity.getProperties();
                    if (properties != null) {
                        return Optional.ofNullable(properties.get(meetingTypeIdentifier))
                                .map(Object::toString)
                                .orElse("");
                    }
                    return "";
                }))
                .entrySet().stream()
                .filter(entry -> !entry.getKey().isEmpty())
                .collect(Collectors.toMap(Map.Entry::getKey, Map.Entry::getValue));


        // 二级 会议类型
        JSONArray secondArray = new JSONArray();
        for (String key : nodeListMap.keySet()) {
            JSONObject secondObject = new JSONObject();
            secondObject.putOpt("name", MeetingTypeEnum.getNameByPropertyValue(key));
            // 三级
            JSONArray thirdArray = new JSONArray();
            List<NodeEntity> meetingList = nodeListMap.get(key);
            for (NodeEntity nodeEntity : meetingList) {
                JSONObject thirdObject = new JSONObject();
                thirdObject.putOpt("name", nodeEntity.getEntityName());
                // 四级
                JSONArray firthArray = new JSONArray();
                // 查询每个会议的所有议题
                String cypher = "MATCH (s:ENTITY)-[r:RELATION]->(e:ENTITY) where s.id=$startId and e.conceptId=$endConceptId RETURN e ";
                List<NodeEntity> issuesList = nodeEntityRepository.findEntityByCypher(cypher, Map.of("startId", nodeEntity.getId(), "endConceptId", issuesConcept.getId()));
                if (ObjectUtil.isNotEmpty(issuesList)) {
                    List<String> issuesNameList = issuesList.stream().map(NodeEntity::getEntityName).collect(Collectors.toList());
                    for (String name : issuesNameList) {
                        JSONObject jsonObject = new JSONObject();
                        jsonObject.putOpt("name", name);
                        firthArray.add(jsonObject);
                    }
                }
                // 三级中添加四级
                if (ObjectUtil.isNotEmpty(firthArray)) {
                    thirdObject.putOpt("children", firthArray);
                }
                thirdArray.add(thirdObject);
            }
            // 二级中添加三级
            secondObject.putOpt("children", thirdArray);
            secondArray.add(secondObject);
        }
        // 一级中添加二级
        result.putOpt("children", secondArray);*/

        //return result;
    }


    /**
     * 缓存议题汇总
     *
     * @return 议题汇总树结构
     */
    @Override
    public void setIssuesSummary(String year, String type) {
        log.info("========更新议题汇总缓存========{}-{}：开始========", year, type);
        final JSONObject entries = new JSONObject();

        KnowledgeConcept meetingConcept = getKnowledgeConcept(ModelConceptConst.CONCEPT_HUIYI);
        // 获取要查询的会议类型

        // 构建正则表达式，不匹配任何已知类型

        Bson in;
        Bson groupFilters = com.mongodb.client.model.Filters.eq("metadata.name", "分组");

        in = com.mongodb.client.model.Filters.in("metadata.value", type);

        Bson knowledgeMatchConfirm = com.mongodb.client.model.Filters.eq("knowledgeMatchConfirm", true);
        Bson bson = com.mongodb.client.model.Filters.and(groupFilters, in, knowledgeMatchConfirm);
        final Set<String> collect = resourceRepository.findByCondition(bson).stream().map(Resource::getId).collect(Collectors.toSet());

        // 对接华能1.6.0配置界面，处理现场遇到的附件排序新问题
        // 重写会议议题树状图接口

        final List<NodeEntity> byConditionAndSort = nodeEntityRepository.findData(meetingConcept.getId(), collect);

        //获取所有的docId
        final Set<String> docIds = byConditionAndSort.stream().map(NodeEntity::getDocIds).flatMap(Collection::stream).collect(Collectors.toSet());

        LambdaEsQueryWrapper<Document> queryWrapper = new LambdaEsQueryWrapper<>();

        queryWrapper.index(esIndexResolver.resolveEsIndexByTenant());
        queryWrapper.in(Document::getId, docIds);
        queryWrapper.nested("metadata", nestedWrapper -> nestedWrapper
                .eq("metadata.name", "年度")
                .in("metadata.value", year));


        // 执行查询
        List<org.irm.lab.front.model.Document> xx = documentEsMapper.selectList(queryWrapper);
        final List<String> collect1 = xx.stream().map(org.irm.lab.front.model.Document::getId).collect(Collectors.toList());

        List<ObjectId> objectIdList = collect1.stream()
                .map(ObjectId::new)
                .collect(Collectors.toList());

        Bson bson1 = com.mongodb.client.model.Filters.in("_id", objectIdList);
        final List<Resource> resourceList = resourceRepository.findByCondition(bson1);

        // 从 resourceList 中提取 ID
        Set<String> resourceIds = resourceList.stream()
                .map(Resource::getId)
                .collect(Collectors.toSet());
        JSONArray first = new JSONArray();


        // 过滤 byConditionAndSort 列表，保留包含 resourceIds 的 NodeEntity
        List<NodeEntity> nodeEntityPrimaryKeys = byConditionAndSort.stream()
                .filter(nodeEntity -> nodeEntity.getDocIds().stream().anyMatch(resourceIds::contains))
                .limit(3)
                .collect(Collectors.toList());

        for (NodeEntity data : nodeEntityPrimaryKeys) {

            final JSONObject jsonObject = new JSONObject();
            jsonObject.putOpt("name", data.getEntityName());
            JSONArray secound = new JSONArray();
            MyPage<IssuesVO> issuesVOMyPage = issuesPage(1, 10, data.getId());
            issuesVOMyPage.getContent().forEach(d -> {
                final JSONObject entries1 = new JSONObject();
                entries1.putOpt("name", d.getIssuesName());
                secound.add(entries1);
            });
            jsonObject.set("children", secound);

            first.add(jsonObject);
        }

        entries.set("children", first);

        entries.set("name", year);

        redisUtil.del(year + "-" + type);
        redisUtil.set(year + "-" + type, entries);
        log.info("========更新议题汇总缓存========{}-{}：完成========", year, type);
    }


    @Override
    public List<JSONObject> returnMetadataValue() {
//        Map<String, Long> metadataValues = getTypeCountVOS("分组");
//        Set<String> values = metadataValues.keySet();
        List<JSONObject> result = new ArrayList<>();
        List<String> list = new ArrayList<>(List.of("党组会纪要", "总办会纪要", "董专会纪要", "董事会纪要", "专题会纪要", "党组中心组学习通报"));

//        for (String value : values) {
//            if(!list.contains(value)){
//                list.add(value);
//            }
//        }
        for (String item : list) {
//            if(item.equals("公司任免文件") || item.equals("部门会议纪要")
//                    || item.equals("专题会议纪要") || item.equals("部门会议纪要")) continue;
            JSONObject enumObject = new JSONObject();
            enumObject.putOpt("name", item);
            enumObject.putOpt("code", item);
            result.add(enumObject);
        }

        return result;
    }

    /**
     * 获取es中的分组数据
     */
    public List<Document> queryDistinctDocument(String metadataName) {
        // 创建一个新的 LambdaEsQueryWrapper 实例
        LambdaEsQueryWrapper<org.irm.lab.front.model.Document> queryWrapper = new LambdaEsQueryWrapper<>();
        queryWrapper.index(esIndexResolver.resolveEsIndexByTenant());

        // 添加嵌套查询条件
        queryWrapper.nested("metadata", nestedWrapper -> {
            nestedWrapper.eq("metadata.name", metadataName)
                    .exists("metadata.value").distinct("metadata.value")
            ; // 检查dateValue元数据是否存在

        }).nested("metadata", nestedWrapper -> {
            nestedWrapper.eq("metadata.name", "公文分类").in("metadata.value", List.of("会议纪要"));
        });
        return documentEsMapper.selectList(queryWrapper);

    }

    /**
     * 嵌套聚合统计
     */
    private Map<String, Long> getTypeCountVOS(String name) {

        final Map<String, Long> typeCount = new HashMap<>();
        // 创建嵌套聚合
        NestedAggregationBuilder nestedAgg = AggregationBuilders.nested("metadata_values", "metadata")
                .subAggregation(
                        AggregationBuilders.filter("metadata_filter", QueryBuilders.termQuery("metadata.name", name))
                                .subAggregation(
                                        AggregationBuilders.terms("distinct_values").field("metadata.value")
                                )
                );
        SearchSourceBuilder searchSourceBuilder = new SearchSourceBuilder();
        searchSourceBuilder.size(0);
        searchSourceBuilder.aggregation(nestedAgg);

        NestedQueryBuilder nestedQueryBuilder = QueryBuilders.nestedQuery(
                "metadata", // 嵌套路径
                QueryBuilders.boolQuery()
                        .must(QueryBuilders.termQuery("metadata.name", "公文分类")) // metadata.name 等于 "公文分类"
                        .must(QueryBuilders.termsQuery("metadata.value", "会议纪要")), // metadata.value 包含 "会议纪要"
                ScoreMode.None
        );
        searchSourceBuilder.query(nestedQueryBuilder);
        final LambdaEsQueryWrapper<Document> wrapper = new LambdaEsQueryWrapper<>();
        wrapper.index(esIndexResolver.resolveEsIndexByTenant());

        wrapper.setSearchSourceBuilder(searchSourceBuilder);
        // 打印生成的查询DSL
        String source = documentEsMapper.getSource(wrapper);
        System.out.println(source);

        final SearchResponse search = documentEsMapper.search(wrapper);

        // 获取聚合结果
        Aggregations aggregations = search.getAggregations();

// 获取嵌套聚合
        Nested nested = aggregations.get("metadata_values");
        if (nested != null) {
            // 获取过滤器聚合
            org.elasticsearch.search.aggregations.bucket.filter.Filter tag1Filter = nested.getAggregations().get("metadata_filter");
            if (tag1Filter != null) {
                // 获取术语聚合
                Terms distinctValues = tag1Filter.getAggregations().get("distinct_values");
                if (distinctValues != null) {
                    // 遍历术语聚合结果
                    for (Terms.Bucket bucket : distinctValues.getBuckets()) {
                        String key = bucket.getKeyAsString();
                        long docCount = bucket.getDocCount();
                        typeCount.put(key, docCount);
                    }
                }
            }
        }
        return typeCount;
    }

    @Override
    public List<JSONObject> returnMetadataValuePlus() {

        List<JSONObject> result = new ArrayList<>();
        Set<String> list = new HashSet<String>(List.of("党组会纪要", "总办会纪要", "董专会纪要", "董事会纪要", "专题会纪要", "党组中心组学习通报"));
        List<Document> documents = queryDistinctDocument("分组");
        for (Document document : documents) {
            for (org.irm.lab.front.model.MetadataVO metadatum : document.getMetadata()) {
                if (metadatum.getName().equals("分组") && CollUtil.isNotEmpty(metadatum.getValue())) {
                    System.out.println(metadatum.getValue());
                    list.addAll(metadatum.getValue());
                    break;
                }
            }
        }

        for (String item : list) {
            // 这两类文件分组甲方不要，但是数据就是有这个分类，只能在这里处理
            if (item.equals("公司任免文件") || item.equals("部门会议纪要")) continue;
            JSONObject enumObject = new JSONObject();
            enumObject.putOpt("name", item);
            enumObject.putOpt("code", item);
            result.add(enumObject);
        }
//        JSONObject enumObject = new JSONObject();
//        enumObject.putOpt("name","其他会议");
//        enumObject.putOpt("code", "other");
//        result.add(enumObject);

        return result;
    }

//    @Override
//    public List<String> returnMetadataValueYear() {
//        List<String> allYearsCache = JSONUtil.parseArray(redisUtil.get("metadataValueYears")).toList(String.class);
//        if (ObjectUtil.isNotEmpty(allYearsCache)) {
//            return allYearsCache;
//        }
//        List<String> allYears = getTypeCountVOS("年度").keySet().stream().sorted().collect(Collectors.toList());
//        redisUtil.set("metadataValueYears", allYears);
//        return allYears;
//    }

    @Override
    public List<String> returnMetadataValueYear() {
        return getTypeCountVOS("年度").keySet().stream().sorted().collect(Collectors.toList());
    }

    @Override
    public List<String> setReturnMetadataValueYear() {
        List<String> allYears = getTypeCountVOS("年度").keySet().stream().sorted().collect(Collectors.toList());
        redisUtil.del("metadataValueYears");
        redisUtil.set("metadataValueYears", allYears);
        return allYears;
    }

    @Override
    public Map<String, String> queryDocOfNodeEntity(String id) {
        Filters filters = new Filters();
        filters.and(new Filter("id", ComparisonOperator.EQUALS, id));
        List<NodeEntity> nodeEntityList = nodeEntityRepository.findByCondition(filters);
        if (CollUtil.isNotEmpty(nodeEntityList)) {
            NodeEntity nodeEntity = nodeEntityList.get(0);
            return obtainResourceMap(nodeEntity.getDocIds());
        }
        throw new ServiceException("节点id：【" + id + "】不存在");
    }

    @Override
    public Map<String, String> queryMeetingNode(String id) {
        KnowledgeConcept meetingConcept = getKnowledgeConcept(ModelConceptConst.CONCEPT_HUIYI);
        String cypherGetMeeting = "MATCH (n:ENTITY)-[r:RELATION]->(p:ENTITY) where p.id=$issueNodeId and n.conceptId=$meetingConceptId RETURN n";
        List<NodeEntity> meetings = nodeEntityRepository.findEntityByCypher(cypherGetMeeting, Map.of(
                "issueNodeId", id,
                "meetingConceptId", meetingConcept.getId()
        ));
        Map<String, String> result = new HashMap<>();
        for (NodeEntity meeting : meetings) {
            result.put(meeting.getId(), meeting.getEntityName());
        }
        return result;
    }

    @Override
    public AttendeesMeetingVO queryMeetingNodeList(String name, Integer page, Integer size) {
        KnowledgeConcept meetingConcept = getKnowledgeConcept(ModelConceptConst.CONCEPT_HUIYI);
        KnowledgeConcept peopleConcept = getKnowledgeConcept(ModelConceptConst.CONCEPT_RENYUAN);
        return getPeopleWithMeetingPlus(meetingConcept,peopleConcept,name, page, size);
    }

    /**
     * 获取时间区间内的es文档数
     */
    public long returnResourceIdByClassificy(String name, String start, String end) {
        System.out.println("开始时间" + start);
        System.out.println("结束时间" + end);
        // 创建一个新的 LambdaEsQueryWrapper 实例
        LambdaEsQueryWrapper<org.irm.lab.front.model.Document> queryWrapper = new LambdaEsQueryWrapper<>();
        queryWrapper.index(esIndexResolver.resolveEsIndexByTenant());
        if (name.equals("createTime")) {
            // 添加嵌套查询条件
            queryWrapper.ge("createTime", start).le("createTime", end);
        } else {
            // 添加嵌套查询条件
            queryWrapper.nested("metadata", nestedWrapper -> {
                nestedWrapper.eq("metadata.name", name)
                        .exists("metadata.dateValue").ge("metadata.dateValue", start).le("metadata.dateValue", end)
                ; // 检查dateValue元数据是否存在

            }).nested("metadata", nestedWrapper -> {
                nestedWrapper.eq("metadata.name", "公文分类").in("metadata.value", List.of("会议纪要"));
            });

        }


        return documentEsMapper.selectCount(queryWrapper);
    }

    public Map<String, String> obtainResourceMap(Set<String> docIds) {
        List<ObjectId> ids = docIds.stream().map(ObjectId::new).collect(Collectors.toList());
        Bson id = com.mongodb.client.model.Filters.in("_id", ids);
        List<Resource> byCondition = resourceRepository.findByCondition(id);
        HashMap<String, String> doc = new HashMap<>();
        if (!byCondition.isEmpty()) {
            for (Resource resource : byCondition) {
                doc.put(resource.getId(), resource.getName());
            }
        }
        return doc;
    }
}
