package org.irm.lab.front;

import javax.annotation.Resource;

import cn.easyes.starter.register.EsMapperScan;
import org.irm.lab.common.constant.AppConstant;
import org.irm.lab.common.utils.SpringUtil;
import org.springframework.boot.CommandLineRunner;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.autoconfigure.data.jpa.JpaRepositoriesAutoConfiguration;
import org.springframework.boot.autoconfigure.jdbc.DataSourceAutoConfiguration;
import org.springframework.cloud.client.discovery.EnableDiscoveryClient;
import org.springframework.cloud.openfeign.EnableFeignClients;
import org.springframework.context.annotation.EnableAspectJAutoProxy;
import org.springframework.context.annotation.Import;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.scheduling.annotation.EnableScheduling;

import com.alibaba.cloud.nacos.NacosConfigManager;
import com.alibaba.cloud.nacos.NacosConfigProperties;
import com.alibaba.nacos.api.config.ConfigService;
import com.alibaba.nacos.api.exception.NacosException;

@SpringBootApplication(scanBasePackages = AppConstant.BASE_PACKAGES, exclude = {JpaRepositoriesAutoConfiguration.class,
        DataSourceAutoConfiguration.class})
@EnableDiscoveryClient
@EnableAsync
@EnableFeignClients(basePackages = AppConstant.BASE_PACKAGES)
@Import(SpringUtil.class)
@EnableAspectJAutoProxy
@EsMapperScan("org.irm.lab.front.mapper")
@EnableScheduling
public class KunifyFrontApplication implements CommandLineRunner {

    @Resource
    private NacosConfigManager nacosConfigManager;

    @Resource
    private NacosConfigProperties nacosConfigProperties;

    public static void main(String[] args) {
        SpringApplication.run(KunifyFrontApplication.class, args);
    }

    @Override
    public void run(String... args) throws Exception {
        System.out.println("Printing remote Nacos configuration:");
        printRemoteNacosConfig();
    }

    private void printRemoteNacosConfig() throws NacosException {
        ConfigService configService = nacosConfigManager.getConfigService();
        String dataId = nacosConfigProperties.getName();
        String group = nacosConfigProperties.getGroup();
        String config = configService.getConfig(dataId, group, 5000);

        if (config != null) {
            System.out.println("=============配置加载成功=============");
            System.out.println(config);
        } else {
            System.out.println("=============配置加载失败=============");
        }
    }
}
