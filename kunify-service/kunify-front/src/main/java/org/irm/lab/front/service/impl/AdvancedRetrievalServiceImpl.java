package org.irm.lab.front.service.impl;

import cn.easyes.core.biz.EsPageInfo;

import cn.easyes.core.biz.SAPageInfo;
import cn.easyes.core.conditions.select.LambdaEsQueryWrapper;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.mongodb.client.model.Accumulators;
import com.mongodb.client.model.Aggregates;
import com.mongodb.client.model.Filters;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.lucene.search.join.ScoreMode;
import org.bson.conversions.Bson;
import org.bson.types.ObjectId;

import org.elasticsearch.index.query.BoolQueryBuilder;
import org.elasticsearch.index.query.QueryBuilder;
import org.elasticsearch.index.query.QueryBuilders;
import org.elasticsearch.search.builder.SearchSourceBuilder;
import org.irm.lab.common.api.R;
import org.irm.lab.common.config.resolve.EsIndexResolver;
import org.irm.lab.common.constant.PermissionConstant;
import org.irm.lab.common.entity.AbstractBaseEntity;
import org.irm.lab.common.pojo.BaseStatus;
import org.irm.lab.common.support.MyPage;
import org.irm.lab.config.entity.*;
import org.irm.lab.config.feign.ClassificationItemFeign;

import org.irm.lab.config.feign.MetadataFeign;
import org.irm.lab.config.repository.*;
import org.irm.lab.front.constant.ResourceMetadataGroupConstant;
import org.irm.lab.front.dto.*;
import org.irm.lab.front.mapper.DocumentEsMapper;
import org.irm.lab.front.model.Document;
import org.irm.lab.front.service.IAdvancedRetrievalService;
import org.irm.lab.front.vo.ResourceRetrievalVO;
import org.irm.lab.front.vo.es.Classify;
import org.irm.lab.front.vo.es.ClassifyArray;
import org.irm.lab.repository.constant.RecordResourceType;
import org.irm.lab.repository.entity.Resource;
import org.irm.lab.repository.repository.ResourceRepository;
import org.irm.lab.repository.vo.MetadataVO;
import org.irm.lab.user.entity.Department;
import org.irm.lab.user.entity.Unit;
import org.irm.lab.user.entity.User;
import org.irm.lab.user.feign.DepartmentFeign;
import org.irm.lab.user.feign.UnitFeign;
import org.springframework.data.domain.Sort;
import org.springframework.data.elasticsearch.core.query.NativeSearchQuery;
import org.springframework.data.elasticsearch.core.query.NativeSearchQueryBuilder;
import org.springframework.stereotype.Service;

import java.time.LocalDate;
import java.time.ZoneId;
import java.time.ZonedDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.atomic.AtomicLong;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2023/4/13 19:05
 * @description 高级检索业务层实现类
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class AdvancedRetrievalServiceImpl implements IAdvancedRetrievalService {

    private final ClassificationSetRepository classificationSetRepository;
    private final ClassificationItemRepository classificationItemRepository;
    private final ClassificationItemFeign classificationItemFeign;
    private final MetadataSchemaRepository metadataSchemaRepository;
    private final ResourceRepository resourceRepository;
    private final MetadataRepository metadataRepository;
    private final DictItemRepository dictItemRepository;
    private final DictRepository dictRepository;
    private final DepartmentFeign departmentFeign;
    private final UnitFeign unitFeign;
    private final MetadataFeign metadataFeign;
    private final DocumentESServiceImpl documentESService;
    private final EsIndexResolver esIndexResolver;
    private final DocumentEsMapper documentEsMapper;

    /**
     * 高级检索
     *
     * @param advancedRetrievalDTOs 查询条件
     * @param page                  页数
     * @param size                  条数
     * @return {@link Resource}
     */
    @Override
    public MyPage<ResourceRetrievalVO> AdvancedRetrieval(String page, String size, List<AdvancedRetrievalDTO> advancedRetrievalDTOs) {
        //将List<AdvancedRetrievalDTO>转换成AdvancedRetrievalDTO
        AdvancedRetrievalDTO retrievalDTO = changeListToVO(advancedRetrievalDTOs);
        //高级检索得到的资源id
        Set<String> retrievalResourceIds = getRetrievalResource(retrievalDTO);
        System.out.println("文件查询根据过滤条件、查询条件查询的ids长度为" + retrievalResourceIds.size() );
        List<Resource> resourceList = resourceRepository.findById(retrievalResourceIds);
        resourceList = resourceList.stream()
                .sorted(Comparator.comparing(Resource::getCreateTime))
                .collect(Collectors.toList());
        //封装资源集合
        List<ResourceRetrievalVO> resourceRetrievalVOS = getResourceRetrievalVOS(resourceList);
        // 获取数据总数
        int totalSize = resourceRetrievalVOS.size();
        // 分页
        resourceRetrievalVOS = resourceRetrievalVOS.stream()
                .skip((long) (Integer.parseInt(page) - 1) * Integer.parseInt(size))
                .limit(Integer.parseInt(size))
                .collect(Collectors.toList());
        resourceRetrievalVOS = resourceRetrievalVOS.stream().peek(resourceRetrievalVO -> {
            List<MetadataVO> showDocument = getShowDocument(resourceRetrievalVO.getResourceId(), resourceRetrievalVO.getMetadata());
            resourceRetrievalVO.setMetadata(showDocument);
        }).collect(Collectors.toList());
        // 返回结果
        return new MyPage<>(Integer.parseInt(page), Integer.parseInt(size), totalSize, resourceRetrievalVOS);
    }

    /**
     * 封装高级查询返回数据
     *
     * @param resourceList 资源集合
     * @return List<ResourceRetrievalVO>
     */
    public List<ResourceRetrievalVO> getResourceRetrievalVOS(List<Resource> resourceList) {
        return resourceList.stream().map(resource -> {
            ResourceRetrievalVO resourceRetrievalVO = new ResourceRetrievalVO();
            resourceRetrievalVO.setResourceId(resource.getId());
            resourceRetrievalVO.setTitle(resource.getName());
            resourceRetrievalVO.setMetadata(resource.getMetadata());
            return resourceRetrievalVO;
        }).collect(Collectors.toList());
    }

    /**
     * 指定资源展示的元数据项
     *
     * @param resourceId   资源id
     * @param metadataList 元数据集合
     * @return
     */
    public List<MetadataVO> getShowDocument(String resourceId, List<MetadataVO> metadataList) {
        String schemaName = "other";
        org.irm.lab.repository.entity.Resource resource = resourceRepository.findById(resourceId);
        String metadataSchemaId = resource.getMetadataSchemaId();
        MetadataSchema schema = null;
        try {
            schema = metadataSchemaRepository.findById(metadataSchemaId);
            schemaName = schema.getName();
        } catch (Exception e) {
            e.printStackTrace();
        }
        List<String> metadataGroup = ResourceMetadataGroupConstant.metadataGroupToShowList(schemaName);
        return metadataList.stream().filter(metadataVO -> ObjectUtil.isNotEmpty(metadataVO) && metadataGroup.contains(metadataVO.getName())).collect(Collectors.toList());

    }

    /**
     * 根据高级查询条件查询资源集合 and or not
     *
     * @param advancedRetrievalDTO 查询条件
     * @return List<Resource>
     */
    public Set<String> getRetrievalResource(AdvancedRetrievalDTO advancedRetrievalDTO) {
        Set<String> idList = new HashSet<>();
        // 先获取查询条件
        List<QueryMetadataDTO> queryCondition = advancedRetrievalDTO.getQueryCondition();
        //获取时间区间查询条件
        List<QueryMetadataDTO> timeCondition = advancedRetrievalDTO.getTimeCondition();
        // 获取过滤条件
        List<FilterMetadataDTO> filterCondition = advancedRetrievalDTO.getFilterCondition();
        if (ObjectUtil.isEmpty(queryCondition) && ObjectUtil.isEmpty(timeCondition) && ObjectUtil.isEmpty(filterCondition))
            return idList;
        //默认查询条件
        Bson firstBson = Filters.and(
                Filters.ne("frontStatus", PermissionConstant.FRONT_FILE_INVISIBLE),
                Filters.eq("stage", RecordResourceType.STAGE_6),
                Filters.eq("isDeleted", BaseStatus.OK));
        //获取字符型查询条件
        //将所有查询条件按照  and   or   not  区分
        List<QueryMetadataDTO> andCondition = queryCondition.stream().filter(queryMetadataDTO -> "AND".equals(queryMetadataDTO.getLogic())).collect(Collectors.toList());
        List<QueryMetadataDTO> orCondition = queryCondition.stream().filter(queryMetadataDTO -> "OR".equals(queryMetadataDTO.getLogic())).collect(Collectors.toList());
        List<QueryMetadataDTO> notCondition = queryCondition.stream().filter(queryMetadataDTO -> "NOT".equals(queryMetadataDTO.getLogic())).collect(Collectors.toList());
        //and检索
        if (ObjectUtil.isNotEmpty(andCondition)) {
            Set<String> andResourceIds = new HashSet<>();
            //拼接and条件
            for (QueryMetadataDTO queryMetadataDTO : andCondition) {
                Set<String> set = getResourceIds(queryMetadataDTO);
                if (ObjectUtil.isEmpty(andResourceIds)) {
                    andResourceIds.addAll(set);
                } else {
                    andResourceIds = andResourceIds.stream().filter(set::contains).collect(Collectors.toSet());
                }
            }
            idList.addAll(ObjectUtil.isEmpty(andResourceIds) ? new HashSet<>() : andResourceIds);
        }
        //or查询条拼接
        if (ObjectUtil.isNotEmpty(orCondition)) {
            //拼接查询条件
            for (QueryMetadataDTO queryMetadataDTO : orCondition) {
                Set<String> orResourceIds = getResourceIds(queryMetadataDTO);
                idList.addAll(orResourceIds);
            }
        }
        //not查询条拼接
        if (ObjectUtil.isNotEmpty(notCondition)) {
            for (QueryMetadataDTO queryMetadataDTO : notCondition) {
                Set<String> notResourceIds = getResourceIds(queryMetadataDTO);
                idList = ObjectUtil.isNotEmpty(idList) ? idList.stream().filter(notResourceIds::contains).collect(Collectors.toSet()) : notResourceIds;
            }
        }
        //时间区间查询
        if (ObjectUtil.isNotEmpty(timeCondition)) {
            for (QueryMetadataDTO queryMetadataDTO : timeCondition) {
                Set<String> timeResourceIds = getResourceIds(queryMetadataDTO);
                timeResourceIds = ObjectUtil.isNotEmpty(timeResourceIds) ? timeResourceIds : new HashSet<>();
                if ("AND".equals(queryMetadataDTO.getLogic())) {
                    idList = ObjectUtil.isNotEmpty(idList) ? idList.stream().filter(timeResourceIds::contains).collect(Collectors.toSet()) : timeResourceIds;
                } else {
                    idList.addAll(timeResourceIds);
                }
            }
        }

        if (ObjectUtil.isNotEmpty(filterCondition)) {
            Map<String, Set<String>> stringSetMap = returnMetadataValues();
            //添加过滤条件
            for (FilterMetadataDTO filterMetadataDTO : filterCondition) {
                List<String> values = filterMetadataDTO.getValues();
                List<Bson> bsonList = new ArrayList<>();
                if (values.contains("其他")) {
                    values.remove("其他");
                    Bson valueNullOrEmptyFilter ;
                    if(stringSetMap.get(filterMetadataDTO.getName()) != null){
                        Set<String> valuesMetadata = stringSetMap.get(filterMetadataDTO.getName());
//                        if(filterMetadataDTO.getName().equals("文件类型")){ // 对制度进行特别处理,因为入库元数据的时候入库为制度为具体的制度类型,没有加上制度的父级,与公文不符
//                            HashSet<String> objects = new HashSet<>();
//                            for (String value : valuesMetadata) {
//                                if(value.contains("制度")){
//                                    String[] split = value.split("/");
//                                    objects.add(split.length == 2?split[1]:split[0]);
//                                }else{
//                                    objects.add(value);
//                                }
//                            }
//                            valuesMetadata.clear();
//                            valuesMetadata.addAll(objects);
//                        }

                        valueNullOrEmptyFilter = Filters.or(
                                Filters.size("metadata.value", 0),
                                Filters.exists("metadata.value",false),
                                Filters.nin("metadata.value",valuesMetadata)

                        );
                    }else{
                        valueNullOrEmptyFilter = Filters.or(
                                Filters.size("metadata.value", 0),
                                Filters.exists("metadata.value",false)
                        );
                    }

                    bsonList.add(
                            Filters.and(
                                    firstBson,
                                    Filters.eq("metadata.name", filterMetadataDTO.getName()),
                                    valueNullOrEmptyFilter
                            ));
                }
                if (ObjectUtil.isNotEmpty(values)) {
                    Metadata metadata = metadataRepository.findOne(Filters.eq("name", filterMetadataDTO.getName()));
                    HashSet<String> metadataValue = new HashSet<>();
                    for (String value : values) {
                        if (!isOrNotParent("制度", value,metadata.getClassificationSetId())) {
                            value = queryMetadataAllPath(value, filterMetadataDTO.getName());
                        }
                        metadataValue.add(value);
                    }
                    bsonList.add(
                            Filters.and(
                                    firstBson,
                                    Filters.eq("metadata.name", filterMetadataDTO.getName()),
                                    Filters.in("metadata.value", metadataValue)
                            ));
                }
                if (ObjectUtil.isEmpty(bsonList)) continue;
                List<Bson> unwind = List.of(
                        Aggregates.unwind("$metadata"),
                        Aggregates.match(Filters.or(bsonList)),
                        Aggregates.group("$_id", Accumulators.addToSet("metadata", "$metadata"))
                );
                List<Resource> andByAggregate = resourceRepository.findByAggregate(unwind);
                Set<String> resourceIds = andByAggregate.stream().map(AbstractBaseEntity::getId).collect(Collectors.toSet());
                idList = idList.stream().filter(resourceIds::contains).collect(Collectors.toSet());
            }
        }
        return idList;
    }

    /**
     * 更具元数据查询文件id集合
     *
     * @param queryMetadataDTO 查询条件
     * @return ids
     */
    public Set<String> getResourceIds(QueryMetadataDTO queryMetadataDTO) {
        //默认查询条件
        Bson firstBson = Filters.and(
                Filters.ne("frontStatus", PermissionConstant.FRONT_FILE_INVISIBLE),
                Filters.eq("stage", RecordResourceType.STAGE_6),
                Filters.eq("isDeleted", BaseStatus.OK));
        List<Bson> bsonList = new ArrayList<>(List.of(firstBson, Filters.eq("metadata.name", queryMetadataDTO.getName())));

        if (ObjectUtil.isNotEmpty(queryMetadataDTO.getStartTime()) && ObjectUtil.isNotEmpty(queryMetadataDTO.getEndTime())) {
            String start = queryMetadataDTO.getStartTime();
            String end = queryMetadataDTO.getEndTime();

            if (ObjectUtil.isAllNotEmpty(start, end) && start.equals(end)) {
//                bsonList.add(Filters.gte("metadata.dateValue", Convert.toDate(start)));
//                bsonList.add(Filters.lte("metadata.dateValue", addOneDay(Convert.toDate(start))));
                //生产环境的时间是字符串类型
                bsonList.add(Filters.eq("metadata.value", start));
            } else {
                if (ObjectUtil.isNotEmpty(start)) {
//                    bsonList.add(Filters.gte("metadata.dateValue", Convert.toDate(start)));
                    bsonList.add(Filters.gte("metadata.value", start));
                }
                if (ObjectUtil.isNotEmpty(end)) {
//                    bsonList.add(Filters.lte("metadata.dateValue", Convert.toDate(end)));
                    bsonList.add(Filters.lte("metadata.value", end));
                }
            }

        } else {
            Bson valueBson;
            if ("NOT".equals(queryMetadataDTO.getLogic())) {
                valueBson = Filters.ne("metadata.value", queryMetadataDTO.getValue());
            } else {
                valueBson = queryMetadataDTO.isPrecise() ? Filters.eq("metadata.value", queryMetadataDTO.getValue()) : Filters.regex("metadata.value", queryMetadataDTO.getValue());
            }
            bsonList.add(valueBson);
        }

        List<Bson> unwind = List.of(
                Aggregates.unwind("$metadata"),
                Aggregates.match(Filters.and(bsonList)),
                Aggregates.group("$_id", Accumulators.addToSet("metadata", "$metadata"))
        );
        List<Resource> andByAggregate = resourceRepository.findByAggregate(unwind);
        return andByAggregate.stream().map(AbstractBaseEntity::getId).collect(Collectors.toSet());
    }

    public static Date addOneDay(Date date) {
        // 获取Calendar实例，并设置时间为传入的Date对象
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);

        // 为Calendar对象增加一天
        calendar.add(Calendar.DATE, 1);

        // 获取增加一天后的Date对象
        return calendar.getTime();
    }

    /**
     * 生成高级查询的资源列表
     *
     * @return 资源列表
     */
    @Override
    public JSONArray getRetrievalClassify(List<AdvancedRetrievalDTO> advancedRetrievalDTOs) {
        //查询条件转换
        AdvancedRetrievalDTO advancedRetrievalDTO = changeListToVO(advancedRetrievalDTOs);
        //用于存储所有分类
        JSONArray metadataArray = null;
        //获取所有查询出来的数据
        Set<String> resourceIds = getRetrievalResource(advancedRetrievalDTO);
        System.out.println("分类根据过滤条件、查询条件查询的ids长度为" + resourceIds.size() );
        // 查询出枚举\类目结构
        metadataArray = metadataFeign.filterCondition("esFilterAble").getData();
//        若当前资源为空，则不会有分类用于展示
        if (ObjectUtil.isEmpty(resourceIds)) return metadataArray;

//        //查询所有可用于查询的元数据项
//        Bson bson = Filters.and(Filters.eq("advancedFilterAble", true));
//        List<Metadata> metadataList = metadataRepository.findByCondition(bson);
//        //根据元数据项类型将所有元数据项分类
//        Map<String, List<Metadata>> map = metadataList.stream().collect(Collectors.groupingBy(Metadata::getDatatype));
//
//        //类目型元数据项
//        List<Metadata> classifyMetadataList = map.get(MetadataDataTypeConstant.CLASSIFICATION);
//        setClassifyMetadata(metadataArray, classifyMetadataList, resourceIds);
        //枚举型元数据项
//        List<Metadata> dictMetadataList = map.get(MetadataDataTypeConstant.ENUM);
//        setDictMetadata(metadataArray, dictMetadataList, resourceIds);
//        //年度
//        setShowYears(metadataArray, resourceIds);
        JSONArray objects = new JSONArray();
        for (Object o : metadataArray) {
            JSONObject entries = JSONUtil.parseObj(o);
            Object children = entries.get("children");
            JSONArray parseArray = JSONUtil.parseArray(children);
            JSONObject jsonObject = new JSONObject();
            jsonObject.putOpt("name", "其他");
            parseArray.add(jsonObject);
            entries.putOpt("children",parseArray);
            objects.add(entries);
        }
        //查询元数据树中对应值域所涉及的资源数量
        JSONArray jsonArray = new JSONArray();
        log.info("分类查询中的文件个数{}",resourceIds.size());
        setMetadataResourceCount(objects, jsonArray, resourceIds);
        return jsonArray;
    }

    // 用于存储id到Node的映射
    private Map<String, ClassificationItem> idToNodeMap;

    // 构建id到Node的映射
    private void buildMap(List<ClassificationItem> nodeList) {
        idToNodeMap = new HashMap<>();
        for (ClassificationItem node : nodeList) {
            idToNodeMap.put(node.getId(), node);
        }
    }

    // 递归函数，用于构建完整的路径名
    private String buildPath(ClassificationItem node) {
        // 如果没有父节点，返回当前节点的名称
        if (node.getParentId().equals("0") || !idToNodeMap.containsKey(node.getParentId())) {
            return node.getName();
        }
        // 否则，递归获取父节点的路径，然后添加当前节点的名称
        return buildPath(idToNodeMap.get(node.getParentId())) + "/" + node.getName();
    }

    public Set<String> buildAllPaths(List<ClassificationItem> nodeList, String name, String classificationSetId) {
        buildMap(nodeList); // 构建映射
        Set<String> paths = new HashSet<>();
        for (ClassificationItem node : nodeList) {
            if(name.equals("文件类型")){
                if(isOrNotParent("制度",node.getName(),classificationSetId)){
                    paths.add(node.getName());
                }else{
                    paths.add(buildPath(node));
                }
            }else{
                // 对于每个节点，构建其完整路径
                paths.add(buildPath(node));
            }

        }
        return paths;
    }


    /**
     *  查询当前系统中可被元数据检索的类目集及字典型元数据的所有值域
     * @return
     */
    private Map<String,Set<String>> returnMetadataValues(){
        Map<String,Set<String>> result = new HashMap();
        //  或查询枚举型、类目型元数据
        Bson or = Filters.or(Filters.eq("datatype", "类目型"), Filters.eq("datatype", "枚举型"));
        Bson bson = Filters.and(Filters.eq("esFilterAble", true),or);
        List<Metadata> dictAndTypeMetadata = metadataRepository.findByCondition(bson);
        for (Metadata dictAndTypeMetadatum : dictAndTypeMetadata){
            if(ObjectUtil.isNotEmpty(dictAndTypeMetadatum.getDatatype()) && dictAndTypeMetadatum.getDatatype().equals("类目型")){
                    // 处理类目集
                if(StrUtil.isNotEmpty(dictAndTypeMetadatum.getClassificationSetId())){
                    List<ClassificationItem> classificationItemList = classificationItemRepository.findByCondition(Filters.and(Filters.eq("setId", dictAndTypeMetadatum.getClassificationSetId())
                            , Filters.eq("status", "正常")));
                    Set<String> strings = buildAllPaths(classificationItemList,dictAndTypeMetadatum.getName(),dictAndTypeMetadatum.getClassificationSetId());
                    result.put(dictAndTypeMetadatum.getName(),strings);
                }
            }else if(ObjectUtil.isNotEmpty(dictAndTypeMetadatum.getDatatype()) && dictAndTypeMetadatum.getDatatype().equals("枚举型")){
                // 处理枚举集
                if(StrUtil.isNotEmpty(dictAndTypeMetadatum.getDictId())){
                    List<DictItem> dictItems = dictItemRepository.findByCondition(Filters.and(Filters.eq("dictId", dictAndTypeMetadatum.getDictId()),Filters.eq("status","正常")));
                    Set<String> items = new HashSet<>();
                    for (DictItem dictItem : dictItems) {
                        items.add(dictItem.getLabel());
                    }
                    result.put(dictAndTypeMetadatum.getName(),items);
                }
            }
        }
            return result;
    }
//    /**
//     * 添加类目型元数据项及值域
//     *
//     * @param jsonArray            返回值
//     * @param classifyMetadataList 元数据项集合
//     */
//    public void setClassifyMetadata(JSONArray jsonArray, List<Metadata> classifyMetadataList, Set<String> resourceIds) {
//
//
//
//        //若元数据项为空直接返回
//        if (ObjectUtil.isEmpty(classifyMetadataList)) return;
//        //遍历所有可用于查询的元数据项
//        for (Metadata metadata : classifyMetadataList) {
////            //获取当前元数据项名称
////            String metadataName = metadata.getName();
////            //查询所有包含该元数据项的资源
////            List<Resource> resourceList = getResourcesByMetadata(metadataName, resourceIds);
////            if (ObjectUtil.isEmpty(resourceList)) continue;
////            //查询当前元数据项的所有使用到的值域
////            Set<String> metadataValues = getMetadataValues(resourceList);
////            log.info("获取类目集元数据{}下通过文件id查询出来的metadataValues的值{}",metadata.getName(),metadataValues);
////            if (ObjectUtil.isEmpty(metadataValues)) continue;
//            if(StrUtil.isNotEmpty(metadata.getClassificationSetId())){
//                List<ClassificationItem> classificationItemList = classificationItemRepository.findByCondition(Filters.and(Filters.eq("setId", dictAndTypeMetadatum.getClassificationSetId())
//                        , Filters.eq("status", "正常")));
//                Set<String> strings = buildAllPaths(classificationItemList);
//                List<JSONObject> dictItemJSONObjectList = strings.stream()
//                        .map(dictItem -> {
//                            JSONObject jsonObject = new JSONObject(dictItem);
//                            jsonObject.putOpt("name", );
//                            return jsonObject;
//                        }).collect(Collectors.toList());
//                result.put(metadata.getName(),strings);
//            }
//
////            //获取当前元数据项类目集
////            ClassificationSet classificationSet = classificationSetRepository.findById(metadata.getClassificationSetId());
////            log.info("获取元数据{}的类目集{}",metadata.getName(),classificationSet.getName());
////            //获取当前元数据项的类目项
////            List<ClassificationItem> classificationItemList = classificationItemRepository.findByCondition(Filters.eq("setId", classificationSet.getId()));
////            //将这些类目项转成树状结构
////            List<ClassificationItemVO> itemVOList = classificationItemList.stream().map(item -> {
////                // 拷贝
////                ClassificationItemVO classificationItemVO = BeanUtil.copyProperties(item, ClassificationItemVO.class);
////                // 如果父节点是0，就是顶级节点
////                if (ObjectUtil.equals(item.getParentId(), "0")) {
////                    // 设置为“顶级”
////                    classificationItemVO.setParentName(AppConstant.TOP_PARENT_NAME);
////                } else {
////                    // 获取父级节点
////                    ClassificationItem parent = classificationItemRepository.findById(item.getParentId());
////                    // 设置父级节点名称
////                    classificationItemVO.setParentName(parent.getName());
////                }
////                return classificationItemVO;
////            }).collect(Collectors.toList());
////            List<ClassificationItemVO> treeData = classificationItemFeign.sortTree(itemVOList).getData();
////
//////            classificationItemList.forEach(classificationItem -> metadataValues.remove(classificationItem.getName()));
//////            for (String metadataValue : metadataValues) {
////                ClassificationItemVO classificationItemVO = new ClassificationItemVO();
////                classificationItemVO.setName("其他");
////                classificationItemVO.setParentName(AppConstant.TOP_PARENT_NAME);
////                treeData.add(classificationItemVO);
//////            }
//            if (ObjectUtil.isEmpty(treeData)) return;
//            JSONObject jsonObject = new JSONObject();
//            jsonObject.putOpt("name", metadata.getName());
//            jsonObject.putOpt("children", treeData);
//            jsonArray.add(jsonObject);
//        }
//    }

    /**
     * 添加枚举型元数据项及值域
     *
     * @param jsonArray        返回值
     * @param dictMetadataList 与数据项集合
     */
    public void setDictMetadata(JSONArray jsonArray, List<Metadata> dictMetadataList, Set<String> resourceIds) {
        if (ObjectUtil.isEmpty(dictMetadataList) || ObjectUtil.isEmpty(resourceIds)) return;
        for (Metadata metadata : dictMetadataList) {
            //获取当前元数据项名称
            String metadataName = metadata.getName();
            //查询所有包含该元数据项的资源
            List<Resource> resourceList = getResourcesByMetadata(metadataName, resourceIds);
            if (ObjectUtil.isEmpty(resourceList)) continue;
            //查询当前元数据项的所有使用到的值域
            Set<String> metadataValues = getMetadataValues(resourceList);
            if (ObjectUtil.isEmpty(metadataValues)) continue;

            //查询元数据项的字典以及值域
            Dict dict = dictRepository.findById(metadata.getDictId());
            List<DictItem> dictItemList = dictItemRepository.findByCondition(Filters.eq("dictId", dict.getId()));
            DictItem otherItem = new DictItem();
            otherItem.setLabel("其他");
            dictItemList.add(otherItem);
            List<JSONObject> dictItemJSONObjectList = dictItemList.stream()
                    .map(dictItem -> {
                        JSONObject jsonObject = new JSONObject(dictItem);
                        jsonObject.putOpt("name", dictItem.getLabel());
                        return jsonObject;
                    }).collect(Collectors.toList());
            if (ObjectUtil.isEmpty(dictItemJSONObjectList)) return;
            JSONObject jsonObject = new JSONObject();
            jsonObject.putOpt("name", metadata.getName());
            jsonObject.putOpt("children", dictItemJSONObjectList);

            jsonArray.add(jsonObject);
        }
    }

    /**
     * 根据元数据项名称查询resourceIds集合
     *
     * @param metadataName 元数据项名称
     * @param resourceIds  资源范围ids
     * @return
     */
    public Set<String> getIdsByMetadata(String metadataName, Set<String> resourceIds) {
        List<Resource> resourceList = getResourcesByMetadata(metadataName, resourceIds);
        return resourceList.stream().map(Resource::getId).collect(Collectors.toSet());
    }

    public List<Resource> getResourcesByMetadata(String metadataName, Set<String> resourceIds) {
        List<Bson> idsFilter = resourceIds.stream().map(id -> Filters.eq(new ObjectId(id))).collect(Collectors.toList());
        //查询所有包含该元数据项的资源
        List<Bson> firstBson = List.of(
                Filters.or(idsFilter),
                Filters.eq("isDeleted", BaseStatus.OK),
                Filters.eq("metadata.name", metadataName)
        );
        return getResourceByAggregate(firstBson);
    }

    /**
     * 查询元数据值域
     *
     * @param resourceList 资源集合
     * @return
     */
    public Set<String> getMetadataValues(List<Resource> resourceList) {
        Set<String> metadataValues = new HashSet<>();
        for (Resource resource : resourceList) {
            List<MetadataVO> metadataVOList = resource.getMetadata();
            if (ObjectUtil.isEmpty(metadataVOList)) continue;
            MetadataVO metadataVO = metadataVOList.get(0);
            List<String> values = metadataVO.getValue();
            if (ObjectUtil.isEmpty(values)) {
                metadataValues.add("其他");
            } else {
                metadataValues.add("其他");
                metadataValues.addAll(values);
            }
        }
        return metadataValues;
    }

    /**
     * 查询所有年份并排序
     *
     * @param jsonArray
     */
    public void setShowYears(JSONArray jsonArray, Set<String> ids) {
        List<Bson> idsFilter = ids.stream().map(id -> Filters.eq(new ObjectId(id))).collect(Collectors.toList());
        List<Bson> unwind = List.of(
                Aggregates.unwind("$metadata"),
                Aggregates.match(Filters.and(
                        Filters.eq("metadata.name", "年度"),
                        Filters.or(idsFilter)
                )),
                Aggregates.group("$_id", Accumulators.addToSet("metadata", "$metadata"))
        );
        List<Resource> resourceList = resourceRepository.findByAggregate(unwind);
        Set<String> yearSet = new HashSet<>();
        for (Resource resource : resourceList) {
            List<MetadataVO> metadata = resource.getMetadata();
            if (ObjectUtil.isEmpty(metadata)) continue;
            MetadataVO metadataVO = metadata.get(0);
            List<String> value = metadataVO.getValue();
            if (ObjectUtil.isNotEmpty(value)) {
                yearSet.addAll(value);
            } else {
                yearSet.add("其他");
            }
        }
        if (ObjectUtil.isNotEmpty(yearSet)) {
            List<String> yearList = new ArrayList<>(yearSet).stream().sorted(Collections.reverseOrder()).collect(Collectors.toList());
            addShowYearList(yearList, jsonArray);
        }
    }

    /**
     * 添加可以用于检索的年份信息
     *
     * @param showYears 可以展示的年份
     * @param jsonArray 返回值
     */
    public void addShowYearList(List<String> showYears, JSONArray jsonArray) {
        if (ObjectUtil.isNotEmpty(showYears)) {
            JSONArray array = new JSONArray();
            for (int i = 0; i < showYears.size(); i++) {
                JSONObject entries = new JSONObject();
                entries.set("name", showYears.get(i));
                entries.set("sort", i + 1);
                array.add(entries);
            }
            JSONObject jsonObject = new JSONObject();
            jsonObject.putOpt("name", "年度");
            jsonObject.putOpt("children", array);
            jsonArray.add(jsonObject);
        }
    }

    /**
     * 添加分类树涉及的资源数量
     *
     * @param metadataArray 分类树
     * @param jsonArray     新分类树
     * @param resourceIds   资源范围
     */
    @Override
    public void setMetadataResourceCount(JSONArray metadataArray, JSONArray jsonArray, Set<String> resourceIds) {
        Map<String, Set<String>> metadataValues = returnMetadataValues();
        for (Map.Entry<String, Set<String>> stringSetEntry : metadataValues.entrySet()) {
            log.info("元数据名称{}的值域有{}",stringSetEntry.getKey(),stringSetEntry.getValue());
        }
        metadataArray.forEach(item -> {
            //item : 每个元数据项（分类）
            JSONObject entries = JSONUtil.parseObj(item);
            //元数据项名称
            String metadataName = entries.get("name").toString();

            // 获取该元数据的值域
            Set<String> values = metadataValues.get(metadataName);
            //元数据项的值域
            Object children = entries.get("children");
            // children:子集    meetadata:元数据名称 resourceIds: 过滤的文件  value:当前文件的值域
            JSONArray countArray = setChildrenResourceCount(children, metadataName, resourceIds, values);
            int allCount = getClassifyCountByArray(countArray, 0);
            if (ObjectUtil.isNotEmpty(countArray)) {
                JSONObject jsonObject = new JSONObject();
                jsonObject.putOpt("name", metadataName);
                jsonObject.putOpt("count", allCount);
                jsonObject.putOpt("children", countArray);
                jsonArray.add(jsonObject);
            }
        });
    }

    /**
     * 根据分类查询资源数量
     *
     * @param countArray
     * @param allCount
     * @return
     */
    @Override
    public int getClassifyCountByArray(JSONArray countArray, int allCount) {
        for (Object object : countArray) {
            JSONObject entries = JSONUtil.parseObj(object);
            Object children = entries.get("children");
            if (ObjectUtil.isNotEmpty(children)) {
                getClassifyCountByArray(JSONUtil.parseArray(children), allCount);
            }
            int count = Convert.toInt(entries.getOrDefault("count", 0));
            allCount = allCount + count;
        }
        return allCount;
    }

//    // 刷新规章制度datevalue的代码
//    private void script() {
//        try{
//            QueryMetadataDTO queryMetadataDTO = retrievalDTO.getQueryCondition().get(0);
//            if (queryMetadataDTO.getValue().equals("刷新制度datevalue")) {
//                queryMetadataDTO.setName("文件类型");
//                queryMetadataDTO.setLogic("AND");
//                queryMetadataDTO.setValue("制度");
//
//                //        getRetrievalResourceWrapper(retrievalDTO, wrapper);
//                BoolQueryBuilder paramBuilder  = dynamicQueryByES(retrievalDTO);
//                SearchSourceBuilder searchSourceBuilder = new SearchSourceBuilder();
//                searchSourceBuilder.query(paramBuilder.minimumShouldMatch(1));
//                wrapper.setSearchSourceBuilder(searchSourceBuilder);
//                Integer totalSize = Convert.toInt(documentEsMapper.selectCount(wrapper));
////        wrapper.size(totalSize);
//                wrapper.maxResultWindow(totalSize);
////        String source = documentEsMapper.getSource(wrapper);
//
//                EsPageInfo<Document> gzzd = documentEsMapper.pageQuery(wrapper, 1, 10000);
//
//                // 模拟数据
////                Document document1 = gzzd.getList().stream().filter(ele -> ele.getId().equals("66d013bba58a6570534cd369")).findFirst().get();
////                List<org.irm.lab.front.model.MetadataVO> metadata1 = document1.getMetadata();
////                metadata1.removeIf(ele -> ele.getName().equals("生效日期"));
////                metadata1.add(new org.irm.lab.front.model.MetadataVO().setName("生效日期").setValue(List.of("2023-01-01")).setSort(10));
////                documentEsMapper.updateById(document1);
//
////                gzzd.getList().forEach(zd->{
////                    List<org.irm.lab.front.model.MetadataVO> metadata = zd.getMetadata();
////                    metadata.removeIf(ele -> ele.getName().equals("生效日期"));
////                    documentEsMapper.updateById(zd);
////                });
////
//                log.info("开始刷新规章制度，共有{}", gzzd.getTotal());
//                for (Document document : gzzd.getList()) {
//                    List<org.irm.lab.front.model.MetadataVO> metadata = document.getMetadata();
//                    if (ObjectUtil.isNotEmpty(metadata)) {
//                        metadata.removeIf(ele -> ele.getName().equals("生效日期"));
//                        metadata.add(new org.irm.lab.front.model.MetadataVO().setName("生效日期").setValue(List.of("2023-01-01")).setSort(10));
//                        metadata.stream().filter(ele -> ele.getName().equals("生效日期") && ObjectUtil.isEmpty(ele.getDateValue())).findFirst().ifPresent(meta->{
//                            if(ObjectUtil.isNotEmpty(meta.getValue())){
//                                log.info("开始刷新数据datevalue：{}",document.getId());
//                                meta.setDateValue(convertStringToDate(document,meta.getValue().get(0)));
//                                documentEsMapper.updateById(document);
//                            }
//                        });
//
//                    }
//                }
//
//
//            }
//        }catch (Exception e){
//            e.printStackTrace();
//        }
//    }

    @Override
    public MyPage<ResourceRetrievalVO> advancedRetrievalPlus(String page, String size, List<AdvancedRetrievalDTO> advancedRetrievalDTOs) {
        //将List<AdvancedRetrievalDTO>转换成AdvancedRetrievalDTO
        AdvancedRetrievalDTO retrievalDTO = changeListToVO(advancedRetrievalDTOs);

        LambdaEsQueryWrapper<Document> wrapper = new LambdaEsQueryWrapper<>();
        wrapper.index(esIndexResolver.resolveEsIndexByTenant());

//        getRetrievalResourceWrapper(retrievalDTO, wrapper);
        BoolQueryBuilder paramBuilder  = dynamicQueryByES(retrievalDTO);
        SearchSourceBuilder searchSourceBuilder = new SearchSourceBuilder();
        searchSourceBuilder.query(paramBuilder.minimumShouldMatch(1));
        wrapper.setSearchSourceBuilder(searchSourceBuilder);
        Integer totalSize = Convert.toInt(documentEsMapper.selectCount(wrapper));
//        wrapper.size(totalSize);
        wrapper.maxResultWindow(totalSize);
//        String source = documentEsMapper.getSource(wrapper);

        EsPageInfo<Document> pageInfo = documentEsMapper.pageQuery(wrapper, Integer.valueOf(page), Integer.valueOf(size));
//        SAPageInfo<Document> documentSAPageInfo = documentEsMapper.searchAfterPage(wrapper, null, Integer.valueOf(size));
//        System.out.println("<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<条件查询+过滤条件" + source + ">>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>");
//        EsPageInfo<Document> pageInfo = documentEsMapper.pageQuery(wrapper, Integer.parseInt(page), Integer.parseInt(size));
        MyPage<Document> document = new MyPage<>(pageInfo.getPageNum(), pageInfo.getPageSize(), totalSize,
                pageInfo.getList());
        //封装资源集合
        List<String> ids = document.getContent().stream().map(Document::getId).collect(Collectors.toList());
        List<Resource> resourceResult = resourceRepository.findById(ids);
        List<ResourceRetrievalVO> resourceRetrievalVOS = getResourceRetrievalVOS(resourceResult);
        resourceRetrievalVOS = resourceRetrievalVOS.stream().peek(resourceRetrievalVO -> {
            List<MetadataVO> showDocument = getShowDocument(resourceRetrievalVO.getResourceId(), resourceRetrievalVO.getMetadata());
            resourceRetrievalVO.setMetadata(showDocument);
        }).collect(Collectors.toList());
        return new MyPage<>(document.getPage(),document.getSize(),document.getTotalElements(),resourceRetrievalVOS);
    }

    @Override
    public List<ClassifyArray> getRetrievalClassifyPlus(AdvanceRetrievalSearchDTO advanceRetrievalSearchDTO) {
        List<AdvancedRetrievalDTO> searchCondition = advanceRetrievalSearchDTO.getAdvancedRetrievalDTOS();
        List<String> refreshCondition = advanceRetrievalSearchDTO.getRefreshCondition();
        AdvancedRetrievalDTO retrievalDTO = changeListToVO(searchCondition);
        LambdaEsQueryWrapper<Document> wrapper = new LambdaEsQueryWrapper<>();
        wrapper.index(esIndexResolver.resolveEsIndexByTenant());
//        getRetrievalResourceWrapper(retrievalDTO, wrapper);
        BoolQueryBuilder paramBuilder  = dynamicQueryByES(retrievalDTO);
        SearchSourceBuilder searchSourceBuilder = new SearchSourceBuilder();
        searchSourceBuilder.query(paramBuilder.minimumShouldMatch(1));
        wrapper.setSearchSourceBuilder(searchSourceBuilder);
        //查询所有可用于es检索的元数据项
        List<ClassifyArray> classifyArrays = metadataFeign.filterCondition("esFilterAble").getData().toList(ClassifyArray.class);
        //设置classifyArray中的count值
        classifyArrays.forEach(classifyArray -> {
            classifyArray.setCount(documentEsMapper.selectCount(wrapper));
        });
        //设置所有待刷新的classify的count值
        classifyArrays.stream()
                .filter(classifyArray -> refreshCondition.contains(classifyArray.getName()))
                .forEach(classifyArray -> {
                    Long allChildCount = buildClassifyCount(classifyArray.getChildren(), classifyArray.getName(), retrievalDTO);
                    //添加其他选项
                    long otherCount = classifyArray.getCount() - allChildCount;
                    classifyArray.getChildren().add(new Classify().setName("其他").setCount(otherCount < 0 ? 0 : otherCount));
                });

        //删除数据结构中所有count为0的classify
        classifyArrays.stream()
                .filter(classifyArray -> refreshCondition.contains(classifyArray.getName()))
                .forEach(ele -> DocumentESServiceImpl.removeItemsWithZeroCount(ele.getChildren()));

        //不刷新显示的classify中的children设为null
        classifyArrays.stream().filter(classifyArray -> !refreshCondition.contains(classifyArray.getName()))
                .forEach(classifyArray -> {
                    classifyArray.setChildren(new ArrayList<>());
                });
        return classifyArrays;
    }

    //设置classify的count值
    private Long buildClassifyCount(List<Classify> classifies, String metaName, AdvancedRetrievalDTO retrievalDTO) {
        AtomicLong count = new AtomicLong();
        classifies.sort(Comparator.comparingInt((Classify c) -> Integer.parseInt(c.getSort())).reversed());
        classifies.stream().sorted(Comparator.comparing(Classify::getSort)).forEach(classify -> {
            LambdaEsQueryWrapper<Document> wrapper = new LambdaEsQueryWrapper<>();
            wrapper.index(esIndexResolver.resolveEsIndexByTenant());
            BoolQueryBuilder paramBuilder  = dynamicQueryByES(retrievalDTO);
            SearchSourceBuilder searchSourceBuilder = new SearchSourceBuilder();

            paramBuilder.must(QueryBuilders.nestedQuery("metadata",
                    QueryBuilders.boolQuery()
                            .must(QueryBuilders.boolQuery()
                                    .must(QueryBuilders.termQuery("metadata.name",metaName))
                                    .must(QueryBuilders.wildcardQuery("metadata.value", "*" + classify.getName() +"*"))
                            ), ScoreMode.Total
            ));
            searchSourceBuilder.query(paramBuilder.minimumShouldMatch(1));
            wrapper.setSearchSourceBuilder(searchSourceBuilder);
            Long classifyCount = documentEsMapper.selectCount(wrapper);
            wrapper.select(Document::getId);
            classify.setCount(classifyCount);
            count.addAndGet(classifyCount);
            if (!classify.getChildren().isEmpty())
                buildClassifyCount(classify.getChildren(), metaName, retrievalDTO);
        });
        return count.get();
    }

    private void buildAndNotPreciseESWrapper(LambdaEsQueryWrapper<Document> wrapper, List<QueryMetadataDTO> andNotPreciseCondition) {
        if(!andNotPreciseCondition.isEmpty()){
            for (QueryMetadataDTO queryMetadataDTO : andNotPreciseCondition) {
                wrapper.should(v2->wrapper.must(v1->wrapper.nested("metadata",w -> w.eq("metadata.name", queryMetadataDTO.getName())
                        .like("metadata.value",queryMetadataDTO.getValue())
                )));
            }
        }
    }

    private void buildNotNoPreciseCondition(LambdaEsQueryWrapper<Document> wrapper, List<QueryMetadataDTO> notNoPreciseCondition) {
        if(!notNoPreciseCondition.isEmpty()){
            for (QueryMetadataDTO queryMetadataDTO : notNoPreciseCondition) {
                wrapper.should(v->wrapper.must(v1->wrapper.nested("metadata",w -> w.eq("metadata.name", queryMetadataDTO.getName())
                        .not().like("metadata.value",queryMetadataDTO.getValue())
                )));
            }
        }
    }

    private void buildNotPreciseCondition(LambdaEsQueryWrapper<Document> wrapper, List<QueryMetadataDTO> notPreciseCondition) {
        if(!notPreciseCondition.isEmpty()){
            for (QueryMetadataDTO queryMetadataDTO : notPreciseCondition) {
                wrapper.should(v->wrapper.must(v1->wrapper.nested("metadata",w -> w.eq("metadata.name", queryMetadataDTO.getName())
                        .not().in("metadata.value",List.of(queryMetadataDTO.getValue()))
                )));
            }
        }
    }

    private void buildOrNotPreciseCondition(LambdaEsQueryWrapper<Document> wrapper, List<QueryMetadataDTO> orNotPreciseCondition) {

        if (!orNotPreciseCondition.isEmpty()) {
            for (QueryMetadataDTO queryMetadataDTO : orNotPreciseCondition) {
                wrapper.should(v->v.nested("metadata", w -> w.eq("metadata.name", queryMetadataDTO.getName())
                        .like("metadata.value", queryMetadataDTO.getValue())
                ));
            }
        }

//        if(!orNotPreciseCondition.isEmpty()){
//
//            for (QueryMetadataDTO queryMetadataDTO : orNotPreciseCondition) {
////                boolQueryBuilder.should(QueryBuilders.nestedQuery("metadata",
////                        QueryBuilders.boolQuery()
////                                .must(QueryBuilders.boolQuery()
////                                        .must(QueryBuilders.termQuery("metadata.name", queryMetadataDTO.getName()))
////                                        .must(QueryBuilders.wildcardQuery("metadata.value", queryMetadataDTO.getValue()))
////                                ).minimumShouldMatch(1), ScoreMode.Total
////                ));
//                wrapper.should(v->wrapper.nested("metadata",w -> w.eq("metadata.name", queryMetadataDTO.getName())
//                        .in("metadata.value",List.of(queryMetadataDTO.getValue()))
//                ));
//            }
//        }
//        return boolQueryBuilder;
    }

    private void buildOrPreciseCondition(LambdaEsQueryWrapper<Document> wrapper, List<QueryMetadataDTO> orPreciseCondition) {
        if (!orPreciseCondition.isEmpty()) {

            for (QueryMetadataDTO queryMetadataDTO : orPreciseCondition) {
//                boolQueryBuilder.should(QueryBuilders.nestedQuery("metadata",
//                        QueryBuilders.boolQuery()
//                                .must(QueryBuilders.boolQuery()
//                                        .must(QueryBuilders.termQuery("metadata.name", queryMetadataDTO.getName()))
//                                        .must(QueryBuilders.wildcardQuery("metadata.value", queryMetadataDTO.getValue()))
//                                ).minimumShouldMatch(1), ScoreMode.Total
//                ));
                wrapper.should(v -> wrapper.nested("metadata", w -> w.eq("metadata.name", queryMetadataDTO.getName())
                        .in("metadata.value", List.of(queryMetadataDTO.getValue()))
                ));
            }
        }
    }


    private void buildAndPreciseESWrapper(LambdaEsQueryWrapper<Document> wrapper, List<QueryMetadataDTO> andPreciseCondition) {
        if(!andPreciseCondition.isEmpty()){
            for (QueryMetadataDTO queryMetadataDTO : andPreciseCondition) {
                wrapper.should(v->wrapper.must(v1->wrapper.nested("metadata",w -> w.eq("metadata.name", queryMetadataDTO.getName())
                        .in("metadata.value",queryMetadataDTO.getValue())
                )));
            }
        }
    }

    private void buildAndFilterPreciseESWrapper(LambdaEsQueryWrapper<Document> wrapper, Map<String, List<FilterMetadataDTO>> filterAndQueryCondition){
        List<FilterMetadataDTO> inFilter = filterAndQueryCondition.getOrDefault("inFilter", new ArrayList<>());
        List<FilterMetadataDTO> notInFilter = filterAndQueryCondition.getOrDefault("notInFilter", new ArrayList<>());
        if (ObjectUtil.isNotEmpty(inFilter)) {
            for (FilterMetadataDTO filterMetadataDTO : inFilter) {
//                boolQueryBuilder.must(QueryBuilders.nestedQuery("metadata",
//                        QueryBuilders.boolQuery()
//                                .must(QueryBuilders.boolQuery()
//                                        .must(QueryBuilders.termQuery("metadata.name", filterMetadataDTO.getName()))
//                                        .must(QueryBuilders.wildcardQuery("metadata.value", filterMetadataDTO.getValues().get(0)))
//                                ), ScoreMode.Total
//                ));
                wrapper.nested("metadata", w -> w.eq("metadata.name", filterMetadataDTO.getName())
                        .like("metadata.value", filterMetadataDTO.getValues().get(0)));
            }
        }

            if (ObjectUtil.isNotEmpty(notInFilter)) {
                //制度的notIn单独处理（去掉一级，只保留二级）
                for (FilterMetadataDTO dto : notInFilter) {
                    List<String> values = dto.getValues();
                    for (int i = 0; i < values.size(); i++) {
                        String value = values.get(i);
                        if (value.contains("制度") && value.split("/").length > 1) {
                            values.set(i, value.split("/")[1]);
                        }
                    }
                }
            }
        if (ObjectUtil.isNotEmpty(notInFilter)) {
            for (FilterMetadataDTO filterMetadataDTO : notInFilter) {
//                wrapper.and(
//                        v -> wrapper.nested(
//                                "metadata", w -> w.eq("metadata.name", filterMetadataDTO.getName())
//                                        .not().in("metadata.value", filterMetadataDTO.getValues())
//                        )
//                );
                wrapper.nested(
                        "metadata", w -> w.eq("metadata.name", filterMetadataDTO.getName())
                                .not().in("metadata.value", filterMetadataDTO.getValues())
                );
//                for (String value : filterMetadataDTO.getValues()) {
//                    boolQueryBuilder.must(QueryBuilders.nestedQuery("metadata",
//                            QueryBuilders.boolQuery()
//                                    .must(QueryBuilders.boolQuery()
//                                            .must(QueryBuilders.termQuery("metadata.name", filterMetadataDTO.getName()))
//                                            .mustNot(QueryBuilders.termQuery("metadata.value", value))
//                                ), ScoreMode.Total
//                     ));
//                }

            }
        }

//        return boolQueryBuilder;
    }

    public JSONArray setChildrenResourceCount(Object children, String metadataName, Set<String> ids,Set<String> values) {
        JSONArray jsonArray = new JSONArray();
        JSONArray parseArray = JSONUtil.parseArray(children);
        for (Object object : parseArray) {
            JSONObject jsonObject = JSONUtil.parseObj(object);
            //获取当前值域有没有下级，若有下级，递归查询
            Object downChildren = jsonObject.getOrDefault("children", "");
            if (ObjectUtil.isNotEmpty(downChildren)) {
                JSONArray childrenArray = setChildrenResourceCount(downChildren, metadataName, ids,values);
                childrenArray = ObjectUtil.isEmpty(childrenArray) ? new JSONArray() : childrenArray;
                jsonObject.putOpt("children", childrenArray);
            }
            //获取值域名称
            String name = jsonObject.get("name").toString();
            log.info("name{}",name);
            Metadata metadata = metadataRepository.findOne(Filters.and(Filters.eq("name", metadataName)));
            if(jsonObject.get("parentId") != null && !jsonObject.get("parentName").toString().equals("制度")){
                name = queryMetadataAllPath(name,metadataName);
            }
            Set<String> resourceIds = getResourceIdsByMetadataNameAndValue(metadataName, List.of(name), ids, values);
            log.info("查询参数metadataName:{},name:{},数量为{}",metadataName,name,resourceIds.size());
            if (ObjectUtil.isEmpty(resourceIds) && ObjectUtil.isEmpty(jsonObject.get("children"))) continue;
            int count = resourceIds.size();

            if(count == 0 && metadata.getDatatype().equals("枚举型")){ continue;
            }else if(count == 0 && metadata.getDatatype().equals("类目型")){
                JSONArray array = JSONUtil.parseArray(jsonObject.get("children"));
                if(array.isEmpty()){ continue;
                }else{
                    int counts = 0;
                    for (Object o : array) {
                        JSONObject entries = JSONUtil.parseObj(o);
                        int count1 = entries.getInt("count",0);
                        counts += count1;
                    }
                    jsonObject.putOpt("name",jsonObject.get("name").toString());
                    jsonObject.putOpt("count",counts);
                    jsonArray.add(jsonObject);
                    continue;
                }

            }else{
                jsonObject.putOpt("name", jsonObject.get("name").toString());
                jsonObject.putOpt("count", count);
                jsonArray.add(jsonObject);
            }

        }
        return jsonArray;
    }

    public BoolQueryBuilder dynamicQueryByES(AdvancedRetrievalDTO retrievalDTO) {
        //ES查询
        BoolQueryBuilder paramBuilder = QueryBuilders.boolQuery();


        BoolQueryBuilder shouldBuilders = QueryBuilders.boolQuery();
        List<BoolQueryBuilder> orBoolQueryBuilder = new ArrayList<BoolQueryBuilder>();

        BoolQueryBuilder filterBuilder = QueryBuilders.boolQuery();
        // 先获取查询条件
        List<QueryMetadataDTO> queryCondition = retrievalDTO.getQueryCondition();
        // 获取过滤条件
        List<FilterMetadataDTO> filterCondition = retrievalDTO.getFilterCondition();
        List<QueryMetadataDTO> timeCondition = retrievalDTO.getTimeCondition();
        //将所有查询条件按照  and   or   not  区分
        // and 日期
        List<QueryMetadataDTO> andTimeCondition = timeCondition.stream().filter(timeMetadataDTO -> "AND".equals(timeMetadataDTO.getLogic())).collect(Collectors.toList());
        // or 日期
        List<QueryMetadataDTO> orTimeCondition = timeCondition.stream().filter(timeMetadataDTO -> "OR".equals(timeMetadataDTO.getLogic())).collect(Collectors.toList());
        // not 日期
        List<QueryMetadataDTO> notTimeCondition = timeCondition.stream().filter(timeMetadataDTO -> "NOT".equals(timeMetadataDTO.getLogic())).collect(Collectors.toList());
        // AND 且 精确
        List<QueryMetadataDTO> andPreciseCondition = queryCondition.stream().filter(queryMetadataDTO -> "AND".equals(queryMetadataDTO.getLogic()) && queryMetadataDTO.isPrecise()).collect(Collectors.toList());
        // AND 且 模糊
        List<QueryMetadataDTO> andNotPreciseCondition = queryCondition.stream().filter(queryMetadataDTO -> "AND".equals(queryMetadataDTO.getLogic()) && !queryMetadataDTO.isPrecise()).collect(Collectors.toList());
        // OR 且 精确
        List<QueryMetadataDTO> orPreciseCondition = queryCondition.stream().filter(queryMetadataDTO -> "OR".equals(queryMetadataDTO.getLogic()) && queryMetadataDTO.isPrecise()).collect(Collectors.toList());
        // OR 且 模糊
        List<QueryMetadataDTO> orNotPreciseCondition = queryCondition.stream().filter(queryMetadataDTO -> "OR".equals(queryMetadataDTO.getLogic()) && !queryMetadataDTO.isPrecise()).collect(Collectors.toList());
        // NOT 且 精确
        List<QueryMetadataDTO> notPreciseCondition = queryCondition.stream().filter(queryMetadataDTO -> "NOT".equals(queryMetadataDTO.getLogic()) && queryMetadataDTO.isPrecise()).collect(Collectors.toList());
        // NOT 且 模糊
        List<QueryMetadataDTO> notNoPreciseCondition = queryCondition.stream().filter(queryMetadataDTO -> "NOT".equals(queryMetadataDTO.getLogic()) && !queryMetadataDTO.isPrecise()).collect(Collectors.toList());

        Map<String, List<FilterMetadataDTO>> filterAndQueryCondition = documentESService.getFilterMap(filterCondition, "esFilterAble");

        andNotPreciseCondition.forEach(and -> {
            String value = and.getValue();
            if (ObjectUtil.isEmpty(value)) {
                return;
            }

            shouldBuilders.must(QueryBuilders.nestedQuery("metadata",
                    QueryBuilders.boolQuery()
                            .must(QueryBuilders.boolQuery()
                                    .must(QueryBuilders.termQuery("metadata.name", and.getName()))
                                    .must(QueryBuilders.wildcardQuery("metadata.value", "*" + and.getValue() + "*"))
                            ), ScoreMode.Total
            ));
        });



        andTimeCondition.forEach(and -> {
            String startTime = and.getStartTime();
            String endTime = and.getEndTime();
            if (ObjectUtil.isEmpty(startTime) || ObjectUtil.isEmpty(endTime)) {
                return;
            }
            Map<String, String> timeMap = convertUTCTime(startTime, endTime);
            String start = timeMap.get("start");
            String end = timeMap.get("end");

            shouldBuilders.must(QueryBuilders.nestedQuery("metadata",
                    QueryBuilders.boolQuery()
                            .must(QueryBuilders.boolQuery()
                                    .must(QueryBuilders.termQuery("metadata.name", and.getName()))
                                    .must(QueryBuilders.rangeQuery("metadata.dateValue").from(start).to(end))
                            ), ScoreMode.Total
            ));
        });

        andPreciseCondition.forEach(and -> {
            String value = and.getValue();
            if (ObjectUtil.isEmpty(value)) {
                return;
            }

            shouldBuilders.must(QueryBuilders.nestedQuery("metadata",
                    QueryBuilders.boolQuery()
                            .must(QueryBuilders.boolQuery()
                                    .must(QueryBuilders.termQuery("metadata.name", and.getName()))
                                    .must(QueryBuilders.termQuery("metadata.value", and.getValue()))
                            ), ScoreMode.Total
            ));
        });

        orNotPreciseCondition.forEach(or -> {
            String value = or.getValue();
            if (ObjectUtil.isEmpty(value)) {
                return;
            }
            BoolQueryBuilder orParamBuilder = QueryBuilders.boolQuery();
            orParamBuilder.must(QueryBuilders.nestedQuery("metadata",
                    QueryBuilders.boolQuery()
                            .must(QueryBuilders.boolQuery()
                                    .must(QueryBuilders.termQuery("metadata.name", or.getName()))
                                    .must(QueryBuilders.wildcardQuery("metadata.value", "*" +or.getValue() + "*" ))
                            ), ScoreMode.Total
            ));
            orBoolQueryBuilder.add(orParamBuilder);
        });

        orTimeCondition.forEach(or -> {
            String startTime = or.getStartTime();
            String endTime = or.getEndTime();
            if (ObjectUtil.isEmpty(startTime) || ObjectUtil.isEmpty(endTime)) {
                return;
            }
            Map<String, String> timeMap = convertUTCTime(startTime, endTime);
            String start = timeMap.get("start");
            String end = timeMap.get("end");

            BoolQueryBuilder orParamBuilder = QueryBuilders.boolQuery();
            orParamBuilder.must(QueryBuilders.nestedQuery("metadata",
                    QueryBuilders.boolQuery()
                            .must(QueryBuilders.boolQuery()
                                    .must(QueryBuilders.termQuery("metadata.name", or.getName()))
                                    .must(QueryBuilders.rangeQuery("metadata.dateValue").from(start).to(end))
                            ), ScoreMode.Total
            ));
            orBoolQueryBuilder.add(orParamBuilder);
        });

        orPreciseCondition.forEach(or -> {
            String value = or.getValue();
            if (ObjectUtil.isEmpty(value)) {
                return;
            }
            BoolQueryBuilder orParamBuilder = QueryBuilders.boolQuery();
            orParamBuilder.must(QueryBuilders.nestedQuery("metadata",
                    QueryBuilders.boolQuery()
                            .must(QueryBuilders.boolQuery()
                                    .must(QueryBuilders.termQuery("metadata.name", or.getName()))
                                    .must(QueryBuilders.termQuery("metadata.value", or.getValue()))
                            ), ScoreMode.Total
            ));
            orBoolQueryBuilder.add(orParamBuilder);
        });

        notNoPreciseCondition.forEach(not -> {
            String value = not.getValue();
            if (ObjectUtil.isEmpty(value)) {
                return;
            }

            shouldBuilders.mustNot(QueryBuilders.nestedQuery("metadata",
                    QueryBuilders.boolQuery()
                            .must(QueryBuilders.boolQuery()
                                    .must(QueryBuilders.termQuery("metadata.name", not.getName()))
                                    .must(QueryBuilders.wildcardQuery("metadata.value", "*" + not.getValue() + "*"))
                            ), ScoreMode.Total
            ));
        });

        notTimeCondition.forEach(not -> {
            String startTime = not.getStartTime();
            String endTime = not.getEndTime();
            if (ObjectUtil.isEmpty(startTime) || ObjectUtil.isEmpty(endTime)) {
                return;
            }
            Map<String, String> timeMap = convertUTCTime(startTime, endTime);
            String start = timeMap.get("start");
            String end = timeMap.get("end");

            shouldBuilders.mustNot(QueryBuilders.nestedQuery("metadata",
                    QueryBuilders.boolQuery()
                            .must(QueryBuilders.boolQuery()
                                    .must(QueryBuilders.termQuery("metadata.name", not.getName()))
                                    .must(QueryBuilders.rangeQuery("metadata.dateValue").from(start).to(end))
                            ), ScoreMode.Total
            ));
        });

        notPreciseCondition.forEach(not -> {
            String value = not.getValue();
            if (ObjectUtil.isEmpty(value)) {
                return;
            }

            shouldBuilders.mustNot(QueryBuilders.nestedQuery("metadata",
                    QueryBuilders.boolQuery()
                            .must(QueryBuilders.boolQuery()
                                    .must(QueryBuilders.termQuery("metadata.name", not.getName()))
                                    .must(QueryBuilders.termQuery("metadata.value", not.getValue()))
                            ), ScoreMode.Total
            ));
        });

        List<FilterMetadataDTO> inFilter = filterAndQueryCondition.getOrDefault("inFilter", new ArrayList<>());
        List<FilterMetadataDTO> notInFilter = filterAndQueryCondition.getOrDefault("notInFilter", new ArrayList<>());
        if (ObjectUtil.isNotEmpty(inFilter)) {
            for (FilterMetadataDTO filterMetadataDTO : inFilter) {
                filterBuilder.must(QueryBuilders.nestedQuery("metadata",
                        QueryBuilders.boolQuery()
                                .must(QueryBuilders.boolQuery()
                                        .must(QueryBuilders.termQuery("metadata.name", filterMetadataDTO.getName()))
                                        .must(QueryBuilders.wildcardQuery("metadata.value", "*" + filterMetadataDTO.getValues().get(0) +"*" ))
                                ), ScoreMode.Total
                ));

            }
        }

        if (ObjectUtil.isNotEmpty(notInFilter)) {
            //制度的notIn单独处理（去掉一级，只保留二级）
            for (FilterMetadataDTO dto : notInFilter) {
                List<String> values = dto.getValues();
                for (int i = 0; i < values.size(); i++) {
                    String value = values.get(i);
                    if (value.contains("制度") && value.split("/").length > 1) {
                        values.set(i, value.split("/")[1]);
                    }
                }
            }
        }

        if (ObjectUtil.isNotEmpty(notInFilter)) {
            for (FilterMetadataDTO filterMetadataDTO : notInFilter) {
                for (String value : filterMetadataDTO.getValues()) {
                    filterBuilder.must(QueryBuilders.nestedQuery("metadata",
                            QueryBuilders.boolQuery()
                                    .must(QueryBuilders.boolQuery()
                                            .must(QueryBuilders.termQuery("metadata.name", filterMetadataDTO.getName()))
                                            .mustNot(QueryBuilders.termQuery("metadata.value", value))
                                ), ScoreMode.Total
                     ));
                }

            }
        }

        if (shouldBuilders.hasClauses()) {
            paramBuilder.should(shouldBuilders);
        }

        for (BoolQueryBuilder boolQueryBuilder : orBoolQueryBuilder) {
            if (boolQueryBuilder.hasClauses()) {
                paramBuilder.should(boolQueryBuilder);
            }
        }

        if (filterBuilder.hasClauses()) {
            paramBuilder.must(filterBuilder);
        }

        return paramBuilder;
    }



    private Map<String,String> convertUTCTime(String startTimeStr,String endTimeStr){
        // 定义日期格式
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");

        // 解析开始和结束日期
        LocalDate startDate = LocalDate.parse(startTimeStr, formatter);
        LocalDate endDate = LocalDate.parse(endTimeStr, formatter);

        // 将开始日期转换为UTC时区的ZonedDateTime，并格式化为ISO 8601格式
        ZonedDateTime startDateTime = startDate.atStartOfDay(ZoneId.of("UTC"));
        String formattedStartTime = startDateTime.format(DateTimeFormatter.ofPattern("yyyy-MM-dd'T00:00:00Z'"));

        // 将结束日期转换为UTC时区的ZonedDateTime，并格式化为ISO 8601格式
        ZonedDateTime endDateTime = endDate.atTime(23, 59, 59).atZone(ZoneId.of("UTC"));
        String formattedEndTime = endDateTime.format(DateTimeFormatter.ofPattern("yyyy-MM-dd'T23:59:59Z'"));
        return Map.of("start",formattedStartTime,"end",formattedEndTime);
    }

    /**
     * 判断父级是否相同
     * @param parent
     * @param value
     * @return
     */
    public boolean isOrNotParent(String parent ,String value,String classificationSetId){
        ClassificationItem item = null;
        ClassificationItem parentItem = null ;
        try {
            item = classificationItemRepository.findOne(Filters.and(Filters.eq("name", value),Filters.eq("setId",classificationSetId)));
             parentItem = classificationItemRepository.findById(item.getParentId());
        } catch (Exception e) {
            return false;
        }

        return parentItem.getName().equals(parent);
    }

    /**
     * 找到类目的全值
     * @param name
     * @param metadataName
     * @return
     */
    private String queryMetadataAllPath(String name, String metadataName) {
        Bson and = Filters.and(Filters.eq("name", metadataName), Filters.eq("datatype", "类目型"), Filters.eq("esFilterAble", true));
        List<Metadata> byCondition = metadataRepository.findByCondition(and);
        if(byCondition.size() != 0){
            Metadata metadata = byCondition.get(0);
            ClassificationSet set = classificationSetRepository.findById(metadata.getClassificationSetId());
            Bson and1 = Filters.and(Filters.eq("setId", set.getId()),Filters.eq( "name", name));
            ClassificationItem item = classificationItemRepository.findOne(and1);
            if(item.getParentId().equals("0")){
                return name;
            }
            StringBuilder nameBuilder = new StringBuilder(name);
            while(!item.getParentId().equals("0")){
                item = classificationItemRepository.findById(item.getParentId());
                nameBuilder.insert(0, item.getName() + "/");
            }
            name = nameBuilder.toString();
            return name;
        }else{ //说明是枚举类型的值/   或者是类目集 但是却不是元数据检索可查询的
            return name;
        }
    }

    /**
     * 查询对应元数据值域下的资源ids
     *
     * @param metadataName  元数据名称
     * @param metadataValue 元数据值域
     * @param values
     * @return
     */
    public Set<String> getResourceIdsByMetadataNameAndValue(String metadataName, List<String> metadataValue, Set<String> ids, Set<String> values) {
//        List<Bson> idsFilter = ids.stream().map(id -> Filters.eq(new ObjectId(id))).collect(Collectors.toList());
        List<ObjectId> objectIdList = ids.stream()
                .map(ObjectId::new)
                .collect(Collectors.toList());

        Bson bson = Filters.and(
                com.mongodb.client.model.Filters.in("_id", objectIdList),
                Filters.eq("stage", RecordResourceType.STAGE_6),
                Filters.eq("metadata.name", metadataName)
        );
        Bson valueBson;

        if (metadataValue.contains("其他")) {
            if (values != null) {
                log.info("*****************{}分类中包含其他,并且值域不为空,对不在value{}中的文件进行查询********************",metadataName,values.toString());
                valueBson = Filters.or(
                        Filters.size("metadata.value", 0),
                        Filters.exists("metadata.value", false),
                        Filters.nin("metadata.value", values)
                );
            } else {
                log.info("*****************{}分类中包含其他,但值域为空进行查询********************",metadataName);
                valueBson = Filters.or(
                        Filters.size("metadata.value", 0),
                        Filters.exists("metadata.value", false)
                );
            }
        } else {
            log.info("<<<<<<<<<<<<<<<<<<<<<<查询{}元数据的值域包含{}的文件",metadataName,metadataValue.toString());
            valueBson = Filters.in("metadata.value", metadataValue);
        }
        return getIdsByAggregate(List.of(bson, valueBson));
    }

    @Override
    public Set<String> getIdsByAggregate(List<Bson> bsonList) {
        return getResourceByAggregate(bsonList).stream().map(AbstractBaseEntity::getId).collect(Collectors.toSet());
    }

    public List<Resource> getResourceByAggregate(List<Bson> bsonList) {
        List<Bson> unwind = List.of(
                Aggregates.unwind("$metadata"),
                Aggregates.match(Filters.and(bsonList)),
                Aggregates.group("$_id", Accumulators.addToSet("metadata", "$metadata"))
        );
        return resourceRepository.findByAggregate(unwind);
    }

    /**
     * 查询权限
     */

    /**
     * 获取用户可见文件的查询条件
     *
     * @return bson
     */
    @Override
    public Bson getVisibleFileByFilePermission(User user) {
        //获取当前用户的文件查看权限
        String filePermission = user.getFilePermission();

        if (PermissionConstant.BACK_FILE_ONLY_DEPARTMENT.equals(filePermission)) {
            String departmentId = user.getDepartmentId();
            R<Department> departmentR = departmentFeign.info(departmentId);
            Department department = departmentR.getData();
            //若当前用户所在部门不存在，则无可查看文件
            if (!departmentR.isSuccess() || ObjectUtil.isEmpty(department)) return null;
            return Filters.and(
                    Filters.eq("metadata.name", "所属部门"),
                    Filters.eq("metadata.value", department.getName())
            );
        } else if (PermissionConstant.BACK_FILE_UNIT_WITH_CHILDREN.equals(filePermission) || PermissionConstant.BACK_FILE_ONLY_UNIT.equals(filePermission)) {
            //获取当前用户所在的单位id、name
            String unitId = user.getUnitId();
            //获取当前单位
            R<Unit> unitR = unitFeign.info(unitId);
            Unit unit = unitR.getData();
            //若当前用户的单位不存在，则无可查看文件
            if (!unitR.isSuccess() || ObjectUtil.isEmpty(unit)) return null;
            List<Bson> bsonList = new ArrayList<>();
            Set<String> unitNames = new HashSet<>();
            unitNames.add(unit.getName());
            if (PermissionConstant.BACK_FILE_UNIT_WITH_CHILDREN.equals(filePermission)) {  //单位及以下可见
                Set<String> departmentNames = new HashSet<>();
                //获取当前单位所有下属单位id、name 和 所有下属部门名称
                getAllUnitId(unitId, unitNames, departmentNames);
                Bson departmentBson = Filters.and(
                        Filters.eq("metadata.name", "所属部门"),
                        Filters.in("metadata.value", departmentNames)
                );
                bsonList.add(departmentBson);
            }
            Bson unitBson = Filters.and(
                    Filters.in("metadata.name", List.of("所属单位", "发文单位", "收文单位")),
                    Filters.in("metadata.value", unitNames)
            );
            bsonList.add(unitBson);
            return Filters.or(bsonList);
        }
        return null;
    }

    /**
     * 递归获取所有下级单位id
     *
     * @param unitId          当前部门id
     * @param unitNames       当前单位所有下属单位
     * @param departmentNames 当前单位的所有下属部门
     * @return ids
     */
    public void getAllUnitId(String unitId, Set<String> unitNames, Set<String> departmentNames) {
        //获取当前单位的下属部门名称集合
        R<List<Department>> listR = departmentFeign.listByUnitId(unitId);
        List<Department> departmentList = listR.getData();
        if (listR.isSuccess() || ObjectUtil.isNotEmpty(departmentList)) {
            Set<String> depNames = departmentList.stream().map(Department::getName).collect(Collectors.toSet());
            departmentNames.addAll(depNames);
        }
        R<List<Unit>> unitR = unitFeign.getChildUnit(unitId);
        List<Unit> childUnits = unitR.getData();
        if (!unitR.isSuccess() || ObjectUtil.isEmpty(childUnits)) return;
        for (Unit unit : childUnits) {
            unitNames.add(unit.getName());
            getAllUnitId(unit.getId(), unitNames, departmentNames);
        }
    }

    /**
     * 将List<AdvancedRetrievalDTO>转换成AdvancedRetrievalDTO
     *
     * @param advancedRetrievalDTOs 查询条件集合
     * @return AdvancedRetrievalDTO
     */
    public AdvancedRetrievalDTO changeListToVO(List<AdvancedRetrievalDTO> advancedRetrievalDTOs) {
        AdvancedRetrievalDTO retrievalDTO = new AdvancedRetrievalDTO();
        if (ObjectUtil.isEmpty(advancedRetrievalDTOs))
            return retrievalDTO;
        List<FilterMetadataDTO> filterCondition = new ArrayList<>();
        List<QueryMetadataDTO> timeCondition = new ArrayList<>();
        List<QueryMetadataDTO> queryCondition = new ArrayList<>();
        for (AdvancedRetrievalDTO advancedRetrievalDTO : advancedRetrievalDTOs) {
            if (ObjectUtil.isNotEmpty(advancedRetrievalDTO.getFilterCondition()))
                filterCondition.addAll(advancedRetrievalDTO.getFilterCondition());
            if (ObjectUtil.isNotEmpty(advancedRetrievalDTO.getTimeCondition()))
                timeCondition.addAll(advancedRetrievalDTO.getTimeCondition());
            if (ObjectUtil.isNotEmpty(advancedRetrievalDTO.getQueryCondition()))
                queryCondition.addAll(advancedRetrievalDTO.getQueryCondition());
        }
        retrievalDTO.setFilterCondition(filterCondition);
        retrievalDTO.setQueryCondition(queryCondition);
        retrievalDTO.setTimeCondition(timeCondition);
        return retrievalDTO;
    }
}
