package org.irm.lab.front.controller.analyse;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.json.JSONObject;
import lombok.RequiredArgsConstructor;
import org.irm.lab.common.api.R;
import org.irm.lab.common.support.MyPage;
import org.irm.lab.front.dto.MeetingKnowledgeQueryDTO;
import org.irm.lab.front.service.IMeetingMinutesService;
import org.irm.lab.front.vo.MeetingDataCountVO;
import org.irm.lab.front.vo.TypeCountVO;
import org.irm.lab.front.vo.analyse.meeting.*;
import org.irm.lab.kg.entity.neo4j.node.NodeEntity;
import org.irm.lab.kg.vo.echarts.EchartsVO;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2023/6/25 9:31
 * @description 辅助分析-会议纪要
 */
@RestController
@RequestMapping("/meeting")
@RequiredArgsConstructor
public class MeetingMinutesController {
    private final IMeetingMinutesService iMeetingMinutesService;


    /**
     * 新增访问数量
     */
    @PostMapping("/visit")
    public R<Void> visitMeetingMinutes() {
        iMeetingMinutesService.visitMeetingMinutes();
        return R.success();
    }

    /**
     * 收录数据统计
     *
     * @return {@link MeetingDataCountVO}
     */
    @GetMapping("/data-count")
    public R<MeetingDataCountVO> dataCount() {
        return R.data(iMeetingMinutesService.dataCount());
    }

//    /**
//     * 获取所有会议类型
//     *
//     * @return {@link MeetingTypeEnum}
//     */
//    @GetMapping("/list-meeting-type")
//    private R<List<JSONObject>> listMeetingType() {
//        return R.data(MeetingTypeEnum.getMeetingTypeToJSONObject());
//    }

    @GetMapping("/list-meeting-type")
    private R<List<JSONObject>> listMeetingType() {
        return R.data(iMeetingMinutesService.returnMetadataValue());
    }

    /**
     * 获取所有会议年份
     * @return {@link JSONObject}
     */
    @GetMapping("/list-meeting-year")
    private R<List<String>> listMeetingYear() {
        return R.data(iMeetingMinutesService.returnMetadataValueYear());
    }

    /**
     * 最新会议
     *
     * @return {@link NodeEntity}
     */
    @GetMapping("/new-meeting")
    public R<List<NodeEntity>> newMeeting() {
        return R.data(iMeetingMinutesService.newMeeting());
    }

    /**
     * 会议查询
     *
     * @param page 当前页码
     * @param size 每页条数
     * @param type 会议类型
     * @return 会议实例
     */
    @GetMapping("/page-query")
    public R<MyPage<NodeEntityVO>> meetingQuery(@RequestParam Integer page, @RequestParam Integer size, @RequestParam String type) {
        page = ObjectUtil.isEmpty(page) ? 1 : page;
        size = ObjectUtil.isEmpty(size) ? 10 : size;
        return R.data(iMeetingMinutesService.meetingQuery(page, size, type));
    }

    /**
     * 知识查询  会议查询
     *
     * @param meetingKnowledgeQueryDTO 查询条件
     * @return {@link MeetingVO}
     */
    @GetMapping("/knowledge-query-meeting")
    public R<MyPage<MeetingVO>> knowledgeQueryMeeting(MeetingKnowledgeQueryDTO meetingKnowledgeQueryDTO) {
        return R.data(iMeetingMinutesService.knowledgeQueryMeeting(meetingKnowledgeQueryDTO));
    }

    /**
     * 知识查询  会议参与人查询
     *
     * @param meetingKnowledgeQueryDTO 查询条件
     * @return {@link MeetingVO}
     */
    @GetMapping("/knowledge-query-attendees")
    public R<MyPage<AttendeesVO>> knowledgeQueryAttendees(MeetingKnowledgeQueryDTO meetingKnowledgeQueryDTO) {
        return R.data(iMeetingMinutesService.knowledgeQueryAttendees(meetingKnowledgeQueryDTO));
    }

    /**
     * 知识查询  会议议题查询
     *
     * @param meetingKnowledgeQueryDTO 查询条件
     * @return {@link MeetingVO}
     */
    @GetMapping("/knowledge-query-issues")
    public R<MyPage<IssuesVO>> knowledgeQueryIssues(MeetingKnowledgeQueryDTO meetingKnowledgeQueryDTO) {
        return R.data(iMeetingMinutesService.knowledgeQueryIssues(meetingKnowledgeQueryDTO));
    }

    /**
     * 会议详情页
     *
     * @param meetingId 会议Id
     * @return {@link MeetingInfoVO}
     */
    @GetMapping("/info")
    public R<MeetingInfoVO> meetingInfo(@RequestParam String meetingId) {
        return R.data(iMeetingMinutesService.meetingInfo(meetingId));
    }

    /**
     * 会议议题分页查询
     *
     * @return {@link IssuesVO}
     */
    @GetMapping("/issues-page")
    public R<MyPage<IssuesVO>> issuesPage(@RequestParam Integer page, @RequestParam Integer size, @RequestParam String meetingId) {
        page = ObjectUtil.isEmpty(page) ? 1 : page;
        size = ObjectUtil.isEmpty(size) ? 10 : size;
        return R.data(iMeetingMinutesService.issuesPage(page, size, meetingId));
    }

    /**
     * 关联会议分页查询
     *
     * @return {@link MeetingVO}
     */
    @GetMapping("/relevancy-meeting-page")
    public R<MyPage<MeetingVO>> relevancyMeetingPage(@RequestParam Integer page, @RequestParam Integer size, @RequestParam String meetingId) {
        page = ObjectUtil.isEmpty(page) ? 1 : page;
        size = ObjectUtil.isEmpty(size) ? 10 : size;
        return R.data(iMeetingMinutesService.relevancyMeetingPage(page, size, meetingId));
    }

    /**
     * 关联议题分页查询
     *
     * @param page      当前页码
     * @param size      每页条数
     * @param meetingId 会议Id
     * @return {@link IssuesVO}
     */
    @GetMapping("/relevancy-issues-page")
    public R<MyPage<IssuesVO>> relevancyIssuesPage(@RequestParam Integer page, @RequestParam Integer size, @RequestParam String meetingId) {
        page = ObjectUtil.isEmpty(page) ? 1 : page;
        size = ObjectUtil.isEmpty(size) ? 10 : size;
//        return R.data(iMeetingMinutesService.relevancyIssuesPage(page, size, meetingId));
        return R.data(iMeetingMinutesService.relevancyIssuesPage2(page, size, meetingId));
    }

    /**
     * 热点议题
     *
     * @return {@link JSONObject}
     */
    @GetMapping("/hot-issues")
    public R<List<JSONObject>> hotIssues() {
        return R.data(iMeetingMinutesService.hotIssues());
    }

    /**
     * 会议图谱
     *
     * @return {@link EchartsVO}
     */
    @GetMapping("/graph")
    public R<EchartsVO> graph() {
        return R.data(iMeetingMinutesService.graph());
    }

    /**
     * 会议分布
     */
    @GetMapping("/distribution")
    public R<List<TypeCountVO>> distribution() {
        return R.data(iMeetingMinutesService.distribution());
    }

    /**
     * 会议年度分布
     */
    @GetMapping("/year-distribution")
    public R<List<JSONObject>> yearDistribution() {
        return R.data(iMeetingMinutesService.yearDistribution());
    }

    /**
     * 议题汇总
     *
     * @return 议题汇总树结构
     */
    @GetMapping("/issues-summary")
    public R<JSONObject> issuesSummary(@RequestParam String year, @RequestParam String type) {
        return R.data(iMeetingMinutesService.issuesSummary(year, type));
    }

    /**
     * 知识查询  会议跳转纪要
     *
     * @param id 节点id  查询条件
     */
    @GetMapping("/doc/query")
    public R<Map<String,String>> queryDocOfNodeEntity(String id) {
        return R.data(iMeetingMinutesService.queryDocOfNodeEntity(id));
    }

    /**
     * 议题查询会议节点列表
     */
    @GetMapping("/meeting-node/query")
    public R<Map<String,String>> queryMeetingNode(String id) {
        return R.data(iMeetingMinutesService.queryMeetingNode(id));
    }

    /**
     * 查询会议节点列表
     */
    @GetMapping("/meeting-node-list/query")
    public R<AttendeesMeetingVO> queryMeetingNodeList(@RequestParam String name, @RequestParam Integer page, @RequestParam Integer size) {
        return R.data(iMeetingMinutesService.queryMeetingNodeList(name, page, size));
    }
}
