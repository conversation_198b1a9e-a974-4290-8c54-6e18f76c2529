package org.irm.lab.front.controller;

import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONObject;
import lombok.RequiredArgsConstructor;
import org.irm.lab.common.api.R;
import org.irm.lab.front.service.ILargeDataScreenService;
import org.irm.lab.front.vo.CommendationHonorVO;
import org.irm.lab.front.vo.DashboardVO;
import org.irm.lab.front.vo.DataCountVO;
import org.irm.lab.front.vo.SituationStatisticsVO;
import org.irm.lab.front.vo.TypeCountVO;
import org.irm.lab.kg.vo.echarts.EchartsVO;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/7/6 15:42
 * @description 数据大屏控制器
 */
@RestController
@RequestMapping("/data-screen")
@RequiredArgsConstructor
public class LargeDataScreenController {

    private final ILargeDataScreenService iLargeDataScreenService;

    /**
     * 数据统计
     *
     * @return {@link DataCountVO}
     */
    @GetMapping("/data-count")
    public R<DataCountVO> dataCount() {
        return R.data(iLargeDataScreenService.dataCount());
    }

    /**
     * 表彰荣誉排行榜
     *
     * @return {@link CommendationHonorVO}
     */
    @GetMapping("/commendation-honor-list")
    public R<List<CommendationHonorVO>> commendationHonorList() {
        return R.data(iLargeDataScreenService.commendationHonorList());
    }

    /**
     * 会议情况统计
     *
     * @return {@link SituationStatisticsVO}
     */
    @GetMapping("/meeting-situation-statistics")
    public R<List<SituationStatisticsVO>> meetingSituationStatistics() {
        return R.data(iLargeDataScreenService.meetingSituationStatistics());
    }

    /**
     * 单位部门累计发文排行
     *
     * @return {@link JSONObject}
     */
    @GetMapping("/accumulated-publication-ranking")
    public R<List<JSONObject>> accumulatedPublicationRanking() {
        return R.data(iLargeDataScreenService.accumulatedPublicationRanking());
    }

    /**
     * 公文量统计
     *
     * @return {@link SituationStatisticsVO}
     */
    @GetMapping("/document-volume-statistics")
    public R<List<SituationStatisticsVO>> documentVolumeStatistics() {
        return R.data(iLargeDataScreenService.documentVolumeStatistics());
    }

    /**
     * 公文类型占比
     *
     * @return {@link TypeCountVO}
     */
    @GetMapping("/document-types-proportion")
    public R<List<TypeCountVO>> documentTypesProportion() {
        return R.data(iLargeDataScreenService.documentTypesProportion());
    }

    @GetMapping("/document-types-count")
    public R<DashboardVO> documentTypesCount() {
        return R.data(iLargeDataScreenService.documentTypesCount());
    }

    /**
     * 人员荣誉关系
     *
     * @return {@link EchartsVO}
     */
    @GetMapping("/personnel-honor-relations")
    public R<EchartsVO> personnelHonorRelations() {
        return R.data(iLargeDataScreenService.personnelHonorRelations());
    }

    /**
     * 机构/部门荣誉关系
     *
     * @return {@link EchartsVO}
     */
    @GetMapping("/department-honor-relations")
    public R<EchartsVO> departmentHonorRelations() {
        return R.data(iLargeDataScreenService.departmentHonorRelations());
    }

    /**
     * 热点查询
     *
     * @return 热点查询
     */
    @GetMapping("/hot-query")
    public R<JSONArray> hotQuery() {
        return R.data(iLargeDataScreenService.hotQuery());
    }

    /**
     * 智能问答统计
     *
     * @return 智能问答统计
     */
    @GetMapping("/intelligent-query")
    public R<JSONArray> intelligentQuery(){
        return R.data(iLargeDataScreenService.intelligentQuery());
    }
}