package org.irm.lab.front.controller.analyse;

import cn.hutool.core.convert.Convert;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.mongodb.client.model.Filters;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.irm.lab.common.api.R;
import org.irm.lab.common.constant.FiledNameConst;
import org.irm.lab.common.constant.ModelConceptConst;
import org.irm.lab.common.support.MyPage;
import org.irm.lab.config.entity.ClassificationItem;
import org.irm.lab.config.entity.ClassificationSet;
import org.irm.lab.config.repository.ClassificationItemRepository;
import org.irm.lab.config.repository.ClassificationSetRepository;
import org.irm.lab.front.model.Document;
import org.irm.lab.front.service.IRegulatoryFrameworkService;
import org.irm.lab.front.vo.RegulatoryFrameworkVO;
import org.irm.lab.front.vo.RegulatoryMapVO;
import org.irm.lab.front.vo.analyse.regulatory.ContentVO;
import org.irm.lab.kg.entity.neo4j.node.NodeEntity;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 辅助分析-规章制度
 */

@RestController
@RequestMapping("/regulatory")
@RequiredArgsConstructor
@Slf4j
public class RegulatoryFrameworkController {

    @Resource
    IRegulatoryFrameworkService regulatoryFrameworkService;
    @Resource
    ClassificationItemRepository classificationItemRepository;
    @Resource
    ClassificationSetRepository classificationSetRepository;

    /**
     * 制度地图
     */
    @GetMapping("/map")
    public R<JSONObject> map() {
        return R.data(sortRegulatory(regulatoryFrameworkService.getMapWithNeo4j()));
    }

    /**
     * 制度地图-分页查询
     */
    @GetMapping("/map-page")
    public R<MyPage<RegulatoryFrameworkVO>> mapPage(@RequestParam Integer page, @RequestParam Integer size, @RequestParam String classify) {
        return R.data(regulatoryFrameworkService.mapPageWithNeo4j(page, size, classify));
    }

    /**
     * 结果排序
     *
     * @param result
     * @return
     */
    private JSONObject sortRegulatory(JSONObject result) {
        List<ClassificationItem> itemsBySort = new ArrayList<>();
        try {
            // 查询类目集为文件类型的类目
            ClassificationSet set = classificationSetRepository.findOne(Filters.eq("name", "文件类型"));
            // 查询文件类型下的制度类目
            ClassificationItem item = classificationItemRepository.findOne(Filters.and(Filters.eq("name", ModelConceptConst.CONCEPT_ZHIDU), Filters.eq("parentId", "0"), Filters.eq("setId", set.getId())));
            // 查询制度类目下的子集类目并正序排序
            itemsBySort = classificationItemRepository.findByConditionAndSorted(Filters.and(Filters.eq("parentId", item.getId())), Filters.eq(FiledNameConst.SORT, 1));
        } catch (Exception e) {
            e.printStackTrace();
            return result;
        }
        //获取正序排序的制度类型名
        List<String> sortName = itemsBySort.stream().map(ClassificationItem::getName).collect(Collectors.toList());
        log.info("数据库中制度下子类目的排序后的内容： {}", sortName.toArray());
        // 获取第二层的secondArray
        JSONObject firstObject = (JSONObject) result.get("name");
        JSONArray secondArray = (JSONArray) firstObject.get("children");
        // 构建一个映射，存储每个name对应的排序位置
        Map<String, Integer> orderMap = new HashMap<>();
        int index = 0;
        for (String name : sortName) {
            orderMap.put(name, index++);
        }
        List<JSONObject> jsonObjectList = secondArray.toList(JSONObject.class);
        // 使用Java自带的Collections.sort进行排序
        jsonObjectList.sort(new Comparator<JSONObject>() {
            @Override
            public int compare(JSONObject o1, JSONObject o2) {
                String name1 = o1.getStr("name");
                String name2 = o2.getStr("name");
                return orderMap.getOrDefault(name1, Integer.MAX_VALUE) -
                        orderMap.getOrDefault(name2, Integer.MAX_VALUE);
            }
        });
        for (JSONObject entries : jsonObjectList) {
            log.info("排序后制度的名称为{}", entries.get("name"));
        }
        // 将排序后的List转换回JSONArray
        JSONArray sortedJsonArray = JSONUtil.parseArray(jsonObjectList);
        firstObject.putOpt("children", sortedJsonArray);
        result.putOpt("name", firstObject);
        return result;
    }

    /**
     * 获取统计数量
     * 全集团制度量、本单位制度量、有效制度总量
     */
    @GetMapping("/number")
    public R<JSONObject> getNum() {
        return R.data(regulatoryFrameworkService.getNum());
    }

    /**
     * 最新制度公布
     */
    @GetMapping("/new-regulatory")
    public R<List<JSONObject>> getNew(@RequestParam(required = false) String number, @RequestParam(required = true) String type) {
        if (StrUtil.isBlank(number)) {
            number = "6";
        }
        return R.data(regulatoryFrameworkService.getNewByNeo4j(number, type));
    }



    /**
     * 公文名称模糊分页查询
     */
    @GetMapping("/query-name")
    public R<ContentVO> findName(@RequestParam Integer page,
                                 @RequestParam Integer size,
                                 @RequestParam String name,
                                 @RequestParam(required = false) List<String> time,
                                 @RequestParam(required = false) List<String> types,
                                 @RequestParam(required = false) String sort) {
        page = ObjectUtil.isEmpty(page) ? 1 : page;
        size = ObjectUtil.isEmpty(size) ? 10 : size;
        return R.data(regulatoryFrameworkService.findName(page, size, name, time, types, sort));
    }

    /**
     * 热词查询
     */
    @GetMapping("/hot-search")
    public R<Set<String>> getHotSearch() {
        return R.data(regulatoryFrameworkService.getHotSearch());
    }

    /**
     * 实例查询
     */
    @GetMapping("/query-concept")
    public R<ContentVO> findConcept(@RequestParam Integer page,
                                    @RequestParam Integer size,
                                    @RequestParam String name,
                                    @RequestParam(required = false) List<String> time,
                                    @RequestParam(required = false) List<String> type,
                                    @RequestParam(required = false) String sort) {
        return R.data(regulatoryFrameworkService.findConcept(page, size, name, time, type, sort));
    }

    /**
     * 获取规章制度模型的所有实例
     *
     * @RequestParam
     */
    @GetMapping("/query-model-concept")
    public R<List<JSONObject>> getConceptOfModel(String type) {
        //原来的代码意义不明，现直接调取最新制度接口，返回数据
        return getNew("6", "制度");
//        return R.data(regulatoryFrameworkService.getConceptOfModel(type));
    }
}
