package org.irm.lab.front.controller;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONObject;
import io.swagger.annotations.ApiModelProperty;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.irm.lab.common.api.R;
import org.irm.lab.config.repository.SortConfigRepository;
import org.irm.lab.front.comparator.AnnexComparator;
import org.irm.lab.front.comparator.MetadataComparator;
import org.irm.lab.front.service.IResourceMessageService;
import org.irm.lab.front.vo.ResourcePageVO;
import org.irm.lab.kg.feign.AlignmentFeign;
import org.irm.lab.kg.vo.echarts.EchartsVO;
import org.irm.lab.repository.entity.Resource;
import org.irm.lab.repository.entity.ResourceAnnex;
import org.irm.lab.repository.feign.ResourceAnnexFeign;
import org.irm.lab.repository.repository.ResourceRepository;
import org.irm.lab.repository.vo.MetadataVO;
import org.springframework.web.bind.annotation.*;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import java.util.stream.IntStream;

@RestController
@RequestMapping("/resource-show")
@RequiredArgsConstructor
@Slf4j
public class ResourceMessageController {
    private final ResourceAnnexFeign resourceAnnexFeign;
    private final AlignmentFeign alignmentFeign;
    private final IResourceMessageService resourceMessageService;
    private final ResourceRepository resourceRepository;
    private final SortConfigRepository sortConfigRepository;

    /**
     * 资源详情
     *
     * @param map 查询条件
     * @return Resource
     */
    @GetMapping("/resource-info")
    public R<Resource> resourceInfo(@RequestParam Map<String, Object> map) {
        String fileType = Convert.toStr(map.getOrDefault("fileType", ""));
        String resourceId = Convert.toStr(map.getOrDefault("resourceId", ""));
        return R.data(wrapperResourceInfo(resourceMessageService.resourceInfo(resourceId, fileType)));
    }

//    public static final String[] dateMeta = {"印发日期"};

    //将题名排在第一显示,dateValue都清空
    private Resource wrapperResourceInfo(Resource resource) {
        List<MetadataVO> sortedMetadata = resource.getMetadata().stream()
                .sorted(new MetadataComparator())
                .peek(metadataVO -> {
                    metadataVO.setDateValue(null);
                    //将value中的日期值，只取yyyy-MM-dd部分
                    List<String> values = metadataVO.getValue();
                    if ("日期型".equals(metadataVO.getDatatype()) && ObjectUtil.isNotEmpty(values)) {
                        String value = values.get(0);
                        String[] splitValue = value.split(" ");
                        if (splitValue.length > 1) {
                            metadataVO.setValue(List.of(splitValue[0]));
                        }
                    }
                    //参会单位（部门），改为参会单位
                    if ("参会单位（部门）".equals(metadataVO.getName())) metadataVO.setName("参会单位");
                    if ("隶属（归档）部门名称".equals(metadataVO.getName())) metadataVO.setName("归档部门");
                })
                .collect(Collectors.toList());

        return resource.setMetadata(sortedMetadata);
    }

    /**
     * 获取当前资源的pdf地址
     *
     * @param resourceId 资源id
     * @return pdf路径
     */
    @GetMapping("/resource-pdf")
    public R<String> resourcePdf(@RequestParam String resourceId) {
        return R.data(resourceMessageService.resourcePdf(resourceId));
    }

    /**
     * 获取当前附件的pdf地址
     *
     * @param annexId 资源id
     * @return pdf路径
     */
    @GetMapping("/annex-pdf")
    public R<JSONObject> getAnnexPdf(@RequestParam String annexId) {
        return R.data(resourceMessageService.getAnnexPdf(annexId));
    }


    /**
     * 获取当前资源的附件
     *
     * @param resourceId 资源id
     * @return 附件集合
     */
    @GetMapping("/get-resource-attach")
    public R<List<ResourceAnnex>> getResourceAttach(@RequestParam String resourceId) {
        ArrayList<ResourceAnnex> result = new ArrayList<>();
        long startTime = System.nanoTime();
        final Resource byId = resourceRepository.findById(resourceId);
        final ResourceAnnex resourceAnnex = BeanUtil.copyProperties(byId, ResourceAnnex.class);
        result.add(0, resourceAnnex);
        long end = System.nanoTime();
        log.info("查询主文件放结果中耗时【{}】秒",(end-startTime)/1000000000.000);
        startTime = System.nanoTime();
        List<ResourceAnnex> resourceAnnexList = resourceAnnexFeign.listByResourceId(resourceId).getData();
        end = System.nanoTime();
        log.info("查询附件集合耗时【{}】秒",(end-startTime)/1000000000.000);
        //fix 附件为空，也将正文返回
        if (ObjectUtil.isNotEmpty(resourceAnnexList)) {
            // 分类附件和非附件
            List<ResourceAnnex> annexList = resourceAnnexList.stream()
                    .filter(r -> r.getName().contains("附件"))
                    .collect(Collectors.toList());  // 找出名字包含附件的
            startTime = System.nanoTime();
            List<ResourceAnnex> annexListSort = annexList.stream()
                    .sorted(new AnnexComparator(sortConfigRepository))
                    .collect(Collectors.toList());
            end = System.nanoTime();
            log.info("对名字中带有附件字眼的附件排序下耗时【{}】秒",(end-startTime)/1000000000.000);
            List<ResourceAnnex> nonAnnexList = resourceAnnexList.stream()
                    .filter(r -> !r.getName().contains("附件"))
                    .collect(Collectors.toList());
            startTime = System.nanoTime();

            List<ResourceAnnex> nonAnnexListSort = nonAnnexList.stream()
                    .sorted(new AnnexComparator(sortConfigRepository))
                    .collect(Collectors.toList());
            end = System.nanoTime();
            log.info("对名字中不带有附件字眼的附件排序下耗时【{}】秒",(end-startTime)/1000000000.000);
            // 查找符合条件的第一个下标
            startTime = System.nanoTime();
            int index = IntStream.range(0, nonAnnexList.size())
                    .filter(i -> nonAnnexList.get(i).getName().matches(".*(稿纸|痕迹|签报原稿|原稿|发文单|办理单|办理过程).*") ||
                            "办理过程.xml".equalsIgnoreCase(nonAnnexList.get(i).getName()))
                    .findFirst()
                    .orElse(-1);
            end = System.nanoTime();
            log.info("找索引耗时【{}】秒",(end-startTime)/1000000000.000);
            // 根据找到的下标插入附件列表
            startTime = System.nanoTime();
            if (index != -1) {
                nonAnnexListSort.addAll(index, annexListSort);
            } else {
                nonAnnexListSort.addAll(nonAnnexListSort.size(), annexListSort);
            }
            result.addAll(nonAnnexListSort);
            end = System.nanoTime();
            log.info("插入索引位置数据耗时【{}】秒",(end-startTime)/1000000000.000);
        }
        log.info("排序结果为:{}", result.stream().map(ResourceAnnex::getName).collect(Collectors.toList()));
        return R.data(result);
    }


    /**
     * 获取当前资源的知识图谱
     *
     * @param resourceId 资源id
     * @return 图谱信息
     */
    @GetMapping("/get-knowledge-atlas")
    public R<EchartsVO> getKnowledgeAtlas(@RequestParam String resourceId) {
        return R.data(alignmentFeign.visual(resourceId, null, null).getData());
    }

    /**
     * 获取当前资源的各种类别相关文件
     *
     * @param resourceId 资源ID
     * @return 获取当前资源的各种类别相关文件
     */
    @PostMapping("/count-resource-page")
    public R<ResourcePageVO> countResourcePage(@RequestParam("resourceId") String resourceId) {
        return R.data(resourceMessageService.countResourcePage(resourceId));
    }

    /**
     * 公文推荐-右上
     *
     * @param resourceId 资源ID
     */
    @PostMapping("/commendation-right-top")
    public R<JSONObject> commendationRightTop(@RequestParam("resourceId") String resourceId) {
        return R.data(resourceMessageService.commendationRightTop(resourceId));
    }

    /**
     * 公文推荐-右下
     *
     * @param resourceId 资源ID
     */
    @PostMapping("/commendation-right-down")
    public R<JSONObject> commendationRightDown(@RequestParam("resourceId") String resourceId) {
        return R.data(resourceMessageService.commendationRightDown(resourceId));
    }

    /**
     * 公文推荐-左上
     *
     * @param resourceId 资源ID
     */
    @PostMapping("/commendation-left-top")
    public R<JSONObject> commendationLeftTop(@RequestParam("resourceId") String resourceId) {
        return R.data(resourceMessageService.commendationLeftTop(resourceId));
    }

    /**
     * 公文推荐-左下
     *
     * @param resourceId 资源ID
     */
    @PostMapping("/commendation-left-down")
    public R<JSONObject> commendationLeftDown(@RequestParam("resourceId") String resourceId) {
        return R.data(resourceMessageService.commendationLeftDown(resourceId));
    }


    /**
     * 与当前公文相关的其它公文文件
     *
     * @param id 资源id
     * @return JSONObject
     */
    @PostMapping("/first-resource-page")
    public R<JSONObject> firstResourcePage(@RequestParam String id) {
        return R.data(resourceMessageService.firstResourcePage(id));
    }

    /**
     * 根据资源id查询同类型文件
     *
     * @param id 资源id
     * @return JSONObject
     */
    @PostMapping("/second-resource-page")
    public R<JSONObject> secondResourcePage(@RequestParam String id) {
        return R.data(resourceMessageService.secondResourcePage(id));
    }

    /**
     * 根据资源id查询同类型文件
     *
     * @param id 资源id
     * @return JSONObject
     */
    @PostMapping("/third-resource-page")
    public R<JSONObject> thirdResourcePage(@RequestParam String id) {
        return R.data(resourceMessageService.thirdResourcePage(id));
    }

    /**
     * 同源文件
     *
     * @param id 资源id
     * @return JSONObject
     */
    @PostMapping("/fourth-resource-page")
    public R<JSONObject> fourthResourcePage(@RequestParam String id) {
        return R.data(resourceMessageService.fourthResourcePage(id));
    }

    /**
     * 资源关联推荐
     *
     * @param resourceId 资源id
     * @return Map<String, Object>
     */
    @PostMapping("/involve-recommend")
    public R<JSONArray> involveRecommend(@RequestParam String resourceId) {
        return R.data(resourceMessageService.involveRecommend(resourceId));
    }

    /**
     * 办理流程查询
     *
     * @param resourceId 资源id
     * @return 办理流程信息
     */
    @PostMapping("/resource-process")
    public R<JSONObject> getResourceProcessNode(String resourceId) {
        return R.data(resourceMessageService.getResourceProcessNode(resourceId));
    }

    @GetMapping("/is-show")
    @ApiModelProperty(value = "是否展示")
    public R<Boolean> isShow(@RequestParam String id) {
        return R.data(resourceMessageService.isShow(id));
    }

    @GetMapping("/leader-instruction")
    @ApiModelProperty(value = "/文件批示查询")
    public R<JSONArray> leaderInstruction(@RequestParam String resourceId) {
        return R.data(resourceMessageService.leaderInstruction(resourceId));
    }

    @GetMapping("/download-url")
    @ApiModelProperty(value = "/文件下载链接获取")
    public R<String> downloadResource(@RequestParam String resourceId) {
        return R.data(resourceMessageService.getDownloadUrl(resourceId));
    }


}
