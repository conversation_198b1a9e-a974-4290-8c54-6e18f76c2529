package org.irm.lab.front.controller;

import java.util.Map;

import org.irm.lab.common.api.R;
import org.irm.lab.common.support.MyPage;
import org.irm.lab.front.dto.req.AppointmentAndDismissalReqDTO;
import org.irm.lab.front.dto.resp.AppointmentAndDismissalRespDTO;
import org.irm.lab.front.dto.resp.DocRespDto;
import org.irm.lab.front.service.AppointmentAndDismissalService;
import org.irm.lab.repository.entity.Resource;
import org.irm.lab.repository.repository.ResourceRepository;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

/**
 * 任职任免控制器
 * <AUTHOR> <br/>
 * @date 2024/6/26 <br/>
 * &#064;Copyright  博客：<a href="https://eliauku.gitee.io/">...</a>  ||  per aspera and astra <br/>
 */
@Slf4j
@RestController
@RequestMapping("/appointmentAndDismissal")
@RequiredArgsConstructor
public class AppointmentAndDismissalController {


    private final AppointmentAndDismissalService appointmentAndDismissalService;

    /**
     * 获取人员列表
     */
    @PostMapping("/getPersonList")
    public R<MyPage<AppointmentAndDismissalRespDTO>> getPersonList(@RequestBody AppointmentAndDismissalReqDTO appointmentAndDismissalReqDTO) {
        return R.data(appointmentAndDismissalService.getPersonList(appointmentAndDismissalReqDTO));
    }

    /**
     * 根据节点获取文档列表
     * @return 来源公文列表
     */
    @PostMapping("/getDocIdsByNodeId")
    public R<MyPage<DocRespDto>> getDocIdsByNodeId(@RequestBody AppointmentAndDismissalReqDTO appointmentAndDismissalReqDTO) {
        return R.data(appointmentAndDismissalService.getDocIdsByNodeId(appointmentAndDismissalReqDTO));
    }






}
