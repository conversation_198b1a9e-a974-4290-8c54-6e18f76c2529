package org.irm.lab.front.service.impl;

import cn.easyes.core.conditions.select.LambdaEsQueryWrapper;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.convert.NumberChineseFormatter;
import cn.hutool.core.date.BetweenFormatter;
import cn.hutool.core.date.DateUnit;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.XmlUtil;
import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.mongodb.client.model.Accumulators;
import com.mongodb.client.model.Aggregates;
import com.mongodb.client.model.Filters;
import lombok.RequiredArgsConstructor;


import org.bson.conversions.Bson;
import org.bson.types.ObjectId;

import org.dromara.streamquery.stream.core.stream.Steam;
import org.elasticsearch.search.sort.FieldSortBuilder;
import org.elasticsearch.search.sort.NestedSortBuilder;
import org.elasticsearch.search.sort.SortOrder;
import org.irm.lab.common.api.R;
import org.irm.lab.common.config.resolve.EsIndexResolver;
import org.irm.lab.common.constant.ExceptionMessageConst;
import org.irm.lab.common.constant.FileAuthConstant;
import org.irm.lab.common.constant.ModelConceptConst;
import org.irm.lab.common.constant.OfficialDocumentsTypeConstant;
import org.irm.lab.common.entity.AbstractBaseEntity;
import org.irm.lab.common.exception.ServiceException;
import org.irm.lab.common.pojo.BaseStatus;
import org.irm.lab.common.provider.MinioLinkProvider;
import org.irm.lab.common.utils.NetWorkFileUtil;
import org.irm.lab.config.entity.*;
import org.irm.lab.config.feign.DictItemFeign;
import org.irm.lab.config.feign.MetadataFeign;
import org.irm.lab.config.repository.LabelRepository;
import org.irm.lab.config.repository.MetadataSchemaRepository;
import org.irm.lab.config.repository.SortConfigRepository;
import org.irm.lab.config.repository.WorkTaskRepository;
import org.irm.lab.front.auth.FileAuthManager;
import org.irm.lab.front.constant.ResourceMetadataGroupConstant;
import org.irm.lab.front.enums.ConceptEnum;
import org.irm.lab.front.enums.LeadershipEnum;
import org.irm.lab.front.mapper.DocumentEsMapper;
import org.irm.lab.front.service.IAdvancedRetrievalService;
import org.irm.lab.front.service.ILeadershipInstructionService;
import org.irm.lab.front.service.IResourceMessageService;
import org.irm.lab.front.utils.SimilarityRatioUtil;
import org.irm.lab.front.vo.ResourceCommendationVO;
import org.irm.lab.front.vo.ResourcePageVO;
import org.irm.lab.front.vo.ResourceProcessNodeVO;
import org.irm.lab.kg.entity.KnowledgeConcept;
import org.irm.lab.kg.entity.neo4j.node.NodeEntity;
import org.irm.lab.kg.entity.neo4j.node.NodeRelation;
import org.irm.lab.kg.entity.processing.process.ProcessRecords;
import org.irm.lab.kg.feign.GraphMaintenanceFeign;
import org.irm.lab.kg.feign.KnowledgeConceptFeign;
import org.irm.lab.kg.repository.KnowledgeConceptRepository;
import org.irm.lab.kg.repository.neo4j.node.NodeEntityRepository;
import org.irm.lab.kg.repository.processing.process.ProcessRecordsRepository;
import org.irm.lab.repository.constant.ResourceConstant;
import org.irm.lab.repository.entity.Resource;
import org.irm.lab.repository.entity.ResourceAnnex;
import org.irm.lab.repository.entity.datasource.DatasourceResource;
import org.irm.lab.repository.feign.DatasourceResourceFeign;
import org.irm.lab.repository.feign.PeripheralInterfaceFeign;
import org.irm.lab.repository.repository.ResourceAnnexRepository;
import org.irm.lab.repository.repository.ResourceRepository;
import org.irm.lab.repository.vo.MetadataVO;
import org.irm.lab.resource.entity.Attach;
import org.irm.lab.resource.feign.AttachFeign;
import org.irm.lab.resource.feign.IOssEndPoint;
import org.irm.lab.user.provider.UserProviderFeign;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.w3c.dom.Document;

import java.io.*;
import java.text.DateFormat;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.concurrent.atomic.AtomicReference;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

@Service
@RequiredArgsConstructor
public class ResourceMessageServiceImpl implements IResourceMessageService, OfficialDocumentsTypeConstant {

    public static final String META_NAME = "公文分类";
    private static final Logger log = LoggerFactory.getLogger(ResourceMessageServiceImpl.class);
    private final GraphMaintenanceFeign graphMaintenanceFeign;
    private final NodeEntityRepository nodeEntityRepository;
    private final KnowledgeConceptRepository knowledgeConceptRepository;
    private final ResourceRepository resourceRepository;
    private final WorkTaskRepository workTaskRepository;
    private final ResourceAnnexRepository resourceAnnexRepository;
    private final MetadataSchemaRepository metadataSchemaRepository;
    private final LabelRepository labelRepository;
    private final UserProviderFeign userProviderFeign;
    private final IAdvancedRetrievalService advancedRetrievalService;
    private final MinioLinkProvider minioLinkProvider;
    private final DatasourceResourceFeign datasourceResourceFeign;
    private final PeripheralInterfaceFeign peripheralInterfaceFeign;
    private final ILeadershipInstructionService leadershipInstructionService;
    private final IOssEndPoint ossEndPoint;
    private final EsIndexResolver esIndexResolver;
    private final DocumentEsMapper documentEsMapper;
    @javax.annotation.Resource
    private ResourceAnnexRepository annexRepository;
    @Autowired
    private FileAuthManager fileAuthManager;
    private final MetadataFeign metadataFeign;

    private final ProcessRecordsRepository processRecordsRepository;

    private final SortConfigRepository sortConfigRepository;

    @javax.annotation.Resource
    private KnowledgeConceptFeign knowledgeConceptFeign;


    /**
     * 资源实例分组
     *
     * @param resourceId 资源id
     * @return Map<String, Set < NodeEntity>>
     */
    public Map<String, Set<NodeEntity>> getconceptNodeMap(String resourceId) {
        //获取资源文件中生成的所有实例
        KnowledgeConcept meetingConcept = knowledgeConceptFeign.findByConceptName(ModelConceptConst.CONCEPT_HUIYI).getData();
        R<List<NodeEntity>> nodeListR = graphMaintenanceFeign.getNodeListByResourceId(resourceId);
        List<NodeEntity> nodeEntityList = nodeListR.getData();
        // 现场需求：如果是会议，需要把会议指向的边也纳入，不考虑是否是当前docId
        List<NodeEntity> meetingNode = nodeEntityList.stream().filter(node -> meetingConcept.getId().equals(node.getConceptId())).collect(Collectors.toList());
        meetingNode.forEach(node -> {
            Set<NodeRelation> relations = nodeEntityRepository.findById(node.getId(), 1).getToEntity();
            relations.forEach(nodeRelation -> {
                NodeEntity end = nodeRelation.getEnd();
                end.setFromEntity(null);
                end.setToEntity(null);
                if (nodeEntityList.stream().noneMatch(ele -> ele.getId().equals(end.getId()))) {
                    nodeEntityList.add(end);
                }
            });
        });

        if (!nodeListR.isSuccess() || ObjectUtil.isEmpty(nodeEntityList)) return null;
        //将查询到的实例分类存储
//        nodeEntityList = traverseNode(nodeEntityList);
        Map<String, Set<NodeEntity>> conceptNodeMap = new HashMap<>();
        nodeEntityList.forEach(node -> {
            String conceptId = node.getConceptId();
            Set<NodeEntity> set = conceptNodeMap.getOrDefault(conceptId, null);
            if (ObjectUtil.isEmpty(set)) {
                conceptNodeMap.put(conceptId, Set.of(node));
            } else {
                Set<NodeEntity> nodeEntities = new HashSet<>(set);
                nodeEntities.add(node);
                conceptNodeMap.put(conceptId, nodeEntities);
            }
        });
        return conceptNodeMap;
    }

    public List<NodeEntity> traverseNode(List<NodeEntity> nodeEntityList) {
        nodeEntityList = nodeEntityList.stream().map(s -> nodeEntityRepository.findById(s.getId())).collect(Collectors.toList());
        List<NodeEntity> nodeEntities = new ArrayList<>();
        Set<String> existIds = new HashSet<>();
        for (NodeEntity nodeEntity : nodeEntityList) {
            traverseGraph(nodeEntity, nodeEntities, existIds, 5);
        }
        return nodeEntities;
    }

    private void traverseGraph(NodeEntity nodeEntity, List<NodeEntity> nodeEntitySet, Set<String> existIds, int depth) {
        if (!existIds.contains(nodeEntity.getId())) nodeEntitySet.add(nodeEntity);
        existIds.add(nodeEntity.getId());
        if (depth <= 0) return;
        Set<NodeRelation> toEntity = nodeEntity.getToEntity();
        if (toEntity == null) return;
        for (NodeRelation nodeRelation : toEntity) {
            if (!nodeRelation.isPositive()) continue;
            NodeEntity end = nodeRelation.getEnd();
            traverseGraph(end, nodeEntitySet, existIds, depth - 1);
        }
    }

    public String getClassifyByResourceOrAnnexId(String id) {
        AtomicReference<List<String>> classify = new AtomicReference<>(new ArrayList<>());
        try {
            //正文
            setClassifyName(resourceRepository.findById(id), classify);
        } catch (Exception e) {
            //附件
            setClassifyName(resourceRepository.findById(
                            annexRepository.findById(id).getResourceId()),
                    classify);
        }
        return classify.get().isEmpty() ? "" : classify.get().get(0);
    }

    private void setClassifyName(Resource resource, AtomicReference<List<String>> classify) {
        resource.getMetadata()
                .stream().filter(meta -> META_NAME.equals(meta.getName()))
                .findFirst().ifPresent(metaVO -> classify.set(metaVO.getValue()));
    }


    @Override
    public JSONObject getResourceProcessNode(String resourceId) {
        JSONObject jsonObject = new JSONObject();
        Bson idFilter = Filters.eq("resourceId", resourceId);
        final List<ProcessRecords> processRecords = processRecordsRepository.findByCondition(idFilter).stream().sorted(Comparator.comparing(vo -> Integer.parseInt(vo.getSort()))).collect(Collectors.toList());

        List<ResourceProcessNodeVO> processNodeVOS = new ArrayList<>();
        String timeDiff = "";
        if (ObjectUtil.isNotEmpty(processRecords)) {
            ProcessRecords startProcess = processRecords.get(0);
            String startTime = startProcess.getSendTime();
            ProcessRecords endProcess = processRecords.get(processRecords.size() - 1);
            String endTime = endProcess.getFinishTime();

            Date date1 = DateUtil.parse(startTime);


            Date date2 = DateUtil.parse(endTime);

            long betweenDay = DateUtil.between(date1, date2, DateUnit.DAY);
            timeDiff = DateUtil.formatBetween(betweenDay, BetweenFormatter.Level.MINUTE);
            jsonObject.putOpt("timeDiff", timeDiff);
            timeDiff = "，共消耗" + timeDiff;
        }

        jsonObject.putOpt("nodeCount", processRecords.size());
        Steam.of(processRecords).forEach(data -> {
            final ResourceProcessNodeVO resourceProcessNodeVO = new ResourceProcessNodeVO();
            resourceProcessNodeVO.setSort(Convert.toInt(data.getSort()));
            resourceProcessNodeVO.setPerson(data.getHandler());
            resourceProcessNodeVO.setOpinion(data.getContent());
            resourceProcessNodeVO.setStartTime(data.getSendTime());
            resourceProcessNodeVO.setEndTime(data.getFinishTime());
            processNodeVOS.add(resourceProcessNodeVO);
        });

        jsonObject.putOpt("summarize", "流程共进行" + processRecords.size() + "个节点" + timeDiff);
        jsonObject.putOpt("node", processNodeVOS);

        return jsonObject;
    }

    /**
     * 判断当前资源是否可查看
     *
     * @param id 资源id
     * @return boolean
     */
    @Override
    public Boolean isShow(String id) {
//        //管理员直接放行
//        User user = userProviderFeign.isAdminAndGetUser().getData();
//        if (user == null) return true;
//        //判断当前资源是否可在前台展示
//        Resource resource = resourceRepository.findById(id);
//        if (PermissionConstant.FRONT_FILE_INVISIBLE.equals(resource.getFrontStatus()))
//            throw new ServiceException(ExceptionMessageConst.NOT_HAVE_PERMISSION);
//        //获取用户的部门权限的查询bson
//        Bson currentBson = advancedRetrievalService.getVisibleFileByFilePermission(user);
//        if (currentBson == null)
//            throw new ServiceException(ExceptionMessageConst.NOT_HAVE_PERMISSION);
//        //查看当前用于有没有该资源的查看权限
//        Bson idBson = Filters.eq(new ObjectId(id));
//        Set<String> ids = advancedRetrievalService.getIdsByAggregate(List.of(currentBson, idBson));
//        if (ObjectUtil.isEmpty(ids))
//            throw new ServiceException(ExceptionMessageConst.NOT_HAVE_PERMISSION);
//        return true;

        //权限校验走FileAuthManager，业务层不要再考虑权限的具体实现
        Resource resource = resourceRepository.findById(id);
        R<Boolean> result = (R) fileAuthManager.strategySelector(resource).isShowAspect();
        return result.getData();
    }

//    public Boolean getResourcePermission(Resource resource) {
//        User user = userProviderFeign.isAdminAndGetUser().getData();
//        if (user != null) {
//            //判断当前资源是否可在前台展示
//            if (!PermissionConstant.FRONT_FILE_VISIBLE.equals(resource.getFrontStatus())) return false;
//            //获取用户的部门权限的查询bson
//            Bson currentBson = advancedRetrievalService.getVisibleFileByFilePermission(user);
//            if (currentBson == null) return false;
//            //查看当前用于有没有该资源的查看权限
//            Bson idBson = Filters.eq(new ObjectId(resource.getId()));
//            Set<String> ids = advancedRetrievalService.getIdsByAggregate(List.of(currentBson, idBson));
//            return !ObjectUtil.isEmpty(ids);
//        }
//        return true;
//    }

    /**
     * 获取资源pdf预览地址
     *
     * @param resourceId 资源id
     * @return url
     */
    @Override
    public String resourcePdf(String resourceId) {
        String url = "";
        try {
            Resource resource = resourceRepository.findById(resourceId);

            // 业务层不要再关注权限，由FileAuthManage统一管理
//            Boolean permission = getResourcePermission(resource);
//            if (permission.equals(false)) return url;
            if (ResourceConstant.LOCAL_UPLOAD.equals(resource.getImportMethod())) {
                String pdfAttachName = resource.getPdfAttachName();
                if (pdfAttachName == null)
                    throw new ServiceException(ExceptionMessageConst.RESOURCE_NOT_EXIST);
                url = minioLinkProvider.getMinioLink(pdfAttachName);
            }
            //规章制度文件走本地浏览
            if (isGzzd(resource)) {
                return minioLinkProvider.getMinioLink(resource.getPdfAttachName());
            }
            if (ResourceConstant.EXTERNAL_IMPORTS.equals(resource.getImportMethod())) {
                // 获取该资源对应的数据源对象
                DatasourceResource datasourceResource = datasourceResourceFeign.info(resource.getDatasourceResourceId()).getData();
                // 通过文件id获取甲方的预览地址
                R<String> previewResult = datasourceResourceFeign.filePreview(datasourceResource.getFileId());
                log.info("预览响应结果为:{}", previewResult);
                url = previewResult.getData();
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        return url;
    }


    private boolean isGzzd(Resource resource) {
        return resource.getMetadata().stream().anyMatch(
                metadataVO -> ("公文分类").equals(metadataVO.getName()) && metadataVO.getValue().contains("规章制度")
        );
    }

    @Override
    public JSONObject getAnnexPdf(String annexId) {
        JSONObject jsonObject = new JSONObject();
        jsonObject.putOpt("type", "pdf");
        try {
            ResourceAnnex resourceAnnex = resourceAnnexRepository.findById(annexId);
            //判断当前资源是否可在前台展示
            String resourceId = resourceAnnex.getResourceId();
//            Resource resource = resourceRepository.findById(resourceId);
//            Boolean permission = getResourcePermission(resource);
//            if (permission.equals(false)) return jsonObject;
            // 判断附件类型是否为xml
            if ("xml".equals(resourceAnnex.getMediaType())) {
                InputStream inputStream = NetWorkFileUtil.urlToInputStream(minioLinkProvider.getMinioLinkIntranet(resourceAnnex.getPrimaryFileName()));
                Document document = XmlUtil.readXML(inputStream);
                jsonObject.putOpt("type", "xml");
                jsonObject.putOpt("data", XmlUtil.toStr(document, true));
                return jsonObject;
            }
            String importMethod = resourceAnnex.getImportMethod();
            if (ResourceConstant.LOCAL_UPLOAD.equals(importMethod)) {
                String pdfAttachName = resourceAnnex.getPdfAttachName();
                if (pdfAttachName == null) return jsonObject;
                resourceAnnex.setDocImagesStatus(BaseStatus.OK);
                String url = minioLinkProvider.getMinioLinkIntranet(pdfAttachName);
                jsonObject.putOpt("data", url);
            }
            if (ResourceConstant.EXTERNAL_IMPORTS.equals(importMethod)) {
                String url = peripheralInterfaceFeign.filePreview(resourceAnnex.getDatasourceCatalogId()).getData();
                jsonObject.putOpt("data", url);
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        return jsonObject;
    }

    public List<ResourceProcessNodeVO> getResourceProcessNodeList(List<NodeRelation> relations, String dataSetId) {
        List<ResourceProcessNodeVO> processNodeVOS = new ArrayList<>();
        for (NodeRelation relation : relations) {
            ResourceProcessNodeVO resourceProcessNodeVO = new ResourceProcessNodeVO();
            //添加办理人
            resourceProcessNodeVO.setPerson(relation.getStart().getEntityName());
            //获取办理节点其他信息
            Map<String, Object> properties = relation.getProperties();
            for (String propIdentifier : properties.keySet()) {
                String[] split = propIdentifier.split("_");
                List<Label> labelList = labelRepository.findByCondition(Filters.and(
                        Filters.eq("identifier", split[1]),
                        Filters.eq("dataSetId", dataSetId),
                        Filters.eq("type", "属性")
                ));
                if (ObjectUtil.isNotEmpty(labelList)) {
                    Label label = labelList.get(0);
                    if ("办理序号".equals(label.getName())) {
                        resourceProcessNodeVO.setSort(Integer.parseInt(properties.get(propIdentifier).toString()));
                    } else if ("办理意见".equals(label.getName())) {
                        resourceProcessNodeVO.setOpinion(properties.get(propIdentifier).toString());
                    } else if ("操作名称".equals(label.getName())) {
                        resourceProcessNodeVO.setName(properties.get(propIdentifier).toString());
                    } else if ("开始日期".equals(label.getName())) {
                        resourceProcessNodeVO.setStartTime(properties.get(propIdentifier).toString());
                    } else if ("结束日期".equals(label.getName())) {
                        resourceProcessNodeVO.setEndTime(properties.get(propIdentifier).toString());
                    }
                }
            }
            processNodeVOS.add(resourceProcessNodeVO);
        }
        if (processNodeVOS.stream().anyMatch(data -> ObjectUtil.isEmpty(data.getSort()))) {
            return processNodeVOS;
        }
        return processNodeVOS.stream().sorted(Comparator.comparing(ResourceProcessNodeVO::getSort)).collect(Collectors.toList());
    }

    public static String calculateDuration(String startTime, String endTime) {
        // 确保startTime和endTime不为null
        if (startTime == null || endTime == null) {
            return "";
        }
        DateFormat df = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        try {
            startTime = startTime.replace("T", " ").replace(".000+00:00", "");
            endTime = endTime.replace("T", " ").replace(".000+00:00", "");

            Date start = df.parse(startTime);
            Date end = df.parse(endTime);

            // 计算时间差
            long differentTime = end.getTime() - start.getTime();
            long days = differentTime / (1000 * 60 * 60 * 24);
            long hours = (differentTime - days * (1000 * 60 * 60 * 24)) / (1000 * 60 * 60);
            long minutes = (differentTime - days * (1000 * 60 * 60 * 24) - hours * (1000 * 60 * 60)) / (1000 * 60);

            return days + "天" + hours + "小时" + minutes + "分";
        } catch (ParseException e) {
            throw new RuntimeException(e);
        }
    }

    /**
     * 资源关联推荐 （）
     *
     * @param resourceId 资源id
     * @return JSONArray
     */
    @Override
    public JSONArray involveRecommend(String resourceId) {
        final ArrayList<JSONObject> jsonArray = new ArrayList<>();
        //获取资源节点分类

        String resourceClassifyType = getClassifyByResourceOrAnnexId(resourceId);

        Map<String, Set<NodeEntity>> nodeMap = getconceptNodeMap(resourceId);
        if (ObjectUtil.isEmpty(nodeMap)) return new JSONArray();
        Set<Map.Entry<String, Set<NodeEntity>>> entries = nodeMap.entrySet();
        List<String> sortingOrder = ConceptEnum.filter(resourceClassifyType).getSortRules();

        for (Map.Entry<String, Set<NodeEntity>> entry : entries) {
            String key = entry.getKey();


            try {
                KnowledgeConcept knowledgeConcept = knowledgeConceptRepository.findById(key);
                log.info("-------------节点名【{}】,节点值【{}】-----------", knowledgeConcept.getName(), entry.getValue());
                JSONObject jsonObject = new JSONObject();
                JSONArray nodeArray = new JSONArray();
                List<NodeEntity> collectBySorted = entry.getValue().stream()
                        .sorted(new CustomComparator())
                        .collect(Collectors.toList());
                log.info("节点名【{}】排序后的value[{}]", knowledgeConcept.getName(), collectBySorted);
                nodeArray.addAll(collectBySorted);
                jsonObject.putOpt("key", knowledgeConcept.getName());
                jsonObject.putOpt("value", nodeArray);
                jsonArray.add(jsonObject);

            } catch (Exception e) {
                log.error("获取资源关联推荐失败,问题为「{}」", e.getMessage());

            }
        }

        final JSONArray result = new JSONArray();
        jsonArray.stream()
                .sorted(Comparator.comparingInt(o -> {
                    int index = sortingOrder.indexOf(o.getStr("key"));
                    return index == -1 ? Integer.MAX_VALUE : index;  // 如果不存在则移到最后，否则按索引排序
                }))
                .forEach(result::add);
        return result;
    }

    public static class CustomComparator implements Comparator<NodeEntity> {
        //        private Pattern pattern = Pattern.compile("[一二三四五六七八九十]+");
        private Pattern pattern = Pattern.compile("^(?:[一二三四五六七八九十]+)");

        @Override
        public int compare(NodeEntity o1, NodeEntity o2) {
            String name1 = o1.getEntityName();
            String name2 = o2.getEntityName();

            Matcher m1 = pattern.matcher(name1);
            Matcher m2 = pattern.matcher(name2);


            List<String> matches = new ArrayList<>();
            List<String> matches2 = new ArrayList<>();

            while (m1.find()) {
                matches.add(m1.group());
            }
            while (m2.find()) {
                matches2.add(m2.group());
            }

            if (ObjectUtil.isNotEmpty(matches) && ObjectUtil.isNotEmpty(matches2)) {
                int num1 = NumberChineseFormatter.chineseToNumber(matches.get(0));

                int num2 = NumberChineseFormatter.chineseToNumber(matches2.get(0));

                return Integer.compare(num1, num2);
            }
            return name1.compareTo(name2);
        }

        /**
         * 将中文数字转换为阿拉伯数字
         *
         * @param chineseNumber 中文数字
         * @return 阿拉伯数字
         */
        private int convertChineseNumberToInteger(String chineseNumber) {
            int result = 0;
            int lastNumber = 0;  // 上一个数字的值

            for (int i = 0; i < chineseNumber.length(); i++) {
                char c = chineseNumber.charAt(i);
                int currentNumber = 0;  // 当前字符表示的数字
                switch (c) {
                    case '一':
                        currentNumber = 1;
                        break;
                    case '二':
                        currentNumber = 2;
                        break;
                    case '三':
                        currentNumber = 3;
                        break;
                    case '四':
                        currentNumber = 4;
                        break;
                    case '五':
                        currentNumber = 5;
                        break;
                    case '六':
                        currentNumber = 6;
                        break;
                    case '七':
                        currentNumber = 7;
                        break;
                    case '八':
                        currentNumber = 8;
                        break;
                    case '九':
                        currentNumber = 9;
                        break;
                    case '十':
                        if (lastNumber == 0) {
                            currentNumber = 10;  // "十" 开头，如“十二”
                        } else {
                            result += lastNumber * 10;  // 前面有数字，如“二十”
                            lastNumber = 0;  // 重置lastNumber，因为已经处理
                            continue;
                        }
                        break;
                }
                lastNumber = currentNumber;
            }
            result += lastNumber;  // 加上最后一位数字
            return result;
        }
    }

    /**
     * 元数据项排序
     *
     * @param resource 资源实体
     * @return 排序后的元数据项集合
     */
    public List<MetadataVO> sortMetadataVO(Resource resource) {
        String metadataSchemaId = resource.getMetadataSchemaId();
        MetadataSchema metadataSchema = metadataSchemaRepository.findById(metadataSchemaId);
        List<MetadataItem> metadata = metadataSchema.getMetadata().stream().sorted(Comparator.comparingInt(MetadataItem::getSort)).collect(Collectors.toList());
        List<MetadataVO> resourceMetadata = resource.getMetadata();
        List<MetadataVO> sortMetadataVO = new ArrayList<>();
        metadata.forEach(metadataItem -> {
            List<MetadataVO> vos = resourceMetadata.stream()
                    .filter(metadataVO -> metadataItem.getName().equals(metadataVO.getName()))
                    .filter(metadataVO -> (ObjectUtil.isNotEmpty(metadataVO.getValue()) || metadataVO.getDateValue() != null))
                    .collect(Collectors.toList());
            if (ObjectUtil.isNotEmpty(vos)) {
                MetadataVO metadataVO = vos.get(0);
                metadataVO.setSort(metadataItem.getSort());
                sortMetadataVO.add(metadataVO);
            }
        });
        return sortMetadataVO.stream().sorted(Comparator.comparingInt(MetadataVO::getSort)).collect(Collectors.toList());
    }


    /**
     * es条目信息详情(某些元数据不显示，且只显示有值的元数据)
     *
     * @param resourceId 条目id
     * @param fileType   文件类型
     * @return
     */
    @Override
    public Resource resourceInfo(String resourceId, String fileType) {
        List<String> notShowMeta = ResourceMetadataGroupConstant.notShowMetadataGroupToMessagePage("");
        HashSet<String> distinctSet = new HashSet<>();

        if (ObjectUtil.isEmpty(fileType)) {
            Resource resource = resourceRepository.findById(resourceId);
            List<MetadataVO> metadataVOS = resource.getMetadata().stream()
                    .filter(metadataVO -> !notShowMeta.contains(metadataVO.getName()))
                    .filter(metadataVO ->
                            (ObjectUtil.isNotEmpty(metadataVO.getValue()) && ObjectUtil.isNotEmpty(metadataVO.getValue().get(0)) && !"空".equals(metadataVO.getValue().get(0))) ||
                                    ObjectUtil.isNotEmpty(metadataVO.getDateValue()))
                    .filter(metadataVO -> distinctSet.add(metadataVO.getName()))
                    .collect(Collectors.toList());
            resource.setMetadata(wrapperMetadataVo(metadataVOS));

            // 根据不同文档类型显示不同的元数据内容
            Optional<MetadataVO> docType = metadataVOS.stream().filter(metadataVO -> "公文分类".equals(metadataVO.getName()))
                    .findFirst();
            if (docType.isPresent() && CollUtil.isNotEmpty(docType.get().getValue())) {
                String value = docType.get().getValue().get(0);
                resource.setMetadata(wrapperMetadataVoPlus(resource.getMetadata(), value));
            }
            return resource;
        } else if ("附件".equals(fileType)) {
            ResourceAnnex annex = null;
            try {
                annex = resourceAnnexRepository.findById(resourceId);
                resourceId = annex.getResourceId();
                resourceInfo(resourceId, "主文件");
            } catch (Exception e) {
                e.printStackTrace();
            }
        } else {
            Resource resource = null;
            try {
                resource = resourceRepository.findById(resourceId);
            } catch (Exception e) {
                e.printStackTrace();
            }
            if (resource != null) {
                String metadataSchemaId = resource.getMetadataSchemaId();
                MetadataSchema schema = null;
                String schemaName = "other";
                try {
                    schema = metadataSchemaRepository.findById(metadataSchemaId);
                    schemaName = schema.getName();
                } catch (Exception e) {
                    e.printStackTrace();
                }

                List<MetadataVO> metadata = resource.getMetadata();
                List<MetadataVO> collect = metadata.stream()
                        .filter(metadataVO ->
                                (ObjectUtil.isNotEmpty(metadataVO.getValue()) && ObjectUtil.isNotEmpty(metadataVO.getValue().get(0)) && !"空".equals(metadataVO.getValue().get(0))) ||
                                        ObjectUtil.isNotEmpty(metadataVO.getDateValue()))
                        .filter(metadataVO -> !notShowMeta.contains(metadataVO.getName()))
                        .filter(metadataVO -> distinctSet.add(metadataVO.getName()))
                        .collect(Collectors.toList());
                resource.setMetadata(wrapperMetadataVo(collect));


                // 根据不同文档类型显示不同的元数据内容
                Optional<MetadataVO> docType = collect.stream().filter(metadataVO -> "公文分类".equals(metadataVO.getName()))
                        .findFirst();
                if (docType.isPresent() && CollUtil.isNotEmpty(docType.get().getValue())) {
                    String value = docType.get().getValue().get(0);
                    resource.setMetadata(wrapperMetadataVoPlus(resource.getMetadata(), value));
                }

                return resource;
            }
        }
        return null;
    }

    private List<MetadataVO> wrapperMetadataVoPlus(List<MetadataVO> metadataVOS, String value) {
        List<String> metadataName;
        Bson eqBson = Filters.eq("type", "全文检索详情元数据配置");
        SortConfig one = sortConfigRepository.findOne(eqBson);
        if (JSONUtil.isTypeJSONObject(one.getOrderJson())) {
            JSONObject entries = JSONUtil.parseObj(one.getOrderJson());
            JSONArray type = entries.getJSONArray(value);
            if (type == null) return metadataVOS;
            metadataName = type.toList(String.class);
            return metadataVOS.stream().filter(metadataVO -> metadataName.contains(metadataVO.getName())).collect(Collectors.toList());
        } else {
            throw new ServiceException("后台参数配置非JSON参数");
        }
    }


//    /**
//     * es条目信息详情
//     *
//     * @param resourceId 条目id
//     * @param fileType   文件类型
//     * @return
//     */
//    @Override
//    public Resource resourceInfo(String resourceId, String fileType) {
//        HashSet<String> distinctSet = new HashSet<>();
//        List<String> queryAbleMetaList = metadataFeign.queryableList().getData().stream().map(Metadata::getName).collect(Collectors.toList());
//
//        if (ObjectUtil.isEmpty(fileType)) {
//            Resource resource = resourceRepository.findById(resourceId);
////            List<MetadataVO> metadataVOList = sortMetadataVO(resource);
//            List<MetadataVO> metadataVOS = resource.getMetadata().stream()
//                    .filter(metadataVO -> distinctSet.add(metadataVO.getName()))
//                    .filter(metadataVO -> queryAbleMetaList.contains(metadataVO.getName()))
//                    .collect(Collectors.toList());
//
//            //
//            resource.setMetadata(wrapperMetadataVo(metadataVOS));
//            return resource;
//        } else if ("附件".equals(fileType)) {
//            ResourceAnnex annex = null;
//            try {
//                annex = resourceAnnexRepository.findById(resourceId);
//                resourceId = annex.getResourceId();
//                resourceInfo(resourceId, "主文件");
//            } catch (Exception e) {
//                e.printStackTrace();
//            }
//        } else {
//            Resource resource = null;
//            try {
//                resource = resourceRepository.findById(resourceId);
//            } catch (Exception e) {
//                e.printStackTrace();
//            }
//            if (resource != null) {
//                String metadataSchemaId = resource.getMetadataSchemaId();
//                MetadataSchema schema = null;
//                String schemaName = "other";
//                try {
//                    schema = metadataSchemaRepository.findById(metadataSchemaId);
//                    schemaName = schema.getName();
//                } catch (Exception e) {
//                    e.printStackTrace();
//                }
//                List<String> metadataGroup = ResourceMetadataGroupConstant.metadataGroupToMessagePage(schemaName);
//
//                List<MetadataVO> metadata = resource.getMetadata();
//                List<MetadataVO> collect = metadata.stream()
////                        .filter(metadataVO -> ObjectUtil.isNotEmpty(metadata) && metadataGroup.contains(metadataVO.getName()))
////                        .filter(metadataVO -> metadataVO.getValue() != null && !metadataVO.getValue().isEmpty())
//                        .filter(metadataVO -> queryAbleMetaList.contains(metadataVO.getName()))
//                        .filter(metadataVO -> distinctSet.add(metadataVO.getName()))
//                        .collect(Collectors.toList());
//                resource.setMetadata(wrapperMetadataVo(collect));
//                return resource;
//            }
//        }
//        return null;
//    }


    //电子文件号设置为件号、知悉范围设置为具体含义
    private List<MetadataVO> wrapperMetadataVo(List<MetadataVO> metadataVOS) {
        //电子文件号
        metadataVOS.stream().filter(metadataVO -> "电子文件号".equals(metadataVO.getName()))
                .findFirst()
                .ifPresent(metadataVO -> {
                    metadataVO.setName("件号");
                });

        //知悉范围
        metadataVOS.stream().filter(metadataVO -> "知悉范围".equals(metadataVO.getName()))
                .findFirst()
                .ifPresent(metadataVO -> {
                    List<String> value = metadataVO.getValue();
                    if (ObjectUtil.isNotEmpty(value)) {
                        if (value.contains(FileAuthConstant.KNOW_RANGE_DISABLE)) metadataVO.setValue(List.of("不可见"));
                        if (value.contains(FileAuthConstant.KNOW_RANGE_PUBLIC)) metadataVO.setValue(List.of("公开"));
                        if (value.contains(FileAuthConstant.KNOW_RANGE_DEPART))
                            metadataVO.setValue(List.of("元数据可见"));
                        if (value.contains(FileAuthConstant.KNOW_RANGE_META))
                            metadataVO.setValue(List.of("元数据可见"));
                    }
                });

        return metadataVOS;
    }

    /**
     * 查询同源文件(相同发文机构/部门)
     *
     * @param id 资源id
     * @return
     */
    public Set<String> getResourceByMaterialIssuingUnit(String id) {
        //查询当前资源信息
        Resource resource = null;
        try {
            resource = resourceRepository.findById(id);
        } catch (Exception e) {
            e.printStackTrace();
        }
        if (resource == null) return new HashSet<>();
        //获取当前资源的发文单位/机构
        List<String> units = List.of("发文机构", "发文单位");
        List<MetadataVO> metadata = resource.getMetadata();
        List<MetadataVO> unitMetadata = metadata.stream().filter(metadataVO -> List.of("发文机构", "发文单位").contains(metadataVO.getName())).collect(Collectors.toList());
        if (ObjectUtil.isEmpty(unitMetadata)) return new HashSet<>();
        Set<String> MaterialIssuingUnits = new HashSet<>();
        unitMetadata.forEach(metadataVO -> {
            if (ObjectUtil.isNotEmpty(metadataVO.getValue()))
                MaterialIssuingUnits.addAll(metadataVO.getValue());
        });
        LambdaEsQueryWrapper<org.irm.lab.front.model.Document> queryWrapper = new LambdaEsQueryWrapper<>();

        queryWrapper.index(esIndexResolver.resolveEsIndexByTenant());
        MaterialIssuingUnits.forEach(name -> {
            for (String unit : units) {
                queryWrapper.nested("metadata", nestedWrapper -> nestedWrapper
                        .eq("metadata.name", name)
                        .in("metadata.value", unit));
            }
        });

        // 执行查询
        List<org.irm.lab.front.model.Document> xx = documentEsMapper.selectList(queryWrapper);
        final List<String> collect1 = xx.stream().map(org.irm.lab.front.model.Document::getId).collect(Collectors.toList());

        List<ObjectId> objectIdList = collect1.stream()
                .map(ObjectId::new)
                .collect(Collectors.toList());

        Bson bson1 = Filters.in("_id", objectIdList);
        final List<Resource> resourceList = resourceRepository.findByCondition(bson1);

        return resourceList.stream().map(AbstractBaseEntity::getId).collect(Collectors.toSet());
    }

    public Resource getResourceById(String id) {
        //查询资源信息
        Resource resource = null;
        try {
            resource = resourceRepository.findById(id);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return resource;
    }

    /**
     * 获取元数据项的值（公文分类/关键词）
     *
     * @param resource 资源实体
     * @return 资源分类
     */
    public List<String> getResourceMetadataValue(Resource resource, String metadataName) {
        //获取公文分类，用于区分查询内容
        List<MetadataVO> divisiveMetadataVO = resource.getMetadata().stream().filter(metadataVO -> metadataVO.getName().equals(metadataName)).collect(Collectors.toList());
        List<String> metadataValue = new ArrayList<>();
        divisiveMetadataVO.forEach(metadataVO -> {
            if (ObjectUtil.isNotEmpty(metadataVO.getValue()))
                metadataValue.addAll(metadataVO.getValue());
        });
        return metadataValue;
    }

    public JSONObject getReturnObject(String title, String description, List<Resource> resources, String id) {
        JSONObject jsonObject = new JSONObject();
        jsonObject.putOpt("title", title);
        jsonObject.putOpt("description", description);
        jsonObject.putOpt("resources", resources);
        return jsonObject;
    }


    /**
     * 公文详情页关联公文位置1 ，根据不同公文类型查询不同种类的关联公文
     * * 会议纪要 ： 同类近期会议纪要
     * * 奖章荣耀 ： 历年荣誉公文
     * * 规章制度 ： 制度同类公文
     * * 领导批示 ：
     * * 其他文件 ： 根据关键词查询
     *
     * @param id 公文id
     * @return
     */
    @Override
    public JSONObject firstResourcePage(String id) {
        String title = "关联文件推荐";
        String description = "根据当前公文的关键词匹配到的相关文件";
        List<Resource> resources = new ArrayList<>();

        JSONObject jsonObject = getReturnObject(title, description, resources, id);
        //若无权限，不显示当前接口的相关信息
        if (isShow(id).equals(false)) return jsonObject;

        //获取当前资源
        Resource resource = getResourceById(id);
        if (resource == null) return jsonObject;
        //获取当前公文的分类
        List<String> fileType = getResourceMetadataValue(resource, "公文分类");
        //获取同类公文ids
        Set<String> idsByClassify = resourceByClassify(fileType);
        if (ObjectUtil.isNotEmpty(fileType)) {
            if (fileType.contains("会议纪要")) {
                title = "同类近期会议纪要";
                description = "根据当前会议纪要匹配到的相关联的近期会议文件";
                //若当前无数据，查询默认方法
                if (ObjectUtil.isNotEmpty(idsByClassify)) {
                    resources = resourceRepository.findById(idsByClassify);
                    resources = resources.stream().sorted(Comparator.comparing(Resource::getCreateTime).reversed()).collect(Collectors.toList());
                }
            } else if (fileType.contains("奖章荣誉")) {
                title = "历年荣誉公文";
                description = "与当前荣誉公文相关联的往年荣誉公文";
                //若当前无数据，查询默认方法
                if (ObjectUtil.isNotEmpty(idsByClassify)) {
                    resources = resourceRepository.findById(idsByClassify);
                    resources = resources.stream().sorted(Comparator.comparing(Resource::getCreateTime).reversed()).collect(Collectors.toList());
                }
            } else if (fileType.contains("规章制度")) {
                title = "制度同类公文";
                description = "与当前制度文件相关联的其他的制度文件";
                //获取文件关键词
                final List<String> keywords = getResourceMetadataValue(resource, "文件类型");
                log.info("文件的类型为{}", keywords.toString());
                //根据关键词查询相关文件
                List<Resource> resourceList = resourceListByKeyword(keywords);
                resources = resourceList.stream().filter(r -> idsByClassify.contains(r.getId())).collect(Collectors.toList());
            } else {
                resources = resourcePageByKeyword(resource);
            }
        } else {
            resources = resourcePageByKeyword(resource);
        }
        resources = resources.stream().filter(res -> !id.equals(res.getId()))
                .skip(0)
                .limit(10)
                .collect(Collectors.toList());
        jsonObject.putOpt("title", title);
        jsonObject.putOpt("description", description);
        jsonObject.putOpt("resources", resources);
        return jsonObject;
    }

    /**
     * 公文详情页关联公文位置2 ，根据不同公文类型查询不同种类的关联公文
     * * 会议纪要 ： 议题相关文件
     * * 奖章荣耀 ： 同人员荣誉公文
     * * 规章制度 ： 制度历史版本                  待定
     * * 领导批示 ： 同领导批示
     *
     * @param id 公文id
     * @return JSONObject
     */
    @Override
    public JSONObject secondResourcePage(String id) {
        String title = "人员关联文件";
        String description = "与当前文件中涉及人员相关联的其他文件";
        Set<String> ids = Set.of();
        List<Resource> resources = new ArrayList<>();

        JSONObject jsonObject = getReturnObject(title, description, resources, id);
        //若无权限，不显示当前接口的相关信息
        if (isShow(id).equals(false)) return jsonObject;

        //获取当前资源
        Resource resource = getResourceById(id);
        if (resource == null) return jsonObject;
        //获取当前公文的分类
        List<String> fileType = getResourceMetadataValue(resource, "公文分类");
        //获取同类公文ids
        Set<String> idsByClassify = resourceByClassify(fileType);
        if (ObjectUtil.isNotEmpty(fileType)) {
            if (fileType.contains("会议纪要")) {
                title = "议题相关公文";
                description = "与当前文件中的议题相关联的其他文件";
                Set<NodeEntity> nodeEntities = getNodeByConceptName(id, "议题");
                ids = resourceIdsByNodeConceptName(id, nodeEntities);
                ids = ids.stream().filter(idsByClassify::contains).collect(Collectors.toSet());
            } else if (fileType.contains("奖章荣誉")) {
                title = "同人员荣誉公文";
                description = "与当前文件中涉及到的受表彰人员相关联的其他文件";
                //获取实例关联公文ids
                ids = resourceIdsByNodeConceptName(id, "人员");
                ids = ids.stream().filter(idsByClassify::contains).collect(Collectors.toSet());
            } else if (fileType.contains("领导批示")) {
                // 相同领导批示的文件
                title = "同领导批示公文";
                description = "当前文件批示的领导批示过的其他的文件";
                resourceIdsByNodeConceptName(id, "人员");
            } else {
                ids = resourceIdsByNodeConceptName(id, "人员");
            }
        } else {
            ids = resourceIdsByNodeConceptName(id, "人员");
        }
        //若当前无数据，查询默认方法
        ids.remove(id);
        if (ObjectUtil.isNotEmpty(ids)) {
            resources = resourceRepository.findById(ids);
            resources = resources.stream().sorted(Comparator.comparing(Resource::getCreateTime).reversed())
                    .skip(0)
                    .limit(10)
                    .collect(Collectors.toList());
        }
        jsonObject.putOpt("title", title);
        jsonObject.putOpt("description", description);
        jsonObject.putOpt("resources", resources);
        return jsonObject;
    }

    /**
     * 公文详情页关联公文位置3 ，根据不同公文类型查询不同种类的关联公文
     * * 会议纪要 ： 近期发布公文
     * * 奖章荣耀 ： 近期发布公文
     * * 规章制度 ： 相关制度推荐
     * * 领导批示 ： 近期领导批示文件
     * * 其他文件 ： 查询同类文件
     *
     * @param id 公文id
     * @return JSONObject
     */
    @Override
    public JSONObject thirdResourcePage(String id) {
        String title = "当前文件同类公文";
        String description = "根据当前公文的类型匹配到的同种类型的文件";
        List<Resource> resources = new ArrayList<>();

        JSONObject jsonObject = getReturnObject(title, description, resources, id);
        //若无权限，不显示当前接口的相关信息
        if (isShow(id).equals(false)) return jsonObject;

        //获取当前资源
        Resource resource = getResourceById(id);
        if (resource == null) return jsonObject;
        //获取当前公文的分类
        List<String> fileType = getResourceMetadataValue(resource, "公文分类");
        //获取同类文件id
        Set<String> ids = resourceByClassify(fileType);
        if (ObjectUtil.isEmpty(ids)) return jsonObject;
        if (ObjectUtil.isNotEmpty(fileType)) {
            if (fileType.contains("会议纪要") || fileType.contains("奖章荣誉") || fileType.contains("领导批示")) {
                title = "近期同类公文推荐";
                description = "根据当前公文的类型匹配到的同种类型的最新文件";
                resources = resourceRepository.findById(ids).stream().sorted(Comparator.comparing(Resource::getCreateTime).reversed()).collect(Collectors.toList());
            } else if (fileType.contains("规章制度")) {
                title = "相关制度公文推荐";
                description = "与当前公文中提及到的规章制度相关联的其他文件";
                //获取文件关键词
                List<String> keywords = getResourceMetadataValue(resource, "关键词");
                //根据关键词查询文件集合
                List<Resource> resourceList = resourceListByKeyword(keywords);
                //筛选同类文件
                resources = resourceList.stream().filter(r -> ids.contains(r.getId())).sorted(Comparator.comparing(Resource::getCreateTime).reversed()).collect(Collectors.toList());
            } else {
                resources = resourceRepository.findById(ids);
            }
        } else {
            resources = resourceRepository.findById(ids);
        }
        resources = resources.stream().filter(res -> !id.equals(res.getId()))
                .skip(0)
                .limit(10)
                .collect(Collectors.toList());
        jsonObject.putOpt("title", title);
        jsonObject.putOpt("description", description);
        jsonObject.putOpt("resources", resources);
        return jsonObject;
    }

    /**
     * 公文详情页关联公文位置4 ，根据不同公文类型查询不同种类的关联公文
     * * 会议纪要 ： 相似议题事项会议
     * * 奖章荣耀 ： 同部门荣誉公文
     * * 规章制度 ： 同年制度发布
     * * 领导批示 ： 同事项批示文件
     *
     * @param id 公文id
     * @return JSONObject
     */
    @Override
    public JSONObject fourthResourcePage(String id) {
        String title = "当前文件同源公文";
        String description = "当前文件的发文机构/部门发布的其他的文件";
        Set<String> ids = new HashSet<>();
        List<Resource> resources = new ArrayList<>();

        JSONObject jsonObject = getReturnObject(title, description, resources, id);
        //若无权限，不显示当前接口的相关信息
        if (isShow(id).equals(false)) return jsonObject;

        //获取当前资源
        Resource resource = getResourceById(id);
        if (resource == null) return jsonObject;
        //获取当前公文的分类
        List<String> fileType = getResourceMetadataValue(resource, "公文分类");
        //获取同类公文ids
        Set<String> idsByClassify = resourceByClassify(fileType);
        if (ObjectUtil.isNotEmpty(fileType)) {
            if (fileType.contains("会议纪要")) {
                title = "相似议题事项公文";
                description = "当前文件的会议议题或事项相关联的其他文件";
                ids = sameTopicResourceId(id);
            } else if (fileType.contains("奖章荣誉")) {
                title = "同部门荣誉公文";
                description = "与当前文件中提及到的受表彰部门相关联的其他文件";
                //获取实例关联公文ids
                ids = resourceIdsByNodeConceptName(id, "机构/部门");
                ids = ids.stream().filter(idsByClassify::contains).collect(Collectors.toSet());
            } else if (fileType.contains("规章制度")) {
                title = "同年发布制度公文";
                description = "与当前公文中提及到的制度同一年发布的其他制度关联的文件";
                //获取当前年份公文的ids
                Calendar calendar = Calendar.getInstance();
                String year = Convert.toStr(calendar.get(Calendar.YEAR));
                ids = resourceInThisYear(year);
                ids = ids.stream().filter(idsByClassify::contains).collect(Collectors.toSet());
            } else if (fileType.contains("领导批示")) {
                title = "同事项批示公文";
                description = "与当前公文中提及到的批示事项相关联的的其他批示文件";
                //获取实例关联公文ids
                ids = resourceIdsByNodeConceptName(id, "事项");
                ids = ids.stream().filter(idsByClassify::contains).collect(Collectors.toSet());
            } else {
                ids = getResourceByMaterialIssuingUnit(id);
            }
        } else {
            ids = getResourceByMaterialIssuingUnit(id);
        }
        //若当前无数据，查询默认方法
        ids.remove(id);
        if (ObjectUtil.isNotEmpty(ids)) {
            resources = resourceRepository.findById(ids);
            resources = resources.stream().sorted(Comparator.comparing(Resource::getCreateTime).reversed())
                    .skip(0)
                    .limit(10)
                    .collect(Collectors.toList());
        }
        jsonObject.putOpt("title", title);
        jsonObject.putOpt("description", description);
        jsonObject.putOpt("resources", resources);
        return jsonObject;
    }

    /**
     * 获取资源指定概念的实例
     *
     * @param id          资源id
     * @param conceptName 实例概念名称
     * @return 实例集合
     */
    public Set<NodeEntity> getNodeByConceptName(String id, String conceptName) {
        Set<NodeEntity> nodeEntities = new HashSet<>();
        //获取当前资源的实例分组
        Map<String, Set<NodeEntity>> nodeGroupMap = getconceptNodeMap(id);
        if (ObjectUtil.isEmpty(nodeGroupMap)) return nodeEntities;
        for (Map.Entry<String, Set<NodeEntity>> entry : nodeGroupMap.entrySet()) {
            String conceptId = entry.getKey();
            try {
                KnowledgeConcept knowledgeConcept = knowledgeConceptRepository.findById(conceptId);
                if (knowledgeConcept.getName().contains(conceptName)) nodeEntities = entry.getValue();
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
        return nodeEntities;
    }

    /**
     * 相似议题文件ids
     *
     * @param id 当前资源id
     * @return ids
     */
    public Set<String> sameTopicResourceId(String id) {
        //获取当前资源的议题实例
        Set<NodeEntity> nodeEntities = getNodeByConceptName(id, "会议议题");
        List<String> topic = new ArrayList<>();
        nodeEntities.forEach(entity -> topic.add(entity.getEntityName()));

        Set<String> conceptIds = nodeEntities.stream().map(NodeEntity::getConceptId).collect(Collectors.toSet());
        Set<NodeEntity> entityList = new HashSet<>();
        if (ObjectUtil.isNotEmpty(conceptIds)) {
            for (String conceptId : conceptIds) {
                //获取所有议题实例
                R<List<NodeEntity>> listR = graphMaintenanceFeign.nodeList(conceptId, "0");
                List<NodeEntity> entities = listR.getData();
                if (!listR.isSuccess() || entities == null) continue;
                entityList.addAll(entities);
            }
        }
        if (ObjectUtil.isEmpty(topic) || ObjectUtil.isEmpty(entityList)) return new HashSet<>();
        //筛选相似度大于百分之五十的议题
        entityList = entityList.stream().filter(entity -> {
            float ratio = 0;
            for (String s : topic) {
                float ratioNext = SimilarityRatioUtil.getSimilarityRatio(entity.getEntityName(), s);
                if (ratioNext > ratio)
                    ratio = ratioNext;
            }
            return ratio > 50;
        }).collect(Collectors.toSet());
        //获取筛选后的实例所涉及的文件ids
        Set<String> idList = new HashSet<>();
        for (NodeEntity nodeEntity : entityList) {
            if (ObjectUtil.isNotEmpty(nodeEntity.getDocIds()))
                idList.addAll(nodeEntity.getDocIds());
        }
        //去除当前文件id
        idList.remove(id);
        return idList;
    }

    /*制度*/

    /**
     * 获取对应年度发布的文件的id集合
     *
     * @param year 年度
     * @return ids
     */
    public Set<String> resourceInThisYear(String year) {

        // 创建一个新的 LambdaEsQueryWrapper 实例
        final LambdaEsQueryWrapper<org.irm.lab.front.model.Document> queryWrapper = new LambdaEsQueryWrapper<>();
        queryWrapper.index(esIndexResolver.resolveEsIndexByTenant());
        // 这里演示了如何添加嵌套查询条件，具体方法取决于 Easy-Es 的版本和功能
        queryWrapper.nested("metadata", nestedWrapper -> nestedWrapper
                .eq("metadata.name", "年度")
                .in("metadata.value", year));

        // 执行查询
        final List<org.irm.lab.front.model.Document> xx = documentEsMapper.selectList(queryWrapper);
        final List<String> collect1 = xx.stream().map(org.irm.lab.front.model.Document::getId).collect(Collectors.toList());

        List<ObjectId> objectIdList = collect1.stream()
                .map(ObjectId::new)
                .collect(Collectors.toList());

        Bson bson1 = Filters.in("_id", objectIdList);
        final List<Resource> resourceList = resourceRepository.findByCondition(bson1);

        return resourceList.stream().map(AbstractBaseEntity::getId).collect(Collectors.toSet());
    }


    public List<Resource> getMeatDate(String name, List<String> value, List<String> ids) {

        // 创建一个新的 LambdaEsQueryWrapper 实例
        final LambdaEsQueryWrapper<org.irm.lab.front.model.Document> queryWrapper = new LambdaEsQueryWrapper<>();
        queryWrapper.index(esIndexResolver.resolveEsIndexByTenant());
        // 这里演示了如何添加嵌套查询条件，具体方法取决于 Easy-Es 的版本和功能
        queryWrapper.nested("metadata", nestedWrapper -> nestedWrapper
                .eq("metadata.name", name)
                .in(ObjectUtil.isNotEmpty(ids), org.irm.lab.front.model.Document::getId, ids)
                .in("metadata.value", value));

        queryWrapper.size(50);
        // 执行查询
        final List<org.irm.lab.front.model.Document> xx = documentEsMapper.selectList(queryWrapper);
        final List<String> collect1 = xx.stream().map(org.irm.lab.front.model.Document::getId).collect(Collectors.toList());

        List<ObjectId> objectIdList = collect1.stream()
                .map(ObjectId::new)
                .collect(Collectors.toList());

        Bson bson1 = Filters.in("_id", objectIdList);
        return resourceRepository.findByCondition(bson1);

    }

    public List<Resource> defaultMeatDate(String name, List<String> value, String name1, List<String> value1) {

        // 创建一个新的 LambdaEsQueryWrapper 实例
        final LambdaEsQueryWrapper<org.irm.lab.front.model.Document> queryWrapper = new LambdaEsQueryWrapper<>();
        queryWrapper.index(esIndexResolver.resolveEsIndexByTenant());

        queryWrapper.nested("metadata", nestedWrapper -> nestedWrapper
                .eq("metadata.name", name)
                .in("metadata.value", value)
        );

        queryWrapper.nested("metadata", nestedWrapper -> nestedWrapper
                .eq("metadata.name", name1)
                .in("metadata.value", value1)
        );

        queryWrapper.size(10);
        // 执行查询
        final List<org.irm.lab.front.model.Document> xx = documentEsMapper.selectList(queryWrapper);
        final List<String> collect1 = xx.stream().map(org.irm.lab.front.model.Document::getId).collect(Collectors.toList());

        List<ObjectId> objectIdList = collect1.stream()
                .map(ObjectId::new)
                .collect(Collectors.toList());

        Bson bson1 = Filters.in("_id", objectIdList);
        return resourceRepository.findByCondition(bson1);

    }

    public List<Resource> getMeatDateNotByIds(String name, List<String> value) {

        // 创建一个新的 LambdaEsQueryWrapper 实例
        final LambdaEsQueryWrapper<org.irm.lab.front.model.Document> queryWrapper = new LambdaEsQueryWrapper<>();
        queryWrapper.index(esIndexResolver.resolveEsIndexByTenant());
        // 这里演示了如何添加嵌套查询条件，具体方法取决于 Easy-Es 的版本和功能
        queryWrapper.nested("metadata", nestedWrapper -> nestedWrapper
                .eq("metadata.name", name)
                .in("metadata.value", value));

        // 执行查询
        final List<org.irm.lab.front.model.Document> xx = documentEsMapper.selectList(queryWrapper);
        final List<String> collect1 = xx.stream().map(org.irm.lab.front.model.Document::getId).collect(Collectors.toList());

        List<ObjectId> objectIdList = collect1.stream()
                .map(ObjectId::new)
                .collect(Collectors.toList());

        Bson bson1 = Filters.in("_id", objectIdList);
        return resourceRepository.findByCondition(bson1);

    }

    /**
     * 仅获取10个
     *
     * @param ids
     * @return
     */
    public List<Resource> getMeatDateLike(String name, List<String> values, List<String> ids) {

        // 创建一个新的 LambdaEsQueryWrapper 实例
        LambdaEsQueryWrapper<org.irm.lab.front.model.Document> queryWrapper = new LambdaEsQueryWrapper<>();
        queryWrapper.index(esIndexResolver.resolveEsIndexByTenant());

        // 添加嵌套查询条件
        queryWrapper.nested("metadata", nestedWrapper -> {
            nestedWrapper.eq("metadata.name", name);
            nestedWrapper.in(org.irm.lab.front.model.Document::getId, ids);

            // 添加模糊匹配条件，使用 should 来实现 OR 逻辑
            nestedWrapper.and(subQuery -> {
                values.forEach(value -> subQuery.or().like("metadata.value", value));
            });
        });

        // 执行查询
        final List<org.irm.lab.front.model.Document> xx = documentEsMapper.selectList(queryWrapper);
        final List<String> collect1 = xx.stream().map(org.irm.lab.front.model.Document::getId).collect(Collectors.toList());

        List<ObjectId> objectIdList = collect1.stream()
                .map(ObjectId::new)
                .collect(Collectors.toList());

        Bson bson1 = Filters.in("_id", objectIdList);
        return resourceRepository.findByCondition(bson1);

    }

    /**
     * 根据某个元数据排序
     */
    public List<Resource> getMeatDateLikeOrderBy(String name, List<String> ids) {

        // 创建一个新的 LambdaEsQueryWrapper 实例
        LambdaEsQueryWrapper<org.irm.lab.front.model.Document> queryWrapper = new LambdaEsQueryWrapper<>();
        queryWrapper.index(esIndexResolver.resolveEsIndexByTenant());

        // 添加嵌套查询条件
        queryWrapper.nested("metadata", nestedWrapper -> {
            nestedWrapper.eq("metadata.name", name)
                    .in(org.irm.lab.front.model.Document::getId, ids)
                    .exists("metadata.dateValue")
            ; // 检查dateValue元数据是否存在

        });

        FieldSortBuilder fieldSortBuilder = new FieldSortBuilder("metadata.dateValue")
                .order(SortOrder.DESC)
                .setNestedSort(new NestedSortBuilder("metadata"));
        queryWrapper.sort(fieldSortBuilder);

        queryWrapper.limit(10);
        final List<org.irm.lab.front.model.Document> xx = documentEsMapper.selectList(queryWrapper);


        final List<String> collect1 = xx.stream().map(org.irm.lab.front.model.Document::getId).collect(Collectors.toList());

        List<ObjectId> objectIdList = collect1.stream()
                .map(ObjectId::new)
                .collect(Collectors.toList());

        Bson bson1 = Filters.in("_id", objectIdList);
        final List<Resource> byCondition = resourceRepository.findByCondition(bson1);

        byCondition.sort(Comparator.comparingInt(resource -> collect1.indexOf(resource.getId())));
        return byCondition;
    }


    /**
     * 制度
     *
     * @param system 制度名称
     * @return
     */
    public List<Resource> resourceSystem(List<String> system) {

        // 创建一个新的 LambdaEsQueryWrapper 实例
        final LambdaEsQueryWrapper<org.irm.lab.front.model.Document> queryWrapper = new LambdaEsQueryWrapper<>();
        queryWrapper.index(esIndexResolver.resolveEsIndexByTenant());
        // 这里演示了如何添加嵌套查询条件，具体方法取决于 Easy-Es 的版本和功能
        queryWrapper.nested("metadata", nestedWrapper -> nestedWrapper
                .eq("metadata.name", "文件类型")
                .in("metadata.value", system));

        // 执行查询
        final List<org.irm.lab.front.model.Document> xx = documentEsMapper.selectList(queryWrapper);
        final List<String> collect1 = xx.stream().map(org.irm.lab.front.model.Document::getId).collect(Collectors.toList());

        List<ObjectId> objectIdList = collect1.stream()
                .map(ObjectId::new)
                .collect(Collectors.toList());

        Bson bson1 = Filters.in("_id", objectIdList);
        return resourceRepository.findByCondition(bson1);

        //return resourceList.stream().map(AbstractBaseEntity::getId).collect(Collectors.toList());
    }

    /**
     * @return
     */
    public List<Resource> resourceMateData(List<String> system, String mateDataName, String fileName) {
        // 创建一个新的 LambdaEsQueryWrapper 实例
        final LambdaEsQueryWrapper<org.irm.lab.front.model.Document> queryWrapper = new LambdaEsQueryWrapper<>();
        queryWrapper.index(esIndexResolver.resolveEsIndexByTenant());
        // 添加嵌套查询条件
        queryWrapper.nested("metadata", nestedWrapper -> nestedWrapper
                .eq("metadata.name", mateDataName)
                .in("metadata.value", system));

        // 执行查询
        final List<org.irm.lab.front.model.Document> documents = documentEsMapper.selectList(queryWrapper);
        final List<String> documentIds = documents.stream()
                .map(org.irm.lab.front.model.Document::getId)
                .collect(Collectors.toList());

        // 将 String ID 转换为 MongoDB 的 ObjectId
        List<ObjectId> objectIdList = documentIds.stream()
                .map(ObjectId::new)
                .collect(Collectors.toList());

        // 创建 MongoDB 查询条件，包括 _id 和文件名匹配
        Bson idFilter = Filters.in("_id", objectIdList);


        // 查询 MongoDB 并按文件名匹配的长度进行排序

        return resourceRepository.findByCondition(idFilter)
                .stream()
                .sorted(Comparator.comparingInt((Resource r) -> matchLength(r.getPrimaryFileName(), fileName)).reversed())
                .limit(10)
                .collect(Collectors.toList());
    }

    /**
     * 计算两个公文文件的最长公共子序列长度，用于排序
     * 空间复杂O(n)
     */
    private int matchLength(String file1, String file2) {
        int m = file1.length();
        int n = file2.length();
        int[] prev = new int[n + 1];
        int[] curr = new int[n + 1];

        for (int i = 1; i <= m; i++) {
            for (int j = 1; j <= n; j++) {
                if (file1.charAt(i - 1) == file2.charAt(j - 1)) {
                    curr[j] = prev[j - 1] + 1;
                } else {
                    curr[j] = Math.max(prev[j], curr[j - 1]);
                }
            }
            int[] temp = prev;
            prev = curr;
            curr = temp;
        }
        return prev[n];
    }

    /**
     * 查询对应类型的文件的id集合
     *
     * @param classifies 资源类型
     * @return ids
     */
    public Set<String> resourceByClassify(List<String> classifies) {

        if (ObjectUtil.isEmpty(classifies)) return new HashSet<>();

        // 创建一个新的 LambdaEsQueryWrapper 实例
        final LambdaEsQueryWrapper<org.irm.lab.front.model.Document> queryWrapper = new LambdaEsQueryWrapper<>();
        queryWrapper.index(esIndexResolver.resolveEsIndexByTenant());

        // 对于每个分类，添加一个嵌套查询条件
        for (String classify : classifies) {
            // 这里演示了如何添加嵌套查询条件，具体方法取决于 Easy-Es 的版本和功能
            queryWrapper.nested("metadata", nestedWrapper -> nestedWrapper
                    .eq("metadata.name", "公文分类")
                    .in("metadata.value", classify));
        }

        // 执行查询
        final List<org.irm.lab.front.model.Document> xx = documentEsMapper.selectList(queryWrapper);
        final List<String> collect1 = xx.stream().map(org.irm.lab.front.model.Document::getId).collect(Collectors.toList());

        List<ObjectId> objectIdList = collect1.stream()
                .map(ObjectId::new)
                .collect(Collectors.toList());

        Bson bson1 = Filters.in("_id", objectIdList);
        final List<Resource> resourceList = resourceRepository.findByCondition(bson1);
        return resourceList.stream().map(Resource::getId).collect(Collectors.toSet());
    }

    public Set<String> resourceByClassifyES(List<String> classifies) {
        if (ObjectUtil.isEmpty(classifies)) return new HashSet<>();
        List<Bson> bsonList = new ArrayList<>();
        for (String classify : classifies) {
            Bson bson = Filters.and(
                    Filters.eq("isDeleted", BaseStatus.OK),
                    Filters.eq("metadata.name", "公文分类"),
                    Filters.in("metadata.value", classify)
            );
            bsonList.add(bson);
        }
        List<Bson> unwind = List.of(
                Aggregates.unwind("$metadata"),
                Aggregates.match(Filters.or(bsonList)),
                Aggregates.group("$_id", Accumulators.addToSet("metadata", "$metadata")));
        List<Resource> aggregate = resourceRepository.findByAggregate(unwind);
        return aggregate.stream().map(AbstractBaseEntity::getId).collect(Collectors.toSet());
    }

    /**
     * 根据关键词查询资源集合
     *
     * @param keywords 关键词
     * @return 资源集合
     */
    public List<Resource> resourceListByKeyword(List<String> keywords) {
        if (ObjectUtil.isEmpty(keywords)) return new ArrayList<>();
        //根据关键词模糊查询当前资源
        List<Bson> bsonList = keywords.stream().map(keyword -> Filters.regex("name", keyword)).collect(Collectors.toList());
        Bson bson = Filters.or(bsonList);
        return resourceRepository.findByCondition(bson);
    }

    /**
     * 相关推荐，根据文件关键词检索相关联文件
     *
     * @param resource 资源详情
     * @return 资源分页
     */
    @Override
    public List<Resource> resourcePageByKeyword(Resource resource) {
        List<String> keywords = getResourceMetadataValue(resource, "关键词");
        return resourceListByKeyword(keywords);
    }

    /**
     * 查询当前文件中涉及到的概念下的实例所涉及的其他文件
     *
     * @param id           资源id
     * @param containsName 概念名称
     * @return
     */
    public Set<String> resourceIdsByNodeConceptName(String id, String containsName) {
        Set<NodeEntity> nodeEntitySet = getNodeByConceptName(id, containsName);
        return resourceIdsByNodeConceptName(id, nodeEntitySet);
    }

    public Set<String> resourceIdsByNodeConceptName(String id, Set<NodeEntity> nodeEntitySet) {
        Set<String> idList = new HashSet<>();
        for (NodeEntity nodeEntity : nodeEntitySet) {
            if (ObjectUtil.isEmpty(nodeEntity.getDocIds())) continue;
            idList.addAll(nodeEntity.getDocIds());
        }
        idList.remove(id);
        return idList;
    }

    private final DictItemFeign dictItemFeign;

    /**
     * 获取文件的批示信息
     *
     * @param resourceId 资源id
     * @return 批示信息
     */
    @Override
    public JSONArray leaderInstruction(String resourceId) {
        List<String> leaderList = LeadershipEnum.leaderList();
        R<List<DictItem>> itemR = dictItemFeign.listByDictName("上级领导");
        List<DictItem> itemList = itemR.getData();
        if (itemR.isSuccess() && ObjectUtil.isNotEmpty(itemList)) {
            leaderList = itemList.stream().map(DictItem::getLabel).collect(Collectors.toList());
        }

        Bson idFilter = Filters.in("resourceId", resourceId);
        Bson handlerFilter = Filters.in("handler", leaderList);
        final Bson and = Filters.and(idFilter, handlerFilter);
        final Bson content = Filters.eq("content", "暂无意见");
        final Bson not = Filters.not(content);

        final Bson and1 = Filters.and(and, not);

        final List<ProcessRecords> collect = processRecordsRepository.findByCondition(and1).stream().sorted(Comparator.comparing(vo -> Integer.parseInt(vo.getSort()))).collect(Collectors.toList());
        final JSONArray objects = new JSONArray();
        collect.stream().forEach(data -> {
            final JSONObject entries = new JSONObject();
            entries.set("sort", data.getSort());
            entries.set("instructionLeader", data.getHandler());
            entries.set("instructionTime", data.getFinishTime());
            entries.set("handlingAdvice", data.getContent());
            objects.add(entries);
        });
        return objects;

    }

    private final AttachFeign attachFeign;

    /**
     * 获取文件压缩包下载路径
     *
     * @param resourceId 资源id
     * @return 下载路径
     */
    @Override
    public String getDownloadUrl(String resourceId) {
        //获取当前资源
        Resource resource = resourceRepository.findById(resourceId);
//        Boolean permission = getResourcePermission(resource);
//        if (permission.equals(false)) throw new ServiceException(ExceptionMessageConst.NOT_HAVE_PERMISSION);

        String resourceName = resource.getName();
        if (resourceName.contains(".")) {
            resourceName = resourceName.substring(0, resourceName.lastIndexOf("."));
        }
        //获取当前资源的导入方式
        String importMethod = resource.getImportMethod();
        //获取文件下载的链接
        if (ResourceConstant.LOCAL_UPLOAD.equals(importMethod)) {
            //若当前资源已经存在zip资源包，直接返回下载链接
            String zipAttachId = resource.getZipAttachId();
            if (ObjectUtil.isNotEmpty(zipAttachId)) {
                R<Attach> attachR = attachFeign.info(zipAttachId);
                Attach attach = attachR.getData();
                if (attachR.isSuccess() && ObjectUtil.isNotEmpty(attach))
                    return attach.getLink();
            }
            //本地文件下载，获取当前文件的下载路径
            List<String> attachIds = new ArrayList<>();
            String pdfAttachId = resource.getPdfAttachId();
            if (ObjectUtil.isNotEmpty(pdfAttachId)) {
                attachIds.add(pdfAttachId);
            } else {
                String primaryFileId = resource.getPrimaryFileId();
                if (ObjectUtil.isNotEmpty(primaryFileId)) {
                    attachIds.add(primaryFileId);
                }
            }
            //获取当前资源所有附件的attachId
            List<ResourceAnnex> annexList = resourceAnnexRepository.findByCondition(Filters.eq("resourceId", resourceId));
            for (ResourceAnnex annex : annexList) {
                String annexPdfId = annex.getPdfAttachId();
                if (ObjectUtil.isNotEmpty(annexPdfId)) {
                    //若存在pdf文件，下载pdf文件
                    attachIds.add(annexPdfId);
                } else {
                    //若不存在pdf文件，下载源文件
                    String annexPrimaryFileId = annex.getPrimaryFileId();
                    if (ObjectUtil.isEmpty(annexPrimaryFileId)) continue;
                    attachIds.add(annexPrimaryFileId);
                }
            }
            if (ObjectUtil.isNotEmpty(attachIds)) {
                //调用文件下载接口,将文件文件打成压缩包下载到本地
                R<Attach> attachR = ossEndPoint.uploadResourceZip(attachIds, resourceName);
                Attach attach = attachR.getData();
                if (!attachR.isSuccess() || ObjectUtil.isEmpty(attach))
                    throw new ServiceException(ExceptionMessageConst.DOWNLOAD_FAIL);
                resource.setZipAttachId(attach.getId());
                resourceRepository.save(resource);
                return attach.getLink();
            }
        } else if (ResourceConstant.EXTERNAL_IMPORTS.equals(importMethod)) {
            //外部导入文件下载，获取该资源的甲方id，调用甲方下载接口
            String catalogId = resource.getDatasourceCatalogId();
            R<String> markZipR = peripheralInterfaceFeign.markZipDownloadUrl(catalogId);
            String markZipUrl = markZipR.getData();
            if (!markZipR.isSuccess() || ObjectUtil.isEmpty(markZipUrl))
                throw new ServiceException(ExceptionMessageConst.DOWNLOAD_FAIL);
            return markZipUrl;
        }
        throw new ServiceException(ExceptionMessageConst.DOWNLOAD_FAIL);
    }

    @Override
    public ResourcePageVO countResourcePage(String resourceId) {
        if (!isShow(resourceId)) {
            return new ResourcePageVO(new JSONObject(), new JSONObject(), new JSONObject(), new JSONObject());
        }

        Resource resource = getResourceById(resourceId);
        if (resource == null) {
            return new ResourcePageVO(new JSONObject(), new JSONObject(), new JSONObject(), new JSONObject());
        }

        List<String> fileType = getResourceMetadataValue(resource, "公文分类");
        Set<String> idsByClassify = resourceByClassify(fileType);

        JSONObject firstResourcePage = handleResourcePageType(resourceId, fileType, idsByClassify, resource, "first");
        JSONObject secondResourcePage = handleResourcePageType(resourceId, fileType, idsByClassify, resource, "second");
        JSONObject thirdResourcePage = handleResourcePageType(resourceId, fileType, idsByClassify, resource, "third");
        JSONObject fourthResourcePage = handleResourcePageType(resourceId, fileType, idsByClassify, resource, "fourth");

        return new ResourcePageVO(firstResourcePage, secondResourcePage, thirdResourcePage, fourthResourcePage);
    }

    // 辅助函数来处理每个资源页面的逻辑
    private JSONObject handleResourcePageType(String id, List<String> fileType, Set<String> idsByClassify, Resource resource, String pageType) {
        String title = "";
        String description = "";

        switch (pageType) {
            case "first":
                // 处理第一个资源页面的逻辑
                return handleFirstPageResourceType(fileType, id, idsByClassify, resource);
            case "second":
                return handleSecondPageResourceType(fileType, id, idsByClassify, resource);
            case "third":
                return handleThirdPageResourceType(fileType, id, idsByClassify, resource);
            case "fourth":
                return handleFourthPageResourceType(fileType, id, idsByClassify, resource);

        }

        JSONObject jsonObject = new JSONObject();
        jsonObject.set("title", title);
        jsonObject.set("description", description);
        jsonObject.set("resources", null);

        return jsonObject;
    }

    // 处理第一个资源页面的逻辑
    private JSONObject handleFirstPageResourceType(List<String> fileType, String id, Set<String> idsByClassify, Resource resource) {
        List<Resource> resources = List.of();
        String title;
        String description;
        if (fileType.contains("会议纪要")) {
            // 根据（分组）元数据 然后 去 排序
            title = "同类同分组会议纪要推荐";
            description = "与当前公文分组元数据相同的会议纪要公文";
            resources = fetchResourcesByIds(idsByClassify);
            final List<String> fenzu = getResourceMetadataValue(resource, "分组");
            if (ObjectUtil.isEmpty(fenzu)) {
                final JSONObject jsonObject = new JSONObject();

                jsonObject.set("title", title);
                jsonObject.set("description", description);
                jsonObject.set("resources", List.of());
                return jsonObject;
            }
            final List<Resource> resourceList = getMeatDate("分组", fenzu, resources.stream().map(Resource::getId).collect(Collectors.toList()));

            resources = resourceList.stream().sorted(new KnowledgeQueryServiceImpl.CustomComparator()).limit(10).collect(Collectors.toList());

        } else if (fileType.contains("奖章荣誉") || fileType.contains("表彰荣誉")) {
            // 逻辑针对奖章荣耀
            title = "同类荣誉相关人员推荐";
            description = "与当前公文提及到的相同人员的公文";
            // 获得文件的题名
            final List<String> fenzu = getResourceMetadataValue(resource, "人员");
            final List<String> collect = fenzu.stream().filter(idsByClassify::contains).collect(Collectors.toList());
            resources = resourceRepository.findById(collect);

        } else if (fileType.contains("规章制度")) {
            // 根据文号元数据匹配
            title = "同制度文件同文号公文推荐";
            description = "规章制度中与当前公文文号元数据相等的公文";
            //获取所有制度类型文件

            // 获取当前文件的文号元数据值
            final List<String> wenhao = getResourceMetadataValue(resource, "文号");

            final List<Resource> resourceList = getMeatDate("文号", wenhao, new ArrayList<>(idsByClassify));

            resources = resourceList.stream().limit(10).collect(Collectors.toList());
        } else if (fileType.contains("领导批示")) {
            title = "同责任者公文推荐";
            description = "领导批示中与当前公文责任者元数据相等的公文";
            resources = fetchResourcesByIds(idsByClassify);
            final List<String> zerenzhe = getResourceMetadataValue(resource, "责任者");

            final List<Resource> resourceList = getMeatDate("责任者", zerenzhe, resources.stream().map(Resource::getId).collect(Collectors.toList()));

            resources = resourceList.stream().limit(10).collect(Collectors.toList());

        } else if (fileType.contains(APPOINTMENT_AND_REMOVAL)) {

            title = "任免相关人员推荐";
            description = "与当前任职任免文件相关人员的其他任免文件";
            // 查询所有涉及到人员的文件
            Set<NodeEntity> nodeEntities = getNodeByConceptName(id, "人员");
            final Set<String> strings1 = resourceIdsByNodeConceptName(id, nodeEntities);
            // 所有任职任免的文件
            final Set<String> strings = resourceByClassify(fileType);
            final Collection<String> intersection = CollUtil.intersection(strings1, strings);

            resources = resourceRepository.findById(intersection);
        } else {

            title = "同责任者同年度公文推荐";
            description = "同责任者同年度的公文";
            // 获取当前文件的文号元数据值
            final List<String> zerenzhe = getResourceMetadataValue(resource, "责任者");

            log.info("当前文件的责任者为{}", zerenzhe);
            final List<String> wenhao = getResourceMetadataValue(resource, "年度");
            log.info("当前文件的年度为{}", wenhao);

            resources = defaultMeatDate("责任者", zerenzhe, "年度", wenhao);

        }
        resources = resources.stream().filter(res -> !id.equals(res.getId()))
                .skip(0)
                .limit(10)
                .collect(Collectors.toList());
        final JSONObject jsonObject = new JSONObject();

        jsonObject.set("title", title);
        jsonObject.set("description", description);
        jsonObject.set("resources", resources);
        return jsonObject;
    }

    // 处理第二个资源页面的逻辑
    private JSONObject handleSecondPageResourceType(List<String> fileType, String id, Set<String> idsByClassify, Resource resource) {
        List<Resource> resources = new ArrayList<>();
        String title = "人员关联文件";
        String description = "与当前文件中涉及人员相关联的其他文件";
        final JSONObject jsonObject = new JSONObject();
        Set<String> ids;

        if (fileType.contains("会议纪要")) {
            title = "当前会议议题推荐";
            description = "当前文件的所有的会议议题";
            Set<NodeEntity> nodeEntities = getNodeByConceptName(id, "议题");

            // 获得当前文件所有的议题
            final List<String> collect = nodeEntities.stream().map(NodeEntity::getEntityName).collect(Collectors.toList());

            jsonObject.putOpt("title", title);
            jsonObject.putOpt("description", description);
            jsonObject.putOpt("resources", collect);
            return jsonObject;

        } else if (fileType.contains("规章制度")) {
            title = "同制度文件同制度类型公文推荐";
            description = "规章制度中与当前公文（制度类型）元数据相等的公文";
            //获取所有制度类型文件 门类（制度类型）元数据一样的展示
            resources = fetchResourcesByIds(idsByClassify);
            // 获取当前文件的文号元数据值
            final List<String> wenhao = getResourceMetadataValue(resource, "制度类型");

            final List<Resource> resourceList = getMeatDate("制度类型", wenhao, resources.stream().map(Resource::getId).collect(Collectors.toList()));

            ids = resourceList.stream().limit(10).map(Resource::getId).collect(Collectors.toSet());
        } else if (fileType.contains("奖章荣誉") || fileType.contains("表彰荣誉")) {
            //同表彰荣誉的元数据 文件类型 等于签报
            title = "同类荣誉签报公文推荐";
            description = "表彰荣誉中文件类型元数据为签报的公文";
            //获取实例关联公文ids

            // 使用ES仅获取10个数据
            resources = getMeatDateLike("文件类型", List.of("签报"), new ArrayList<>(idsByClassify));

            ids = resources.stream().limit(10).map(Resource::getId).collect(Collectors.toSet());
        } else if (fileType.contains("领导批示")) {

            final List<String> strings = LeadershipEnum.leaderList();

            Bson idFilter = Filters.in("resourceId", id);
            Bson handlerFilter = Filters.in("handler", strings);

            final Bson and = Filters.and(idFilter, handlerFilter);

            // 查询当前这个公文的所有领导
            final List<ProcessRecords> processRecords = processRecordsRepository.findByCondition(and);

            final List<String> list = Steam.of(processRecords).map(ProcessRecords::getHandler).toList();

            final Bson handler = Filters.in("handler", list);

            final List<ProcessRecords> processRecords1 = processRecordsRepository.findByCondition(handler);

            // 使用 Stream API 对 processRecords 进行分组
            final Map<String, List<ProcessRecords>> processRecordMap = processRecords1.stream()
                    .collect(Collectors.groupingBy(ProcessRecords::getResourceId));

            // 使用 Stream API 对分组后的结果进行分页
            final Map<String, List<ProcessRecords>> finalProcessRecordMap = processRecordMap.entrySet().stream()
                    .limit(10)
                    .collect(Collectors.toMap(Map.Entry::getKey, Map.Entry::getValue));

            // 同领导
            title = "同领导批示公文推荐";
            description = "同领导批示的公文";
            ids = finalProcessRecordMap.keySet();


        } else if (fileType.contains(APPOINTMENT_AND_REMOVAL)) {

            title = "任免相关机构推荐";
            description = "与当前任职任免文件相关机构的其他任免文件";
            // 查询所有涉及到人员的文件
            Set<NodeEntity> nodeEntities = getNodeByConceptName(id, "机构");
            final Set<String> strings1 = resourceIdsByNodeConceptName(id, nodeEntities);
            // 所有任职任免的文件
            final Set<String> strings = resourceByClassify(fileType);
            final Collection<String> intersection = CollUtil.intersection(strings1, strings);

            ids = new HashSet<>(intersection);
        } else {
            title = "同主办部门同年度公文推荐";
            description = "同主办部门同年度的公文";
            // 获取当前文件的文号元数据值
            final List<String> zerenzhe = getResourceMetadataValue(resource, "年度");

            final List<String> wenhao = getResourceMetadataValue(resource, "主办部门");

            final List<Resource> resources1 = defaultMeatDate("年度", zerenzhe, "主办部门", wenhao);

            ids = resources1.stream().map(Resource::getId).collect(Collectors.toSet());
        }

        //若当前无数据，查询默认方法
        ids.remove(id);
        if (ObjectUtil.isNotEmpty(ids)) {
            resources = resourceRepository.findById(ids);
            resources = resources.stream().sorted(Comparator.comparing(Resource::getCreateTime).reversed())
                    .skip(0)
                    .limit(10)
                    .collect(Collectors.toList());
        }
        jsonObject.putOpt("title", title);
        jsonObject.putOpt("description", description);
        jsonObject.putOpt("resources", resources);
        return jsonObject;

    }

    // 处理第三个资源页面的逻辑
    private JSONObject handleThirdPageResourceType(List<String> fileType, String id, Set<String> idsByClassify, Resource resource) {
        List<Resource> resources = new ArrayList<>();
        String title = "当前文件同类公文";
        String description = "根据当前公文的类型匹配到的同种类型的文件";
        final JSONObject jsonObject = new JSONObject();
        if (fileType.contains("会议纪要")) {

            // 根据印发日期排序
            title = "最新会议纪要推荐";
            description = "根据印发日期最新的前十条会议纪要进行推荐";

            resources = getMeatDateLikeOrderBy("印发日期", new ArrayList<>(idsByClassify));
        } else if (fileType.contains("表彰荣誉") || fileType.contains("奖章荣誉")) {
            title = "同类荣誉收、发文 公文推荐";
            description = "表彰荣誉中文件类型元数据为收文、发文的公文";

            // 使用ES仅获取10个数据
            resources = getMeatDateLike("文件类型", List.of("收文", "发文"), new ArrayList<>(idsByClassify));

        } else if (fileType.contains("规章制度")) {
            // 逻辑针对规章制度
            // 还未定下来
            title = "相关概念术语推荐";
            description = "当前文件相关概念术语推荐";
            Set<NodeEntity> nodeEntities = getNodeByConceptName(id, "概念术语");

            // 获得当前文件所有的议题
            final List<String> collect = nodeEntities.stream().map(NodeEntity::getEntityName).collect(Collectors.toList());

            jsonObject.putOpt("title", title);
            jsonObject.putOpt("description", description);
            jsonObject.putOpt("resources", collect);

        } else if (fileType.contains("领导批示")) {

            // 同领导
            title = "同年度同领导批示公文推荐";
            description = "同年度同领导批示的公文";
            //final Set<String> docIds = resourceIdsByNodeConceptName(id, "人员");

            //final List<String> collect = docIds.stream().filter(idsByClassify::contains).collect(Collectors.toList());
            // 获得当前文件的同年度数据
            final List<String> nianduMetaData = getResourceMetadataValue(resource, "年度");
            // 同领导批示同年度的所有文件
            final List<Resource> niandu = getMeatDate("年度", nianduMetaData, new ArrayList<>(idsByClassify));

            final List<String> list = Steam.of(niandu).map(Resource::getId).toList();

            final List<String> strings = LeadershipEnum.leaderList();

            Bson idFilter = Filters.in("resourceId", resource.getId());
            Bson handlerFilter = Filters.in("handler", strings);

            final Bson and = Filters.and(idFilter, handlerFilter);

            // 查询当前这个公文的所有领导
            final List<ProcessRecords> processRecords = processRecordsRepository.findByCondition(and);

            final List<String> lingdao = Steam.of(processRecords).map(ProcessRecords::getHandler).toList();

            final Bson handler = Filters.in("handler", lingdao);

            final List<ProcessRecords> processRecords1 = processRecordsRepository.findByCondition(handler);

            // 使用 Stream API 对 processRecords 进行分组
            final Map<String, List<ProcessRecords>> processRecordMap = processRecords1.stream()
                    .collect(Collectors.groupingBy(ProcessRecords::getResourceId));

            // 使用 Stream API 对分组后的结果进行分页
            final Map<String, List<ProcessRecords>> finalProcessRecordMap = processRecordMap.entrySet().stream().filter(process -> list.contains(process.getKey()))
                    .limit(10)
                    .collect(Collectors.toMap(Map.Entry::getKey, Map.Entry::getValue));

            resources = resourceRepository.findById(finalProcessRecordMap.keySet());

        } else {
            title = "同责任者同主办部门公文推荐";
            description = "同责任者同主办部门的公文";
            // 获取当前文件的文号元数据值
            final List<String> zerenzhe = getResourceMetadataValue(resource, "责任者");

            final List<String> wenhao = getResourceMetadataValue(resource, "主办部门");

            resources = defaultMeatDate("责任者", zerenzhe, "主办部门", wenhao);
        }

        resources = resources.stream().filter(res -> !id.equals(res.getId()))
                .skip(0)
                .limit(10)
                .collect(Collectors.toList());
        jsonObject.set("title", title);
        jsonObject.set("description", description);
        jsonObject.set("resources", resources);
        return jsonObject;

    }

    // 处理第四个资源页面的逻辑
    private JSONObject handleFourthPageResourceType(List<String> fileType, String id, Set<String> idsByClassify, Resource resource) {

        String title = "当前文件同源公文";
        String description = "当前文件的发文机构/部门发布的其他的文件";
        Set<String> ids = new HashSet<>();
        List<Resource> resources = new ArrayList<>();
        final JSONObject jsonObject = new JSONObject();
        if (fileType.contains("会议纪要")) {
            // 逻辑针对会议纪要
            title = "同部门会议纪要推荐";
            description = "与当前会议同机构部门的公文推荐";
            ids = resourceIdsByNodeConceptName(id, "机构/部门");
            ids = ids.stream().filter(idsByClassify::contains).collect(Collectors.toSet());
        } else if (fileType.contains("表彰荣誉") || fileType.contains("奖章荣誉")) {
            // 逻辑针对 同表彰荣誉中根据授奖机构节点完全命中展示
            title = "同授奖机构公文推荐";
            description = "与当前公文存在同一个授奖机构的公文推荐";
            ids = resourceIdsByNodeConceptName(id, "授奖机构");
            ids = ids.stream().filter(idsByClassify::contains).collect(Collectors.toSet());
        } else if (fileType.contains("规章制度")) {
            title = "同制度文件同部门公文推荐";
            description = "与当前公文提及到的相同部门的公文";
            ids = resourceIdsByNodeConceptName(id, "机构");
            ids = ids.stream().filter(idsByClassify::contains).collect(Collectors.toSet());
        } else if (fileType.contains("领导批示")) {
            // 逻辑针对领导批示
            title = "同责任者同主办部门公文推荐";
            description = "同责任者同主办部门的公文";

            // 获取当前文件的文号元数据值
            final List<String> zerenzhe = getResourceMetadataValue(resource, "责任者");

            final List<Resource> resourceList = getMeatDate("责任者", zerenzhe, new ArrayList<>(idsByClassify));

            final List<String> wenhao = getResourceMetadataValue(resource, "主办部门");

            final List<Resource> resourceListCopy = getMeatDate("主办部门", wenhao, new ArrayList<>(idsByClassify));
            final Collection<String> intersection = CollUtil.intersection(resourceList.stream().map(Resource::getId).collect(Collectors.toList()),
                    resourceListCopy.stream().map(Resource::getId).collect(Collectors.toList()));
            ids = new HashSet<>(intersection);
        } else {
            title = "同分组同主办部门推荐";
            description = "同分组同主办部门的公文";

            // 获取当前文件的文号元数据值

            final List<String> zerenzhe = getResourceMetadataValue(resource, "分组");

            final List<String> wenhao = getResourceMetadataValue(resource, "主办部门");
            ids = defaultMeatDate("分组", zerenzhe, "主办部门", wenhao).stream().map(Resource::getId).collect(Collectors.toSet());

            // 默认逻辑
            //ids = getResourceByMaterialIssuingUnit(id);
        }
        ids.remove(id);
        if (ObjectUtil.isNotEmpty(ids)) {
            resources = resourceRepository.findById(ids);
            resources = resources.stream().sorted(Comparator.comparing(Resource::getCreateTime).reversed())
                    .skip(0)
                    .limit(10)
                    .collect(Collectors.toList());
        }
        jsonObject.putOpt("title", title);
        jsonObject.putOpt("description", description);
        jsonObject.putOpt("resources", resources);
        return jsonObject;

    }


    public static boolean isSimilar(String str1, String str2, double percentage) {
        int len1 = str1.length();
        int len2 = str2.length();
        int maxLen = Math.max(len1, len2);

        if (maxLen == 0) return true; // If both strings are empty

        int distance = levenshteinDistance(str1, str2);
        double similarity = (1 - (double) distance / maxLen) * 100;

        return similarity >= percentage;
    }

    private static int levenshteinDistance(String str1, String str2) {
        int len1 = str1.length();
        int len2 = str2.length();
        int[][] dp = new int[len1 + 1][len2 + 1];

        for (int i = 0; i <= len1; i++) {
            for (int j = 0; j <= len2; j++) {
                if (i == 0) {
                    dp[i][j] = j;
                } else if (j == 0) {
                    dp[i][j] = i;
                } else {
                    int cost = (str1.charAt(i - 1) == str2.charAt(j - 1)) ? 0 : 1;
                    dp[i][j] = Math.min(Math.min(dp[i - 1][j] + 1, dp[i][j - 1] + 1), dp[i - 1][j - 1] + cost);
                }
            }
        }

        return dp[len1][len2];
    }

    // 辅助方法：根据ID集合获取资源列表
    private List<Resource> fetchResourcesByIds(Set<String> ids) {
        // 根据ID集合从资源库中获取资源列表
        return resourceRepository.findById(ids);
    }

    // 辅助方法：根据关键词和ID集合获取资源列表
    private List<Resource> fetchResourcesByKeywordsAndIds(List<String> keywords, Set<String> ids) {
        // 根据关键词和ID集合从资源库中获取资源列表
        List<Resource> resources = resourceListByKeyword(keywords);
        return resources.stream().filter(r -> ids.contains(r.getId())).collect(Collectors.toList());
    }


    @Override
    public JSONObject commendationRightTop(String resourceId) {
        if (!isShow(resourceId)) {
            return new JSONObject();
        }

        Resource resource = getResourceById(resourceId);
        if (resource == null) {
            return new JSONObject();
        }

        List<String> fileType = getResourceMetadataValue(resource, "公文分类");
        Set<String> idsByClassify = resourceByClassify(fileType);

        return handleResourcePageType(resourceId, fileType, idsByClassify, resource, "second");
    }

    @Override
    public JSONObject commendationRightDown(String resourceId) {
        if (!isShow(resourceId)) {
            return new JSONObject();
        }

        Resource resource = getResourceById(resourceId);
        if (resource == null) {
            return new JSONObject();
        }

        List<String> fileType = getResourceMetadataValue(resource, "公文分类");
        Set<String> idsByClassify = resourceByClassify(fileType);

        return handleResourcePageType(resourceId, fileType, idsByClassify, resource, "fourth");
    }

    @Override
    public JSONObject commendationLeftTop(String resourceId) {
        if (!isShow(resourceId)) {
            return new JSONObject();
        }

        Resource resource = getResourceById(resourceId);
        if (resource == null) {
            return new JSONObject();
        }

        List<String> fileType = getResourceMetadataValue(resource, "公文分类");
        Set<String> idsByClassify = resourceByClassify(fileType);

        return handleResourcePageType(resourceId, fileType, idsByClassify, resource, "first");
    }

    @Override
    public JSONObject commendationLeftDown(String resourceId) {
        if (!isShow(resourceId)) {
            return new JSONObject();
        }

        Resource resource = getResourceById(resourceId);
        if (resource == null) {
            return new JSONObject();
        }

        List<String> fileType = getResourceMetadataValue(resource, "公文分类");
        Set<String> idsByClassify = resourceByClassify(fileType);

        return handleResourcePageType(resourceId, fileType, idsByClassify, resource, "third");
    }
}
