package org.irm.lab.front.controller;

import lombok.RequiredArgsConstructor;
import org.irm.lab.common.api.R;
import org.irm.lab.common.support.MyPage;
import org.irm.lab.common.utils.Func;
import org.irm.lab.front.dto.DocumentAccessRecordsDTO;
import org.irm.lab.front.dto.DocumentPushDTO;
import org.irm.lab.front.dto.PersonalCollectDTO;
import org.irm.lab.front.entity.DocumentAccessRecords;
import org.irm.lab.front.entity.DocumentPush;
import org.irm.lab.front.entity.PersonalCollections;
import org.irm.lab.front.service.IPersonalCenterService;
import org.springframework.web.bind.annotation.*;

/**
 * <AUTHOR>
 * @date 2023/7/4 17:18
 * @description 用户中心控制器
 */
@RestController
@RequestMapping("/center")
@RequiredArgsConstructor
public class PersonalCenterController {

    private final IPersonalCenterService iPersonalCenterService;


    /**
     * 收藏公文
     *
     * @param personalCollections {@link PersonalCollections}
     */
    @PostMapping("/collect")
    public R<Void> collect(@RequestBody PersonalCollections personalCollections) {
        iPersonalCenterService.collect(personalCollections);
        return R.success();
    }

    /**
     * 我的收藏 分页查询
     *
     * @return {@link PersonalCollections}
     */
    @GetMapping("/page-collect")
    public R<MyPage<PersonalCollections>> pageCollect(PersonalCollectDTO personalCollectDTO) {
        return R.data(iPersonalCenterService.pageCollect(personalCollectDTO));
    }

    /**
     * 是否收藏
     *
     * @param resourceId 资源Id
     * @return 收藏记录
     */
    @GetMapping("/is-collect")
    public R<String> isCollect(@RequestParam String resourceId) {
        return R.data(iPersonalCenterService.isCollect(resourceId));
    }

    /**
     * 删除我的收藏
     *
     * @param ids id
     */
    @PostMapping("/remove-collect")
    public R<Void> removeCollect(@RequestBody String ids) {
        iPersonalCenterService.removeCollect(Func.objToStrList(ids));
        return R.success();
    }

    /**
     * 删除全部收藏
     */
    @PostMapping("/remove-all-collect")
    public R<Void> removeAllCollect() {
        iPersonalCenterService.removeAllCollect();
        return R.success();
    }

    /**
     * 公文推送
     *
     * @param resourceId 公文Id
     * @param remark     备注
     * @param ids        用户Id
     */
    @PostMapping("/document-push")
    public R<Void> documentPush(@RequestParam String resourceId, @RequestParam String remark, @RequestBody String ids) {
        iPersonalCenterService.documentPush(resourceId, remark, Func.objToStrList(ids));
        return R.success();
    }

    /**
     * 公文推送-分页
     *
     * @param documentPushDTO {@link DocumentPushDTO}
     * @return {@link DocumentPush}
     */
    @GetMapping("/page-document-push")
    public R<MyPage<DocumentPush>> pageDocumentPush(DocumentPushDTO documentPushDTO) {
        return R.data(iPersonalCenterService.pageDocumentPush(documentPushDTO));
    }

    /**
     * 删除推送记录
     *
     * @param type 类型 推送、接收
     * @param ids  id
     */
    @PostMapping("/remove-document-push")
    public R<Void> removeDocumentPush(@RequestParam String type, @RequestBody String ids) {
        iPersonalCenterService.removeDocumentPush(type, Func.objToStrList(ids));
        return R.success();
    }

    /**
     * 查阅记录
     *
     * @param documentAccessRecords {@link DocumentAccessRecords}
     */
    @PostMapping("/access-records")
    public R<Void> accessRecords(@RequestBody DocumentAccessRecords documentAccessRecords) {
        iPersonalCenterService.accessRecords(documentAccessRecords);
        return R.success();
    }

    /**
     * 查阅记录分页
     *
     * @param documentAccessRecordsDTO {@link DocumentAccessRecordsDTO}
     * @return {@link  DocumentAccessRecords}
     */
    @GetMapping("/page-access-records")
    public R<MyPage<DocumentAccessRecords>> pageAccessRecords(DocumentAccessRecordsDTO documentAccessRecordsDTO){
        return R.data(iPersonalCenterService.pageAccessRecords(documentAccessRecordsDTO));
    }

}
