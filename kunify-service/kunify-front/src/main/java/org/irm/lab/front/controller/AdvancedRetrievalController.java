package org.irm.lab.front.controller;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.irm.lab.common.annotation.MyLog;
import org.irm.lab.common.api.R;
import org.irm.lab.common.constant.LogConstant;
import org.irm.lab.common.support.MyPage;
import org.irm.lab.config.entity.Metadata;
import org.irm.lab.config.feign.MetadataFeign;
import org.irm.lab.config.vo.ClassificationItemVO;
import org.irm.lab.config.vo.MetadataVO;
import org.irm.lab.front.dto.AdvanceRetrievalSearchDTO;
import org.irm.lab.front.dto.AdvancedRetrievalDTO;
import org.irm.lab.front.service.IAdvancedRetrievalService;
import org.irm.lab.front.vo.ResourceRetrievalVO;
import org.irm.lab.front.vo.es.ClassifyArray;
import org.irm.lab.repository.entity.Resource;
import org.springframework.web.bind.annotation.*;

import java.util.ArrayList;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2023/4/13 19:03
 * @description 高级检索控制器
 */
@RestController
@RequestMapping("/query")
@RequiredArgsConstructor
public class AdvancedRetrievalController {

    private final IAdvancedRetrievalService iAdvancedRetrievalService;
    private final MetadataFeign metadataFeign;


//    /**
//     * 高级检索
//     *
//     * @param advancedRetrievalDTOs 查询条件
//     * @param page 页数
//     * @param size 条数
//     * @return {@link Resource}
//     */
//    @PostMapping("/advanced-retrieval")
//    @MyLog(menu = LogConstant.MENU_META_MATCH, dataType = LogConstant.DATA_META, operation = LogConstant.OPERATION_SEARCH, system = LogConstant.SYSTEM_FRONT)
//    public R<MyPage<ResourceRetrievalVO>> AdvancedRetrieval(@RequestParam String page ,@RequestParam String size, @RequestBody List<AdvancedRetrievalDTO> advancedRetrievalDTOs) {
//        return R.data(iAdvancedRetrievalService.AdvancedRetrieval(page,size,advancedRetrievalDTOs));
//    }

    /**
     * 高级检索
     *
     * @param advancedRetrievalDTOs 查询条件
     * @param page                  页数
     * @param size                  条数
     * @return {@link Resource}
     */
    @PostMapping("/advanced-retrieval")
    @MyLog(menu = LogConstant.MENU_META_MATCH, dataType = LogConstant.DATA_META, operation = LogConstant.OPERATION_SEARCH, system = LogConstant.SYSTEM_FRONT)
    public R<MyPage<ResourceRetrievalVO>> AdvancedRetrievalPlus(@RequestParam String page, @RequestParam String size, @RequestBody List<AdvancedRetrievalDTO> advancedRetrievalDTOs) {
        return R.data(iAdvancedRetrievalService.advancedRetrievalPlus(page, size, advancedRetrievalDTOs));
    }

//    /**
//     * 根据资源信息生成动态分类列表
//     *
//     * @return 列表信息
//     */
//    @PostMapping("/retrieval-classify")
//    public R<JSONArray> getRetrievalClassify(@RequestBody List<AdvancedRetrievalDTO> advancedRetrievalDTOS) {
//        return R.data(iAdvancedRetrievalService.getRetrievalClassify(advancedRetrievalDTOS));
//    }

    /**
     * 根据资源信息生成动态分类列表
     *
     * @return 列表信息
     */
    @PostMapping("/retrieval-classify")
    public R<List<ClassifyArray>> getRetrievalClassify(@RequestBody AdvanceRetrievalSearchDTO advanceRetrievalSearchDTO) {
        return R.data(iAdvancedRetrievalService.getRetrievalClassifyPlus(advanceRetrievalSearchDTO));
    }

    /**
     * 返回所有可用于查询的元数据项
     *
     * @return 元数据项list
     */
    @ApiOperation(value = "可用于查询的元数据项", hidden = true)
    @GetMapping("/filterable-list")
    public R<List<Metadata>> filterableList() {
        List<Metadata> metadataList = metadataFeign.filterableList().getData();
        return R.data(metadataList);
    }


    public static final List<String> filterClassify = List.of("会议纪要", "发文", "收文", "签报", "工作联系", "简报", "用印审批", "业务沟通");


    /**
     * 获取枚举型/类目型元数据项的值域
     *
     * @param metadataId 元数据项id
     * @return 元数据项的值域
     */
    @ApiOperation(value = "可用于查询的元数据项", hidden = true)
    @GetMapping("/get-rage-by-metadata")
    public R<List<?>> filterableList(@RequestParam String metadataId) {
        return R.data(wrapperWJLX(metadataFeign.getRageByMetadataId(metadataId).getData(), metadataId));
    }

    @SuppressWarnings("unchecked")
    private List<?> wrapperWJLX(List<?> data, String metadataId) {
        MetadataVO metadataVO = metadataFeign.info(metadataId).getData();
        // 【文件类型】下的[公文]，只展示一级类目即可；并且和首页下方的保持一致
        if ("文件类型".equals(metadataVO.getName())) {
            data.stream().map(e -> (LinkedHashMap<String, Object>) e).forEach(ele -> {
                if ("公文".equals(ele.get("name"))) {
                    List<JSONObject> childrenVO = JSONUtil.parseArray(ele.get("children")).stream().map(JSONUtil::parseObj)
                            .filter(vo -> filterClassify.contains(vo.get("name").toString()))
                            .peek(vo -> vo.remove("children")).collect(Collectors.toList());
                    ele.put("children", childrenVO);
                }
            });
        }
        if ("年度".equals(metadataVO.getName())) {
            return data.stream().map(e -> (LinkedHashMap<String, Object>) e).sorted((ele1, ele2) -> Integer.parseInt(ele2.get("sort").toString()) - Integer.parseInt(ele1.get("sort").toString())).collect(Collectors.toList());
        }
        return data;
    }
}
