package org.irm.lab.front.controller;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.json.JSONObject;
import lombok.RequiredArgsConstructor;
import org.irm.lab.common.annotation.MyLog;
import org.irm.lab.common.api.R;
import org.irm.lab.common.constant.LogConstant;
import org.irm.lab.common.support.MyPage;
import org.irm.lab.common.tool.INode;
import org.irm.lab.common.utils.ForestNodeMerger;
import org.irm.lab.front.dto.KnowledgeQueryDTO;
import org.irm.lab.front.service.IKnowledgeQueryService;
import org.irm.lab.kg.entity.KnowledgeConcept;
import org.irm.lab.kg.feign.GraphMaintenanceFeign;
import org.irm.lab.kg.feign.KnowledgeConceptFeign;
import org.irm.lab.kg.vo.KnowledgeConceptVO;
import org.irm.lab.kg.vo.echarts.EchartsVO;
import org.irm.lab.kg.vo.node.NodeEntityVO;
import org.irm.lab.repository.entity.Resource;
import org.springframework.web.bind.annotation.*;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2023/4/20 9:32
 * @description 知识查询控制器
 */
@RestController
@RequestMapping("/knowledge-query")
@RequiredArgsConstructor
public class KnowledgeQueryController {
    private final IKnowledgeQueryService knowledgeQueryService;
    private final KnowledgeConceptFeign knowledgeConceptFeign;
    private final GraphMaintenanceFeign graphMaintenanceFeign;

    /**
     * 知识查询分页
     *
     * @param knowledgeQueryDTO {@link KnowledgeQueryDTO}
     */
    @PostMapping("/page")
    @MyLog(menu = LogConstant.MENU_KG_MATCH, dataType = LogConstant.DATA_KEY_WORK, operation = LogConstant.OPERATION_SEARCH, system = LogConstant.SYSTEM_FRONT)
    public R<MyPage<JSONObject>> page(@RequestBody KnowledgeQueryDTO knowledgeQueryDTO) {
        return R.data(knowledgeQueryService.page(knowledgeQueryDTO));
    }

    /**
     * 查询所有用于检索的概念
     *
     * @return 概念列表
     */
    @GetMapping("/knowledge-concept")
    public R<List<KnowledgeConcept>> getKnowledgeConcept() {
        List<KnowledgeConcept> conceptList = knowledgeConceptFeign.list().getData();
        return R.data(conceptList.stream().filter(knowledgeConcept -> !"0".equals(knowledgeConcept.getParentId())).collect(Collectors.toList()));
    }

    /**
     * 查询所有用于检索的概念
     *
     * @return 概念列表
     */
    @GetMapping("/concept-tree")
    public R<List<INode>> getConceptTree() {
        List<INode> conceptTree = new ArrayList<>();
        List<KnowledgeConcept> conceptList = knowledgeConceptFeign.list().getData();
        List<KnowledgeConceptVO> collect = conceptList.stream()
                .map(concept ->
                        BeanUtil.copyProperties(concept, KnowledgeConceptVO.class))
                .collect(Collectors.toList());
        List<KnowledgeConceptVO> merge = ForestNodeMerger.merge(collect);
        if (ObjectUtil.isNotEmpty(merge))
            merge.forEach(knowledgeConceptVO -> conceptTree.addAll(knowledgeConceptVO.getChildren()));
        return R.data(conceptTree);
    }

    /**
     * 查询与当前节点关联的实例和关系
     *
     * @return 概念列表
     */
    @GetMapping("/echarts-node-with-relation")
    public R<EchartsVO> echartsNodesWithRelation(@RequestParam String nodeId) {
        HashMap<String, Object> map = new HashMap<>();
        map.put("nodeId",nodeId);
        return R.data(graphMaintenanceFeign.echartsNodesWithRelation(map).getData());
    }

    /**
     * 查询单个实例
     *
     * @param id 实例id
     * @return NodeEntity
     */
    @GetMapping("/node-info")
    public R<NodeEntityVO> nodeInfo(@RequestParam String id) {
        return R.data(graphMaintenanceFeign.nodeVOInfo(id).getData());
    }

    /**
     * 实例关系分类展示
     *
     * @param id 实例id
     * @return Map<String, Object>
     */
    @GetMapping("/node-relation-show")
    public R<Map<String, Object>> nodeRelationShow(@RequestParam String id) {
        return R.data(knowledgeQueryService.nodeRelationShow(id));
    }

    /**
     * 获取实例关联的资源
     *
     * @param map 查询条件
     * @return 资源集合
     */
    @GetMapping("/node-involve-resource")
    public R<MyPage<Resource>> nodeInvolveResource(@RequestParam Map<String, Object> map) {
        return R.data(knowledgeQueryService.nodeInvolveResource(map));
    }

    @GetMapping("/node-echarts-tree")
    public R<JSONObject> nodeFileEchartsTree(@RequestParam Map<String, Object> map) {
        return R.data(knowledgeQueryService.nodeFileEchartsTree(map));
    }
}
