package org.irm.lab.front.controller;

import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONObject;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.irm.lab.common.api.R;
import org.irm.lab.front.service.IHomePageService;
import org.springframework.web.bind.annotation.*;

import java.util.Map;


/**
 * <AUTHOR>
 * @date 2023/4/13 11:46
 * @description 首页控制器
 */
@RestController
@RequestMapping("/home-page")
@RequiredArgsConstructor
public class HomePageController {
    private final IHomePageService iHomePageService;

    /**
     * 新增热搜词
     *
     * @param type 热搜词类型
     * @param word 热搜词
     */
    @ApiOperation(value = "记录热搜词")
    @PostMapping("/add-hot-search")
    public R<Long> addHotSearchWord(@RequestParam String type, @RequestParam(required = false) String word) {
        if (StrUtil.isBlank(word)) return R.success();
        // 记录热搜词
        return R.data(iHomePageService.addHotSearchWord(type, word));
    }

    /**
     * 热搜词列表
     *
     * @param type 热搜词类型
     * @return 热搜词列表
     */
    @ApiOperation(value = "热搜词列表")
    @GetMapping("/hot-word-list")
    public R<JSONArray> hotSearchWordList(@RequestParam String type) {
        // 获取热搜词和其对应的搜索次数
        Map<String, Long> hotSearchWordsWithCount = iHomePageService.hotSearchWordList(type);
        return generateResult(hotSearchWordsWithCount);
    }


    /**
     * 热搜词列表
     *
     * @param type 热搜词类型
     * @return 热搜词列表
     */
    @ApiOperation(value = "热搜词列表")
    @GetMapping("/hot-word-list-condition")
    public R<JSONArray> hotSearchWordList(@RequestParam String type, @RequestParam int start, @RequestParam int limit) {
        // 获取热搜词和其对应的搜索次数
        Map<String, Long> hotSearchWordsWithCount = iHomePageService.hotSearchWordList(type, start, limit);
        return generateResult(hotSearchWordsWithCount);
    }


    /**
     * 封装热搜词结果列表
     *
     * @param hotSearchWordsWithCount key：热搜词 value：次数
     * @return 热搜词结果
     */
    private static R<JSONArray> generateResult(Map<String, Long> hotSearchWordsWithCount) {
        JSONArray jsonArray = new JSONArray();
        // 如果不存在热搜词返回null
        if (hotSearchWordsWithCount == null) return R.data(null);
        // 遍历生成
        for (Map.Entry<String, Long> stringLongEntry : hotSearchWordsWithCount.entrySet()) {
            JSONObject jsonObject = new JSONObject();
            jsonObject.putOpt("name", stringLongEntry.getKey());
            jsonObject.putOpt("count", stringLongEntry.getValue());
            jsonArray.add(jsonObject);
        }
        return R.data(jsonArray);
    }



}
