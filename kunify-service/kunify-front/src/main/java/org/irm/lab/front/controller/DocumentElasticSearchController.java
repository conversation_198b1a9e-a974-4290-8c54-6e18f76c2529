package org.irm.lab.front.controller;

import cn.easyes.core.conditions.select.LambdaEsQueryWrapper;
import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import lombok.extern.log4j.Log4j2;
import org.irm.ai.api.doc_qa.DocQaApi;
import org.irm.lab.common.annotation.MyLog;
import org.irm.lab.common.api.R;
import org.irm.lab.common.config.resolve.EsIndexResolver;
import org.irm.lab.common.constant.LogConstant;
import org.irm.lab.common.support.MyPage;
import org.irm.lab.config.entity.ClassificationItem;
import org.irm.lab.config.feign.ClassificationItemFeign;
import org.irm.lab.config.feign.MetadataFeign;
import org.irm.lab.front.config.AiSdkLink;
import org.irm.lab.front.dto.ElasticSearchClassifyDTO;
import org.irm.lab.front.dto.ElasticSearchDTO;
import org.irm.lab.front.enums.ElasticSearchShowEnum;
import org.irm.lab.front.mapper.DocumentEsMapper;
import org.irm.lab.front.model.Document;
import org.irm.lab.front.service.IDocumentESService;
import org.irm.lab.front.service.ILargeDataScreenService;
import org.irm.lab.front.vo.es.Classify;
import org.irm.lab.front.vo.es.ClassifyArray;
import org.irm.lab.kg.algorithm.DocumentUnit;
import org.irm.lab.kg.feign.DocumentUnitFeign;
import org.irm.lab.repository.entity.Resource;
import org.irm.lab.repository.feign.ResourceFeign;
import org.irm.lab.system.repository.TenantRepository;
import org.jasypt.encryption.StringEncryptor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.web.bind.annotation.*;

import javax.annotation.PostConstruct;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2023/4/17 15:42
 * @description 全文检索控制器
 */
@RestController
@Log4j2
@RequestMapping("/es-search")
@RequiredArgsConstructor
public class DocumentElasticSearchController {
    private final IDocumentESService idocumentESService;
    private final ResourceFeign resourceFeign;
    private final DocumentUnitFeign documentUnitFeign;
    private final ClassificationItemFeign classificationItemFeign;
    private final DocumentEsMapper documentEsMapper;
    private final MetadataFeign metadataFeign;
    private final TenantRepository tenantRepository;
    private final EsIndexResolver esIndexResolver;
    private final ILargeDataScreenService largeDataScreenService;

    @Qualifier("jasyptStringEncryptor")
    @Autowired
    private StringEncryptor stringEncryptor;

    /**
     * 根据租户，初始化es索引
     */
    @PostConstruct
    public void initES() {
        try {
            log.info(">>>>>>>>>>>>>>>>>>>>初始化ES索引>>>>>>>>>>>>>>>>>>>>");
            tenantRepository.findAll()
                    .forEach(tenant -> documentEsMapper.createIndex(EsIndexResolver.ES_INDEX_PREFIX + "_" + tenant.getTenantId()));
        } catch (Exception e) {
            log.error("es索引初始化失败,已存在指定索引，无需再次初始化!");
        }
    }

    @PostMapping("/update-meta")
    public R<Document> updateMeta(@RequestBody Document document) {
        return R.data(idocumentESService.update(document));
    }

    @PostMapping("deleteById")
    public R<Integer> deleteById(@RequestParam String id) {
        return R.data(idocumentESService.deleteById(id));
    }

    @GetMapping("/info")
    public R<Document> info(@RequestParam String id) {
        return R.data(idocumentESService.info(id));
    }

    @PostMapping("/getDocumentById")
    public R<Document> getDocumentById(@RequestParam String resourceId) {
        return R.data(idocumentESService.getDocumentById(resourceId));
    }

    /**
     * 手动同步资源
     *
     * @param resourceId 资源Id
     */
    @PostMapping("/sync-resource")
    public R<Void> syncResource(@RequestParam(required = false) String resourceId) {
        if (ObjectUtil.isEmpty(resourceId)) {
            // 同步所有审核通过的公文到ES
            R<List<Resource>> resourceR = resourceFeign.listByCondition(Map.of("stage", "已通过"));
            List<Resource> resourceList = resourceR.getData();
            for (Resource resource : resourceList) {
                List<DocumentUnit> documentUnitList = documentUnitFeign.findByResourceId(resource.getId()).getData();
                if (ObjectUtil.isEmpty(documentUnitList)) continue;
                LambdaEsQueryWrapper<Document> wrapper = new LambdaEsQueryWrapper<>();
                wrapper.eq(Document::getId, resource.getId());
                documentEsMapper.setCurrentActiveIndex(esIndexResolver.resolveEsIndexByTenant());
                List<String> syncedDocumentId = documentEsMapper.selectList(wrapper).stream().map(Document::getId).collect(Collectors.toList());
                if (syncedDocumentId.contains(resource.getId())) continue;
                syncResource(resource);
            }
            return R.success();
        }
        syncResource(resourceFeign.info(resourceId).getData());
        return R.success();
    }

    /**
     * 同步资源到Es中
     *
     * @param resource 资源对象
     */
    private void syncResource(Resource resource) {
        List<DocumentUnit> documentUnitList = documentUnitFeign.findByResourceId(resource.getId()).getData();
        // 合并内容
        StringBuffer stringBuffer = new StringBuffer();
        documentUnitList.forEach(documentUnit -> stringBuffer.append(documentUnit.getContent()));
        Document document = BeanUtil.copyProperties(resource, Document.class);
        document.setTitle(resource.getName());
        document.setTitleCopy(resource.getName());
        document.setContent(stringBuffer.toString());
        document.setContentSuggest(stringBuffer.toString());
        idocumentESService.save(document);
    }

    /**
     * 自动补全
     *
     * @param keyword 搜索词
     * @return 补全词列表
     */
    @GetMapping("/suggest")
    public R<List<String>> autoCompletion(@RequestParam String keyword) {
        return R.data(idocumentESService.autoCompletion(keyword));
    }

    /**
     * 全文检索页面用于固定展示的元数据项列表
     */
    @GetMapping("/show-list")
    public R<JSONArray> showList() {
        return R.data(ElasticSearchShowEnum.toJSONArray());
    }

    /**
     * 全文检索
     *
     * @param elasticSearchDTO 搜索条件
     * @return {@link Document}
     */
    @MyLog(menu = LogConstant.MENU_CONTENT_MATCH, dataType = LogConstant.DATA_KEY_WORK, operation = LogConstant.OPERATION_SEARCH, system = LogConstant.SYSTEM_FRONT)
    @PostMapping("/match")
    public R<MyPage<Document>> match(@RequestParam String page, @RequestParam String size, @RequestBody List<ElasticSearchDTO> elasticSearchDTO) {
        //【脚本】删除重复title的es数据
        if ("删除重复title的es数据".equals(elasticSearchDTO.get(0).getKeyword())) {
            idocumentESService.delDuplicateEsData();
        }
        //【脚本】获取AES加密后的字符串
        if (elasticSearchDTO.get(0).getKeyword().contains("aes加密结果")) {
            String[] split = elasticSearchDTO.get(0).getKeyword().split("、");
            log.info("【{}】加密结果：【{}】", split[1], stringEncryptor.encrypt(split[1]));
        }
        return R.data(idocumentESService.match(page, size, elasticSearchDTO));
    }


    /**
     * 查询所有用于检索的公文分类的值
     *
     * @return 所有公文分类类型
     */
    @GetMapping("/document-classify")
    @ApiOperation(value = "查询所有的公文分类的值")
    public R<List<ClassificationItem>> documentClassify() {
        return R.data(classificationItemFeign.enableListByName("公文分类").getData());
    }

    /**
     * 公文下的一级实例
     *
     * @return 所有公文下的一级实例
     */
    @GetMapping("/document-classify-gw")
    @ApiOperation(value = "查询所有公文下的的类目")
    public R<List<ClassificationItem>> documentClassifyGW() {
        return R.data(wrapperGWList(classificationItemFeign.gwList().getData()));
    }

    public static final List<String> filterClassify = List.of("会议纪要", "发文", "收文", "签报", "工作联系", "简报", "用印审批", "业务沟通");


    private List<ClassificationItem> wrapperGWList(List<ClassificationItem> classificationItemList) {
        classificationItemList.sort(Comparator.comparing(ClassificationItem::getSort).reversed());
        return classificationItemList.stream().filter(item -> filterClassify.contains(item.getName())).collect(Collectors.toList());
    }

    /**
     * 查询所有用于检索的年度
     *
     * @return 所有年份
     */
    @GetMapping("/document-year")
    @ApiOperation(value = "查询所有的公文年度")
    public R<List<String>> documentYear() {
        return R.data(idocumentESService.findAllDocumentYear());
    }

    /**
     * 获取检索条件
     *
     * @return JSONArray
     */
    @GetMapping("/es-filter-condition")
    public R<JSONArray> filterCondition() {
        return R.data(metadataFeign.filterCondition("advancedFilterAble").getData());
    }

//    /**
//     * 根据资源信息生成动态分类列表
//     *
//     * @return 列表信息
//     */
//    @PostMapping("/search-classify")
//    public R<List<ClassifyArray>> getSearchClassify(@RequestBody List<ElasticSearchDTO> elasticSearchDTOs) {
//        return R.data(idocumentESService.getSearchClassify(elasticSearchDTOs));
//    }

    /**
     * 根据资源信息生成动态分类列表2
     *
     * @return 列表信息
     */
    @PostMapping("/search-classify2")
    public R<List<ClassifyArray>> getSearchClassify(@RequestBody ElasticSearchClassifyDTO elasticSearchDTOs) {
//        return R.data(idocumentESService.getSearchClassifyBySetting(elasticSearchDTOs));
        return R.data(idocumentESService.getSearchClassifyByResult(elasticSearchDTOs));
    }


    @GetMapping("/infer-new")
    public R<JSONArray> inferNew(@RequestParam String question) {
        DocQaApi qaModelApi = DocQaApi.build(AiSdkLink.sdkApikey, AiSdkLink.sdkSecretKey, AiSdkLink.qaHost);
        //todo 通过question到es中match
        LambdaEsQueryWrapper<Document> wrapper = new LambdaEsQueryWrapper<>();
        wrapper.match(Document::getContent, question);
        wrapper.limit(1);
        JSONArray result = JSONUtil.createArray();
        List<Document> documents = documentEsMapper.selectList(wrapper);
        // TODO 过滤掉分数低的，暂时只拿第一个
        // TODO 关键词过滤
        long time1 = System.currentTimeMillis();
        for (Document document : documents) {
            String stringBuilder = document.getHighlightContent() +
                    "\n" +
                    "根据以上信息回答，" +
                    question;
            log.info("问题 ：{}" , stringBuilder);
            JSONObject infer = qaModelApi.chat(stringBuilder, AiSdkLink.qaHost);
            if (infer == null || !infer.containsKey("data")) return R.failed();
            String data = infer.getStr("data");
            log.info(data);
            JSONObject obj = JSONUtil.createObj();
            obj.putOpt("docId", document.getId()).putOpt("answer", data).putOpt("title", document.getTitle());
            result.add(obj);
        }
        long time2 = System.currentTimeMillis();
        log.info("计算时间：【{}】", time2 - time1);
        return R.data(result);
    }

    @GetMapping("/meta-value-count")
    public R<Map<String, Long>> getTypeCountVOS(@RequestParam String name) {
        return R.data(largeDataScreenService.getTypeCountVOS(name));
    }


}
