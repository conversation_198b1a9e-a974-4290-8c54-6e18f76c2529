# Feign请求头传播问题诊断指南

## 问题现象
你在`kunify-user`服务的`UserProviderServiceImpl.getCurrentUser()`方法中无法获取到从`kunify-system`服务传过来的`user`请求头。

## 问题分析

### 调用链路
```
客户端请求 → 网关 → kunify-system服务 → (Feign调用) → kunify-user服务
```

### 关键点
1. **网关层**：`AuthGlobalFilter`将JWT token解析后注入`user`头
2. **kunify-system服务**：接收到带有`user`头的请求
3. **Feign调用**：`FeignAuthRequestInterceptor`应该传播`user`头
4. **kunify-user服务**：应该接收到传播的`user`头

## 诊断步骤

### 第1步：验证kunify-system服务能否获取到user头

1. **调用测试接口**：
   ```
   GET /feign-test/header-propagation
   ```

2. **查看日志**，确认：
   - 是否看到"当前请求上下文中存在user头"
   - 是否看到"FeignAuthRequestInterceptor.apply() 开始处理Feign请求头传播"

### 第2步：验证FeignAuthRequestInterceptor是否工作

1. **启用DEBUG日志**：
   ```yaml
   logging:
     level:
       org.irm.lab.common.config.FeignAuthRequestInterceptor: DEBUG
   ```

2. **重启服务**，查看启动日志是否有：
   ```
   注册FeignAuthRequestInterceptor - 用于传播HTTP请求头到Feign调用
   ```

3. **调用Feign接口**，查看是否有DEBUG日志：
   ```
   DEBUG - FeignAuthRequestInterceptor.apply() 开始处理Feign请求头传播
   DEBUG - 成功传播user头: {...}
   ```

### 第3步：验证kunify-user服务是否接收到请求头

1. **直接调用kunify-user服务的测试接口**：
   ```
   GET /user-provider/test-headers
   ```
   
   **注意**：这个调用需要手动添加user头：
   ```
   curl -H "user: {your-user-json}" http://kunify-user-host/user-provider/test-headers
   ```

2. **通过kunify-system服务间接调用**：
   ```
   GET /feign-test/header-propagation
   ```
   这会触发对kunify-user服务的Feign调用

### 第4步：检查日志输出

#### kunify-system服务日志应该显示：
```
INFO  - === 开始获取当前用户菜单列表 ===
INFO  - 当前请求上下文中存在user头: {...}
DEBUG - FeignAuthRequestInterceptor.apply() 开始处理Feign请求头传播
DEBUG - 成功传播user头: {...}
INFO  - 准备调用userProviderFeign.getRoleNames()...
```

#### kunify-user服务日志应该显示：
```
INFO  - === UserProviderServiceImpl.getCurrentUser() 开始获取当前用户 ===
INFO  - ✅ 请求头中存在user信息: {...}
INFO  - 从HTTP请求头解析用户信息: {...}
INFO  - 成功获取到用户: ID=xxx, Name=xxx
```

## 可能的问题和解决方案

### 问题1：FeignAuthRequestInterceptor没有被注册

**症状**：启动日志中看不到"注册FeignAuthRequestInterceptor"

**解决方案**：
1. 确认`kunify-common`模块被正确依赖
2. 确认应用主类的`scanBasePackages`包含`org.irm.lab`
3. 手动检查`FeignConfiguration`是否被扫描到

### 问题2：拦截器被注册但没有被调用

**症状**：看到注册日志，但看不到拦截器的DEBUG日志

**解决方案**：
1. 检查是否有其他Feign配置覆盖了默认配置
2. 检查Feign客户端的配置是否正确
3. 确认DEBUG日志级别已启用

### 问题3：拦截器被调用但RequestContextHolder.getRequestAttributes()返回null

**症状**：看到"无法获取ServletRequestAttributes"

**解决方案**：
1. 确认调用是在HTTP请求线程中，不是异步线程
2. 检查是否有其他拦截器或过滤器影响了请求上下文
3. 考虑使用ThreadLocal作为备选方案

### 问题4：请求头被传播但kunify-user服务接收不到

**症状**：kunify-system日志显示传播成功，但kunify-user日志显示接收失败

**解决方案**：
1. 检查网络层面是否有代理或负载均衡器过滤了请求头
2. 检查kunify-user服务的请求处理链是否正常
3. 使用网络抓包工具验证HTTP请求是否包含user头

## 快速修复方案

如果上述诊断仍然无法解决问题，可以使用以下临时方案：

### 方案1：强制使用ThreadLocal

在`FeignAuthRequestInterceptor`中强制设置ThreadLocal：

```java
@Override
public void apply(RequestTemplate requestTemplate) {
    // 现有逻辑...
    
    // 强制从ThreadLocal获取并设置
    String userFromThreadLocal = ThreadLocalUtil.get("user");
    if (userFromThreadLocal != null) {
        requestTemplate.header("user", userFromThreadLocal);
        log.info("强制从ThreadLocal传播user头");
    }
}
```

### 方案2：在调用前手动设置ThreadLocal

在`MenuServiceImpl.currentMenuList()`中：

```java
// 在Feign调用前设置ThreadLocal
String currentUserId = getCurrentUserId();
if (currentUserId != null) {
    // 构造用户信息并设置到ThreadLocal
    JSONObject userJson = new JSONObject();
    userJson.put("user_id", currentUserId);
    ThreadLocalUtil.set("user", userJson.toString());
}

// 然后进行Feign调用
List<String> roleNames = userProviderFeign.getRoleNames().getData();
```

## 验证修复效果

修复后，你应该能在kunify-user服务的日志中看到：

```
INFO  - === UserProviderServiceImpl.getCurrentUser() 开始获取当前用户 ===
INFO  - ✅ 请求头中存在user信息: {...}
INFO  - 从HTTP请求头解析用户信息: {...}
INFO  - 解析得到用户ID: your-user-id
INFO  - 成功获取到用户: ID=your-user-id, Name=your-user-name
```

## 联系支持

如果按照上述步骤仍然无法解决问题，请提供：

1. **两个服务的启动日志**（特别是FeignAuthRequestInterceptor相关的）
2. **调用/feign-test/header-propagation的完整日志**
3. **调用/user-provider/test-headers的返回结果**
4. **网络配置信息**（如果有代理或负载均衡器）

这样可以更准确地定位问题所在。
