# Base64编码解决方案（备选）

如果压缩JSON字段后仍然有问题，可以使用Base64编码：

## 修改AuthGlobalFilter（网关）

```java
// 在网关中使用Base64编码
String compactUserStr = compactUserJson.toString();
String encodedUser = Base64.getEncoder().encodeToString(compactUserStr.getBytes(StandardCharsets.UTF_8));
LOGGER.info("Base64编码后长度: {} 字符", encodedUser.length());

ServerHttpRequest request = exchange.getRequest().mutate()
    .header("user", encodedUser)
    .header("user-encoding", "base64")  // 标记编码方式
    .build();
```

## 修改FeignAuthRequestInterceptor

```java
// 在拦截器中传播编码标记
if ("user".equals(headerName)) {
    requestTemplate.header(headerName, headerValue);
    // 同时传播编码标记
    String encoding = request.getHeader("user-encoding");
    if (encoding != null) {
        requestTemplate.header("user-encoding", encoding);
    }
}
```

## 修改UserProviderServiceImpl

```java
private String decodeUserHeader(String userHeader) {
    ServletRequestAttributes attributes = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
    if (attributes != null) {
        String encoding = attributes.getRequest().getHeader("user-encoding");
        if ("base64".equals(encoding)) {
            try {
                byte[] decodedBytes = Base64.getDecoder().decode(userHeader);
                return new String(decodedBytes, StandardCharsets.UTF_8);
            } catch (Exception e) {
                log.error("Base64解码失败: {}", e.getMessage());
            }
        }
    }
    return userHeader; // 如果不是Base64编码，直接返回
}
```

但是，我建议先尝试方案2（压缩JSON），因为它更简单且通常足够解决问题。
