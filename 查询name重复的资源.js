var mapFunction = function() {
    // 发射文档的name和_id
    emit(this.name, {id: this._id});
};

var reduceFunction = function(key, values) {
    // 创建一个数组来收集所有_id
    var ids = [];
    values.forEach(function(value) {
        ids.push(value.id.str);
    });
    // 返回一个对象，包含_id数组和计数
    return {ids: ids, count: ids.length};
};

var finalizeFunction = function(key, reducedValue) {
    // 如果name只出现一次，则不返回结果
    if (reducedValue.count > 1) {
        // 返回一个新对象，包含name和_id数组
        return {
            "name": key, // 保留name
            "docIds": reducedValue.ids // 使用新的字段名docIds
        };
    } else {
        return null;
    }
};

var mrOptions = {
    query: {"isDeleted":"正常"}, // 可选的过滤条件
    out: { inline: 1 }, // 结果直接返回，不写入集合
    finalize: finalizeFunction
};

var results = db.kunify_repository_resource.mapReduce(mapFunction, reduceFunction, mrOptions);

// 过滤掉那些 value 为 null 的结果
var filteredResults = results.results.filter(function(doc) {
    return doc.value !== null;
});

// 打印过滤后的结果
printjson(filteredResults.length);

// 准备批量插入操作
var bulk = db.kunify_repository_resource_temp.initializeUnorderedBulkOp();
filteredResults.forEach(function(doc) {
    bulk.insert({
        "_id": ObjectId(), // 使用MongoDB自动生成的ObjectId
        "name": doc._id, // 使用原来的name作为文件名
        "docIds": doc.value.docIds ,// 使用新的字段名docIds
        "isDeleted" : "正常" ,
        "deleteFlag" : "未删除"
    });
});

// 执行批量插入操作
bulk.execute();