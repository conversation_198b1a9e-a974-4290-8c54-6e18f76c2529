pipeline {
    agent any

    tools {
        // jenkins中配置的名称
        jdk 'jdk-11'
        maven 'maven-local'
    }

    environment {
        MAVEN_HOME = tool 'maven-local'
        MAVEN_SETTINGS = "${env.MAVEN_HOME}/conf/settings.xml"
        GIT_URL = "http://************:8300/Suwell-PLSS/huaneng-server.git"
        GIT_CREDENTIALS = "44615952-a251-403b-b040-2490bb33c502"
    }

    parameters {
        choice(name: 'BRANCH', choices: ['master', 'v1.6.0'], description: '仓库分支')
        choice(name: 'MODULE', choices: ['kunify-auth', 'kunify-gateway',
                                         'kunify-config', 'kunify-front', 'kunify-kg',
                                         'kunify-log', 'kunify-repository', 'kunify-resource',
                                         'kunify-system', 'kunify-user'],
                description: '更新模块')
    }

    stages {
        stage('check repository') {
            steps {
                script {
                    // 打印当前工作区路径
                    echo "current workspace: ${env.WORKSPACE}"
                    // 会自动判断执行 git clone pull（自动切换到对应分支）
                    git branch: "${params.BRANCH}", url: "${GIT_URL}", credentialsId: "${GIT_CREDENTIALS}"
                }
            }
        }

        stage('build maven') {
            steps {
                // 使用 Maven 打包，跳过测试；使用全局级别的 settings.xml，而不是用户级别 ~/.m2/settings.xml
                echo "${env.MAVEN_HOME}"
                sh "java --version"
                sh "mvn clean install -DskipTests=true -Dmaven.multiModuleProjectDirectory=${env.WORKSPACE} -s ${env.MAVEN_SETTINGS}"
            }
        }

        stage('deploy docker') {
            steps {
                script {
                    def modulePath;
                    if (params.MODULE == "kunify-auth" || params.MODULE == "kunify-gateway") {
                        modulePath = "${params.MODULE}/target/${params.MODULE}.jar"
                    } else {
                        modulePath = "kunify-service/${params.MODULE}/target/${params.MODULE}.jar"
                    }
                    echo "deploy ${modulePath}"
                    sh "sudo docker cp ${modulePath} ${params.MODULE}:/app/app.jar && sudo docker restart ${params.MODULE}"
                }
            }
        }

    }

    post {
        success {
            echo 'pipeline complete!'
        }
        failure {
            echo 'pipeline failed!'
        }
    }
}