pipeline {
    agent any

    tools {
        // jenkins中配置的名称
        jdk 'jdk-11'
        maven 'maven-local'
    }

    environment {
        MAVEN_HOME = tool 'maven-local'
        MAVEN_SETTINGS = "${env.MAVEN_HOME}/conf/settings.xml"
        GIT_URL = "http://************:8300/Suwell-PLSS/huaneng-server.git"
        REMOTE_PATH = "/home/<USER>/jar/"
        SSH_NAME = "************"
    }

    parameters {
        choice(name: 'BRANCH', choices: ['master', 'v1.6.0', 'hzdt-dc', ' v1.6.1'], description: '仓库分支')
        choice(name: 'MODULE', choices: ['kunify-auth', 'kunify-gateway',
                                         'kunify-config', 'kunify-front', 'kunify-kg',
                                         'kunify-log', 'kunify-repository', 'kunify-resource',
                                         'kunify-system', 'kunify-user'],
                description: '更新模块')
    }

    stages {
        stage('check repository') {
            steps {
                script {
                    // 打印当前工作区路径
                    echo "current workspace: ${env.WORKSPACE}"
                    // 会自动判断执行 git clone pull（自动切换到对应分支）
                    git branch: "${params.BRANCH}", url: "${GIT_URL}"
                }
            }
        }

        stage('build maven') {
            steps {
                // 使用 Maven 打包，跳过测试；使用全局级别的 settings.xml，而不是用户级别 ~/.m2/settings.xml
                sh "mvn clean install -DskipTests=true -Dmaven.multiModuleProjectDirectory=${env.WORKSPACE} -s ${env.MAVEN_SETTINGS}"
            }
        }

        stage('upload server') {
            steps {
                script {
                    def modulePath;
                    if (params.MODULE == "kunify-auth" || params.MODULE == "kunify-gateway") {
                        modulePath = "${params.MODULE}/target/${params.MODULE}.jar"
                    } else {
                        modulePath = "kunify-service/${params.MODULE}/target/${params.MODULE}.jar"
                    }
                    echo "Transfer ${modulePath} to ${SSH_NAME} ${REMOTE_PATH}"
                    // 使用 SSH 插件上传 JAR 包到服务器
                    sshPublisher(publishers: [
                            sshPublisherDesc(
                                    configName: "${SSH_NAME}",
                                    transfers: [
                                            sshTransfer(
                                                    sourceFiles: "${modulePath}",
                                                    remoteDirectory: "${REMOTE_PATH}",
                                                    flatten: true
                                            )
                                    ],
                                    usePromotionTimestamp: false,
                                    useWorkspaceInPromotion: false,
                                    verbose: true
                            )
                    ])
                }
            }
        }

        stage('deploy docker') {
            steps {
                script {
                    sshagent(credentials: ['0a845cad-7722-4580-8845-199bb19ffee5']) {
                        def command =
                                "ssh -o StrictHostKeyChecking=no root@${SSH_NAME} 'docker cp ${REMOTE_PATH}${params.MODULE}.jar ${params.MODULE}:/app/app.jar && docker restart ${params.MODULE}'" as Object
                        // 使用 sh 步骤执行远程命令
                        sh command
                    }
                }
            }
        }
    }

    post {
        success {
            echo 'pipeline complete!'
        }
        failure {
            echo 'pipeline failed!'
        }
    }
}