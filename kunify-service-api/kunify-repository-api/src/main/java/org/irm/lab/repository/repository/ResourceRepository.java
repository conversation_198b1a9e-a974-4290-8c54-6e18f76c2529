package org.irm.lab.repository.repository;

import com.mongodb.client.MongoCollection;
import com.mongodb.client.MongoDatabase;
import com.mongodb.client.model.Filters;
import org.bson.Document;
import org.bson.conversions.Bson;
import org.irm.lab.common.annotation.MongoDao;
import org.irm.lab.common.constant.AbstractBaseEntityFieldConstant;
import org.irm.lab.common.pojo.BaseStatus;
import org.irm.lab.common.repository.mongo.BaseDao;
import org.irm.lab.repository.entity.Resource;

import java.util.ArrayList;
import java.util.List;


/**
 * <AUTHOR>
 * @date 2023/2/1 14:36
 * @description 资源持久层
 */
@MongoDao
public class ResourceRepository extends BaseDao<Resource> {

    public List<Document> taskFindDocument(Bson filter) {
        Bson isDel = Filters.eq(AbstractBaseEntityFieldConstant.IS_DELETED, BaseStatus.OK);
        filter = filter == null ? isDel : Filters.and(isDel, filter);
        MongoDatabase mongoDatabase = mongoTenantFactory.selectDatabase(tenantProvider.getTenantId());
        MongoCollection<Document> collection = mongoDatabase.getCollection("kunify_repository_resource");
        Document document = new Document();
        ArrayList<Document> into;
        try {
            into = collection.find(filter).projection(document).into(new ArrayList<>());
        } catch (NullPointerException e) {
            into = new ArrayList<>();
        }
        return into;
    }

}
