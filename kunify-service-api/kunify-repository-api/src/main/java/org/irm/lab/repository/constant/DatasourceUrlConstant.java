//package org.irm.lab.repository.constant;
//
//public interface DatasourceUrlConstant {
//
//    String ip = "**************";
//
//    String port = "80";
//
//    //访问ip + 端口
//    String DOMAIN = "http://" + ip + ":" + port;
//
//    //登录(获取token)
//    String LOGIN_PATH = "/auth/login";
//
//    //登录名称
//    String USER_NAME = "admin";
//
//    //登陆密码
//    String PASSWORD = "Passw0rd@123";
//
//    //获取所有业务类型
//    String ALL_BIZ_TYPE = "/zisecm/metadata/getAllBizType";
//
//    //根据分类获取元数据对照表
//    String METADATA_BY_TYPE = "/zisecm/metadata/getMetadataByType";
//
//    //获取文件预览页面
//    String FILE_PREVIEW = "/#/viewDoc";
//
//    //获取甲方短token
//    String SHORT_TOKEN = "/zisecm/view/preview";
//
//    //无水印文件下载
//    String DOWNLOAD_NO_WATER_MARK = "/zisecm/sync/doc/downloadNoWatermark";
//
//    String DOWNLOAD_MARK_ZIP = "/zisecm/sync/doc/download";
//
//    String GET_DOC = "/zisecm/sync/doc/getdoc";
//}
