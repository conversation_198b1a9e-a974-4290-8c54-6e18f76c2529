package org.irm.lab.repository.constant;

public interface DatasourceResourceConstant {

    String NAME = "name";

    String MEDIA_TYPE = "mediaType";

    String FILE_ID = "fileId";

    String FILE_TYPE = "fileType";

    String CLASSIFY = "classify";

    String AUTHOR = "gzzdAuthor";

    String CATEGORY_ID = "gzzdCategoryId";

    String NEWS_DATE = "gzzdNewsDate";

    String URL = "gzzdUrl";

    String SORT_NUMBER = "gzzdSortNumber";


    String ATTACHES = "attaches";

    String METAS = "metas";


//    String META_NAME = "metaName";
//
//    String META_DESCRIPTION = "metaDescription";
//
//    String META_VALUE = "metaValue";
//
//    String REMOTE_ATTACH_FILE_ID = "remoteAttachFileId";
//
//    String REMOTE_ATTACH_FILE_NAME = "remoteAttachName";
//
//    String REMOTE_ATTACH_MEDIA_TYPE = "remoteAttachMediaType";
}
