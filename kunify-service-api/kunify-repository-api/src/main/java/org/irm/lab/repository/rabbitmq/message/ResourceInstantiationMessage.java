package org.irm.lab.repository.rabbitmq.message;

import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2023/3/3 10:04
 * @description 资源实例化消息
 */
@Data
public class ResourceInstantiationMessage implements Serializable {
    /**
     * 资源实例化 交换机
     */
    public static final String EXCHANGE = "KUNIFY4_EXCHANGE_RESOURCE_INSTANTIATION_";
    /**
     * 资源实例化 消息队列
     */
    public static final String QUEUE = "KUNIFY4_QUEUE_RESOURCE_INSTANTIATION_";
    /**
     * 资源实例化 routingKey
     */
    public static final String ROUTING_KEY = "KUNIFY4_ROUTING_RESOURCE_INSTANTIATION_";
    /**
     * 当前用户信息
     */
    private String user;
    /**
     * 工作任务Id
     */
    private String workTaskId;
}
