package org.irm.lab.repository.constant;

/**
 * <AUTHOR>
 * @date 2023/2/15 11:50
 * @description 用于查询资源时的不同状态区分
 */
public interface ResourceStageConstant {
    /**
     * 待著录
     */
    String UN_RECORD = "un_record";
    /**
     * 已驳回
     */
    String REJECTED = "rejected";
    /**
     * 待审核
     */
    String UN_AUDIT = "un_audit";

    /**
     * 驳回_待审核
     */
    String REJECTED_UN_AUDIT = "rejected_un_audit";
}
