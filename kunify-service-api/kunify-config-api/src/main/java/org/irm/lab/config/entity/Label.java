package org.irm.lab.config.entity;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import org.irm.lab.common.annotation.MongoDBCollection;
import org.irm.lab.common.entity.AbstractBaseEntity;

import javax.validation.constraints.NotBlank;

/**
 * <AUTHOR>
 * @date 2023/2/7 17:32
 * @description 数据集标签实体类
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@EqualsAndHashCode(callSuper = true)
@MongoDBCollection(collectionName = "kunify_config_label")
@ApiModel("数据集标签")
public class Label extends AbstractBaseEntity {
    @ApiModelProperty("关联的数据集ID")
    @NotBlank(message = "数据集ID不能为空")
    private String dataSetId;
    @ApiModelProperty("标签名称")
    @NotBlank(message = "标签名称不能为空")
    private String name;
    @ApiModelProperty("数据集标签标识")
    @NotBlank(message = "数据集标签标识不能为空")
    private String identifier;
    @ApiModelProperty(value = "类型", notes = "概念、关系、属性")
    @NotBlank(message = "标签类型不能为空")
    private String type;
    @ApiModelProperty(value = "描述")
    private String description;
    @ApiModelProperty("自定义颜色rgba")
    private String labelColorRGBA;
    @ApiModelProperty(value = "来源id",notes = "来源于知识模型的 概念、关系、属性 的Id")
    private String sourceId;

    private Integer sort;

}
