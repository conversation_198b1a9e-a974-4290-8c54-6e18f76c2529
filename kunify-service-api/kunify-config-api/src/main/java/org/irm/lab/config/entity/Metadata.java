package org.irm.lab.config.entity;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import org.irm.lab.common.annotation.MongoDBCollection;
import org.irm.lab.common.entity.AbstractBaseEntity;
import org.irm.lab.common.pojo.BaseStatus;

import javax.validation.constraints.NotBlank;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/1/5 17:10
 * @description 元数据实体类
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@EqualsAndHashCode(callSuper = true)
@MongoDBCollection(collectionName = "kunify_config_metadata")
@ApiModel("元数据")
public class Metadata extends AbstractBaseEntity {
    @ApiModelProperty("元数据中文名称")
    @NotBlank(message = "元数据中文名称不能为空")
    private String name;

    @ApiModelProperty("元数据英文名称")
    @NotBlank(message = "元数据英文名称不能为空")
    private String nameEn;

    @ApiModelProperty("元数据标识")
    @NotBlank(message = "元数据标识不能为空")
    private String definition;

    @ApiModelProperty("元数据数据类型")
    @NotBlank(message = "元数据数据类型不能为空")
    private String datatype;

    @ApiModelProperty("字典ID")
    private String dictId;

    @ApiModelProperty("类目集ID")
    private String classificationSetId;

    @ApiModelProperty("最小值")
    private String min;

    @ApiModelProperty("最大值")
    private String max;

    @ApiModelProperty("精度")
    private String precision;

    @ApiModelProperty("短名")
    @NotBlank(message = "短名不能为空")
    private String nameShort;

    @ApiModelProperty("默认值")
    private String defaultValue;

    @ApiModelProperty("是否可修改")
    @NotBlank(message = "是否可修改字段不能为空")
    private String modifiable;

    @ApiModelProperty("是否多值")
    private String multiple;

    @ApiModelProperty("自动化填充算法")
    private String autoDescribeAlgorithm;

    @ApiModelProperty("检测算法")
    private List<String> checkAlgorithms;

    @ApiModelProperty("是否用于综合查询展示字段")
    private String queryable = BaseStatus.FALSE;

    @ApiModelProperty("是否用于综合查询中查询字段")
    private String filterable = BaseStatus.FALSE;

    @ApiModelProperty("是否用于高级查询的过滤条件")
    private boolean esFilterAble = false;

    @ApiModelProperty("是否用于全文检索的过滤条件")
    private boolean advancedFilterAble = false;

    @ApiModelProperty("排序")
    private Integer sort;
}
