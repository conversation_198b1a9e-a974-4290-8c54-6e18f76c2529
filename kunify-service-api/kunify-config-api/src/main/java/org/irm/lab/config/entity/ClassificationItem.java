package org.irm.lab.config.entity;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import org.irm.lab.common.annotation.MongoDBCollection;
import org.irm.lab.common.entity.AbstractBaseEntity;
import org.irm.lab.common.pojo.BaseStatus;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @date 2022/12/30 18:11
 * @description 类目
 */
@Data
@EqualsAndHashCode(callSuper = true)
@AllArgsConstructor
@NoArgsConstructor
@MongoDBCollection(collectionName = "kunify_config_classification_item")
@ApiModel("类目")
public class ClassificationItem extends AbstractBaseEntity {

    @ApiModelProperty("类目集ID")
    @NotBlank(message = "类目集ID不能为空")
    private String setId;

    @ApiModelProperty("父级类目ID")
    private String parentId;

    @ApiModelProperty("类目名称")
    @NotBlank(message = "类目名称不能为空")
    private String name;

    @ApiModelProperty("类目标识")
    @NotBlank(message = "类目标识不能为空")
    private String identifier;

    @ApiModelProperty("排序")
    @NotNull(message = "排序字段不能为空")
    private Integer sort;

    @ApiModelProperty(value = "状态" ,notes = "两种状态：禁用 启用")
    private String status = BaseStatus.OK;

    @ApiModelProperty("描述")
    private String description;


}
