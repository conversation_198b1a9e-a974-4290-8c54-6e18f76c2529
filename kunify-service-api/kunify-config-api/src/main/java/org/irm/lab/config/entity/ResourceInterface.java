package org.irm.lab.config.entity;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import org.irm.lab.common.annotation.MongoDBCollection;
import org.irm.lab.common.entity.AbstractBaseEntity;
import org.irm.lab.common.pojo.BaseStatus;

import javax.validation.constraints.NotBlank;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2023/1/17 11:04
 * @description 资源接口
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@EqualsAndHashCode(callSuper = true)
@MongoDBCollection(collectionName = "kunify_config_resource_interface")
@ApiModel("资源接口")
public class ResourceInterface extends AbstractBaseEntity {
    @ApiModelProperty("接口名称")
    @NotBlank(message = "接口名称不能为空")
    private String name;
    @ApiModelProperty("接口标识")
    @NotBlank(message = "接口标识不能为空")
    private String identifier;
    @ApiModelProperty("接口描述")
    private String description;
    @ApiModelProperty("是否定时执行")
    @NotBlank(message = "请选择是否定时执行")
    private String isTimed = BaseStatus.FALSE;
    @ApiModelProperty("是否定制化")
    @NotBlank(message = "请选择是否定制化")
    private String isCustom = BaseStatus.FALSE;
    @ApiModelProperty("请求地址")
    @NotBlank(message = "请求地址不能为空")
    private String url;
    @ApiModelProperty("请求方式")
    @NotBlank(message = "请求方式不能为空")
    private String method;
    @ApiModelProperty("Cron表达式")
    private String cronStr;
    @ApiModelProperty("请求参数")
    private Map<String, Object> reqParams;
    @ApiModelProperty("请求体")
    private Map<String, Object> reqBody;
    @ApiModelProperty("响应结果")
    private Map<String, Object> resBody;
}
