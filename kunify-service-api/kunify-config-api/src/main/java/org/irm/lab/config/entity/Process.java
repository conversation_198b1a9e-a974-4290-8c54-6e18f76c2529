package org.irm.lab.config.entity;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import org.irm.lab.common.annotation.MongoDBCollection;
import org.irm.lab.common.entity.AbstractBaseEntity;

import javax.validation.constraints.NotBlank;
import java.util.List;
import java.util.Objects;

@Data
@AllArgsConstructor
@NoArgsConstructor
@MongoDBCollection(collectionName = "kunify_config_process")
@ApiModel("处理流程")
@JsonIgnoreProperties(ignoreUnknown = true)
public class Process extends AbstractBaseEntity {

    @ApiModelProperty(value = "处理流程名称")
    @NotBlank(message = "处理流程名称不能为空")
    private String name;

    @ApiModelProperty(value = "描述")
    private String description;

    @ApiModelProperty(value = "元数据方案id")
    private String metadataSchemaId;

    private String type;

    @ApiModelProperty(value = "模型id")
    private String kgModelId;

    @ApiModelProperty(value = "数据集id")
    private String dataSetId;

    @ApiModelProperty(value = "流程负责人id集合", notes = "有权限依据此处理流程创建工作任务的人")
    private List<String> ownerIds;

    @ApiModelProperty(value = "资源库部署状态")
    private Boolean repoStatus;

    @ApiModelProperty(value = "知识库部署状态")
    private Boolean kgStatus;

    private List<ProcessNode> repoProcessNodes;

    private List<ProcessNode> kgProcessNodes;

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;

        Process that = (Process) o;

        return this.getId().equals(that.getId());
    }

    @Override
    public int hashCode() {
        return 1;
    }
}
