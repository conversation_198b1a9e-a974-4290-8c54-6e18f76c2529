package org.irm.lab.config.entity;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import org.irm.lab.common.annotation.MongoDBCollection;
import org.irm.lab.common.entity.AbstractBaseEntity;
import org.irm.lab.common.pojo.BaseStatus;

import javax.validation.constraints.NotBlank;

/**
 * <AUTHOR>
 * @date 2023/1/3 18:51
 */
@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
@EqualsAndHashCode(callSuper = true)
@MongoDBCollection(collectionName = "kunify_config_algorithm")
@ApiModel("算法")
public class Algorithm extends AbstractBaseEntity {
    @ApiModelProperty("算法名称")
    @NotBlank(message = "名称不能为空")
    private String name;

    @ApiModelProperty("算法标识")
    @NotBlank(message = "标识不能为空")
    private String identifier;

    /**
     * 元数据自动填充算法、元数据检测算法、文档解析算法、实体识别算法、关系识别算法
     */
    @ApiModelProperty("算法类型")
    @NotBlank(message = "类型不能为空")
    private String type;

    @ApiModelProperty("状态")
    private String status = BaseStatus.OK;

    @ApiModelProperty("描述")
    private String description;
}
