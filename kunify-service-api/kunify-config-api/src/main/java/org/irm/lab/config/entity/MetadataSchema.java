package org.irm.lab.config.entity;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import org.irm.lab.common.annotation.MongoDBCollection;
import org.irm.lab.common.entity.AbstractBaseEntity;
import org.irm.lab.common.pojo.BaseStatus;

import javax.validation.constraints.NotBlank;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/1/6 14:07
 * @description 元数据方案实体类
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@EqualsAndHashCode(callSuper = true)
@MongoDBCollection(collectionName = "kunify_config_metadata_schema")
@ApiModel("元数据方案")
public class MetadataSchema extends AbstractBaseEntity{
    @ApiModelProperty("元数据方案名称")
    @NotBlank(message = "元数据方案名称不能为空")
    private String name;

    @ApiModelProperty("元数据方案标识")
    @NotBlank(message = "元数据方案标识不能为空")
    private String identifier;

    @ApiModelProperty("是否启用分组标签")
    @NotBlank(message = "请设置是否使用分组标签")
    private String labelEnabled;

    @ApiModelProperty("分组标签的ID列表")
    private List<String> labelIds;

    @ApiModelProperty("描述")
    private String description;

    @ApiModelProperty("状态")
    private String status= BaseStatus.OK;

    @ApiModelProperty("元数据项复制体")
    private List<MetadataItem> metadata;

}
