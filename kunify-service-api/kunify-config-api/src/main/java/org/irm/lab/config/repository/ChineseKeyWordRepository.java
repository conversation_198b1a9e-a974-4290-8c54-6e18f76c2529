package org.irm.lab.config.repository;

import org.irm.lab.common.annotation.MongoDao;
import org.irm.lab.common.repository.mongo.BaseDao;
import org.irm.lab.config.entity.Algorithm;
import org.irm.lab.config.entity.ChineseKeyWord;

/**
 * <AUTHOR> <br/>
 * @date 2024/6/26 <br/>
 * &#064;Copyright  博客：<a href="https://eliauku.gitee.io/">...</a>  ||  per aspera and astra <br/>
 */
@MongoDao
public class ChineseKeyWordRepository extends BaseDao<ChineseKeyWord> {
}
