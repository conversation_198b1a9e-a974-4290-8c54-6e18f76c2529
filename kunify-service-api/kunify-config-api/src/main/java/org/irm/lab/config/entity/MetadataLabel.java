package org.irm.lab.config.entity;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import org.irm.lab.common.annotation.MongoDBCollection;
import org.irm.lab.common.entity.AbstractBaseEntity;
import org.irm.lab.common.pojo.BaseStatus;

import javax.validation.constraints.NotBlank;

/**
 * <AUTHOR>
 * @date 2023/1/4 9:31
 * @description 元数据标签
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@EqualsAndHashCode(callSuper = true)
@MongoDBCollection(collectionName = "kunify_config_metadata_label")
@ApiModel("元数据标签")
public class MetadataLabel extends AbstractBaseEntity {
    @ApiModelProperty("元数据标签名称")
    @NotBlank(message = "元数据标签名称不能为空")
    private String name;

    @ApiModelProperty("元数据标签标识")
    @NotBlank(message = "元数据标签标识不能为空")
    private String identifier;

    @ApiModelProperty(value = "状态" ,notes = "两种状态：禁用 启用")
    private String status = BaseStatus.OK;

    @ApiModelProperty("描述")
    private String description;
}
