package org.irm.lab.config.entity;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@AllArgsConstructor
@NoArgsConstructor
@ApiModel("工作任务节点")
public class WorkTaskNode{

    private String identifier;
    @ApiModelProperty(value = "工作任务id")
    private String workTaskId;

    @ApiModelProperty(value = "工作任务节点名称", notes = "同步流程节点名")
    private String name;

    @ApiModelProperty(value = "工作任务节点描述")
    private String description;

    @ApiModelProperty(value = "流程节点复制体")
    private ProcessNode processNode;

    private Integer sort;

    @ApiModelProperty(value = "任务参与人id集合")
    private List<String> ownerIds;

    @ApiModelProperty(value = "是否展示当前任务节点")
    private Boolean isShow = true;

}
