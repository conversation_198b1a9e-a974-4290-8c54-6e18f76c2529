package org.irm.lab.config.feign;

import io.swagger.annotations.ApiOperation;
import org.irm.lab.common.api.R;
import org.irm.lab.common.constant.AppConstant;
import org.irm.lab.config.entity.MetadataItem;
import org.irm.lab.config.entity.MetadataSchema;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;
import java.util.Map;


@FeignClient(
        name = AppConstant.APPLICATION_CONFIG_NAME + "${router.name}",
        contextId = "MetadataSchemaFeign"
)
public interface MetadataSchemaFeign {

    String API_PREFIX = "/metadata-schema";

    @GetMapping(API_PREFIX + "/info")
    R<MetadataSchema> info(@RequestParam("id") String id);


    /**
     * 查询所有可用的元数据方案
     *
     * @return {@link MetadataSchema}
     */
    @ApiOperation(value = "元数据方案列表")
    @GetMapping(API_PREFIX + "/list")
    R<List<MetadataSchema>> list(@RequestParam(required = false) Map<String, Object> queryParam);


    /**
     * 根据Id列表查询元数据方案列表
     *
     * @param ids 元数据方案Id
     * @return {@link MetadataSchema}
     */
    @PostMapping(API_PREFIX + "/list-by-ids")
    R<List<MetadataSchema>> listByIds(@RequestBody String ids);


    /**
     * 根据元数据方案id集合，元数据项id，获取每一个元数据meta
     */
    @ApiOperation(value = "获取元数据metadata")
    @PostMapping(API_PREFIX + "/meta-by-schema-and-meta-id")
    R<List<MetadataItem>> metaBySchemaAndMetaId(@RequestBody Map<String, Object> body);
}
