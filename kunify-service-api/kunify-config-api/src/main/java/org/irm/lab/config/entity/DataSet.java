package org.irm.lab.config.entity;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import org.irm.lab.common.annotation.MongoDBCollection;
import org.irm.lab.common.entity.AbstractBaseEntity;

import javax.validation.constraints.NotBlank;

/**
 * <AUTHOR>
 * @date 2023/1/13 10:05
 * @description 数据集实体类
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@EqualsAndHashCode(callSuper = true)
@MongoDBCollection(collectionName = "kunify_config_data_set")
@ApiModel("数据集")
public class DataSet extends AbstractBaseEntity {
    @ApiModelProperty("数据集名称")
    @NotBlank(message = "数据集名称不能为空")
    private String name;

    @ApiModelProperty("数据集标识")
    @NotBlank(message = "数据集标识不能为空")
    private String identifier;

    @ApiModelProperty("模型Id")
    private String modelId;

    @ApiModelProperty("描述")
    private String description;

}
