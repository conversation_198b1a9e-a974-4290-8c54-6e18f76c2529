package org.irm.lab.config.constant;

import java.util.List;

public class ProcessNodeStage {

    public static final String RESOURCE_SUBMISSION = "资源提交";
    public static final String RESOURCE_DESCRIPTION = "资源著录";
    public static final String RESOURCE_AUDIT = "资源审核";
    public static final String KNOWLEDGE_VALIDATION = "知识校验";
    public static final String DATA_CONVERSION = "数据转换";
    public static final String RESOURCE_PROCESSING = "资源加工";
    public static final String KNOWLEDGE_MATCH = "知识匹配";
    public static final String KNOWLEDGE_ALIGNMENT = "实体对齐";
    public static final String KNOWLEDGE_VISUALIZATION = "知识可视化";





    public static final String TO_BE_MANUALLY_RECORDED = "待人工著录";




    public static List<String> repoResourceStatus(String status) {
        // 资源提交环节
        if ((RESOURCE_SUBMISSION+"环节").equals(status)) {
            return List.of("待人工著录");
        }
        // 资源著录环节
        if ((RESOURCE_DESCRIPTION+"环节").equals(status)) {
            return List.of("待人工著录", "已驳回");
        }
        // 资源审核环节
        if ((RESOURCE_AUDIT+"环节").equals(status)) {
            return List.of("待审核", "驳回_待审核");
        }
        return null;
    }
}
