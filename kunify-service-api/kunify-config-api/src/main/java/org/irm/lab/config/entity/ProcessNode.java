package org.irm.lab.config.entity;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.List;

@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
@ApiModel("流程节点")
public class ProcessNode {

    @ApiModelProperty(value = "流程节点名称")
    @NotBlank(message = "流程节点名称不能为空")
    private String stageName;
    @ApiModelProperty(value = "所属流程Id")
    private String processId;
    @ApiModelProperty(value = "描述")
    private String description;
    @ApiModelProperty(value = "排序")
    @NotNull(message = "排序字段不能为空")
    private Integer sort;
    @ApiModelProperty(value = "顶级标签id")
    private String topLabelId;
    /**
     * 资源库
     */
    @ApiModelProperty(value = "元数据项自动填充")
    private Boolean isMetadataAutofill;
    @ApiModelProperty(value = "元数据项校验")
    private Boolean isMetadataVerify = true;
    @ApiModelProperty(value = "自动交接")
    private Boolean isAutoPass;
    @ApiModelProperty(value = "资源审核")
    private Boolean isAudit;
    /**
     * 资源加工字段
     */
    @ApiModelProperty(value = "资源实例化")
    private Boolean isResourceInstantiation = true;
    @ApiModelProperty(value = "元数据转化")
    private Boolean isMetadataConversion;
    @ApiModelProperty(value = "文档解析")
    private Boolean isDocParsing;
    @ApiModelProperty(value = "知识标注")
    private Boolean isKgAnnotation;
    @ApiModelProperty(value = "规则解析")
    private Boolean isDocRuleParsing;
    @ApiModelProperty(value = "知识匹配")
    private String kgMatchId;
    @ApiModelProperty(value = "元数据转化器ID")
    private String metadataConverterId;
    @ApiModelProperty(value = "知识标注转化器ID")
    private String knowledgeConverterId;
    @ApiModelProperty(value = "文档解析器", notes = "多值")
    private List<String> docParsing;
    @ApiModelProperty(value = "文档规则匹配器", notes = "多值")
    private List<String> docRuleParsing;
    /**
     * 实体对齐
     */
    @ApiModelProperty(value = "是否自动进行实体对齐")
    private Boolean isKnowledgeAlignment;
    /**
     * 流程节点负责人
     */
    @ApiModelProperty(value = "负责人ids")
    private List<String> ownerIds;
}
