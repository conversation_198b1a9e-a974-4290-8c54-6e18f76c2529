package org.irm.lab.config.entity;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import org.irm.lab.common.annotation.MongoDBCollection;
import org.irm.lab.common.entity.AbstractBaseEntity;
import org.irm.lab.common.pojo.BaseStatus;

import javax.validation.constraints.NotBlank;

/**
 * <AUTHOR>
 * @date 2022/12/27 15:09
 * @description 数据字典实体类
 */

@Data
@AllArgsConstructor
@NoArgsConstructor
@EqualsAndHashCode(callSuper = true)
@MongoDBCollection(collectionName = "kunify_config_dict")
@ApiModel("数据字典")
public class Dict extends AbstractBaseEntity {


    @ApiModelProperty("字典名称")
    @NotBlank(message = "字典名称不能为空")
    private String name;

    @ApiModelProperty("字典标识")
    @NotBlank(message = "字典标识不能为空")
    private String identifier;

    @ApiModelProperty(value = "状态" ,notes = "两种状态：禁用 启用")
    private String status = BaseStatus.OK;

    @ApiModelProperty("描述")
    private String description;
}
