package org.irm.lab.config.entity;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.irm.lab.common.annotation.MongoDBCollection;
import org.irm.lab.common.entity.AbstractBaseEntity;

import javax.validation.constraints.NotBlank;
import java.util.List;

@Data
@AllArgsConstructor
@NoArgsConstructor
@MongoDBCollection(collectionName = "kunify_config_work_task")
@JsonIgnoreProperties(ignoreUnknown = true)
@ApiModel("工作任务")
public class WorkTask extends AbstractBaseEntity {

    @NotBlank(message = "名称不能为空")
    @ApiModelProperty(value = "工作任务名称")
    private String name;

    @ApiModelProperty(value = "工作任务说明")
    private String description;

    @ApiModelProperty(value = "任务状态")
    private String taskStatus;

    @ApiModelProperty(value = "当前任务的资源总量")
    private Long totalResourceNum;

    @ApiModelProperty(value = "任务负责人id集合", notes = "流程负责人一定是对应任务的负责人")
    private List<String> ownerIds;

    @ApiModelProperty(value = "所属处理流程的复制体")
    private Process process;

    @ApiModelProperty(value = "任务节点ids")
    private List<WorkTaskNode> workTaskNodes;

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        WorkTask that = (WorkTask) o;
        return this.getId().equals(that.getId());
    }

    @Override
    public int hashCode() {
        return 1;
    }
}
