package org.irm.lab.config.entity;

import lombok.Getter;
import lombok.Setter;

/**
 * <AUTHOR>
 * @date 2023/2/9 16:40
 */
@Getter
@Setter
public class DataConverterItem {
    /**
     * 唯一标识
     */
    private String identifier;
    /**
     * 元数据复制体Id，同时是元数据项Id
     */
    private String sourceMetadataId;
    /**
     * 元数据项复制体名称
     */
    private String metadataItemName;
    /**
     * 属性标签Id
     */
    private String labelPropertyId;
    /**
     * 关系标签Id
     */
    private String relationId;
    /**
     * 标签Id（宾语）
     */
    private String labelId;
}
