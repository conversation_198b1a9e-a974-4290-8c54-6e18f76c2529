package org.irm.lab.config.entity;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;


/**
 * <AUTHOR>
 * @date 2023/1/6 14:11
 * @description 元数据项复制体实体类
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@EqualsAndHashCode(callSuper = true)
@ApiModel("元数据项复制体")
public class MetadataItem extends Metadata {

    @ApiModelProperty("元数据项的源")
    private String sourceMetadataId;
    @ApiModelProperty("元数据分组标签ID")
    private String labelId;
    @ApiModelProperty("元数据分组标签名称")
    private String labelName;
}
