package org.irm.lab.config.entity;

import org.bson.codecs.pojo.annotations.BsonProperty;
import org.irm.lab.common.annotation.MongoDBCollection;
import org.irm.lab.common.constant.AbstractBaseEntityFieldConstant;
import org.irm.lab.common.entity.AbstractBaseEntity;

import io.swagger.annotations.ApiModel;
import lombok.AllArgsConstructor;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

/**
 * <AUTHOR> <br/>
 * @date 2024/6/26 <br/>
 * &#064;Copyright  博客：<a href="https://eliauku.gitee.io/">...</a>  ||  per aspera and astra <br/>
 */

@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
@EqualsAndHashCode(callSuper = true)
@MongoDBCollection(collectionName = "kunify_config_chinese_key_word")
@ApiModel("算法")
public class ChineseKeyWord extends AbstractBaseEntity {

    @BsonProperty("zi")
    private String zi;

    @BsonProperty("stroke_count")
    private String strokeCount;

    @BsonProperty("stroke_count_decomposed")
    private String strokeCountDecomposed;

    @BsonProperty("mandarin_pinyin")
    private String mandarinPinyin;

    @BsonProperty("cantonese_pinyin")
    private String cantonesePinyin;

    @BsonProperty("english")
    private String english;

    @BsonProperty("radical")
    private String radical;

    @BsonProperty("radical_stroke_count")
    private Long radicalStrokeCount;

    @BsonProperty("radical_pinyin")
    private String radicalPinyin;

    @BsonProperty("radical_english")
    private String radicalEnglish;


    @BsonProperty("variant")
    private String variant;


    @BsonProperty("fc_code")
    private String fcCode;

    @BsonProperty("cj_code")
    private String cjCode;

    @BsonProperty("leaf_component")
    private String leafComponent;

    @BsonProperty("zis_with_this_component")
    private String zisWithThisComponent;


}
