package org.irm.lab.config.entity;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import org.irm.lab.common.annotation.MongoDBCollection;
import org.irm.lab.common.entity.AbstractBaseEntity;
import org.irm.lab.common.pojo.BaseStatus;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @date 2022/12/27 15:10
 * @description 字典条目实体类
 */
@Data
@EqualsAndHashCode(callSuper = true)
@AllArgsConstructor
@NoArgsConstructor
@MongoDBCollection(collectionName = "kunify_config_dict_Item")
@ApiModel("字典条目")
public class DictItem extends AbstractBaseEntity {

    @ApiModelProperty("字典Id")
    @NotBlank(message = "字典Id不能为空")
    private String dictId;

    @ApiModelProperty("字典条目标识")
    @NotBlank(message = "字典条目标识不能为空")
    private String identifier;

    @ApiModelProperty("排序")
    @NotNull(message = "排序字段不能为空")
    private Integer sort;

    @ApiModelProperty("字典条目标签")
    @NotBlank(message = "字典条目标签不能为空")
    private String label;

    @ApiModelProperty("字典条目值")
    @NotBlank(message = "字典条目值不能为空")
    private String value;

    @ApiModelProperty(value = "状态" ,notes = "两种状态：禁用 启用")
    private String status =BaseStatus.OK;

    @ApiModelProperty("描述")
    private String description;
}
