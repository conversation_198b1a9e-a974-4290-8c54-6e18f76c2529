package org.irm.lab.config.entity;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import org.irm.lab.common.annotation.MongoDBCollection;
import org.irm.lab.common.entity.AbstractBaseEntity;

import javax.validation.constraints.NotBlank;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/1/12 15:23
 * @description 数据转化器实体类
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@EqualsAndHashCode(callSuper = true)
@MongoDBCollection(collectionName = "kunify_config_data_converter")
@ApiModel("数据转化器")
public class DataConverter extends AbstractBaseEntity {
    @ApiModelProperty("数据转化器名称")
    @NotBlank(message = "数据转化器名称不能为空")
    private String name;

    @ApiModelProperty("数据转化器标识")
    @NotBlank(message = "数据转化器标识不能为空")
    private String identifier;

    @NotBlank(message = "数据集不能为空")
    private String dataSetId;

    @ApiModelProperty("元数据方案Id")
    private String metadataSchemaId;

    @ApiModelProperty(value = "转化器类型")
    @NotBlank(message = "转化器类型不能为空")
    private String type;

    @ApiModelProperty("元数据转化")
    private List<DataConverterItem> dataConverterItems;
    @ApiModelProperty("知识标注转化")
    private List<SchemaMappingItem> schemaMappingItems;
    @ApiModelProperty("描述")
    private String description;

}
