package org.irm.lab.config.entity;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.irm.lab.common.annotation.MongoDBCollection;
import org.irm.lab.common.entity.AbstractBaseEntity;


@Data
@AllArgsConstructor
@NoArgsConstructor
@MongoDBCollection(collectionName = "kunify_config_document_template")
@JsonIgnoreProperties(ignoreUnknown = true)
@ApiModel("文档模板")
public class  DocumentTemplate extends AbstractBaseEntity {

    @ApiModelProperty(value = "文档模板名称")
    private String name;

    @ApiModelProperty(value = "文档类型", notes = "制度、公文")
    private String documentType;

    @ApiModelProperty(value = "文档模板描述")
    private String description;

    @ApiModelProperty(value = "文档类别", notes = "收文、发文")
    private String documentCategory;

    @ApiModelProperty(value = "文档状态", notes = "启用、禁用")
    private String status;

    @ApiModelProperty(value = "模板被引用次数")
    private Long count = 0L;

    @ApiModelProperty(value = "文档模板attach")
    private String attachId;

}
