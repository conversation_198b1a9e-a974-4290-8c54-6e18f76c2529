package org.irm.lab.config.entity;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import org.irm.lab.common.annotation.MongoDBCollection;
import org.irm.lab.common.entity.AbstractBaseEntity;
import org.irm.lab.common.pojo.BaseStatus;

import javax.validation.constraints.NotBlank;

/**
 * <AUTHOR>
 * @date 2022/12/30 18:10
 * @description 类目集
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@EqualsAndHashCode(callSuper = true)
@MongoDBCollection(collectionName = "kunify_config_classification_set")
@ApiModel("类目集")
public class ClassificationSet extends AbstractBaseEntity {
    @ApiModelProperty("类目集名称")
    @NotBlank(message = "类目集名称不能为空")
    private String name;

    @ApiModelProperty("类目集标识")
    @NotBlank(message = "类目集标识不能为空")
    private String identifier;

    @ApiModelProperty(value = "状态" ,notes = "两种状态：禁用 启用")
    private String status = BaseStatus.OK;

    @ApiModelProperty("描述")
    private String description;
}
