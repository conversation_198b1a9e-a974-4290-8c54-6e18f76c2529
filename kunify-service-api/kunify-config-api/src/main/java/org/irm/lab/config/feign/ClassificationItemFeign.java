package org.irm.lab.config.feign;

import io.swagger.annotations.ApiOperation;
import org.irm.lab.common.api.R;
import org.irm.lab.common.constant.AppConstant;
import org.irm.lab.config.entity.ClassificationItem;
import org.irm.lab.config.vo.ClassificationItemVO;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/2/16 16:54
 * @description 类目项Feign接口
 */
@FeignClient(
        name = AppConstant.APPLICATION_CONFIG_NAME + "${router.name}",
        contextId = "ClassificationItemFeign"
)
public interface ClassificationItemFeign {
    String API_PREFIX = "/classification-item";

    /**
     * 根据类目集Id，查询该类目集下的所有可用类目项
     *
     * @param setId 类目集Id
     * @return {@link ClassificationItem}
     */
    @GetMapping(API_PREFIX + "/enable-list")
    R<List<ClassificationItem>> enableList(@RequestParam("setId") String setId);

    @GetMapping(API_PREFIX + "/enable-list-by-name")
    R<List<ClassificationItem>> enableListByName(@RequestParam String setName);

    @PostMapping(API_PREFIX + "/sort-tree")
    R<List<ClassificationItemVO>> sortTree(@RequestBody List<ClassificationItemVO> classificationItemList);

    @GetMapping(API_PREFIX + "/gw-list")
    R<List<ClassificationItem>> gwList();

    @GetMapping(API_PREFIX + "/zd-list")
    R<List<ClassificationItem>> zdList();
}
