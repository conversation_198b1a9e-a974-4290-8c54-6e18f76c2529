package org.irm.lab.config.entity;

import org.irm.lab.common.annotation.MongoDBCollection;
import org.irm.lab.common.entity.AbstractBaseEntity;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;

import io.swagger.annotations.ApiModel;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 排序配置
 * <AUTHOR> <br/>
 * @date 2024/6/26 <br/>
 * &#064;Copyright  博客：<a href="https://eliauku.gitee.io/">...</a>  ||  per aspera and astra <br/>
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@MongoDBCollection(collectionName = "kunify_config_sort")
@JsonIgnoreProperties(ignoreUnknown = true)
@ApiModel("排序配置")
public class SortConfig extends AbstractBaseEntity {

    /**
     * 顺序Json
     */
    private String orderJson;

    /**
     * 排序类型
     */
    private String type;



}
