package org.irm.lab.front.enums;

import lombok.Getter;

/**
 * 热搜词类型枚举
 */
@Getter
public enum HotSearchWordType {
    FULL_TEXT_SEARCH("FULL_TEXT_SEARCH_", "全文检索"),
    KNOWLEDGE_QUERY("KNOWLEDGE_QUERY_", "知识查询"),
    LEADER_INTELLIGENT("LEADER_INTELLIGENT_", "领导批示"),
    INTELLIGENT_QA("INTELLIGENT_QA_", "智能问答"),
    REGULATORY_FRAMEWORK("REGULATORY_FRAMEWORK", "规章制度");

    private final String code;
    private final String value;

    HotSearchWordType(String code, String value) {
        this.code = code;
        this.value = value;
    }


    public static String getCodeByType(String type) {
        for (HotSearchWordType hotSearchWordType : HotSearchWordType.values()) {
            if (hotSearchWordType.value.equals(type)) {
                return hotSearchWordType.code;
            }
        }
        return "未知类型";
    }
}
