package org.irm.lab.front.model;

import cn.easyes.annotation.IndexField;
import cn.easyes.annotation.rely.FieldType;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

@Data
@Accessors(chain = true)
@AllArgsConstructor
@NoArgsConstructor
public class MetadataVO implements Serializable {
    @ApiModelProperty("元数据中文名称")
    @IndexField(fieldType = FieldType.KEYWORD)
    private String name;

    @ApiModelProperty(value = "其他类型元数据项值")
    @IndexField(fieldType = FieldType.KEYWORD)
    private List<String> value;

    @ApiModelProperty("日期类型元数据项")
    @IndexField(fieldType = FieldType.DATE)
    private Date dateValue;

    @ApiModelProperty("排序")
    @IndexField(fieldType = FieldType.INTEGER)
    private Integer sort;
}