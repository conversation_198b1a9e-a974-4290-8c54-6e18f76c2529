package org.irm.lab.front.entity;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import io.swagger.annotations.ApiModel;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import org.irm.lab.common.annotation.MongoDBCollection;
import org.irm.lab.common.entity.AbstractBaseEntity;

/**
 * <AUTHOR>
 * @date 2023/7/4 17:29
 * @description 个人收藏实体类
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@EqualsAndHashCode(callSuper = true)
@JsonIgnoreProperties(ignoreUnknown = true)
@MongoDBCollection(collectionName = "kunify_front_personal_collections")
@ApiModel(value = "个人收藏")
public class PersonalCollections extends AbstractBaseEntity {
    /**
     * 资源Id
     */
    private String resourceId;
    /**
     * 资源名称
     */
    private String resourceName;
    /**
     * 用户Id
     */
    private String userId;

}
