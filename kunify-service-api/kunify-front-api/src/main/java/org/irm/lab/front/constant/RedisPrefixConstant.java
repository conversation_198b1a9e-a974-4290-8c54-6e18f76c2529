package org.irm.lab.front.constant;

/**
 * <AUTHOR>
 * @date 2023/6/25 17:21
 * @description
 */
public interface RedisPrefixConstant {

    String COUNT= "count_";

    /**
     * 最上边6条数据
     */
    String TOP6 = "dataCountKey";

    /**
     * 公文类型占比，公文累积量，部门累计公文排行
     */
    String DATA_COUNT_KEY = "gwDataCountKey";

    /**
     * 会议情况统计
     */
    String MEETING_COUNT_KEY = "meetingDataCountKey";

    /**
     * 后台统计
     */
    String BACK_COUNT_KEY = "backDataCountKey";

    /**
     * 后台统计树状图
     */
    String BACK_COUNT_TREE_KEY = "backDataCountTreeKey";

    String VISITS_USER_PRE="visits_user_";

    String RESOURCE_STATISTICS_KEY = "resource_statistics_";
}
