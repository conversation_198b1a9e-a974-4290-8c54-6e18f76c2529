package org.irm.lab.front.enums;

import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONObject;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * @date 2023/4/20 18:17
 * @description 全文检索元数据展示枚举
 */
@Getter
@AllArgsConstructor
public enum ElasticSearchShowEnum {

    WEN_JIAN_BIAN_HAO("文件编号", true),
    GONG_WEN_FEN_LEI("公文分类", true),
    FA_WEN_RI_QI("发文日期", true),
    QIAN_FA_REN("签发人", true),
    SUO_SHU_BU_MEN("所属部门", true),
    GUI_DANG_RI_QI("归档日期", true);


    private final String name;
    private final boolean show;

    public static JSONArray toJSONArray() {
        JSONArray jsonArray = new JSONArray();
        for (ElasticSearchShowEnum e : ElasticSearchShowEnum.values()) {
            JSONObject jsonObject = new JSONObject();
            jsonObject.putOpt("name",e.getName());
            jsonObject.putOpt("flag",e.isShow());
            jsonArray.add(jsonObject);
        }
        return jsonArray;
    }

}
