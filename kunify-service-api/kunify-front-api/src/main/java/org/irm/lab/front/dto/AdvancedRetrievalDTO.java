package org.irm.lab.front.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.sql.Time;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/4/14 10:33
 * @description 高级检索条件DTO
 */
@Data
public class AdvancedRetrievalDTO {
    @ApiModelProperty("查询条件")
    private List<QueryMetadataDTO> queryCondition;

    @ApiModelProperty("过滤条件")
    private List<FilterMetadataDTO> filterCondition;

    @ApiModelProperty("时间区间")
    private List<QueryMetadataDTO> timeCondition;
}
