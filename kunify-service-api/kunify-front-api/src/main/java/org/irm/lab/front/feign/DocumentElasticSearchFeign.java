package org.irm.lab.front.feign;


import org.irm.lab.common.api.R;
import org.irm.lab.common.constant.AppConstant;
import org.irm.lab.front.model.Document;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.Map;

@FeignClient(name = AppConstant.APPLICATION_FRONT_NAME + "${router.name}", contextId = "DocumentElasticSearchFeign")
public interface DocumentElasticSearchFeign {
    String API_PREFIX = "/es-search";

    @PostMapping(API_PREFIX + "/update-meta")
    R<Document> updateMeta(@RequestBody Document document);

    @PostMapping(API_PREFIX + "/getDocumentById")
    R<Document> getDocumentById(@RequestParam String resourceId);

    @GetMapping(API_PREFIX + "/info")
    R<Document> info(@RequestParam String id);

    @PostMapping(API_PREFIX + "/deleteById")
    R<Integer> deleteById(@RequestParam String id);

    @GetMapping(API_PREFIX + "/meta-value-count")
    R<Map<String, Long>> getTypeCountVOS(@RequestParam String name);
}
