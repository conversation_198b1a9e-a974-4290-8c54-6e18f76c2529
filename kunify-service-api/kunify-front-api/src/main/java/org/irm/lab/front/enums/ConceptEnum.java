package org.irm.lab.front.enums;

import java.util.Arrays;
import java.util.List;
import java.util.Set;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR> <br/>
 * @date 2024/6/19 <br/>
 * @Copyright 博客：https://eliauku.gitee.io/  ||  per aspera and astra <br/>
 */
@Getter
@AllArgsConstructor
public enum ConceptEnum {
    BIAO_ZHUN_ZHI_DU(Set.of("规章制度"), Arrays.asList("制度", "制定目的", "制定依据", "适用范围", "概念术语", "机构/部门", "职责", "制度条款", "废纸文件")),
    HUI_YI(Set.of("会议纪要"), Arrays.asList("公文", "会议", "机构/部门", "人员", "项目", "会议议题", "纪要", "文件")),
    GE_REN_RONG_YU(Set.of("表彰荣誉","奖章荣誉"), Arrays.asList("公文", "表彰年度", "表彰时间", "授奖机构", "机构/部门", "人员", "职务", "寄语", "附件")),
    LING_DAO_PI_FU(Set.of("领导批示"),List.of()),
    NULL(Set.of("无分类"),List.of());

    private final Set<String> fileType;


    private final List<String> sortRules;

    public static ConceptEnum filter(String fileType) {
        for (ConceptEnum setEnum : ConceptEnum.values()) {
            if (setEnum.fileType.contains(fileType)) return setEnum;
        }
        return NULL;
    }
}
