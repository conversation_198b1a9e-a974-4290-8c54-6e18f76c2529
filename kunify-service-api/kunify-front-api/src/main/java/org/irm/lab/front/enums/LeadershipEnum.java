package org.irm.lab.front.enums;

import cn.hutool.core.util.ObjectUtil;
import lombok.Getter;

import java.util.*;
import java.util.stream.Collectors;

@Getter
public enum LeadershipEnum {
    LEADERSHIP("领导", List.of("温枢刚", "张文峰", "王益华", "王利民", "李向良", "张涛", "郝金玉", "司为国", "舒印彪", "曹培玺", "李小鹏", "邓建玲", "黄永达", "王森", "樊启祥","王文宗","王敏"));

    final String type;
    final List<String> extensions;

    LeadershipEnum(String type, List<String> extensions) {
        this.type = type;
        this.extensions = extensions;
    }

    public static List<String> leaderList() {
        List<String> leaders = new ArrayList<>();
        for (LeadershipEnum mediaType : LeadershipEnum.values()) {
            if (mediaType != LEADERSHIP) continue;
            if (ObjectUtil.isEmpty(mediaType.extensions)) continue;
            leaders.addAll(mediaType.extensions);
        }
        return leaders;
    }

}
