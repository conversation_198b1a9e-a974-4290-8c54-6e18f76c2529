package org.irm.lab.front.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.ArrayList;
import java.util.List;

@Data
public class AdvanceRetrievalSearchDTO {
    @ApiModelProperty(value = "查询条件")
    private List<AdvancedRetrievalDTO> advancedRetrievalDTOS;

    @ApiModelProperty(value = "刷新条件", notes = "表示哪些元数据需要重新统计数量")
    private List<String> refreshCondition = new ArrayList<>();
}
