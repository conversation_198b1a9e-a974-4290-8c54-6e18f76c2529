package org.irm.lab.front.feign;

import org.irm.lab.common.constant.AppConstant;
import org.irm.lab.front.dto.AIMiddle.OcrInferResultDTO;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RequestPart;
import org.springframework.web.multipart.MultipartFile;

/**
     
 * @date 2024/2/18 <br/>
      
 */

@FeignClient(name = AppConstant.APPLICATION_FRONT_NAME + "${router.name}", contextId = "AIFeign")
public interface AIFeign {

    String API_PREFIX = "/aiController";

    @PostMapping(value =  "/ocrInfer", consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
    OcrInferResultDTO ocrInfer(@RequestPart MultipartFile file, @RequestParam(required = false) String tenant,
                               @RequestParam(required = false, defaultValue = "1") String drawBox,
                               @RequestParam(required = false, defaultValue = "dg") String model);

}

