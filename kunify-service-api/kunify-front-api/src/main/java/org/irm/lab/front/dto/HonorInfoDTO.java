package org.irm.lab.front.dto;

import lombok.Data;
import org.irm.lab.front.vo.analyse.meeting.PropertyVO;

import java.util.List;

@Data
public class HonorInfoDTO {


    private String id;

    /**
     * 资源Id
     */
    private String resourceId;

    /**
     * 表彰称号
     */
    private String honorName;

    /**
     * 表彰类型
     */
    private String honorType;

    /**
     *发文日期
     */
    private String date;

    /**
     * 来源公文
     */
    private String resourceName;

    /**
     * 人员或集体名称
     */
    private List<String> names;

    /**
     * 表彰属性
     */
    private List<PropertyVO> properties;

    /**
     * 称号是否关于人员，是为人员，否为集体
     */
    private boolean isPerson;
}
