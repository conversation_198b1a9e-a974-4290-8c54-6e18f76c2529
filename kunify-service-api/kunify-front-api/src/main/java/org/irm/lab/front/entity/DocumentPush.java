package org.irm.lab.front.entity;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import io.swagger.annotations.ApiModel;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import org.irm.lab.common.annotation.MongoDBCollection;
import org.irm.lab.common.entity.AbstractBaseEntity;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/7/5 11:22
 * @description 推送公文
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@EqualsAndHashCode(callSuper = true)
@JsonIgnoreProperties(ignoreUnknown = true)
@MongoDBCollection(collectionName = "kunify_front_document_push")
@ApiModel(value = "公文推送")
public class DocumentPush extends AbstractBaseEntity {
    /**
     * 资源Id
     */
    private String resourceId;
    /**
     * 资源名称
     */
    private String resourceName;
    /**
     * 推送人
     */
    private String pushUser;
    /**
     * 接收人
     */
    private List<String> pushedUser;
    /**
     * 备注
     */
    private String remark;
    /**
     * 推送类型 ： 推送 / 接收
     */
    private String type;
}
