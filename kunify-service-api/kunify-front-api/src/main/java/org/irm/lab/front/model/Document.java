package org.irm.lab.front.model;

import cn.easyes.annotation.*;
import cn.easyes.annotation.rely.Analyzer;
import cn.easyes.annotation.rely.FieldType;
import cn.easyes.annotation.rely.IdType;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/4/17 14:30
 * @description 公文Es实体
 */
@Data
@Accessors(chain = true)
@AllArgsConstructor
@NoArgsConstructor
@org.springframework.data.elasticsearch.annotations.Document(indexName = "#{@esIndexResolver.resolveEsIndexByTenant()}", createIndex = false)
public class Document implements Serializable {

    @ApiModelProperty(value = "主键", notes = "使用自定义Id策略")
    @IndexId(type = IdType.CUSTOMIZE)
    private String id;

    @ApiModelProperty(value = "文档名称", notes = "默认使用原文件名称")
    @HighLight(mappingField = "highlightTitle", preTag = "<span style='color:red;'>", postTag = "</span>")
    @IndexField(fieldType = FieldType.KEYWORD_TEXT, analyzer = Analyzer.IK_MAX_WORD, searchAnalyzer = Analyzer.IK_MAX_WORD)
    private String title;

    @ApiModelProperty(value = "文档名称Copy", notes = "默认使用原文件名称")
    @IndexField(fieldType = FieldType.KEYWORD)
    @HighLight(mappingField = "highlightTitleCopy", preTag = "<span style='color:red;'>", postTag = "</span>")
    private String titleCopy;

    @ApiModelProperty(value = "高亮词标题")
    private String highlightTitle;

    @ApiModelProperty(value = "高亮词标题keyword")
    private String highlightTitleCopy;

    @ApiModelProperty(value = "文档内容")
    @HighLight(mappingField = "highlightContent", preTag = "<span style='color:red;'>", postTag = "</span>")
    @IndexField(fieldType = FieldType.KEYWORD_TEXT, analyzer = Analyzer.IK_MAX_WORD, searchAnalyzer = Analyzer.IK_MAX_WORD)
    private String content;

//    @ApiModelProperty(value = "文档内容Copy")
//    @IndexField(fieldType = FieldType.KEYWORD)
//    private String contentCopy = content;

    @ApiModelProperty(value = "高亮返回值被映射的字段")
    private String highlightContent;

    @ApiModelProperty(value = "内容补全建议")
    @IndexField(fieldType = FieldType.COMPLETION, analyzer = Analyzer.IK_SMART, searchAnalyzer = Analyzer.IK_MAX_WORD)
    private String contentSuggest;

    @ApiModelProperty(value = "元数据项")
    @IndexField(fieldType = FieldType.NESTED, nestedClass = MetadataVO.class)
    private List<MetadataVO> metadata = new ArrayList<>();

    @ApiModelProperty(value = "资源类型", notes = "主文件、附件")
    @IndexField(fieldType = FieldType.KEYWORD)
    private String fileType;

    @ApiModelProperty(value = "提交时间")
    @IndexField(fieldType = FieldType.DATE)
    private Date createTime;

    @Score
    @ApiModelProperty(value = "分数")
    private Float score;

    @ApiModelProperty(value = "前台权限状态")
    private String frontStatus;

    @ApiModelProperty(value = "版本更新")
    @IndexField(fieldType = FieldType.KEYWORD)
    private String version;

}
