package org.irm.lab.front.sort;

import java.util.Comparator;

import org.irm.lab.config.entity.ChineseKeyWord;
import org.irm.lab.config.repository.ChineseKeyWordRepository;

import com.mongodb.client.model.Filters;

import cn.hutool.core.convert.Convert;
import net.sourceforge.pinyin4j.PinyinHelper;
import net.sourceforge.pinyin4j.format.HanyuPinyinCaseType;
import net.sourceforge.pinyin4j.format.HanyuPinyinOutputFormat;
import net.sourceforge.pinyin4j.format.HanyuPinyinToneType;
import net.sourceforge.pinyin4j.format.exception.BadHanyuPinyinOutputFormatCombination;

/**
 * <AUTHOR> <br/>
 * @date 2024/6/26 <br/>
 * &#064;Copyright  博客：<a href="https://eliauku.gitee.io/">...</a>  ||  per aspera and astra <br/>
 */
public class HonorPersonPINYINComparator implements Comparator<String> {

    private final ChineseKeyWordRepository chineseKeyWordRepository;
    public HonorPersonPINYINComparator(ChineseKeyWordRepository chineseKeyWordRepository) {

        this.chineseKeyWordRepository = chineseKeyWordRepository;
    }

    private String getPinyin(String word) {
        StringBuilder pinyinBuilder = new StringBuilder();
        HanyuPinyinOutputFormat format = new HanyuPinyinOutputFormat();
        format.setCaseType(HanyuPinyinCaseType.LOWERCASE);
        format.setToneType(HanyuPinyinToneType.WITHOUT_TONE);

        for (char c : word.toCharArray()) {
            try {
                if (Character.toString(c).matches("[\\u4E00-\\u9FA5]+")) {
                    String[] pinyinArray = PinyinHelper.toHanyuPinyinStringArray(c, format);
                    if (pinyinArray != null && pinyinArray.length > 0) {
                        pinyinBuilder.append(pinyinArray[0]);
                    }
                } else {
                    pinyinBuilder.append(c);
                }
            } catch (BadHanyuPinyinOutputFormatCombination e) {
                e.printStackTrace();
            }
        }
        return pinyinBuilder.toString();
    }

    private int getBiHua(String word) {

        final ChineseKeyWord oneNotExist = chineseKeyWordRepository.findOneNotExist(Filters.eq("zi", word));

        if (oneNotExist != null) {
            final String strokeCount = oneNotExist.getStrokeCount();
            final String replace = strokeCount.replace("画", "");
            final Integer anInt = Convert.toInt(replace);

            return anInt;
        }else {
            return Integer.MAX_VALUE;
        }

    }


    @Override
    public int compare(String o1, String o2) {

        final int biHua = getBiHua(o1);
        final int biHua1 = getBiHua(o2);

        if (biHua != biHua1) {
            return Integer.compare(biHua, biHua1);
        }

        return getPinyin(o1).compareTo(getPinyin(o2));
    }
}
