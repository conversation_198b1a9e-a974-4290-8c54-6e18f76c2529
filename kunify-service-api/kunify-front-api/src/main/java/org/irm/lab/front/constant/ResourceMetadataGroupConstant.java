package org.irm.lab.front.constant;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.List;

@Getter
@AllArgsConstructor
public class ResourceMetadataGroupConstant {
    //方案元数据项
    public static final String DRAINAGE_DEFAULT = "通用";

    //会议纪要方案元数据项
    public static final String DRAINAGE_MEETING = "会议纪要";

    //表彰荣誉方案元数据项
    public static final String DRAINAGE_AWARDS = "表彰荣誉";

    //规章制度方案元数据项
    public static final String DRAINAGE_SYSTEM = "制度";

    //规章制度方案元数据项
    public static final String DRAINAGE_ZHIWU = "职务任免";

    //领导批示方案元数据项
    public static final String DRAINAGE_LEADER = "收文";

    /**
     * 详情页元数据展示
     *
     * @param processName 元数据方案名称
     * @return 元数据方案匹配项
     */
    public static List<String> metadataGroupToMessagePage(String processName) {
        if (processName.startsWith(DRAINAGE_MEETING)) {// 会议纪要
            return List.of("题名", "年份", "密级", "保密期限", "所属部门", "文件类型", "分组", "文号", "分类号",
                    "分类名称", "保管期限", "来源", "知悉范围", "主办部门", "签发人", "印发日期", "拟稿人", "签发日期", "责任者", "档号", "归档方式", "原件形态", "档案门类");
        } else if (processName.startsWith(DRAINAGE_AWARDS)) {// 表彰荣誉
            return List.of("题名", "年度", "密级", "保密期限", "所属部门", "文件类型", "分组", "分类号",
                    "分类名称", "保管期限", "来源", "知悉范围", "主办部门", "责任者", "档号", "归档方式", "文件形态", "档案门类", "备注");
        } else if (processName.startsWith(DRAINAGE_SYSTEM)) { // 制度
            return List.of("题名", "公文分类", "文号", "制度编号", "制度名称", "制度类型", "生效日期");
        } else if (processName.startsWith(DRAINAGE_ZHIWU)) {    //任免
            return List.of("题名", "年度", "密级", "保密期限", "所属部门", "文件类型", "分组", "文号", "分类号",
                    "分类名称", "保管期限", "来源", "知悉范围", "主办部门", "签发人", "印发日期", "责任者", "档号", "归档方式", "文件形态", "档案门类", "备注", "主送单位", "抄送单位");
        } else if (processName.startsWith(DRAINAGE_LEADER)) { // 领导批示
            return List.of("题名", "年度", "密级", "保密年限", "所属部门", "文件类型", "分组", "分类号",
                    "分组名称", "保管期限", "来源", "知悉范围", "主办部门", "责任者", "档号", "归档方式", "文件形态", "档案门类", "备注", "原文号", "收文编号", "主送单位", "外发单位", "收文日期");
        } else { // 通用
            return List.of("题名", "公文分类", "关键词", "签发人", "发文机构", "文种", "保管期限", "发文日期", "文件编号", "文件格式", "收文日期", "归档时间");
        }
    }

    /**
     * 详情页元数据展示(不展示项)
     *
     * @param processName 元数据方案名称
     * @return 元数据方案匹配项
     */
    public static List<String> notShowMetadataGroupToMessagePage(String processName) {
        if (processName.startsWith(DRAINAGE_MEETING)) {// 会议纪要
            return List.of("发文类型", "行文类型", "是否报备", "重要发文", "主键", "来源", "数据来源", "印发份数", "信息公开", "模块ID", "入口id", "创建(归档)人id", "隶属(归档)部门代字", "聚合层次", "拟稿人员工编号");
        } else if (processName.startsWith(DRAINAGE_AWARDS)) {// 表彰荣誉
            return List.of("模块ID", "主键", "入口id", "数据来源", "办结时长", "限办日期", "办结日期", "四性检测", "同步系统", "是否归档", "数字化情况");
        } else if (processName.startsWith(DRAINAGE_SYSTEM)) { // 制度
            return List.of("制度类型");
        } else if (processName.startsWith(DRAINAGE_ZHIWU)) {
            return List.of("来源", "印发份数", "聚合层次", "同步系统字段");
        } else if (processName.startsWith(DRAINAGE_LEADER)) { // 领导批示
            return List.of("数据来源", "主键", "页数");
        } else { // 通用
            return List.of("发文类型", "行文类型", "是否报备", "重要发文", "主键", "来源",
                    "数据来源", "印发份数", "信息公开", "模块ID", "入口id", "聚合层次", "拟稿人员工编号",
                    "办结时长", "限办日期", "办结日期", "四性检测", "四性检测状态", "同步系统", "是否归档", "数字化情况",
                    "制度类型",
                    "同步系统字段",
                    "页数", "原文号",
                    "创建(归档)人id", "创建（归档）人id", "创建(归档）人id", "创建（归档)人id", "创建归档人id",
                    "隶属(归档)部门代字", "隶属（归档）部门代字", "隶属(归档）部门代字", "隶属（归档)部门代字", "隶属归档部门代字",
                    "归档日期", "归档时间", "档案标题", "文件档号", "归档人", "件号",
                    "知悉人（多个人员id）", "知悉人(多个人员id)", "知悉人(多个人员id）", "知悉人（多个人员id)", "知悉人多个人员id");
        }
    }


    /**
     * 全文检索源数据展示
     *
     * @param processName 元数据方案名称
     * @return 元数据方案匹配项
     */
    public static List<String> metadataGroupToShowList(String processName) {
        if (processName.startsWith(DRAINAGE_MEETING)) {// 会议纪要
            return List.of("年度", "文号", "所属部门", "文件类型", "公文分类", "密级");
        } else if (processName.startsWith(DRAINAGE_AWARDS)) {// 表彰荣誉
            return List.of("年度", "文号", "所属部门", "文件类型", "公文分类", "密级");
        } else if (processName.startsWith(DRAINAGE_SYSTEM)) { // 规章制度
            return List.of("年度", "文号", "所属部门", "文件类型", "公文分类", "密级");
        } else if (processName.startsWith(DRAINAGE_LEADER)) { // 领导批示
            return List.of("年度", "文号", "所属部门", "文件类型", "公文分类", "密级");
        } else { // 通用
            return List.of("年度", "文号", "所属部门", "文件类型", "公文分类", "密级");
        }
    }
}
