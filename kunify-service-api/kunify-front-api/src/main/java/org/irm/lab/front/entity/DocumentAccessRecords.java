package org.irm.lab.front.entity;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import io.swagger.annotations.ApiModel;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import org.irm.lab.common.annotation.MongoDBCollection;
import org.irm.lab.common.entity.AbstractBaseEntity;

/**
 * <AUTHOR>
 * @date 2023/7/5 18:11
 * @description
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@EqualsAndHashCode(callSuper = true)
@JsonIgnoreProperties(ignoreUnknown = true)
@MongoDBCollection(collectionName = "kunify_front_document_access_records")
@ApiModel(value = "公文访问记录")
public class DocumentAccessRecords extends AbstractBaseEntity {
    /**
     * 资源Id
     */
    private String resourceId;
    /**
     * 资源名称
     */
    private String resourceName;
    /**
     * 用户Id
     */
    private String userId;
}
