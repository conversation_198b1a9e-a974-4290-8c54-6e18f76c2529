package org.irm.lab.front.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.*;

/**
 * <AUTHOR>
 * @date 2023/4/20 10:01
 * @description 知识查询节点信息展示枚举
 */
@Getter
@AllArgsConstructor
public enum KnowledgeNodeEnum {
    GONG_WEN_FEN_LEI(
            "公文分类",
            Map.of("名称", "mingcheng"),
            Map.of("包含", List.of("公文"))
    ),
    GONG_WEN(
            "公文",
            Map.of(
                    "标题", "mingcheng",
                    "文件编号", "mingcheng",
                    "发文日期", "mingcheng",
                    "印发日期", "mingcheng",
                    "公文分类", "mingcheng",
                    "密级", "mingcheng"
            ),
            Map.of(
                    "签发人", List.of("签发人"),
                    "签发部门", List.of("机构/部门"),
                    "涉及人员", List.of("人员"),
                    "事项", List.of("事务"),
                    "印发单位", List.of("机构/部门"),
                    "发文单位", List.of("机构/部门")
            )
    ),
    JI_GOU_BU_MEN(
            "机构/部门",
            Map.of(
                    "名称", "mingcheng",
                    "主要职责", "mingcheng",
                    "工作要求", "mingcheng",
                    "成立日期", "mingcheng",
                    "成立背景", "mingcheng",
                    "发文", "mingcheng"
            ),
            Map.of("涉及人员", List.of("人员"),
                    "关联事项", List.of("事件"),
                    "涉及项目", List.of("制度文件"),
                    "包含职位", List.of("职务")
            )
    ),
    REN_YUAN(
            "人员",
            Map.of(
                    "名称", "mingcheng",
                    "发文", "mingcheng"
            ),
            Map.of("获得", List.of("公文"),
                    "任职", List.of("职务"),
                    "参与", List.of("事件"),
                    "离职", List.of("职务"),
                    "涉及", List.of("制度文件")
            )
    );

    private final String name;
    private final Map<String, String> dataProperty;
    private final Map<String, List<String>> objectProperty;


    public static Map<String, String> getDataPropertyByName(String name) {
        for (KnowledgeNodeEnum knowledgeNodeEnum : KnowledgeNodeEnum.values()) {
            if (knowledgeNodeEnum.getName().equals(name)) {
                return knowledgeNodeEnum.getDataProperty();
            }
        }
        return null;
    }


    public static Map<String, List<String>> getObjectPropertyByName(String name) {
        for (KnowledgeNodeEnum knowledgeNodeEnum : KnowledgeNodeEnum.values()) {
            if (knowledgeNodeEnum.getName().equals(name)) {
                return knowledgeNodeEnum.getObjectProperty();
            }
        }
        // 没有找到对应的枚举值，返回 null
        return null;
    }


}
