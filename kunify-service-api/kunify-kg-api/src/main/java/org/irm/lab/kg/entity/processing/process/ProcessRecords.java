package org.irm.lab.kg.entity.processing.process;

import org.irm.lab.common.annotation.MongoDBCollection;
import org.irm.lab.common.entity.AbstractBaseEntity;

import io.swagger.annotations.ApiModel;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

/**
 * 办理过程表
 * <AUTHOR> <br/>
 * @date 2024/6/26 <br/>
 * &#064;Copyright  博客：<a href="https://eliauku.gitee.io/">...</a>  ||  per aspera and astra <br/>
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@EqualsAndHashCode(callSuper = true)
@MongoDBCollection(collectionName = "kunify_kg_ProcessRecords")
@ApiModel(value = "所有领导批示表")
public class ProcessRecords extends AbstractBaseEntity {

    /**
     * 来源公文id
     */
    private String resourceId;

    /**
     * 序号
     */
    private String sort;

    /**
     * 开始节点
     */
    private String startNode;

    /**
     * 办理人员
     */
    private String handler;

    /**
     * 当前节点
     */
    private String currentNode;

    /**
     * 发送时间
     */
    private String sendTime;

    /**
     * 签收时间
     */
    private String signTime;

    /**
     * 完成时间
     */
    private String finishTime;

    /**
     * 操作状态
     */
    private String status;

    /**
     * 办理意见
     */
    private String content;



}
