package org.irm.lab.kg.config.tenant;

import cn.hutool.core.util.ObjectUtil;
import lombok.extern.slf4j.Slf4j;
import org.irm.lab.common.support.Condition;
import org.irm.lab.system.entity.Tenant;
import org.irm.lab.system.repository.TenantRepository;
import org.irm.lab.user.entity.User;
import org.neo4j.ogm.config.Configuration;
import org.neo4j.ogm.session.Session;
import org.neo4j.ogm.session.SessionFactory;
import org.springframework.context.annotation.DependsOn;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.util.HashMap;
import java.util.List;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.LinkedBlockingQueue;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2022-12-30
 */
@Component("neo4jTenantFactory")
@Slf4j
@DependsOn("neo4jConfig")
public class Neo4jTenantFactory {

    @Resource
    private SessionFactory defaultSessionFactory;
    @Resource
    private TenantRepository tenantRepository;

    private static final String packages = "org.irm.lab.kg.entity.neo4j";

    private static final ConcurrentHashMap<String, LinkedBlockingQueue<Session>> neo4jSessionPool = new ConcurrentHashMap<>();
    //每个租户设置的连接数
    private static final int initPoolSize = 10;

    //@PostConstruct
    public void initPool() {
        log.info(">>>>>>>>>>>>>>>>>>>>初始化neo4j连接池>>>>>>>>>>>>>>>>>>>>");
        List<Tenant> tenantList = tenantRepository.findAll().stream()
                .filter(s -> ObjectUtil.isNotEmpty(s.getDbUrl())
                        && ObjectUtil.isNotEmpty(s.getDbPassword())
                        && ObjectUtil.isNotEmpty(s.getDbPassword()))
                .collect(Collectors.toList());
        for (Tenant tenant : tenantList) {
            LinkedBlockingQueue<Session> sessionLinkedList = new LinkedBlockingQueue<>();
            int n = 0;
            SessionFactory sessionFactory = buildTenantSessionFactory(tenant.getTenantId());
            while (n < initPoolSize) {
                sessionLinkedList.offer(sessionFactory.openSession());
                n++;
            }
            neo4jSessionPool.put(tenant.getTenantId(),sessionLinkedList);
        }
        log.info(">>>>>>>>>>>>>>>>>>>>neo4j连接池初始化完毕>>>>>>>>>>>>>>>>>>>>");
    }

    public SessionFactory buildTenantSessionFactory(String tenantId) {
        return ObjectUtil.isEmpty(tenantId) ? defaultSessionFactory : new SessionFactory(buildConfiguration(tenantId), packages);
    }

    protected Configuration buildConfiguration(String tenantId) {
        HashMap<String, Object> queryParam = new HashMap<>();
        queryParam.put("tenantId", tenantId);
        Tenant tenant = tenantRepository.findOne(Condition.getFilter(queryParam, User.class));
        log.info("初始化租户 【{}】 --- 租户ID 【{}】 --- 数据源地址 【{}】",tenant.getTenantName(),tenantId,tenant.getDbUrl());
        return new Configuration.Builder()
                .uri(tenant.getDbUrl())
                .credentials(tenant.getDbUsername(), tenant.getDbPassword())
                .verifyConnection(true)
                .build();
    }

    public Session openSession(String tenantId) {
//        log.info("当前操作选择租户数据库为【{}】", tenantId);
        LinkedBlockingQueue<Session> sessionLinkedList = neo4jSessionPool.get(tenantId);
        if (ObjectUtil.isEmpty(sessionLinkedList)) {
            log.info("新增neo4j连接对象,租户 ===> {}", tenantId);
            SessionFactory sessionFactory = buildTenantSessionFactory(tenantId);
            LinkedBlockingQueue<Session> newSessionLinkedList = new LinkedBlockingQueue<>();
            int n = 0;
            while (n < initPoolSize) {
                newSessionLinkedList.offer(sessionFactory.openSession());
                n++;
            }
            neo4jSessionPool.put(tenantId, newSessionLinkedList);
            sessionLinkedList = neo4jSessionPool.get(tenantId);
        }
        return sessionLinkedList.poll();
    }

    public static void releaseSession(Session session,String tenantId){
        if (tenantId == null || session ==null) return;
        session.clear();
        neo4jSessionPool.get(tenantId).offer(session);
    }
}
