package org.irm.lab.kg.algorithm;


import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @date 2023/2/23 13:44
 * @description 文档高亮词
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class DocumentTextLight {
    @ApiModelProperty("唯一标识")
    private String identifier;
    @ApiModelProperty(value = "高亮词实体对应的标签Id")
    private String labelId;
    @ApiModelProperty(value = "高亮词实体对应的标签名称")
    private String labelName;
    @ApiModelProperty(value = "高亮词实体对应的标签数据缓存Id")
    private String cacheLabelDataId;
    @ApiModelProperty(value = "高亮词在本段的起始位置")
    private Long start;
    @ApiModelProperty(value = "高亮词在本段的终点位置")
    private Long end;
    @ApiModelProperty(value = "高亮词",notes = "对应的是标签数据")
    private String text;
}
