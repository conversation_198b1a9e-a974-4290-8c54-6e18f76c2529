package org.irm.lab.kg.constant;

public class RuleMatchingConstant {
    public static final String PEOPLE_TYPE = "人员类型";
    public static final String HIGH_LEADER = "高层领导";
    public static final String ORDINARY_PERSONNEL = "普通人员";
    public static final String SERIAL_NUMBER = "办理序号";
    public static final String OPERATION_NAME = "操作名称";
    public static final String START_TIME = "开始日期";
    public static final String FINISH_TIME = "结束日期";
    public static final String HANDLING_ADVICE = "办理意见";
    //领导批示
    public static final String LEADER_INSTRUCTION = "领导批示";


    public static final String NO_CHANCE = "暂无意见";
    public static final String TRANSACTION_FILE = "办理公文";
    public static final String TRANSACTION_PEOPLE = "办理人";
    public static final String NO_TRANSACTION_PEOPLE = "暂无办理人员";

    public static final String ATTRIBUTE = "attribute";
    public static final String VALUE = "Value";
    public static final String RELATION = "relation";
    public static final String RESULT = "result";
    public static final String ITEM_INFO = "ItemInfo";
    public static final String SCHEMA = "schema";
    public static final String UUID = "uuid";

    public static final String PDF = ".pdf";
    public static final String DOC = ".doc";
    public static final String DOCX = ".docx";

    public static final String NAME_LIST = "名单";
    public static final String GROUP = "集体";
    public static final String FIRM = "企业";
    public static final String DEPARTMENT = "部门";
    public static final String COMMENDATION_TITLE = "表彰称号";
    public static final String COMMENDATION_TYPE = "表彰类型";
    public static final String COMMENDATION_YEAR = "表彰年度";
    public static final String GROUP_ORIENTED = "面向群体";

    public static final String PART_COMMENDATION_TYPE = "属于表彰类型";

    public static final String YEAR = "年度";

    public static final String ZERO = "0";



    public static final String  CURRENT_NODE_HANDLER = "当前节点办理人员";

    public static final String  SERIAL_NUMBER_NEW = "序号";

    public static final String  SEND_TIME = "发送时间";

    public static final String  COMPLETION_TIME = "完成时间";

}
