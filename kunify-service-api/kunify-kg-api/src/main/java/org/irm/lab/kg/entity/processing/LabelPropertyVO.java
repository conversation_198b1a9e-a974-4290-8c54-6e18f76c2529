package org.irm.lab.kg.entity.processing;

import lombok.Data;


import java.io.Serializable;
import java.util.Date;


/**
 * <AUTHOR>
 * @date 2023/2/28 16:53
 * @description 标签属性VO
 */
@Data
public class LabelPropertyVO implements Serializable {
    /**
     * 唯一标识
     */
    private String identifier;
    /**
     * 标签属性Id
     */
    private String labelPropertyId;
    /**
     * 标签属性名
     */
    private String labelPropertyName;
    /**
     * 属性值
     */
    private String value;
    /**
     * 日期类型
     */
    private Date dateValue;
    /**
     * 布尔类型
     */
    private boolean booleanValue;
}
