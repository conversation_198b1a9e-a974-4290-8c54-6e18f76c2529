package org.irm.lab.kg.entity;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import org.irm.lab.common.annotation.MongoDBCollection;
import org.irm.lab.common.entity.AbstractBaseEntity;

import javax.validation.constraints.NotBlank;
import java.util.ArrayList;
import java.util.List;

@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
@ToString
@MongoDBCollection(collectionName = "kunify_kg_knowledge_relation")
@ApiModel(value = "关系")
public class KnowledgeRelation extends AbstractBaseEntity {

    @ApiModelProperty(value = "关系名称")
    private String name;

    @NotBlank
    @ApiModelProperty(value = "概念一id")
    private String conceptOne;

    @NotBlank
    @ApiModelProperty(value = "概念二id")
    private String conceptTwo;

    @ApiModelProperty(value = "描述")
    private String description;

    @ApiModelProperty(value = "对称性", notes = "是/否")
    private Boolean isSymmetric = false;

    @ApiModelProperty(value = "传递性", notes = "是/否")
    private Boolean isTransitive = false;

    @ApiModelProperty(value = "正向谓词")
    private String forwardPre;

    @ApiModelProperty(value = "正向谓词同义词")
    private List<String> forwardPreSynonyms;

    @ApiModelProperty(value = "反向谓词")
    private String inversePre;

    @ApiModelProperty(value = "反向谓词同义词")
    private List<String> inversePreSynonyms;

    @ApiModelProperty(value = "关联属性")
    private List<String> propertyIds = new ArrayList<>();
}
