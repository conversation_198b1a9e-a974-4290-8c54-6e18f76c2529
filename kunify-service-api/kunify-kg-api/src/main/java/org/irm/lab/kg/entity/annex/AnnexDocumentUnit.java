package org.irm.lab.kg.entity.annex;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import org.irm.lab.common.annotation.MongoDBCollection;
import org.irm.lab.kg.algorithm.DocumentUnit;


/**
 * <AUTHOR>
 * @date 2023/1/30 14:25
 */
@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
@ToString
@MongoDBCollection(collectionName = "kunify_kg_annex_document_unit")
@ApiModel(value = "附件语料单元")
public class AnnexDocumentUnit extends DocumentUnit {
    @ApiModelProperty(value = "对应的资源附件Id")
    private String annexId;
}
