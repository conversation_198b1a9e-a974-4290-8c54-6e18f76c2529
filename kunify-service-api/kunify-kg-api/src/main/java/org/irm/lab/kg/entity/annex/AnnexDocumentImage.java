package org.irm.lab.kg.entity.annex;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import org.irm.lab.common.annotation.MongoDBCollection;
import org.irm.lab.kg.algorithm.DocumentImage;

/**
 * <AUTHOR>
 * @date 2023/1/30 15:00
 */
@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
@ToString
@MongoDBCollection(collectionName = "kunify_kg_annex_document_image")
@ApiModel(value = "附件文档转化为图片")
public class AnnexDocumentImage extends DocumentImage {
    @ApiModelProperty(value = "对应资源附件Id")
    private String annexId;
    @ApiModelProperty(value = "图片文件的attachId")
    private String attachId;
    @ApiModelProperty(value = "图片文件的attachName")
    private String attachName;
    @ApiModelProperty(value = "所在页码")
    private Integer page;

    public AnnexDocumentImage(String attachId, String attachName, Integer page) {
        this.attachId = attachId;
        this.attachName = attachName;
        this.page = page;
    }



}
