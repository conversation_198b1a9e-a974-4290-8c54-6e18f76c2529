package org.irm.lab.kg.repository.neo4j.base;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.collection.IterUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.util.StrUtil;
import lombok.extern.slf4j.Slf4j;
import org.irm.lab.common.annotation.Neo4jDao;
import org.irm.lab.common.constant.FiledNameConst;
import org.irm.lab.common.exception.ServiceException;
import org.irm.lab.common.provider.TenantProvider;
import org.irm.lab.common.support.MyPage;
import org.irm.lab.kg.config.tenant.Neo4jTenantFactory;
import org.irm.lab.kg.dto.BZRYQuery;
import org.irm.lab.kg.dto.BZRYQueryDTO;
import org.irm.lab.kg.entity.KnowledgeConcept;
import org.irm.lab.kg.entity.neo4j.base.Neo4jAbstractBaseEntity;
import org.irm.lab.kg.entity.neo4j.node.NodeEntity;
import org.irm.lab.kg.entity.neo4j.node.NodeRelation;
import org.irm.lab.kg.exception.Neo4jExceptionConstant;
import org.neo4j.ogm.cypher.ComparisonOperator;
import org.neo4j.ogm.cypher.Filter;
import org.neo4j.ogm.cypher.Filters;
import org.neo4j.ogm.cypher.query.Pagination;
import org.neo4j.ogm.cypher.query.SortOrder;
import org.neo4j.ogm.model.Property;
import org.neo4j.ogm.model.Result;
import org.neo4j.ogm.response.model.RelationshipModel;
import org.neo4j.ogm.session.Session;
import org.neo4j.ogm.transaction.Transaction;

import javax.annotation.Resource;
import java.lang.reflect.ParameterizedType;
import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.StreamSupport;

/**
 * <AUTHOR>
 * @date 2023-01-04
 */
@Neo4jDao
@Slf4j
public class Neo4jBaseDao<T extends Neo4jAbstractBaseEntity> {
    @Resource
    private Neo4jTenantFactory neo4jTenantFactory;
    @Resource
    private TenantProvider tenantProvider;
    public static final int DEFAULT_DEPTH = 0;

    protected Class<T> getTClass() {
        return (Class<T>) ((ParameterizedType) getClass().getGenericSuperclass()).getActualTypeArguments()[0];
    }

    public Session getSession() {
        try {
            return neo4jTenantFactory.openSession(tenantProvider.getTenantId());
        } catch (Exception e) {
            e.printStackTrace();
            throw new ServiceException(Neo4jExceptionConstant.AUTH_ERROR);
        }
    }

    public void releaseConnection(Session session) {
        Neo4jTenantFactory.releaseSession(session, tenantProvider.getTenantId());
    }

    public T findById(String id) {
        Session session = null;
        try {
            session = getSession();
            return session.load(getTClass(), id);
        } finally {
            if (session != null) {
                releaseConnection(session);
            }
        }
    }

    public T findById(String id, int depth) {
        Session session = null;
        try {
            session = getSession();
            return session.load(getTClass(), id, depth);
        } finally {
            if (session != null) {
                releaseConnection(session);
            }
        }
    }

    public Long countByCondition(Filters filters) {
        Session session = null;
        try {
            session = getSession();
            return session.count(getTClass(), filters);
        } finally {
            if (session != null) {
                releaseConnection(session);
            }
        }
    }

    public T save(T entity) {
        Session session = getSession();
        Transaction transaction = session.beginTransaction();
        try {
            if (entity.getId() != null) {
                entity.setUpdateTime(currentDate());
            } else {
                entity.setCreateTime(currentDate());
            }
            session.save(entity);
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            transaction.commit();
            transaction.close();
            releaseConnection(session);
        }
        return findById(entity.getId());
    }

    public T save(T entity, int depth) {
        Session session = getSession();
        try {
            if (entity.getId() != null) {
                entity.setUpdateTime(currentDate());
            } else {
                entity.setCreateTime(currentDate());
            }
            session.save(entity, depth);
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            releaseConnection(session);
        }
        return findById(entity.getId());
    }


    public void deleteById(String id) {
        Session session = getSession();
        Transaction transaction = session.beginTransaction();
        T entity = session.load(getTClass(), id);
        try {
            session.delete(entity);
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            transaction.commit();
            transaction.close();
            releaseConnection(session);
        }
    }

    public Iterable<T> findAll() {
        return findAll(DEFAULT_DEPTH);
    }

    public Iterable<T> findAll(int depth) {
        Session session = null;
        try {
            session = getSession();
            return session.loadAll(getTClass(), depth);
        } finally {
            if (session != null) {
                releaseConnection(session);
            }
        }
    }

    public List<T> findAllByFilter(int depth, Filters filters) {
        Session session = null;
        try {
            session = getSession();
            return IterUtil.toList(session.loadAll(getTClass(), filters, depth));
        } finally {
            if (session != null) {
                releaseConnection(session);
            }
        }
    }

    public Iterable<T> findAll(SortOrder order) {
        Session session = null;
        try {
            session = getSession();
            return session.loadAll(getTClass(), order, DEFAULT_DEPTH);
        } finally {
            if (session != null) {
                releaseConnection(session);
            }
        }
    }

    /**
     * Cypher语句查实例
     */
    public List<T> findEntityByCypher(String cypher, Map<String, ?> filterMap) {
        List<T> list = new ArrayList<>();
        Session session = null;
        try {
            session = getSession();
            Result result = session.query(cypher, filterMap, true);
            for (Map<String, Object> stringObjectMap : result) {
                for (Map.Entry<String, Object> stringObjectEntry : stringObjectMap.entrySet()) {
                    T value = (T) stringObjectEntry.getValue();
                    list.add(value);
                }
            }
            return list;
        } finally {
            if (session != null) {
                releaseConnection(session);
            }
        }
    }

    /**
     * Cypher语句查实例（表彰人/机构）
     */
    public Map<NodeEntity, List<String>> findEntityByCypherWithBZRYPeopleAndInstitution(String cypher, Map<String, ?> filterMap) {
        HashMap<NodeEntity, List<String>> map = new HashMap<>();
        Session session = null;
        try {
            session = getSession();
            Result result = session.query(cypher, filterMap, true);
            for (Map<String, Object> queryResult : result.queryResults()) {
                NodeEntity nodeEntity = (NodeEntity) queryResult.get("n");
                String[] es = (String[]) queryResult.get("es");
                map.put(nodeEntity, Arrays.asList(es));
            }
            return map;
        } finally {
            if (session != null) {
                releaseConnection(session);
            }
        }
    }

    /**
     * Cypher语句查实例（表彰荣誉）
     */
    public BZRYQueryDTO findEntityByCypherWithBZRY(String cypher, Map<String, ?> filterMap) {
        Session session = null;
        BZRYQueryDTO dto = new BZRYQueryDTO();
        ArrayList<BZRYQuery> queryResults = new ArrayList<>();
        try {
            session = getSession();
            Result result = session.query(cypher, filterMap, true);
            for (Map<String, Object> queryResult : result.queryResults()) {
                BZRYQuery bzryQuery = new BZRYQuery();
                NodeEntity nodeEntity = (NodeEntity) queryResult.get("n");
                // 看前人代码后：表彰称号，默认只能是表彰机构，或者表彰人员，不可两者都有
                List<String> institution = Arrays.stream((Object[]) queryResult.get("institution")).map(Object::toString).collect(Collectors.toList());
                List<String> people = Arrays.stream((Object[]) queryResult.get("people")).map(Object::toString).collect(Collectors.toList());
                String totalEntityCount = queryResult.get("totalEntityCount").toString();
                bzryQuery.setNodeEntity(nodeEntity);
                dto.setCount(totalEntityCount);
                if (!people.isEmpty()) {
                    bzryQuery.setPeoples(people);
                } else {
                    bzryQuery.setInstitutions(institution);
                }
                queryResults.add(bzryQuery);
            }
            dto.setResults(queryResults);
            return dto;
        } finally {
            if (session != null) {
                releaseConnection(session);
            }
        }
    }


    /**
     * Cypher语句查conceptId
     */
    public List<String> findAllConceptId(String cypher, Map<String, ?> filterMap) {
        cypher += " return distinct n.conceptId";
        List<String> list = new ArrayList<>();
        Session session = null;
        try {
            session = getSession();
            Result result = session.query(cypher, filterMap, true);
            for (Map<String, Object> stringObjectMap : result) {
                for (Map.Entry<String, Object> stringObjectEntry : stringObjectMap.entrySet()) {
                    list.add(stringObjectEntry.getValue().toString());
                }
            }
            return list;
        } finally {
            if (session != null) {
                releaseConnection(session);
            }
        }
    }

    /**
     * Cypher语句查实例数量
     */
    public int findEntityCountByCypher(String cypher, Map<String, ?> filterMap) {
        cypher += " return count(distinct n.id)";
        Session session = null;
        try {
            session = getSession();
            Result query = session.query(cypher, filterMap, true);
            long totalNum = 0L;
            for (Map<String, Object> queryResult : query.queryResults()) {
                Object num = queryResult.get("count(distinct n.id)");
                if (num instanceof Long) {
                    totalNum = Convert.toLong(num);
                }
            }
            return (int) totalNum;
        } finally {
            if (session != null) {
                releaseConnection(session);
            }
        }
    }

    /**
     * Cypher语句查关系
     */
    public List<NodeRelation> findRelationByCypher(String cypher, Map<String, ?> filterMap) {
        List<NodeRelation> relationList = new ArrayList<>();
        Session session = null;
        try {
            session = getSession();
            Result result = session.query(cypher, filterMap, true);
            for (Map<String, Object> relationMap : result) {
                NodeRelation nodeRelation = (NodeRelation) relationMap.get("r");
                relationList.add(nodeRelation);
            }
            return relationList;
        } finally {
            if (session != null) {
                releaseConnection(session);
            }
        }
    }

    /**
     * Cypher语句查关系Id
     */
    public List<String> findRelationIdByCypher(String cypher, Map<String, ?> filterMap) {
        List<String> relationList = new ArrayList<>();
        Session session = null;
        try {
            session = getSession();
            Result result = session.query(cypher, filterMap, true);
            for (Map<String, Object> relationMap : result) {
                RelationshipModel model = (RelationshipModel) relationMap.get("r");
                model.getPropertyList().stream().filter(ele -> "id".equals(ele.getKey())).findFirst().ifPresent(id -> {
                    relationList.add(id.getValue().toString());
                });
            }
            return relationList;
        } finally {
            if (session != null) {
                releaseConnection(session);
            }
        }
    }


    /**
     * Cypher语句查关系
     */
    public HashSet<NodeRelation> findRelationByCypherPlus(String cypher, Map<String, ?> filterMap) {
        HashSet<NodeRelation> nodeRelations = new HashSet<>();
        Session session = null;
        try {
            session = getSession();
            Result result = session.query(cypher, filterMap, true);
            for (Map<String, Object> relationMap : result) {
                NodeRelation nodeRelation = (NodeRelation) relationMap.get("r");
                nodeRelations.add(nodeRelation);
            }
            return nodeRelations;
        } finally {
            if (session != null) {
                releaseConnection(session);
            }
        }
    }

    /**
     * Cypher语句查关系
     */
    public MyPage<NodeEntity> findPeopleAndMeeting(KnowledgeConcept peopleConcept, String value, KnowledgeConcept meetingConcept, Integer size, Integer page) {
        Session session = null;
        ArrayList<NodeEntity> list = new ArrayList<>();
        try {
            session = getSession();
            Map<String, Object> baseCondition = Map.of("startConceptId", peopleConcept.getId(), "startEntityName", value, "endConceptId", meetingConcept.getId(), "page", page, "pageSize", size);
            String cypher = "MATCH (s:ENTITY)-[r:RELATION]->(e:ENTITY) " +
                    "where s.conceptId=$startConceptId " +
                    "and s.entityName contains $startEntityName " +
                    "and e.conceptId=$endConceptId " +
                    "and r.predicateName='参会' " +
                    "with s,r " +
                    "ORDER BY s.createTime DESC " +
                    "SKIP $pageSize * ($page - 1) " +
                    "LIMIT $pageSize " +
                    "return s AS N, collect(r) AS relationships";
            // 查询总数
            String cypherCount = "MATCH (s:ENTITY)-[r:RELATION]->(e:ENTITY) " +
                    "where s.conceptId=$startConceptId " +
                    "and s.entityName contains $startEntityName " +
                    "and e.conceptId=$endConceptId " +
                    "and r.predicateName='参会' " +
                    "return count(s)";
            Result query = session.query(cypherCount, baseCondition, true);
            long totalNum = 0L;
            for (Map<String, Object> queryResult : query.queryResults()) {
                Object num = queryResult.get("count(s)");
                if (num instanceof Long) {
                    totalNum = Convert.toLong(num);
                }
            }
            String cypherRelation = "match (n:ENTITY) where id(n) = $endNode return n ";
            Result result = session.query(cypher, baseCondition, true);
            for (Map<String, Object> queryResult : result.queryResults()) {
                NodeEntity startNodeEntity = (NodeEntity) queryResult.get("N");
                Set<NodeRelation> nodeRelations = new HashSet<>();
                Object relationships = queryResult.get("relationships");
                if (relationships instanceof ArrayList) {
                    for (Object relationship : ((ArrayList) relationships)) {
                        NodeRelation nodeRelation = new NodeRelation();
                        if (relationship instanceof RelationshipModel) {
                            List<Property<String, Object>> predicateName = ((RelationshipModel) relationship).getPropertyList().stream().filter(model -> {
                                return model.getKey().equals("predicateName");
                            }).collect(Collectors.toList());
                            if (!predicateName.isEmpty()) {
                                nodeRelation.setPredicateName(predicateName.get(0).getValue().toString());
                            }
                            Long endNode = ((RelationshipModel) relationship).getEndNode();
                            Result startNode1 = session.query(cypherRelation, Map.of("endNode", endNode));
                            for (Map<String, Object> stringObjectMap : startNode1.queryResults()) {
                                for (Map.Entry<String, Object> stringObjectEntry : stringObjectMap.entrySet()) {
                                    NodeEntity node = (NodeEntity) stringObjectEntry.getValue();
                                    if (node != null) {
                                        nodeRelation.setEnd(node);
                                    }
                                }
                            }
                        }
                        nodeRelations.add(nodeRelation);
                    }
                }
                startNodeEntity.setToEntity(nodeRelations);
                list.add(startNodeEntity);
            }
            return new MyPage<NodeEntity>(page, size, totalNum, list);
        } finally {
            if (session != null) {
                releaseConnection(session);
            }
        }
    }

    /**
     * Cypher语句查关系数量
     *
     * @param cypher
     * @param filterMap
     * @return 关系数量
     */
    public Long findRelationCountByCypher(String cypher, Map<String, ?> filterMap) {
        Long count = Long.parseLong("0");
        cypher += " return count(distinct r.id)";
        Session session = null;
        try {
            session = getSession();
            Result result = session.query(cypher, filterMap, true);
            for (Map<String, Object> relationMap : result) {
                count = (Long) relationMap.get("count(distinct r.id)");
            }
            return count;
        } finally {
            if (session != null) {
                releaseConnection(session);
            }
        }
    }

    /**
     * 条件分页查询，默认深度0
     *
     * @param map  条件map
     * @param page 当前页码
     * @param size 每页条数
     * @return {@link MyPage}
     */
    public MyPage<T> findPageByCondition(Map<String, Object> map, int page, int size) {
        return findPageByCondition(map, page, size, DEFAULT_DEPTH);
    }

    /**
     * 条件分页查询，并指定深度
     *
     * @param map   条件map
     * @param page  当前页码
     * @param size  每页条数
     * @param depth 深度
     * @return {@link MyPage}
     */
    public MyPage<T> findPageByCondition(Map<String, Object> map, int page, int size, int depth) {
        map.remove("page");
        map.remove("size");
        Filters filters = getFilter(map);
        return findPageByCondition(filters, page, size, depth);
    }

    /**
     * 条件分页查询
     *
     * @param filters 条件Filters
     * @param page    当前页码
     * @param size    每页条数
     * @return {@link MyPage}
     */
    public MyPage<T> findPageByCondition(Filters filters, int page, int size) {
        return findPageByCondition(filters, page, size, DEFAULT_DEPTH);
    }

    /**
     * 条件分页查询
     *
     * @param filters 条件Filters
     * @param page    当前页码
     * @param size    每页条数
     * @return {@link MyPage}
     */
    public MyPage<T> findPageByCondition(Filters filters, int page, int size, int depth) {
        Session session = null;
        try {
            session = getSession();
            // 获取总数
            long totalElements = session.count(getTClass(), filters);
            // 分页查询
            List<T> list = IterUtil.toList(session.loadAll(getTClass(), filters, new Pagination(page == 0 ? page : page - 1, size), depth));
            return new MyPage<T>(page, size, totalElements, list);
        } finally {
            if (session != null) {
                releaseConnection(session);
            }
        }
    }

    /**
     * 条件分页查询，并默认使用创建时间进行降序,默认深度0
     *
     * @param filters 条件Filters
     * @param page    当前页码
     * @param size    每页条数
     * @return {@link MyPage}
     */
    public MyPage<T> findPageByConditionAndTimeSort(Filters filters, Integer page, Integer size) {
        return findPageByConditionAndSort(filters, new SortOrder().desc("createTime"), page, size, DEFAULT_DEPTH);
    }

    /**
     * 条件分页查询，并默认使用创建时间进行降序,指定深度
     *
     * @param filters 条件Filters
     * @param page    当前页码
     * @param size    每页条数
     * @return {@link MyPage}
     */
    public MyPage<T> findPageByConditionAndTimeSort(Filters filters, Integer page, Integer size, int depth) {
        return findPageByConditionAndSort(filters, new SortOrder().desc("createTime"), page, size, depth);
    }

    /**
     * 条件分页查询，并排序,默认深度0
     *
     * @param filters   条件Filters
     * @param sortOrder 排序条件
     * @param page      当前页码
     * @param size      每页条数
     * @return {@link MyPage}
     */
    public MyPage<T> findPageByConditionAndSort(Filters filters, SortOrder sortOrder, Integer page, Integer size) {
        return findPageByConditionAndSort(filters, sortOrder, page, size, DEFAULT_DEPTH);
    }

    public Integer findCount(String concept, Set<String> ids) {
        Map<String, Object> param = new HashMap();
        param.put("conceptId", concept);
        param.put("docIdsToCheck", ids);
        Session session = null;
        Integer count = 0;
        try {
            session = getSession();

            String cpyherCount = "MATCH (n:ENTITY) " +
                    "WHERE n.conceptId = $conceptId " +
                    "AND any(x IN n.docIds WHERE x IN $docIdsToCheck) return count(n)";

            Result query = session.query(cpyherCount, param, true);
            long totalNum = 0L;
            for (Map<String, Object> queryResult : query.queryResults()) {
                Object num = queryResult.get("count(n)");
                if (num instanceof Long) {
                    totalNum = Convert.toLong(num);
                }
                ;
            }
            count = Convert.toInt(totalNum);

            return count;
        } finally {
            if (session != null) {
                releaseConnection(session);
            }
        }


    }


    public List<T> findData(String concept, Set<String> ids) {

        ArrayList<T> list = new ArrayList<>();

        Map<String, Object> param = new HashMap();
        param.put("conceptId", concept);
        param.put("docIdsToCheck", ids);
        Session session = null;
        Integer count = 0;
        try {
            session = getSession();

            String cpyherCount = "MATCH (n:ENTITY) " +
                    "WHERE n.conceptId = $conceptId " +
                    "AND any(x IN n.docIds WHERE x IN $docIdsToCheck) return n";

            Result query = session.query(cpyherCount, param, true);
            for (Map<String, Object> queryResult : query.queryResults()) {
                for (Map.Entry<String, Object> stringObjectEntry : queryResult.entrySet()) {
                    T value = (T) stringObjectEntry.getValue();
                    list.add(value);
                }
            }

            return list;
        } finally {
            if (session != null) {
                releaseConnection(session);
            }
        }


    }

    public MyPage<T> findPageByConditionAndSort(String concept, Set<String> ids, Integer page, Integer size) {
        Session session = null;
        ArrayList<T> list = new ArrayList<>();
        try {
            session = getSession();
            String cpyher = "MATCH (n:ENTITY) " +
                    "WHERE n.conceptId = $conceptId " +
                    "AND any(x IN n.docIds WHERE x IN $docIdsToCheck) " +
                    "WITH n " +
                    "ORDER BY n.createTime DESC " +
                    "SKIP $pageSize * ($page - 1) " +
                    "LIMIT $pageSize " +
                    "RETURN n";
            Map<String, Object> param = new HashMap();
            param.put("conceptId", concept);
            param.put("docIdsToCheck", ids);
            param.put("pageSize", size);
            param.put("page", page);
            Result query = session.query(cpyher, param, true);

            String cpyherCount = "MATCH (n:ENTITY) " +
                    "WHERE n.conceptId = $conceptId " +
                    "AND any(x IN n.docIds WHERE x IN $docIdsToCheck) return count(n)";
            Result result = session.query(cpyherCount, param, true);
            long totalNum = 0L;
            for (Map<String, Object> queryResult : result.queryResults()) {
                Object num = queryResult.get("count(n)");
                if (num instanceof Long) {
                    totalNum = Convert.toLong(num);
                }
            }
            for (Map<String, Object> queryResult : query.queryResults()) {
                for (Map.Entry<String, Object> stringObjectEntry : queryResult.entrySet()) {
                    T value = (T) stringObjectEntry.getValue();
                    list.add(value);
                }
            }

            return new MyPage<T>(page, size, totalNum, list);
        } finally {
            if (session != null) {
                releaseConnection(session);
            }
        }
    }

    public MyPage<NodeEntity> findPageSort(String concept, String name, Integer page, Integer size, Set<String> ids, String meetingConceptId) {

        Session session = null;
        ArrayList<NodeEntity> list = new ArrayList<>();
        try {
            session = getSession();
            Map<String, Object> param = new HashMap();
            String cypher = "MATCH (n:ENTITY) <- [r:RELATION]-(p:ENTITY)\n" +
                    "WHERE n.conceptId = $conceptId\n" +
                    "AND n.entityName contains $entityName\n" +
                    "AND r.predicateName = '提及会议议题'\n" +
                    "AND p.conceptId = $meetingConceptId\n";
            if (CollectionUtil.isNotEmpty(ids)) {
                param.put("docIdsToCheck", ids);
                cypher += "AND any(x IN n.docIds WHERE x IN $docIdsToCheck)\n";
            }
            cypher += "with n,collect(r) AS relationships " +
                    "ORDER BY n.createTime DESC " +
                    "SKIP $pageSize * ($page - 1) " +
                    "LIMIT $pageSize " +
                    "return n AS N, relationships";
            param.put("conceptId", concept);
            param.put("entityName", name);
            param.put("meetingConceptId", meetingConceptId);
            param.put("pageSize", size);
            param.put("page", page);
            Result query = session.query(cypher, param, true);
            Iterable<Map<String, Object>> queryMaps = query.queryResults();
            String countCypher = "MATCH (n:ENTITY) <- [r:RELATION]-(p:ENTITY)\n" +
                    "WHERE n.conceptId = $conceptId\n" +
                    "AND n.entityName contains $entityName\n" +
                    "AND r.predicateName = '提及会议议题'\n" +
                    "AND p.conceptId = $meetingConceptId\n";
            if (CollectionUtil.isNotEmpty(ids)) {
                param.put("docIdsToCheck", ids);
                countCypher += "AND any(x IN n.docIds WHERE x IN $docIdsToCheck)\n";
            }
            countCypher += "with n,collect(r) AS relationships " +
                    "ORDER BY n.createTime DESC " +
                    "return n AS N, relationships";
            Result countQuery = session.query(countCypher, param, true);
            Iterable<Map<String, Object>> countQueryMaps = countQuery.queryResults();
            long count = StreamSupport.stream(countQueryMaps.spliterator(), false).count();
            log.info("查询到 query.queryResults() 数量为:[{}] 分页参数size为:[{}] 查询到 countQuery:[{}] ", StreamSupport.stream(queryMaps.spliterator(), false).count(), size, count);
            log.info("查询 cypher:[{}]", cypher);
            log.info("查询 cypherCount:[{}]", cypher);
            String cypherRelation = "match (n:ENTITY) where id(n) = $startNode return n ";
            for (Map<String, Object> queryResult : query.queryResults()) {
                log.info("当前查询结果: {}", queryResult);
                NodeEntity value = (NodeEntity) queryResult.get("N");
                Set<NodeRelation> nodeRelations = new HashSet<>();
                Object relationships = queryResult.get("relationships");
                if (relationships instanceof ArrayList) {
                    for (Object relationship : ((ArrayList) relationships)) {
                        NodeRelation nodeRelation = new NodeRelation();
                        if (relationship instanceof RelationshipModel) {
                            Long startNode = ((RelationshipModel) relationship).getStartNode();
                            Result startNode1 = session.query(cypherRelation, Map.of("startNode", startNode));
                            for (Map<String, Object> stringObjectMap : startNode1.queryResults()) {
                                for (Map.Entry<String, Object> stringObjectEntry : stringObjectMap.entrySet()) {
                                    NodeEntity node = (NodeEntity) stringObjectEntry.getValue();
                                    if (node != null) {
                                        nodeRelation.setStart(node);
                                    }
                                }
                            }
                        } else {
                            log.warn("relationships 不是RelationshipModel 类型");
                        }
                        nodeRelations.add(nodeRelation);
                    }
                } else {
                    log.warn("relationships 不是List 类型: {}", relationships);
                }
                value.setFromEntity(nodeRelations);
                list.add(value);
            }

            return new MyPage<NodeEntity>(page, size, count, list);
        } finally {
            if (session != null) {
                releaseConnection(session);
            }
        }
    }

    public MyPage<T> findPageByConceptSort(String concept, String name, Integer page, Integer size) {
        Session session = null;
        ArrayList<T> list = new ArrayList<>();
        try {
            session = getSession();
            String cpyher = "MATCH (n:ENTITY) " +
                    "WHERE n.conceptId = $conceptId " +
                    "AND n.entityName contains $entityName " +
                    "WITH n " +
                    "ORDER BY n.createTime DESC " +
                    "SKIP $pageSize * ($page - 1) " +
                    "LIMIT $pageSize " +
                    "RETURN n";
            Map<String, Object> param = new HashMap();
            param.put("conceptId", concept);
            param.put("entityName", name);
            param.put("pageSize", size);
            param.put("page", page);
            Result query = session.query(cpyher, param, true);

            String cpyherCount = "MATCH (n:ENTITY) " +
                    "WHERE n.conceptId = $conceptId " +
                    "AND n.entityName contains $entityName return count(n)";
            Result result = session.query(cpyherCount, param, true);
            long totalNum = 0L;
            for (Map<String, Object> queryResult : result.queryResults()) {
                Object num = queryResult.get("count(n)");
                if (num instanceof Long) {
                    totalNum = Convert.toLong(num);
                }
                ;
            }
            for (Map<String, Object> queryResult : query.queryResults()) {
                for (Map.Entry<String, Object> stringObjectEntry : queryResult.entrySet()) {
                    T value = (T) stringObjectEntry.getValue();
                    list.add(value);
                }
            }

            return new MyPage<T>(page, size, totalNum, list);
        } finally {
            if (session != null) {
                releaseConnection(session);
            }
        }
    }

    /**
     * 条件分页查询，并排序，指定深度
     *
     * @param filters   条件Filters
     * @param sortOrder 排序条件
     * @param page      当前页码
     * @param size      每页条数
     * @param depth     深度
     * @return {@link MyPage}
     */
    public MyPage<T> findPageByConditionAndSort(Filters filters, SortOrder sortOrder, Integer page, Integer size, int depth) {
        Session session = null;
        try {
            session = getSession();
            // 获取总数
            long totalElements = session.count(getTClass(), filters);
            List<T> list = IterUtil.toList(session.loadAll(getTClass(), filters, sortOrder, new Pagination(page == 0 ? page : page - 1, size), depth));
            return new MyPage<>(page, size, totalElements, list);
        } finally {
            if (session != null) {
                releaseConnection(session);
            }
        }
    }

    public List<T> findByCondition(Filters filters) {
        return findByCondition(filters, DEFAULT_DEPTH);
    }

    public List<T> findByCondition(Filters filters, int depth) {
        Session session = null;
        try {
            session = getSession();
            Collection<T> result = session.loadAll(getTClass(), filters, depth);
            return IterUtil.toList(result);
        } finally {
            if (session != null) {
                releaseConnection(session);
            }
        }
    }

    public List<T> findByCondition(Map<String, Object> map, int depth) {
        Session session = null;
        try {
            session = getSession();
            return IterUtil.toList(session.loadAll(getTClass(), getFilter(map), depth));
        } finally {
            if (session != null) {
                releaseConnection(session);
            }
        }
    }

    public Long countByCondition(Map<String, Object> map) {
        Session session = null;
        try {
            session = getSession();
            return session.count(getTClass(), getFilter(map));
        } finally {
            if (session != null) {
                releaseConnection(session);
            }
        }
    }

    public List<T> findByCondition(Map<String, Object> map) {
        return IterUtil.toList(findByCondition(map, DEFAULT_DEPTH));
    }

    public List<T> findByConditionAndSort(Filters filters, SortOrder sortOrder, int depth) {
        Session session = null;
        try {
            session = getSession();
            return IterUtil.toList(session.loadAll(getTClass(), filters, sortOrder, depth));
        } finally {
            if (session != null) {
                releaseConnection(session);
            }
        }
    }

    public List<T> findByConditionAndSort(Filters filters, SortOrder sortOrder) {
        return findByConditionAndSort(filters, sortOrder, DEFAULT_DEPTH);
    }


    private Filters getFilter(Map<String, Object> map) {
        Filters filters = new Filters();
        for (Map.Entry<String, Object> entry : map.entrySet()) {
            if (StrUtil.endWith(entry.getKey(), "id", true)) {
                filters.and(new Filter(entry.getKey(), ComparisonOperator.EQUALS, entry.getValue()));
            } else if (StrUtil.endWith(entry.getKey(), "startTime", true)) {
                filters.and(new Filter(FiledNameConst.CREATE_TIME, ComparisonOperator.GREATER_THAN_EQUAL, entry.getValue()));
            } else if (StrUtil.endWith(entry.getKey(), "endTime", true)) {
                filters.and(new Filter(FiledNameConst.CREATE_TIME, ComparisonOperator.LESS_THAN, entry.getValue()));
            } else {
                filters.and(new Filter(entry.getKey(), ComparisonOperator.CONTAINING, entry.getValue()));
            }
        }
        return filters;
    }

    private Date currentDate() {
        // 获取当前时间
        Date currentDate = new Date();
        // 创建 Calendar 对象并设置时间为当前时间
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(currentDate);
        // 将时间增加 8 小时
        calendar.add(Calendar.HOUR_OF_DAY, 8);
        // 获取调整后的时间
        return calendar.getTime();
    }

    public void deleteByProperty(String propertyName, Object value) {
        Session session = null;
        Transaction transaction = null;
        try {
            // 获取会话
            session = getSession();
            // 开始事务
            transaction = session.beginTransaction();

            // 构建Cypher删除语句
            String cypherQuery = "MATCH (n) " +
                    "WHERE n." + propertyName + " = $value " +
                    "DETACH DELETE n";

            // 执行Cypher删除语句，传入参数
            Map<String, Object> parameters = Collections.singletonMap("value", value);
            session.query(cypherQuery, parameters, true);

            // 提交事务
            transaction.commit();
        } catch (Exception e) {
            if (transaction != null) {
                // 发生异常时回滚事务
                transaction.rollback();
            }
            e.printStackTrace();
            throw new ServiceException("删除失败，原因是：" + e.getMessage());
        } finally {
            // 释放会话资源
            if (session != null) {
                releaseConnection(session);
            }
        }
    }

    /**
     * 此方法在并发情况下 将可能出现docIds为空的情况，高频使用需要优化该功能。
     *
     * @param docIds
     */
    public void deleteByDocIds(String docIds) {
        Session session = null;
        Transaction transaction = null;
        try {
            // 获取会话
            session = getSession();
            // 开始事务
            transaction = session.beginTransaction();

            // 删除具有单一匹配docIds的节点及关系
            String cypherQueryDeleteSingleNode = "MATCH (n) " +
                    "WHERE size(n.docIds) = 1 AND n.docIds[0] = $value " +
                    "DETACH DELETE n";

// 更新具有多个docIds的节点，移除匹配的值， 容易出现并发问题
            String cypherQueryUpdateMultipleNodes = "MATCH (n) " +
                    "WHERE size(n.docIds) > 1 AND $value IN n.docIds " +
                    "SET n.docIds = [x IN n.docIds WHERE x <> $value] " +
                    "return n";

            String cypherQueryUpdateMultipleRelation = "MATCH (n)-[r]-() " +
                    "WHERE size(r.docIds) > 1 AND $value IN r.docIds " +
                    "SET r.docIds = [x IN r.docIds WHERE x <> $value] ";

            String cypherQueryDeleteSingleRelation = "MATCH (n)-[r]-() " +
                    "WHERE size(r.docIds) = 1 AND r.docIds[0] = $value " +
                    "DELETE r";

            // 执行Cypher删除语句，传入参数
            Map<String, Object> parameters = Collections.singletonMap("value", docIds);

            session.query(cypherQueryDeleteSingleNode, parameters, true);
            Result query = session.query(cypherQueryUpdateMultipleNodes, parameters, true);
            session.query(cypherQueryUpdateMultipleRelation, parameters, true);
            session.query(cypherQueryDeleteSingleRelation, parameters, true);

            // 处理并发问题 手动再执行一遍
            ArrayList<NodeEntity> list = new ArrayList<>();
            for (Map<String, Object> queryResult : query.queryResults()) {
                for (Map.Entry<String, Object> stringObjectEntry : queryResult.entrySet()) {
                    NodeEntity value = (NodeEntity) stringObjectEntry.getValue();
                    list.add(value);
                }
            }

            List<String> nodeId = list.stream().map(NodeEntity::getId).collect(Collectors.toList());
            String deleteNode = "match (n) where size(n.docIds) = 0 AND n.id IN $list DETACH DELETE n";
            Map<String, Object> deleteMap = Collections.singletonMap("list", nodeId);
            session.query(deleteNode, deleteMap, true);


            // 提交事务
            transaction.commit();
        } catch (Exception e) {
            if (transaction != null) {
                // 发生异常时回滚事务
                transaction.rollback();
            }
            e.printStackTrace();
        } finally {
            // 释放会话资源
            if (session != null) {
                releaseConnection(session);
            }
        }
    }

    public void deleteByAnnexDocIds(String annexDocIds) {
        Session session = null;
        Transaction transaction = null;
        try {
            // 获取会话
            session = getSession();
            // 开始事务
            transaction = session.beginTransaction();

            // 删除具有单一匹配annexIds的节点及关系
            String cypherQueryDeleteSingleNode = "MATCH (n) " +
                    "WHERE size(n.annexIds) = 1 AND n.annexIds[0] = $value " +
                    "DETACH DELETE n";

// 更新具有多个docIds的节点，移除匹配的值
            String cypherQueryUpdateMultipleNodes = "MATCH (n) " +
                    "WHERE size(n.annexIds) > 1 AND $value IN n.annexIds " +
                    "SET n.annexIds = [x IN n.annexIds WHERE x <> $value] " +
                    "RETURN n";

            String cypherQueryUpdateMultipleRelation = "MATCH (n)-[r]-() " +
                    "WHERE size(r.annexIds) > 1 AND $value IN r.annexIds " +
                    "SET r.annexIds = [x IN r.annexIds WHERE x <> $value] " +
                    "RETURN r";

            String cypherQueryDeleteSingleRelation = "MATCH (n)-[r]-() " +
                    "WHERE size(r.annexIds) = 1 AND r.annexIds[0] = $value " +
                    "DELETE r";

            // 执行Cypher删除语句，传入参数
            Map<String, Object> parameters = Collections.singletonMap("value", annexDocIds);

            session.query(cypherQueryDeleteSingleNode, parameters, true);
            session.query(cypherQueryUpdateMultipleNodes, parameters, true);
            session.query(cypherQueryUpdateMultipleRelation, parameters, true);
            session.query(cypherQueryDeleteSingleRelation, parameters, true);

            // 提交事务
            transaction.commit();
        } catch (Exception e) {
            if (transaction != null) {
                // 发生异常时回滚事务
                transaction.rollback();
            }
            e.printStackTrace();
        } finally {
            // 释放会话资源
            if (session != null) {
                releaseConnection(session);
            }
        }
    }

    public MyPage<T> findPageByConceptAndNameSort(String concept, String name, Set<String> ids, Integer page, Integer size) {
        Session session = null;
        ArrayList<T> list = new ArrayList<>();
        try {
            session = getSession();
            String cpyher = "MATCH (n:ENTITY) " +
                    "WHERE n.conceptId = $conceptId " +
                    "AND n.entityName contains $entityName " +
                    "AND any(x IN n.docIds WHERE x IN $docIdsToCheck) " +
                    "WITH n " +
                    "ORDER BY n.createTime DESC " +
                    "SKIP $pageSize * ($page - 1) " +
                    "LIMIT $pageSize " +
                    "RETURN n";
            Map<String, Object> param = new HashMap();
            param.put("conceptId", concept);
            param.put("entityName", name);
            param.put("docIdsToCheck", ids);
            param.put("pageSize", size);
            param.put("page", page);
            Result query = session.query(cpyher, param, true);

            String cpyherCount = "MATCH (n:ENTITY) " +
                    "WHERE n.conceptId = $conceptId " +
                    "AND n.entityName contains $entityName " +
                    "AND any(x IN n.docIds WHERE x IN $docIdsToCheck) return count(n)";
            Result result = session.query(cpyherCount, param, true);
            long totalNum = 0L;
            for (Map<String, Object> queryResult : result.queryResults()) {
                Object num = queryResult.get("count(n)");
                if (num instanceof Long) {
                    totalNum = Convert.toLong(num);
                }
                ;
            }
            for (Map<String, Object> queryResult : query.queryResults()) {
                for (Map.Entry<String, Object> stringObjectEntry : queryResult.entrySet()) {
                    T value = (T) stringObjectEntry.getValue();
                    list.add(value);
                }
            }

            return new MyPage<T>(page, size, totalNum, list);
        } finally {
            if (session != null) {
                releaseConnection(session);
            }
        }
    }
}
