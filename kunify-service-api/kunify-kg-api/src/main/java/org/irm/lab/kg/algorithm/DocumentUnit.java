package org.irm.lab.kg.algorithm;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import org.irm.lab.common.annotation.MongoDBCollection;
import org.irm.lab.common.entity.AbstractBaseEntity;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/1/30 14:25
 */
@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
@ToString
@MongoDBCollection(collectionName = "kunify_kg_document_unit")
@ApiModel(value = "语料单元")
public class DocumentUnit extends AbstractBaseEntity {

    @ApiModelProperty(value = "对应资源的Id")
    private String resourceId;

    @ApiModelProperty(value = "所处页码")
    private Integer page;

    @ApiModelProperty(value = "当前sort")
    private Integer sortInCurrentPage;

    @ApiModelProperty(value = "内容")
    private String content;

    @ApiModelProperty(value = "高亮词列表")
    private List<DocumentTextLight> documentTextLights=new ArrayList<>();

    /**
     * 0：内容
     * 1：1级标题
     * 2：2级标题
     * A：表格
     * B：图片
     */
    @ApiModelProperty(value = "语料单元类型")
    private String type;

    @ApiModelProperty(value = "图片链接")
    private String attachName;
    private String attachId;

    @ApiModelProperty(value = "表头")
    private List<TableHeader> tableHeader;

    @ApiModelProperty(value = "表格信息")
    private String tableData;

    private Double llx;

    private Double lly;

    private Double height;

    private Double width;

    private String ieInferResult;

    private String aiInferResult;

    private String aiContent;
}
