package org.irm.lab.kg.entity;

import io.swagger.annotations.ApiModel;
import lombok.*;
import org.irm.lab.common.annotation.MongoDBCollection;
import org.irm.lab.common.entity.AbstractBaseEntity;

/**
 * <AUTHOR>
 * @date 2023/3/20 15:00
 */
@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
@ToString
@MongoDBCollection(collectionName = "kunify_kg_pre_alignment")
@ApiModel(value = "概念")
public class PreAlignment extends AbstractBaseEntity {
    String nodeId;
    String cacheNodeId;
    String resourceId;
}
