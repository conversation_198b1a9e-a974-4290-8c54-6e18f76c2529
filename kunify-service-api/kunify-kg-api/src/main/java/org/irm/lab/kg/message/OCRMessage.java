package org.irm.lab.kg.message;

import java.io.Serializable;
import java.util.Map;

import org.irm.lab.kg.algorithm.DocumentImage;
import org.irm.lab.repository.entity.Resource;

import lombok.Data;

/**
 * <AUTHOR> <br/>
 * @date 2024/6/26 <br/>
 * &#064;Copyright  博客：<a href="https://eliauku.gitee.io/">...</a>  ||  per aspera and astra <br/>
 */
@Data
public class OCRMessage implements Serializable {

    /**
     * 文档解析 交换机
     */
    public static final String EXCHANGE = "KUNIFY4_EXCHANGE_OCR_";
    /**
     * 文档解析 消息队列
     */
    public static final String QUEUE = "KUNIFY4_QUEUE_OCR_";
    /**
     * 文档解析 routingKey
     */
    public static final String ROUTING_KEY = "KUNIFY4_ROUTING_OCR_";

    /**
     * 当前用户信息
     */
    private String user;
    /**
     * 资源Id
     */
    private String resourceId;

    private Resource resource;

    private DocumentImage documentImage;

    private Integer page;

    private Boolean isEnd;

    private Map<String, Object> map;

}
