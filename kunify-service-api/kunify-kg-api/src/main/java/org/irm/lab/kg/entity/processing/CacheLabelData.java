package org.irm.lab.kg.entity.processing;


import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import org.irm.lab.common.annotation.MongoDBCollection;


/**
 * <AUTHOR>
 * @date 2023/2/27 14:47
 * @description 标签数据缓存
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@EqualsAndHashCode(callSuper = true)
@MongoDBCollection(collectionName = "kunify_kg_cache_label_data")
@ApiModel(value = "标签数据缓存")
public class CacheLabelData extends LabelData{
    @ApiModelProperty(value = "数据来源", notes = "元数据转化/知识标注/文档解析")
    private String type;

    @ApiModelProperty(value = "算法识别出来uuid")
    private String schemaUUID;
}
