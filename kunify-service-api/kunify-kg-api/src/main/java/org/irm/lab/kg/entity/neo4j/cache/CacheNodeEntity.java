/*
package org.irm.lab.kg.entity.neo4j.cache;

import com.fasterxml.jackson.annotation.JsonIgnore;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.experimental.Accessors;
import org.irm.lab.kg.config.converter.CompositeMapConverter;
import org.irm.lab.kg.entity.neo4j.base.Neo4jAbstractBaseEntity;
import org.neo4j.ogm.annotation.Relationship;
import org.neo4j.ogm.annotation.typeconversion.Convert;

import java.util.HashMap;
import java.util.HashSet;
import java.util.Map;
import java.util.Set;

*/
/**
 * <AUTHOR>
 * @date 2023/2/23 14:38
 * @description 缓存实例
 *//*

@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
@Accessors(chain = true)
@org.neo4j.ogm.annotation.NodeEntity(label = "CACHE_ENTITY")
public class CacheNodeEntity extends Neo4jAbstractBaseEntity {

    */
/**
     * 实例名称
     *//*

    private String entityName;


    */
/**
     * 实例对应的概念
     *//*

    private String conceptId;


    */
/**
     * 实例对应的模型
     *//*

    private String modelId;

    */
/**
     * 来源公文
     *//*

    private Set<String> docIds = new HashSet<>();

    */
/**
     * 来源语料单元
     *//*

    private Set<String> documentUnitIds;

    */
/**
     * 实例的属性
     *//*

    @Convert(CompositeMapConverter.class)
    private Map<String, Object> properties = new HashMap<>();


    */
/**
     * 指向当前实例的节点
     *//*

    @Relationship(type = "RELATION", direction = Relationship.INCOMING)
    @JsonIgnore
    private Set<CacheNodeRelation> fromEntity;

    */
/**
     * 当前实例指向的节点
     *//*

    @Relationship(type = "RELATION")
    @JsonIgnore
    private Set<CacheNodeRelation> toEntity;

    */
/**
     * 标签数据Id
     *//*

    private String labelDataId;

    */
/**
     * 是否为顶级节点
     *//*

    private boolean topNodeEntity = false;

}

*/
