package org.irm.lab.kg.config;

import lombok.Setter;
import org.neo4j.ogm.session.SessionFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;


@Setter
@Configuration
@ConfigurationProperties(prefix = "neo4j")
public class Neo4jConfig {
    @Value("${neo4j.url}")
    private String databaseUrl;

    @Value("${neo4j.username}")
    private String userName;

    @Value("${neo4j.password}")
    private String password;

    private static final String packages = "org.irm.lab.kg.entity.neo4j";

    @Bean
    public SessionFactory sessionFactory() {
        org.neo4j.ogm.config.Configuration configuration = new org.neo4j.ogm.config.Configuration.Builder()
                .uri(databaseUrl)
                .credentials(userName, password)
                .build();
        return new SessionFactory(configuration, packages);
    }


}