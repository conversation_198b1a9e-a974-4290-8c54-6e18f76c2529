package org.irm.lab.kg.algorithm;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import org.irm.lab.common.annotation.MongoDBCollection;
import org.irm.lab.common.entity.AbstractBaseEntity;
import org.irm.lab.common.pojo.BaseStatus;

/**
 * 文档生成式摘要
 * <AUTHOR>
 * @date 2023/1/30 15:15
 */
@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
@ToString
@MongoDBCollection(collectionName = "kunify_kg_document_summary")
@ApiModel(value = "文档生成式摘要")
public class DocumentSummary extends AbstractBaseEntity {

    @ApiModelProperty(value = "对应资源的id")
    private String resourceId;

    @ApiModelProperty(value = "原始内容")
    private String originContent;

    @ApiModelProperty(value = "摘要内容")
    private String summaryContent;

    private Integer contentSort;

    @ApiModelProperty(value = "摘要生成状态，失败的话直接写失败原因")
    private String summaryStatus;

    public DocumentSummary(String resourceId,String originContent,String summaryStatus,Integer contentSort){
        this.resourceId = resourceId;
        this.originContent = originContent;
        this.summaryContent = summaryStatus;
        this.contentSort = contentSort;
    }
}
