package org.irm.lab.kg.repository.neo4j.node;

import cn.hutool.core.util.ObjectUtil;
import org.irm.lab.common.annotation.Neo4jDao;
import org.irm.lab.kg.entity.KnowledgeConcept;
import org.irm.lab.kg.entity.neo4j.node.NodeEntity;
import org.irm.lab.kg.repository.neo4j.base.Neo4jBaseDao;
import org.neo4j.ogm.model.Property;
import org.neo4j.ogm.model.Result;
import org.neo4j.ogm.response.model.RelationshipModel;
import org.neo4j.ogm.session.Session;
import org.neo4j.ogm.transaction.Transaction;

import java.util.*;

/**
 * <AUTHOR>
 * @date 2023-02-09
 */

@Neo4jDao
public class NodeEntityRepository extends Neo4jBaseDao<NodeEntity> {
    /**
     *
     * @param newStartNodeId 保留的节点id
     * @param oldId 需要删除的节点id
     * @param docIds
     */
    public void redirectRelations(String newStartNodeId, String oldId, Set<String> docIds) {
        Session session = null;
        Transaction transaction = null;
        try {
            // 获取会话
            session = getSession();
            // 开始事务
            transaction = session.beginTransaction();

            // 构建Cypher语句
            String cypher = "MATCH (:ENTITY{id:$oldId}) -[r:RELATION]-> (n:ENTITY) return r ,n.id AS uid";
            Map<String, Object> parameters = new HashMap<>();
            parameters.put("newStartNodeId", newStartNodeId);
            parameters.put("oldId", oldId);
            Result result = session.query(cypher, parameters);
            for (Map<String, Object> stringObjectMap : result) {
                HashMap<String, Object> relP = new HashMap<>();
                String uid = stringObjectMap.get("uid").toString();
                Object r = stringObjectMap.get("r");
                if( r instanceof RelationshipModel){
                    for (Property<String, Object> stringObjectProperty : ((RelationshipModel) r).getPropertyList()) {
                        String key = stringObjectProperty.getKey();
                        Object value = stringObjectProperty.getValue();
                        if(key.equals("docIds")){
                            relP.put(key,docIds);
                        }else{
                            relP.put(key,value);
                        }
                    }

                    String query = "MATCH (m:ENTITY{id:$newStartNodeId}), (n:ENTITY{id:$uid}) create (m)-[r:RELATION $relProperties ]->(n)";

                    Map<String, Object> param = new HashMap<>();
                    param.put("newStartNodeId", newStartNodeId);
                    param.put("uid", uid);
                    param.put("relProperties",relP);

                    session.query(query,param);

                    String query1 = "MATCH (:ENTITY{id:$oldId}) -[r:RELATION]-> (n:ENTITY) delete r";

                    Map<String, Object> param1 = new HashMap<>();
                    param1.put("oldId", oldId);

                    session.query(query1,param1);
                }

            }


            // 提交事务
            transaction.commit();
        } catch (Exception e) {
            if (transaction != null) {
                // 发生异常时回滚事务
                transaction.rollback();
            }
            e.printStackTrace();
        } finally {
            // 释放会话资源
            if (session != null) {
                releaseConnection(session);
            }
        }
    }

    public void incoming(String newStartNodeId, String oldId, Set<String> docIds) {
        Session session = null;
        Transaction transaction = null;
        try {
            // 获取会话
            session = getSession();
            // 开始事务
            transaction = session.beginTransaction();

            // 构建Cypher语句
            String cypher = "MATCH (:ENTITY{id:$oldId}) <-[r:RELATION]- (n:ENTITY) return r ,n.id AS uid";
            Map<String, Object> parameters = new HashMap<>();
            parameters.put("newStartNodeId", newStartNodeId);
            parameters.put("oldId", oldId);
            Result result = session.query(cypher, parameters);
            for (Map<String, Object> stringObjectMap : result) {
                HashMap<String, Object> relP = new HashMap<>();
                String uid = stringObjectMap.get("uid").toString();
                Object r = stringObjectMap.get("r");
                if( r instanceof RelationshipModel){
                    for (Property<String, Object> stringObjectProperty : ((RelationshipModel) r).getPropertyList()) {
                        String key = stringObjectProperty.getKey();
                        Object value = stringObjectProperty.getValue();
                        if(key.equals("docIds")){
                            relP.put(key,docIds);
                        }else{
                            relP.put(key,value);
                        }
                    }

                    String query = "MATCH (m:ENTITY{id:$newStartNodeId}), (n:ENTITY{id:$uid}) create (m)<-[r:RELATION $relProperties ]-(n)";

                    Map<String, Object> param = new HashMap<>();
                    param.put("newStartNodeId", newStartNodeId);
                    param.put("uid", uid);
                    param.put("relProperties",relP);

                    session.query(query,param);

                    String query1 = "MATCH (:ENTITY{id:$oldId}) <-[r:RELATION]- (n:ENTITY) delete r";

                    Map<String, Object> param1 = new HashMap<>();
                    param1.put("oldId", oldId);

                    session.query(query1,param1);
                }

            }


            // 提交事务
            transaction.commit();
        } catch (Exception e) {
            if (transaction != null) {
                // 发生异常时回滚事务
                transaction.rollback();
            }
            e.printStackTrace();
        } finally {
            // 释放会话资源
            if (session != null) {
                releaseConnection(session);
            }
        }
    }


    public List<NodeEntity> findByCypher(String conceptId, String resourceId) {
        Session session = null;
        try {
            // 获取会话
            session = getSession();
            List<NodeEntity> list = new ArrayList<>();
            // 构建Cypher语句
            String cypher = "MATCH (n:ENTITY) " +
                    "WHERE n.conceptId = $conceptId " +
                    "AND any(x IN n.docIds WHERE x IN $docIdsToCheck) return n";
            Map<String, Object> parameters = new HashMap<>();
            parameters.put("conceptId", conceptId);
            parameters.put("docIdsToCheck", List.of(resourceId));
            Result result = session.query(cypher, parameters);
            for (Map<String, Object> stringObjectMap : result) {
                for (Map.Entry<String, Object> stringObjectEntry : stringObjectMap.entrySet()) {
                    NodeEntity value = (NodeEntity) stringObjectEntry.getValue();
                    list.add(value);
                }
            }
            return list;
        } finally {
            // 释放会话资源
            if (session != null) {
                releaseConnection(session);
            }
        }
    }

    public List<NodeEntity> findMainNodeEntityByCypher(Set<String> resourceIds, List<String> concepts, String name) {
        Session session = null;
        try {
            // 获取会话
            session = getSession();
            List<NodeEntity> list = new ArrayList<>();
            Map<String, Object> parameters = new HashMap<>();
            // 构建Cypher语句
//            String cypher = "MATCH (n:ENTITY) " +
//                    "WHERE n.conceptId IN $conceptIds " +
                    String cypher = "MATCH (n:ENTITY) " +
                    "WHERE any(x IN n.docIds WHERE x IN $docIdsToCheck) ";
//            String cypher = "MATCH (n:ENTITY) " +
//                    "WHERE  any(x IN n.docIds WHERE x IN $docIdsToCheck) ";
            if (ObjectUtil.isNotEmpty(name)) { // 是否模糊查询
                cypher +=  "AND n.entityName contains $name return n";
                parameters.put("name",name);
            }else{
                cypher += "return n";
            }
//            parameters.put("conceptIds", concepts);
            parameters.put("docIdsToCheck", new ArrayList<String>(resourceIds));
            Result result = session.query(cypher, parameters);
            for (Map<String, Object> stringObjectMap : result) {
                for (Map.Entry<String, Object> stringObjectEntry : stringObjectMap.entrySet()) {
                    NodeEntity value = (NodeEntity) stringObjectEntry.getValue();
                    list.add(value);
                }
            }
            return list;
        } finally {
            // 释放会话资源
            if (session != null) {
                releaseConnection(session);
            }
        }
    }

    public List<NodeEntity> findMainNodeEntityByCypherLimit(Set<String> resourceIds, List<String> concepts, String name, int limit) {
        Session session = null;
        try {
            // 获取会话
            session = getSession();
            List<NodeEntity> list = new ArrayList<>();
            Map<String, Object> parameters = new HashMap<>();
            // 构建Cypher语句
//            String cypher = "MATCH (n:ENTITY) " +
//                    "WHERE n.conceptId IN $conceptIds " +
            String cypher = "MATCH (n:ENTITY) " +
                    "WHERE any(x IN n.docIds WHERE x IN $docIdsToCheck) ";
//            String cypher = "MATCH (n:ENTITY) " +
//                    "WHERE  any(x IN n.docIds WHERE x IN $docIdsToCheck) ";
            if (ObjectUtil.isNotEmpty(name)) { // 是否模糊查询
                cypher +=  "AND n.entityName contains $name return n";
                parameters.put("name",name);
            }else{
                cypher += "return n";
            }
            cypher += " LIMIT $limit";
            parameters.put("limit",limit);
//            parameters.put("conceptIds", concepts);
            parameters.put("docIdsToCheck", new ArrayList<String>(resourceIds));
            Result result = session.query(cypher, parameters);
            for (Map<String, Object> stringObjectMap : result) {
                for (Map.Entry<String, Object> stringObjectEntry : stringObjectMap.entrySet()) {
                    NodeEntity value = (NodeEntity) stringObjectEntry.getValue();
                    list.add(value);
                }
            }
            return list;
        } finally {
            // 释放会话资源
            if (session != null) {
                releaseConnection(session);
            }
        }
    }


    public Map<String, List<NodeEntity>> findSameNodeEntity(String conceptId) {
        Session session = null;
        try {
            // 获取会话
            session = getSession();
            Map<String, Object> parameters = new HashMap<>();
            // 构建Cypher语句
            String cypher = "match (n) " +
                    "where n.conceptId= $conceptId " +
                    "with n.entityName AS entityName, COLLECT(n) AS nodes " +
                    "where size(nodes)>1 " +
                    "return entityName,nodes " +
                    "ORDER BY entityName ";
            parameters.put("conceptId",conceptId);


            Map<String, List<NodeEntity>> resultNodeEntity = new HashMap<>();
            Result result = session.query(cypher, parameters);
            for (Map<String, Object> stringObjectMap : result) {

                String entityName = (String)stringObjectMap.get("entityName");
                ArrayList<NodeEntity> nodeEntities = new ArrayList<>();
                if(stringObjectMap.get("nodes") instanceof ArrayList){
                    for(Object node : (List)stringObjectMap.get("nodes")){
                        NodeEntity nodeEntity =  (NodeEntity) node;
                        nodeEntities.add(nodeEntity);
                    }
                }
                resultNodeEntity.put(entityName,nodeEntities);
            }
            return resultNodeEntity;
        } finally {
            // 释放会话资源
            if (session != null) {
                releaseConnection(session);
            }
        }
    }
}
