package org.irm.lab.kg.config.converter;

import cn.hutool.core.util.StrUtil;
import org.irm.lab.kg.constant.CompositeConstant;
import org.neo4j.ogm.typeconversion.CompositeAttributeConverter;

import java.util.HashMap;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2023-02-07
 */
@SuppressWarnings("all")
public class CompositeMapConverter implements CompositeAttributeConverter<Map<String, Object>> {


    @Override
    public Map<String, ?> toGraphProperties(Map<String, Object> value) {
        return value;
    }

    @Override
    public Map<String, Object> toEntityAttribute(Map<String, ?> value) {
        return value.entrySet().stream().filter(key -> StrUtil.startWith(key.getKey(), CompositeConstant.prefix)).collect(Collectors.toMap(Map.Entry::getKey, Map.Entry::getValue));
    }
}
