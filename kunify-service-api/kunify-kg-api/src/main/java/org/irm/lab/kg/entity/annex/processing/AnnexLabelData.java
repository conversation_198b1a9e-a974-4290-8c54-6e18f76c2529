package org.irm.lab.kg.entity.annex.processing;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import org.irm.lab.common.annotation.MongoDBCollection;
import org.irm.lab.common.entity.AbstractBaseEntity;
import org.irm.lab.kg.entity.processing.LabelPropertyVO;

import java.util.*;

/**
 * <AUTHOR>
 * @date 2023/4/23 10:29
 * @description 附件-标签数据
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@EqualsAndHashCode(callSuper = true)
@MongoDBCollection(collectionName = "kunify_kg_annex_label_data")
@ApiModel(value = "附件标签数据")
public class AnnexLabelData extends AbstractBaseEntity {
    @ApiModelProperty(value = "资源Id", notes = "该标签所属的资源Id")
    private String resourceId;
    @ApiModelProperty(value = "附件Id", notes = "该标签所属附件Id")
    private String annexId;
    @ApiModelProperty(value = "对应的标签Id")
    private String labelId;
    @ApiModelProperty(value = "对应的标签名称")
    private String labelName;
    @ApiModelProperty(value = "属性")
    private List<LabelPropertyVO> labelProperties = new ArrayList<>();
    @ApiModelProperty(value = "数据值")
    private String content;
    @ApiModelProperty(value = "日期类型数据值")
    private Date dateContent;
    @ApiModelProperty(value = "来源语料单元")
    private Set<String> documentUnitIds = new HashSet<>();
    @ApiModelProperty(value = "顶级标签")
    private boolean topLabel = false;
}
