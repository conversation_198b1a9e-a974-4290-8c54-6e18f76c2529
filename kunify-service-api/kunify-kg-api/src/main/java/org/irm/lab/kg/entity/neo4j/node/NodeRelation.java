package org.irm.lab.kg.entity.neo4j.node;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.experimental.Accessors;
import org.irm.lab.kg.config.converter.CompositeMapConverter;
import org.irm.lab.kg.entity.neo4j.base.Neo4jAbstractBaseEntity;
import org.neo4j.ogm.annotation.EndNode;
import org.neo4j.ogm.annotation.RelationshipEntity;
import org.neo4j.ogm.annotation.StartNode;
import org.neo4j.ogm.annotation.typeconversion.Convert;

import java.util.HashMap;
import java.util.HashSet;
import java.util.Map;
import java.util.Objects;
import java.util.Set;

/**
 * <AUTHOR>
 * @date 2023-02-09
 */
@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
@Accessors(chain = true)
@RelationshipEntity(type = "RELATION")
public class NodeRelation extends Neo4jAbstractBaseEntity {

    /**
     * 谓词名称
     */
    private String predicateName;

    /**
     * 对应的谓词id
     */
    private String predicateId;


    /**
     * 来源公文
     */
    private Set<String> docIds = new HashSet<>();

    /**
     * 来源附件Id
     */
    private Set<String> annexIds = new HashSet<>();

    /**
     * 属性
     */
    @Convert(CompositeMapConverter.class)
    private Map<String, Object> properties = new HashMap<>();

    /**
     * 发出关系的实例
     */
    @StartNode
    private NodeEntity start;

    /**
     * 关系终止的实例
     */
    @EndNode
    private NodeEntity end;

    /**
     * 反关系的id
     */
    private String reverseRelationId;

    /**
     * 是否为正向关系
     */
    private boolean positive;

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        NodeRelation that = (NodeRelation) o;

        // 首先检查 predicateName 是否相等
        boolean predicateNamesEqual = Objects.equals(predicateName, that.predicateName);

        // 检查 start 和 that.start 是否相等，考虑了可能为 null 的情况
        boolean startEqual = (start == null && that.start == null) ||
                (start != null && start.equals(that.start));

        // 检查 end 和 that.end 是否相等，考虑了可能为 null 的情况
        boolean endEqual = (end == null && that.end == null) ||
                (end != null && end.equals(that.end));


        return predicateNamesEqual && startEqual && endEqual;
    }

    @Override
    public int hashCode() {
        int result = predicateName != null ? predicateName.hashCode() : 0;
        result = 31 * result + (start != null ? start.hashCode() : 0);
        result = 31 * result + (end != null ? end.hashCode() : 0);
        return result;
    }

    @Override
    public String toString() {

        return "NodeRelation{" +
                "predicateName='" + predicateName + '\'' +
                ", predicateId='" + predicateId + '\'' +
                ", docIds=" + docIds +
                ", annexIds=" + annexIds +
                ", properties=" + properties +
                (start!=null?", start=[entityName: " + start.getEntityName() +", conceptId: "+ start.getConceptId() + ", docIds: " + start.getDocIds() + ", Properties: "+ start.getProperties():"start = null ") +
                (end!=null?", end=[entityName: " + end.getEntityName() +", conceptId: "+ end.getConceptId() + ", docIds: " + end.getDocIds() +", properties: "+ end.getProperties():"end = null") +
                ", reverseRelationId='" + reverseRelationId + '\'' +
                ", positive=" + positive +
                '}';
    }
}
