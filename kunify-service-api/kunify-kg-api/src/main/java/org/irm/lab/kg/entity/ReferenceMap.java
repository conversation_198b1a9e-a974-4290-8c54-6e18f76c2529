package org.irm.lab.kg.entity;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.experimental.Accessors;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023-02-16
 */

//只是用于方便模型类中的存储
@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
@Accessors(chain = true)
public class ReferenceMap {
    private String id;
    private List<String> propertyIds;

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        if (this.getId() == null) return false;
        ReferenceMap that = (ReferenceMap) o;
        return this.getId().equals(that.getId());
    }

    @Override
    public int hashCode() {
        return 1;
    }
}