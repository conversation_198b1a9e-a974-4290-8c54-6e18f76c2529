package org.irm.lab.kg.entity;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import org.irm.lab.common.annotation.MongoDBCollection;
import org.irm.lab.common.entity.AbstractBaseEntity;
import org.irm.lab.common.pojo.BaseStatus;

import javax.validation.constraints.NotBlank;

@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
@ToString
@MongoDBCollection(collectionName = "kunify_kg_knowledge_property")
@ApiModel(value = "属性")
public class KnowledgeProperty extends AbstractBaseEntity {

    @ApiModelProperty(value = "名称")
    private String name;

    @ApiModelProperty(value = "唯一标识")
    private String identifier;

    @ApiModelProperty(value = "数据类型")
    @NotBlank(message = "数据类型不能为空")
    private String datatype;

    @ApiModelProperty(value = "描述")
    private String description;

    @ApiModelProperty(value = "是否展示")
    private Boolean isShow = true;

    @ApiModelProperty(value = "是否部署")
    private String deployed = BaseStatus.NOT_DEPLOYED;
}
