package org.irm.lab.kg.entity;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import lombok.experimental.Accessors;
import org.irm.lab.common.annotation.MongoDBCollection;
import org.irm.lab.common.entity.AbstractBaseEntity;
import org.irm.lab.common.pojo.BaseStatus;

import javax.validation.constraints.NotNull;
import java.util.ArrayList;
import java.util.List;

@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
@ToString
@Accessors(chain = true)
@MongoDBCollection(collectionName = "kunify_kg_knowledge_concept")
@ApiModel(value = "概念")
public class KnowledgeConcept extends AbstractBaseEntity {

    @ApiModelProperty(value = "名称")
    private String name;

    @ApiModelProperty(value = "所属知识模型")
    private List<String> modelIds = new ArrayList<>();


    @ApiModelProperty(value = "概念标识", notes = "概念英文名称，首字母需大写")
    @NotNull
    private String identifier;

    @ApiModelProperty(value = "概念继承")
    private String parentId = "0";

    @ApiModelProperty(value = "描述")
    private String description;

    @ApiModelProperty(value = "关联属性")
    private List<String> propertyIds = new ArrayList<>();

}
