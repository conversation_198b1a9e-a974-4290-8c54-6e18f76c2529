package org.irm.lab.kg.algorithm;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import org.irm.lab.common.annotation.MongoDBCollection;
import org.irm.lab.common.entity.AbstractBaseEntity;

/**
 * <AUTHOR>
 * @date 2023/1/30 15:00
 */
@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
@ToString
@MongoDBCollection(collectionName = "kunify_kg_document_image")
@ApiModel(value = "文档转化为图片")
public class DocumentImage extends AbstractBaseEntity {

    @ApiModelProperty(value = "对应文档的uuid")
    private String resourceId;

    private String attachId;

    private String attachName;

    @ApiModelProperty(value = "所在页码")
    private Integer page;

    public DocumentImage( String attachId, String attachName, Integer page) {
        this.attachId = attachId;
        this.attachName = attachName;
        this.page = page;
    }



}
