/*
package org.irm.lab.kg.entity.neo4j.cache;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.experimental.Accessors;
import org.irm.lab.kg.config.converter.CompositeMapConverter;
import org.irm.lab.kg.entity.neo4j.base.Neo4jAbstractBaseEntity;
import org.neo4j.ogm.annotation.EndNode;
import org.neo4j.ogm.annotation.RelationshipEntity;
import org.neo4j.ogm.annotation.StartNode;
import org.neo4j.ogm.annotation.typeconversion.Convert;

import java.util.HashMap;
import java.util.HashSet;
import java.util.Map;
import java.util.Set;

*/
/**
 * <AUTHOR>
 * @date 2023/3/16 10:29
 * @description 缓存关系
 *//*

@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
@Accessors(chain = true)
@RelationshipEntity(type = "CACHE_RELATION")
public class CacheNodeRelation extends Neo4jAbstractBaseEntity {
    */
/**
     * 谓词名称
     *//*

    private String predicateName;

    */
/**
     * 对应的谓词id
     *//*

    private String predicateId;


    */
/**
     * 来源公文
     *//*

    private Set<String> docIds = new HashSet<>();


    */
/**
     * 属性
     *//*

    @Convert(CompositeMapConverter.class)
    private Map<String, Object> properties = new HashMap<>();

    */
/**
     * 发出关系的实例
     *//*

    @StartNode
    private CacheNodeEntity start;

    */
/**
     * 关系终止的实例
     *//*

    @EndNode
    private CacheNodeEntity end;
}
*/
