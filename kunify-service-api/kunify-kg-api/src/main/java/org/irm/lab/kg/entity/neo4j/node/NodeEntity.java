package org.irm.lab.kg.entity.neo4j.node;

import com.fasterxml.jackson.annotation.JsonIgnore;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.experimental.Accessors;
import org.irm.lab.kg.config.converter.CompositeMapConverter;
import org.irm.lab.kg.entity.neo4j.base.Neo4jAbstractBaseEntity;
import org.neo4j.ogm.annotation.Relationship;
import org.neo4j.ogm.annotation.typeconversion.Convert;

import java.util.HashMap;
import java.util.HashSet;
import java.util.Map;
import java.util.Set;

/**
 * <AUTHOR>
 * @date 2023-02-09
 */

@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
@Accessors(chain = true)
@org.neo4j.ogm.annotation.NodeEntity(label = "ENTITY")
public class NodeEntity extends Neo4jAbstractBaseEntity {

    /**
     * 实例名称
     */
    private String entityName;


    /**
     * 实例对应的概念
     */
    private String conceptId;


    /**
     * 实例对应的模型
     */
    private String modelId;

    /**
     * 来源公文
     */
    private Set<String> docIds = new HashSet<>();

    /**
     * 来源附件
     */
    private Set<String> annexIds = new HashSet<>();

    /**
     * 来源语料单元
     */
    private Set<String> documentUnitIds = new HashSet<>();


    /**
     * 实例的属性
     */
    @Convert(CompositeMapConverter.class)
    private Map<String, Object> properties = new HashMap<>();


    /**
     * 指向当前实例的关系
     */
    @Relationship(type = "RELATION", direction = Relationship.INCOMING)
    @JsonIgnore
    private Set<NodeRelation> fromEntity;

    /**
     * 当前实例发出的关系
     */
    @Relationship(type = "RELATION", direction = Relationship.OUTGOING)
    @JsonIgnore
    private Set<NodeRelation> toEntity;


    /**
     * 标签数据Id
     */
    private String labelDataId;


    /**
     * 是否为顶级节点
     */
    private boolean topNodeEntity = false;

    @Override
    public String toString() {
        StringBuilder fromEntityString = new StringBuilder("[");
        if (fromEntity == null) {
            fromEntityString.append("null");
        } else {
            for (NodeRelation nodeRelation : fromEntity) {
                fromEntityString.append(nodeRelation != null ? nodeRelation.toString() : "").append(", ");
            }
        }
        StringBuilder toEntityString = new StringBuilder("[");
        if (toEntity == null) {
            toEntityString.append("null");
        } else {
            for (NodeRelation nodeRelation : toEntity) {
                toEntityString.append(nodeRelation != null ? nodeRelation.toString() : "").append(", ");
            }
        }
        return "NodeEntity{" +
                "entityName='" + entityName + '\'' +
                ", conceptId='" + conceptId + '\'' +
//                ", modelId='" + modelId + '\'' +
//                ", docIds=" + docIds +
//                ", annexIds=" + annexIds +
//                ", documentUnitIds=" + documentUnitIds +
//                ", properties=" + properties +
//                ", fromEntity=" + fromEntityString.toString() + "]" +
//                ", toEntity=" + toEntityString.toString() + "]" +
//                ", labelDataId='" + labelDataId + '\'' +
//                ", topNodeEntity=" + topNodeEntity +
                '}';
    }
}
