package org.irm.lab.kg.constant;

/**
 * <AUTHOR> <br/>
 * @date 2024/5/17 <br/>
 * @Copyright 博客：https://eliauku.gitee.io/  ||  per aspera and astra <br/>
 */
public interface InferStatusConstant {

    /**
     * 待识别
     */
    String WAITING = "waiting";

    /**
     * 识别中
     */
    String PROCESSING = "processing";

    /**
     * 识别完成
     */
    String FINISHED = "finished";

    /**
     * 识别失败
     */
    String FAILED = "failed";
}
