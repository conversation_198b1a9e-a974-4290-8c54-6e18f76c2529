package org.irm.lab.kg.entity;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import lombok.experimental.Accessors;
import org.irm.lab.common.annotation.MongoDBCollection;
import org.irm.lab.common.entity.AbstractBaseEntity;
import org.irm.lab.common.pojo.BaseStatus;

import java.util.*;

@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
@ToString
@MongoDBCollection(collectionName = "kunify_kg_knowledge_model")
@ApiModel(value = "知识模型")
public class KnowledgeModel extends AbstractBaseEntity {

    @ApiModelProperty(value = "名称")
    private String name;

    @ApiModelProperty(value = "唯一标识")
    private String identifier;

    @ApiModelProperty(value = "是否部署")
    private String deployed = BaseStatus.NOT_DEPLOYED;

    @ApiModelProperty(value = "描述")
    private String description;

    @ApiModelProperty(value = "引用的概念")
//    private List<KnowledgeConcept> concepts = new ArrayList<>();
    private Set<ReferenceMap> concepts = new HashSet<>();

    @ApiModelProperty(value = "引用的关系")
//    private List<KnowledgeRelation> relations = new ArrayList<>();
    private Set<ReferenceMap> relations = new HashSet<>();

}