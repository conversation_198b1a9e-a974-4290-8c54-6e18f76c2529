package org.irm.lab.kg.message;

import lombok.Data;
import org.irm.lab.kg.entity.annex.AnnexDocumentImage;
import org.irm.lab.repository.entity.ResourceAnnex;

import java.io.Serializable;
import java.util.Map;

/**
     
 * @date 2024/2/27 <br/>
      
 */
@Data
public class OCRAnnexMessage implements Serializable {
    /**
     * 文档解析 交换机
     */
    public static final String EXCHANGE = "KUNIFY4_EXCHANGE_OCRAnnex_";
    /**
     * 文档解析 消息队列
     */
    public static final String QUEUE = "KUNIFY4_QUEUE_OCRAnnex_";
    /**
     * 文档解析 routingKey
     */
    public static final String ROUTING_KEY = "KUNIFY4_ROUTING_OCRAnnex_";

    /**
     * 当前用户信息
     */
    private String user;
    /**
     * 资源Id
     */
    private String resourceId;

    private ResourceAnnex resourceAnnex;

    private AnnexDocumentImage annexDocumentImage;

    private Integer page;

    private Boolean isEnd;

    private Map<String, Object> map;
}
