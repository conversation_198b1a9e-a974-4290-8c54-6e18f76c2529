package org.irm.lab.kg.entity.annex.processing;


import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import org.irm.lab.common.annotation.MongoDBCollection;
import org.irm.lab.common.entity.AbstractBaseEntity;
import org.irm.lab.kg.entity.processing.LabelPropertyVO;

import java.util.ArrayList;
import java.util.List;


/**
 * <AUTHOR>
 * @date 2023/4/23 10:29
 * @description 附件-标签关系数据
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@EqualsAndHashCode(callSuper = true)
@MongoDBCollection(collectionName = "kunify_kg_annex_triple_label_data")
@ApiModel(value = "附件标签关系数据")
@JsonIgnoreProperties(ignoreUnknown = true)
public class AnnexTripleLabelData extends AbstractBaseEntity {

    @ApiModelProperty(value = "资源Id")
    private String resourceId;
    @ApiModelProperty(value = "附件Id", notes = "该标签所属附件Id")
    private String annexId;
    @ApiModelProperty(value = "起始节点所属的数据集标签概念Id")
    private String startLabelId;
    @ApiModelProperty(value = "起始节点标签数据Id")
    private String startLabelDataId;
    @ApiModelProperty(value = "关系标签Id", notes = "数据集标签谓词Id")
    private String relationLabelId;
    @ApiModelProperty(value = "关系名称")
    private String relationName;
    @ApiModelProperty(value = "关系的属性 k：属性Id v：属性值")
    private List<LabelPropertyVO> relationProperties =new ArrayList<>();
    @ApiModelProperty(value = "末尾节点所属的数据集标签概念Id")
    private String endLabelId;
    @ApiModelProperty(value = "末尾节点标签数据Id")
    private String endLabelDataId;

}
