package org.irm.lab.kg.message;

import lombok.Data;
import org.irm.lab.repository.entity.Resource;
import org.irm.lab.repository.entity.ResourceAnnex;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2023/4/17 16:27
 * @description Es同步消息
 */
@Data
public class EsSyncMessage implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * es同步 交换机
     */
    public static final String EXCHANGE = "KUNIFY4_EXCHANGE_ES_SYNC";
    /**
     * es同步 消息队列
     */
    public static final String QUEUE = "KUNIFY4_QUEUE_ES_SYNC";
    /**
     * es同步 routingKey
     */
    public static final String ROUTING_KEY = "KUNIFY4_ROUTING_ES_SYNC";
    /**
     * 当前用户信息
     */
    private String user;
    /**
     * 资源对象
     */
    private Resource resource;
    /**
     * 附件对象
     */
    private ResourceAnnex resourceAnnex;
    /**
     * 资源内容
     */
    private String content;
    /**
     * 资源类型
     */
    private String fileType;

}
