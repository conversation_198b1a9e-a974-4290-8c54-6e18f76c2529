package org.irm.lab.kg.repository.processing.process;

import org.irm.lab.common.annotation.MongoDao;
import org.irm.lab.common.repository.mongo.BaseDao;
import org.irm.lab.kg.entity.processing.process.ProcessRecords;

/**
 * <AUTHOR> <br/>
 * @date 2024/6/26 <br/>
 * &#064;Copyright  博客：<a href="https://eliauku.gitee.io/">...</a>  ||  per aspera and astra <br/>
 */
@MongoDao
public class ProcessRecordsRepository extends BaseDao<ProcessRecords> {

}
