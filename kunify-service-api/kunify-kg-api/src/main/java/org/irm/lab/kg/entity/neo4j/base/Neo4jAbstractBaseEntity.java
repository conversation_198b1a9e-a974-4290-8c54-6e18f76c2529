package org.irm.lab.kg.entity.neo4j.base;

import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.neo4j.ogm.annotation.GeneratedValue;
import org.neo4j.ogm.annotation.Id;
import org.neo4j.ogm.id.UuidStrategy;

import java.util.Date;

/**
 * <AUTHOR>
 * @date 2023-01-04
 */
@Setter
@Getter
@NoArgsConstructor
public class Neo4jAbstractBaseEntity {

    @Id
    @GeneratedValue(strategy = UuidStrategy.class)
    private String id;

    /**
     * 创建时间
     */
    @ApiModelProperty(value = "创建时间")
    private Date createTime;

    /**
     * 更新时间
     */
    @ApiModelProperty(value = "更新时间")
    private Date updateTime;

    /**
     * 创建人
     */
    @ApiModelProperty(value = "创建人")
    private String createUser;

    /**
     * 更新人
     */
    @ApiModelProperty(value = "更新人")
    private String updateUser;

}
