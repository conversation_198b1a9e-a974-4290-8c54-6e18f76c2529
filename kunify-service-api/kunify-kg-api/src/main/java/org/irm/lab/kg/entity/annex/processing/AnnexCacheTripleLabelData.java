package org.irm.lab.kg.entity.annex.processing;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import org.irm.lab.common.annotation.MongoDBCollection;

/**
 * <AUTHOR>
 * @date 2023/4/23 10:29
 * @description 附件-标签关系数据缓存
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@EqualsAndHashCode(callSuper = true)
@MongoDBCollection(collectionName = "kunify_kg_annex_cache_triple_label_data")
@ApiModel(value = "附件标签关系数据缓存")
public class AnnexCacheTripleLabelData extends AnnexTripleLabelData {
    @ApiModelProperty(value = "数据来源", notes = "元数据转化/知识标注/文档解析")
    private String type;
}
