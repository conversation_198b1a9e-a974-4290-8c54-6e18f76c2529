package org.irm.lab.kg.constant;

/**
 * <AUTHOR>
 * @date 2023/3/30 14:04
 * @description 标签常量池
 */
public interface LabelConstant {
    String CONCEPT = "概念";
    String RELATION = "关系";
    String PROPERTY = "属性";
    String TOP_LABEL = "公文";
    String DOCUMENT = "文件";
    String ZHI_DU = "制度";
    String ANNEX_LABEL = "附件";
    String RELEVANCY_ANNEX = "关联附件";
    String BELONGS = "所属";
    String TOPIC = "议题";
    String PEOPLE = "人员";
    String DEPARTMENT = "机构/部门";
    // 批示内容
    String INSTRUCTION = "批示内容";

    String POST = "职务";
    String TAKE_OFFICE = "任职";
    String APPOINT = "任命";
    String GRANTOR = "授予人员";
    String GIVE_THE_TITLE_OF = "被授予称号";
    String GRANT_DEPARTMENT = "授予机构/部门";
    String MENTION_TITLE = "提及称号";
}
