# Complete HTTP Header Truncation Solution

## 🎯 Implementation Steps

### Step 1: Update Application Configurations

#### For kunify-system service (`application.yml`):
```yaml
server:
  tomcat:
    max-http-header-size: 65536  # 64KB
    max-http-post-size: 2097152  # 2MB

feign:
  client:
    config:
      default:
        connectTimeout: 10000
        readTimeout: 60000
  httpclient:
    max-header-size: 65536  # 64KB
    enabled: true
```

#### For kunify-user service (`application.yml`):
```yaml
server:
  tomcat:
    max-http-header-size: 65536  # 64KB
    max-http-post-size: 2097152  # 2MB
```

#### For kunify-gateway service (`application.yml`):
```yaml
server:
  tomcat:
    max-http-header-size: 65536  # 64KB

spring:
  cloud:
    gateway:
      httpclient:
        max-header-size: 65536  # 64KB
```

### Step 2: Add Required Dependencies

Add to `kunify-common/pom.xml`:
```xml
<dependency>
    <groupId>io.github.openfeign</groupId>
    <artifactId>feign-httpclient</artifactId>
</dependency>
<dependency>
    <groupId>org.apache.httpcomponents</groupId>
    <artifactId>httpclient</artifactId>
</dependency>
```

### Step 3: Restart Services in Order

1. **kunify-gateway** (first - to apply new header limits)
2. **kunify-user** (second - to apply new receiving limits)
3. **kunify-system** (last - to apply new sending limits)

### Step 4: Test and Verify

#### Test Command:
```bash
curl -H "Authorization: Bearer YOUR_JWT_TOKEN" \
     http://your-gateway/menu/current-menu-list
```

#### Expected Log Output:

**Gateway logs:**
```
INFO - AuthGlobalFilter.filter() 完整用户信息长度: 260 字符
INFO - AuthGlobalFilter.filter() 压缩后用户信息长度: XX 字符
```

**kunify-system logs:**
```
INFO - 原始user头长度: XX 字符
INFO - user头长度: XX 字符
INFO - ✅ 最终确认：Feign请求模板中包含user头
```

**kunify-user logs:**
```
INFO - user头长度: XX 字符 (should match system service)
INFO - ✅ user头JSON解析成功，包含字段: [user_id, tenant_id, ...]
INFO - 解析得到用户ID: 63b79eb5c7531a66748e230a
```

## 🔧 Alternative Solutions

### Solution A: If Configuration Changes Don't Work

Use Redis for user context caching:

```java
// In AuthGlobalFilter
String userId = userJson.getStr("user_id");
String sessionKey = "user_session:" + userId;
redisTemplate.opsForValue().set(sessionKey, fullUserStr, 30, TimeUnit.MINUTES);

// Only send user ID in header
JSONObject minimalUser = JSONUtil.createObj();
minimalUser.put("user_id", userId);
minimalUser.put("session_key", sessionKey);
```

### Solution B: Use Custom Header Compression

```java
// Compress user data using GZIP
public String compressUserData(String userData) {
    try {
        ByteArrayOutputStream baos = new ByteArrayOutputStream();
        GZIPOutputStream gzipOut = new GZIPOutputStream(baos);
        gzipOut.write(userData.getBytes(StandardCharsets.UTF_8));
        gzipOut.close();
        return Base64.getEncoder().encodeToString(baos.toByteArray());
    } catch (IOException e) {
        log.error("Failed to compress user data", e);
        return userData;
    }
}
```

## 🚨 Troubleshooting

### Issue 1: Still Getting Truncation After Configuration
**Check:**
- Verify all services restarted with new configuration
- Check if there's a load balancer with header size limits
- Verify no proxy servers are truncating headers

### Issue 2: Feign Client Not Using New Configuration
**Solution:**
```java
@FeignClient(name = "kunify-user", configuration = FeignConfiguration.class)
public interface UserProviderFeign {
    // Your methods
}
```

### Issue 3: Network Infrastructure Limits
**Check:**
- Nginx: `large_client_header_buffers 4 64k;`
- HAProxy: `tune.http.maxhdr 64`
- AWS ALB: Default 8KB limit (cannot be changed)

## 📊 Monitoring and Validation

### Add Header Size Monitoring:

```java
@Component
public class HeaderSizeMonitor {
    
    @EventListener
    public void onRequest(ServletRequestEvent event) {
        HttpServletRequest request = (HttpServletRequest) event.getServletRequest();
        String userHeader = request.getHeader("user");
        
        if (userHeader != null) {
            int size = userHeader.length();
            if (size > 1000) {  // Threshold
                log.warn("Large user header detected: {} characters", size);
            }
        }
    }
}
```

## ✅ Success Criteria

The solution is working when you see:

1. **No JSON parsing errors** in kunify-user service
2. **Consistent header lengths** between sending and receiving services
3. **Complete user information** available in all services
4. **Menu functionality** working correctly
5. **No truncation warnings** in logs

## 🔄 Rollback Plan

If issues occur, quickly rollback by:

1. Remove new configuration from `application.yml`
2. Restart services
3. Use the enhanced parsing method as temporary fix
4. Investigate infrastructure limitations

The enhanced parsing method will handle truncated headers gracefully while you resolve the underlying configuration issues.
