# User头截断问题诊断

## 问题现象
从你的日志可以看出：
- ✅ FeignAuthRequestInterceptor正常工作
- ✅ kunify-user服务接收到了user头
- ❌ **但user头内容被截断了**：`{"tenant_id":"028641","user_id"}` - 缺少user_id的值

## 可能的原因

### 1. **日志输出限制**
某些日志框架会限制单行日志的长度，导致显示被截断。

### 2. **HTTP头长度限制**
- Web服务器（如Nginx、Apache）可能限制HTTP头的长度
- 应用服务器（如Tomcat）也有头长度限制
- 网关或负载均衡器可能截断长头

### 3. **JSON序列化问题**
在网关层将JWT解析为JSON时可能出现截断。

### 4. **字符编码问题**
特殊字符可能导致JSON被错误截断。

## 立即诊断步骤

### 第1步：重启服务并测试
1. **重启kunify-system和kunify-user服务**
2. **调用菜单接口**
3. **查看新的详细日志**

现在日志会显示：
- 原始user头的完整内容和长度
- 传播过程中user头的完整内容和长度
- 接收端user头的完整内容和长度

### 第2步：对比日志输出

#### kunify-system服务应该显示：
```
INFO  - 原始user头长度: XXX 字符
INFO  - 原始user头完整内容: [{"tenant_id":"028641","user_id":"actual-user-id",...}]
INFO  - ✅ 原始user头JSON解析成功，包含字段: [tenant_id, user_id, ...]
INFO  - ✅ 包含user_id字段: actual-user-id
```

#### FeignAuthRequestInterceptor应该显示：
```
INFO  - user头长度: XXX 字符
INFO  - user头完整内容: [{"tenant_id":"028641","user_id":"actual-user-id",...}]
```

#### kunify-user服务应该显示：
```
INFO  - user头长度: XXX 字符
INFO  - user头完整内容: [{"tenant_id":"028641","user_id":"actual-user-id",...}]
INFO  - ✅ user头JSON解析成功，包含字段: [tenant_id, user_id, ...]
INFO  - ✅ 包含user_id字段: actual-user-id
```

### 第3步：分析对比结果

#### 情况A：原始头就是截断的
**症状**：kunify-system服务显示的原始user头就缺少user_id值
**原因**：网关层AuthGlobalFilter解析JWT时出现问题
**解决**：检查网关的AuthGlobalFilter实现

#### 情况B：传播过程中被截断
**症状**：kunify-system显示完整，但kunify-user接收到的是截断的
**原因**：网络层面的限制（负载均衡器、代理服务器等）
**解决**：检查网络配置

#### 情况C：长度不一致
**症状**：显示的长度和实际内容不匹配
**原因**：字符编码问题或特殊字符
**解决**：检查字符编码设置

## 常见解决方案

### 解决方案1：增加HTTP头长度限制

#### Nginx配置：
```nginx
http {
    large_client_header_buffers 4 32k;
    client_header_buffer_size 32k;
}
```

#### Tomcat配置：
```xml
<Connector port="8080" 
           maxHttpHeaderSize="32768"
           ... />
```

### 解决方案2：检查网关配置

查看`AuthGlobalFilter`的实现：
```java
String userStr = jwsObject.getPayload().toString();
// 确保这里的userStr是完整的
log.info("网关解析的用户信息: {}", userStr);
```

### 解决方案3：使用Base64编码

如果JSON内容太长或包含特殊字符，可以考虑Base64编码：

#### 在网关中：
```java
String userStr = jwsObject.getPayload().toString();
String encodedUser = Base64.getEncoder().encodeToString(userStr.getBytes());
ServerHttpRequest request = exchange.getRequest().mutate()
    .header("user", encodedUser)
    .build();
```

#### 在服务中：
```java
String userHeader = request.getHeader("user");
String decodedUser = new String(Base64.getDecoder().decode(userHeader));
JSONObject userJsonObject = JSONUtil.parseObj(decodedUser);
```

## 临时修复方案

如果问题仍然存在，可以使用这个临时方案：

### 在UserProviderServiceImpl中添加容错处理：

```java
private String extractUserIdFromHeader(String userHeader) {
    try {
        JSONObject userJsonObject = JSONUtil.parseObj(userHeader);
        String userId = userJsonObject.getStr(AuthConstant.USER_ID);
        
        if (ObjectUtil.isEmpty(userId)) {
            // 如果user_id为空，尝试从其他字段获取
            log.warn("user_id字段为空，尝试其他方式获取用户ID");
            
            // 可以尝试从ThreadLocal或其他方式获取
            String userFromThreadLocal = ThreadLocalUtil.get("user");
            if (ObjectUtil.isNotEmpty(userFromThreadLocal)) {
                JSONObject threadLocalUser = JSONUtil.parseObj(userFromThreadLocal);
                userId = threadLocalUser.getStr(AuthConstant.USER_ID);
                log.info("从ThreadLocal获取到用户ID: {}", userId);
            }
        }
        
        return userId;
    } catch (Exception e) {
        log.error("解析用户头失败: {}", e.getMessage());
        throw new ServiceException("用户信息解析失败");
    }
}
```

## 下一步行动

1. **立即重启服务**并测试
2. **查看详细日志**，对比三个阶段的user头内容
3. **根据日志结果**确定截断发生的位置
4. **应用相应的解决方案**

请运行测试后，将三个服务的详细日志发给我，我可以帮你精确定位问题所在。
